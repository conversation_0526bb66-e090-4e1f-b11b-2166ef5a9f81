import { WebSocket } from 'ws';
import { streamAITherapistResponse, transcribeAudio } from './openai';
import { VoiceConfig, ConversationConfig } from '@shared/schema';
import { loadVoiceConfig, loadConversationConfig } from './db';
import { WebSocketMessage, ConversationMessage, ClientState, ClientMetrics } from './types';
import fs from 'fs';
import path from 'path';
import { v4 as randomUUID } from 'uuid';

// Ensure uploads directory exists
const AUDIO_DIR = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(AUDIO_DIR)) {
  fs.mkdirSync(AUDIO_DIR, { recursive: true });
}

// Interface for real-time audio state
interface RealTimeAudioState {
  audioBuffer: Buffer[];
  isProcessing: boolean;
  lastProcessedTime: number;
  conversationHistory: ConversationMessage[];
  voiceConfig: VoiceConfig;
  conversationConfig: ConversationConfig;
}

// Map to store real-time audio states
const audioStates = new Map<WebSocket, RealTimeAudioState>();

// Process audio data in real-time
export async function processRealTimeAudio(
  ws: WebSocket,
  audioData: Buffer,
  state: RealTimeAudioState
): Promise<void> {
  // Add new audio data to buffer
  state.audioBuffer.push(audioData);
  
  // Calculate total buffer size
  const totalBufferSize = state.audioBuffer.reduce((size, buf) => size + buf.length, 0);
  
  // Process audio if enough data has accumulated (about 0.5 seconds of audio at 16kHz, 16-bit)
  // This is approximately 16000 samples/sec * 0.5 sec * 2 bytes/sample = 16000 bytes
  if (totalBufferSize >= 16000 && !state.isProcessing) {
    state.isProcessing = true;
    
    try {
      // Concatenate all buffers
      const audioBuffer = Buffer.concat(state.audioBuffer);
      
      // Clear the buffer after concatenation
      state.audioBuffer = [];
      
      // Save audio to a temporary file for debugging
      const tempFilename = `${randomUUID()}.webm`;
      const filePath = path.join(AUDIO_DIR, tempFilename);
      fs.writeFileSync(filePath, audioBuffer);
      console.log(`Saved real-time audio chunk to ${filePath}, size: ${audioBuffer.length} bytes`);
      
      // Transcribe the audio
      console.log(`Transcribing real-time audio chunk of size: ${audioBuffer.length} bytes`);
      const transcription = await transcribeAudio(audioBuffer, tempFilename);
      
      // Process the transcription if we got text
      if (transcription.text && transcription.text.trim().length > 0) {
        console.log(`Real-time transcription: "${transcription.text}"`);
        
        // Add user message to conversation history
        state.conversationHistory.push({
          role: 'user',
          content: transcription.text,
          timestamp: new Date()
        });
        
        // Send transcription to client for immediate feedback
        ws.send(JSON.stringify({
          type: 'transcription',
          text: transcription.text
        }));
        
        // Stream AI response
        await streamAITherapistResponse(
          ws,
          transcription.text,
          state.conversationHistory,
          true,
          undefined,
          state.voiceConfig as any,
          state.conversationConfig
        );
      } else {
        console.log('No text transcribed from audio chunk');
      }
    } catch (error) {
      console.error('Error processing real-time audio:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Failed to process audio: ' + (error instanceof Error ? error.message : String(error))
      }));
    } finally {
      state.isProcessing = false;
      state.lastProcessedTime = Date.now();
    }
  }
}

// Handle WebSocket message for audio processing
export async function handleRealTimeAudioMessage(
  ws: WebSocket,
  message: any
): Promise<void> {
  // Get or create state for this connection
  let state = audioStates.get(ws);
  
  if (!state) {
    console.error('No state found for WebSocket connection. Initialize first.');
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Session not initialized. Please start a new session.'
    }));
    return;
  }
  
  try {
    // Process different audio data formats
    let audioBuffer: Buffer;
    
    if (message.audio_data && Array.isArray(message.audio_data)) {
      console.log(`Received audio data array of length: ${message.audio_data.length}`);
      
      // Handle int16 format from client
      if (message.format === 'int16') {
        // Create a proper Int16Array and then convert to Buffer
        const int16Samples = new Int16Array(message.audio_data.length);
        for (let i = 0; i < message.audio_data.length; i++) {
          int16Samples[i] = message.audio_data[i];
        }
        
        // Convert to buffer ensuring proper byte layout
        audioBuffer = Buffer.from(int16Samples.buffer);
        console.log(`Processed int16 audio format, samples: ${message.audio_data.length}, buffer size: ${audioBuffer.length} bytes`);
      } else {
        // Legacy format handling - convert float to int16
        const floatSamples = message.audio_data;
        const int16Samples = new Int16Array(floatSamples.length);
        
        for (let i = 0; i < floatSamples.length; i++) {
          const sample = Math.max(-1, Math.min(1, floatSamples[i]));
          int16Samples[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);
        }
        
        audioBuffer = Buffer.from(int16Samples.buffer);
        console.log(`Processed float audio data, converted to int16 format: ${audioBuffer.length} bytes`);
      }
    } else if (typeof message.audioData === 'string') {
      // Convert base64 audioData to Buffer
      audioBuffer = Buffer.from(message.audioData, 'base64');
      console.log(`Processed base64 audio data: ${audioBuffer.length} bytes`);
    } else {
      console.error('No valid audio data found in message');
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid audio data format'
      }));
      return;
    }
    
    // Process the audio data
    await processRealTimeAudio(ws, audioBuffer, state);
  } catch (error) {
    console.error('Error handling real-time audio message:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Error processing audio data: ' + (error instanceof Error ? error.message : String(error))
    }));
  }
}

// Initialize real-time audio state
export async function initializeRealTimeAudio(
  ws: WebSocket,
  userId: string
): Promise<void> {
  try {
    console.log(`Initializing real-time audio for user: ${userId}`);
    
    // Load configurations
    const voiceConfig = await loadVoiceConfig(userId);
    const conversationConfig = await loadConversationConfig(userId);
    
    if (!voiceConfig || !conversationConfig) {
      console.warn(`Missing configurations for user ${userId}, using defaults`);
    }
    
    // Use defaults if configurations are missing
    const defaultVoiceConfig: VoiceConfig = {
      voice: 'shimmer',
      speed: 1.0,
      pitch: 1.0,
      emphasis: 'moderate',
      prosody: {
        emotionalRange: 70,
        questionInflection: 60,
        pauseDuration: 50
      }
    };
    
    const defaultConversationConfig: ConversationConfig = {
      temperature: 0.7,
      maxTokens: 1000,
      presencePenalty: 0.6,
      frequencyPenalty: 0.4,
      turnTaking: {
        backchannelFrequency: 50,
        minSilenceDuration: 500,
        maxInterruptionGap: 200
      },
      responseStyle: {
        minResponseLength: 50,
        maxResponseLength: 150,
        temperature: 0.7,
        presencePenalty: 0.6,
        frequencyPenalty: 0.4
      }
    };
    
    // Create state with configurations
    const state: RealTimeAudioState = {
      audioBuffer: [],
      isProcessing: false,
      lastProcessedTime: Date.now(),
      conversationHistory: [],
      voiceConfig: voiceConfig || defaultVoiceConfig,
      conversationConfig: conversationConfig || defaultConversationConfig
    };
    
    // Store state for this connection
    audioStates.set(ws, state);
    
    console.log(`Real-time audio initialized for user ${userId}`);
    ws.send(JSON.stringify({
      type: 'ready',
      message: 'Voice session initialized successfully'
    }));
  } catch (error) {
    console.error('Error initializing real-time audio:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to initialize voice session: ' + (error instanceof Error ? error.message : String(error))
    }));
  }
}

// Clean up real-time audio state when WebSocket closes
export function cleanupRealTimeAudio(ws: WebSocket): void {
  if (audioStates.has(ws)) {
    console.log('Cleaning up real-time audio state');
    audioStates.delete(ws);
  }
}