/**
 * Temporary login fix route
 * This provides a direct authentication route to bypass Passport.js issues
 */

import { Request, Response } from 'express';
import { authenticateUser } from './pg-direct';

// Direct login route handler
export async function directLogin<PERSON>andler(req: Request, res: Response) {
  try {
    const { username, password } = req.body;
    
    console.log(`[DIRECT-LOGIN] Login attempt with username: ${username}`);
    
    if (!username || !password) {
      return res.status(400).json({ 
        success: false, 
        message: 'Username and password required'
      });
    }
    
    const user = await authenticateUser(username, password);
    
    if (!user) {
      console.log(`[DIRECT-LOGIN] Authentication failed for user: ${username}`);
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid credentials'
      });
    }
    
    // Use Passport's login method to properly set up the session
    req.login(user, (err) => {
      if (err) {
        console.error('[DIRECT-LOGIN] Error setting up session:', err);
        return res.status(500).json({
          success: false,
          message: 'Session setup error'
        });
      }

      console.log(`[DIRECT-LOGIN] Session created with ID: ${req.sessionID}`);
      console.log(`[DIRECT-LOGIN] User authenticated:`, user.username);

      // Return success with user data
      return res.status(200).json({
        success: true,
        user
      });
    });
  } catch (error) {
    console.error('[DIRECT-LOGIN] Error during authentication:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Authentication error' 
    });
  }
}

// Create admin user for testing
export async function createAdminUser() {
  const { pgPool, hashPassword } = await import('./pg-direct');
  
  try {
    // Check if admin exists
    const checkResult = await pgPool.query(
      "SELECT id FROM users WHERE username = 'admin'"
    );
    
    if (checkResult.rows.length > 0) {
      console.log('Admin user already exists');
      return;
    }
    
    // Create admin user
    const hashedPassword = await hashPassword('admin123');
    
    await pgPool.query(
      `INSERT INTO users (username, password, name, email, user_role, professional_role) 
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        'admin',
        hashedPassword,
        'Administrator',
        '<EMAIL>',
        'admin',
        'System Administrator'
      ]
    );
    
    console.log('Admin user created successfully');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}