{"runs": [{"id": ".replit/run", "name": "sh -c 'npm run dev'", "fileTypeAttrs": {}, "run": {"command": {"args": ["sh", "-c", "npm run dev"]}}}, {"id": "module:nodejs-20/runner:nodeJS", "name": "Node.js", "fileParam": true, "language": "javascript", "fileTypeAttrs": {}, "displayVersion": "20.18.1", "run": {"command": {"args": ["sh", "-c", "/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin/node $file"]}}, "defaultEntrypoints": ["index.js", "main.js"]}], "debuggers": [{"id": "module:nodejs-20/debugger:nodeDAP", "name": "Node DAP", "fileParam": true, "language": "javascript", "fileTypeAttrs": {}}], "languageServers": [{"id": "module:replit/languageServer:dotreplit-lsp", "name": ".replit LSP", "language": "dotreplit", "fileTypeAttrs": {}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/bz8k1njgmm249fr5krhaq1jsi7jrhx5k-taplo-0.patched/bin/taplo lsp -c /nix/store/2zhz6va20gizdlqmvryab9b7pn6dp0v1-taplo-config.toml stdio"]}}}, {"id": "module:web/languageServer:css", "name": "CSS Language Server", "language": "css", "fileTypeAttrs": {"extensions": [".css", ".less", ".scss"]}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/ydkb3d7r0zs7wd7jcwnk1v24qmzjqnkh-vscode-langservers-extracted-4.10.0/bin/vscode-css-language-server --stdio"]}, "configurationJson": "{\"css\":{\"completion\":{\"completePropertyWithSemicolon\":true,\"triggerPropertyValueCompletion\":true},\"hover\":{\"documentation\":true,\"references\":true},\"lint\":{\"argumentsInColorFunction\":\"error\",\"boxModel\":\"ignore\",\"compatibleVendorPrefixes\":\"ignore\",\"duplicateProperties\":\"warning\",\"emptyRules\":\"warning\",\"float\":\"ignore\",\"fontFaceProperties\":\"warning\",\"hexColorLength\":\"error\",\"idSelector\":\"ignore\",\"ieHack\":\"ignore\",\"importStatement\":\"ignore\",\"important\":\"ignore\",\"propertyIgnoredDueToDisplay\":\"warning\",\"universalSelector\":\"ignore\",\"unknownAtRules\":\"warning\",\"unknownProperties\":\"warning\",\"unknownVendorSpecificProperties\":\"ignore\",\"validProperties\":[],\"vendorPrefix\":\"warning\",\"zeroUnits\":\"ignore\"},\"trace\":{\"server\":\"off\"}},\"less\":{\"completion\":{\"completePropertyWithSemicolon\":true,\"triggerPropertyValueCompletion\":true},\"hover\":{\"documentation\":true,\"references\":true},\"lint\":{\"argumentsInColorFunction\":\"error\",\"boxModel\":\"ignore\",\"compatibleVendorPrefixes\":\"ignore\",\"duplicateProperties\":\"warning\",\"emptyRules\":\"warning\",\"float\":\"ignore\",\"fontFaceProperties\":\"warning\",\"hexColorLength\":\"error\",\"idSelector\":\"ignore\",\"ieHack\":\"ignore\",\"importStatement\":\"ignore\",\"important\":\"ignore\",\"propertyIgnoredDueToDisplay\":\"warning\",\"universalSelector\":\"ignore\",\"unknownAtRules\":\"warning\",\"unknownProperties\":\"warning\",\"unknownVendorSpecificProperties\":\"ignore\",\"validProperties\":[],\"vendorPrefix\":\"warning\",\"zeroUnits\":\"ignore\"},\"trace\":{\"server\":\"off\"}},\"scss\":{\"completion\":{\"completePropertyWithSemicolon\":true,\"triggerPropertyValueCompletion\":true},\"hover\":{\"documentation\":true,\"references\":true},\"lint\":{\"argumentsInColorFunction\":\"error\",\"boxModel\":\"ignore\",\"compatibleVendorPrefixes\":\"ignore\",\"duplicateProperties\":\"warning\",\"emptyRules\":\"warning\",\"float\":\"ignore\",\"fontFaceProperties\":\"warning\",\"hexColorLength\":\"error\",\"idSelector\":\"ignore\",\"ieHack\":\"ignore\",\"importStatement\":\"ignore\",\"important\":\"ignore\",\"propertyIgnoredDueToDisplay\":\"warning\",\"universalSelector\":\"ignore\",\"unknownAtRules\":\"warning\",\"unknownProperties\":\"warning\",\"unknownVendorSpecificProperties\":\"ignore\",\"validProperties\":[],\"vendorPrefix\":\"warning\",\"zeroUnits\":\"ignore\"},\"trace\":{\"server\":\"off\"}}}", "initializationOptionsJson": "{\"provideFormatter\":true}"}}, {"id": "module:web/languageServer:html", "name": "HTML Language Server", "language": "html", "fileTypeAttrs": {"extensions": [".html"]}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/ydkb3d7r0zs7wd7jcwnk1v24qmzjqnkh-vscode-langservers-extracted-4.10.0/bin/vscode-html-language-server --stdio"]}, "configurationJson": "{\"html\":{\"autoClosingTags\":true,\"autoCreateQuotes\":true,\"completion\":{\"attributeDefaultValue\":\"doublequotes\"},\"customData\":[],\"format\":{\"contentUnformatted\":\"pre,code,textarea\",\"enable\":true,\"endWithNewline\":false,\"extraLiners\":\"head, body, /html\",\"indentHandlebars\":false,\"indentInnerHtml\":false,\"preserveNewLines\":true,\"templating\":false,\"unformatted\":\"wbr\",\"unformattedContentDelimiter\":\"\",\"wrapAttributes\":\"auto\",\"wrapLineLength\":120},\"hover\":{\"documentation\":true,\"references\":true},\"mirrorCursorOnMatchingTag\":false,\"suggest\":{\"html5\":true},\"trace\":{\"server\":\"off\"},\"validate\":{\"scripts\":true,\"styles\":true}}}", "initializationOptionsJson": "{\"enable\":true,\"provideFormatter\":true}"}}, {"id": "module:web/languageServer:typescript-language-server", "name": "TypeScript Language Server", "language": "javascript", "fileTypeAttrs": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".mjs", ".mts", ".cjs", ".cts", ".es6", ".json"]}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/9cd76kqpml5gkw8jjnjx0flwdf0a1gv1-typescript-language-server-4.3.3/bin/typescript-language-server --stdio"]}, "initializationOptionsJson": "{\"tsserver\":{\"fallbackPath\":\"/nix/store/g6ns6m42fvybfzb2xjppcsfmb6k0jv5x-typescript-5.6.3/lib/node_modules/typescript/lib\"}}"}, "displayVersion": "4.3.3"}], "packagers": [{"id": "module:nodejs-20/packager:upmNodejs", "name": "Node.js packager (npm, yarn, pnpm, bun)", "language": "nodejs", "packageSearch": true, "guessImports": true}], "formatters": [{"id": "module:nodejs-20/formatter:prettier", "name": "<PERSON>ttier", "startCommand": {"args": ["/nix/store/070ycjyhpfv8n895zq7yz6z0pp57g0q9-run-prettier/bin/run-prettier"], "lifecycle": "STDIN", "splitStderr": true}, "fileTypeAttrs": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".json", ".html"]}, "displayVersion": "3.3.3", "supportsRangeFormatting": true}, {"id": "module:replit/languageServer:dotreplit-lsp", "name": ".replit LSP", "fileTypeAttrs": {}}, {"id": "module:web/languageServer:css", "name": "CSS Language Server", "fileTypeAttrs": {"extensions": [".css", ".less", ".scss"]}}, {"id": "module:web/languageServer:html", "name": "HTML Language Server", "fileTypeAttrs": {"extensions": [".html"]}}, {"id": "module:web/languageServer:typescript-language-server", "name": "TypeScript Language Server", "fileTypeAttrs": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".mjs", ".mts", ".cjs", ".cts", ".es6", ".json"]}, "displayVersion": "4.3.3"}]}