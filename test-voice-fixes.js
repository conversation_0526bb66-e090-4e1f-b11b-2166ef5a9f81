#!/usr/bin/env node

import { WebSocket } from 'ws';

console.log('🔧 TESTING VOICE CONVERSATION FIXES');
console.log('=' * 50);

const ws = new WebSocket('ws://localhost:5000/ws');

let messageCount = 0;
let sessionReady = false;
let greetingReceived = false;
let speechDetected = false;
let transcriptionReceived = false;
let responseReceived = false;
const receivedMessages = [];

function logEvent(event, details = '', important = false) {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const logLine = `[${timestamp}] ${event}${details ? ': ' + details : ''}`;
  console.log(important ? `🔥 ${logLine}` : logLine);
}

// Generate realistic test audio data
function generateTestAudioChunk(duration = 0.1) {
  const sampleRate = 24000;
  const samples = Math.floor(sampleRate * duration);
  const buffer = new ArrayBuffer(samples * 2);
  const view = new DataView(buffer);
  
  for (let i = 0; i < samples; i++) {
    const t = i / sampleRate;
    const fundamental = 150;
    const harmonics = Math.sin(2 * Math.PI * fundamental * t) * 0.3 +
                     Math.sin(2 * Math.PI * fundamental * 2 * t) * 0.2 +
                     Math.sin(2 * Math.PI * fundamental * 3 * t) * 0.1;
    
    const noise = (Math.random() - 0.5) * 0.1;
    const envelope = Math.sin(t * 10) * 0.5 + 0.5;
    
    const sample = (harmonics + noise) * envelope * 0.4;
    const int16Sample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));
    view.setInt16(i * 2, int16Sample, true);
  }
  
  const uint8Array = new Uint8Array(buffer);
  return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
}

ws.on('open', () => {
  logEvent('WebSocket connected', '', true);
  
  const startMessage = {
    type: "start",
    userId: "test-fixes",
    clientId: "test-fixes",
    useRealtimeAPI: true,
    mode: "realtime",
    behavior: {
      model: "gpt-4o-realtime-preview-2024-10-01",
      temperature: 0.7,
      voice: { voice: "shimmer", speed: 1.0 }
    },
    instructions: "You are Vale, an empathetic AI therapeutic assistant. Greet the user warmly and ask how they're feeling today."
  };
  
  logEvent('Sending start message', '', true);
  ws.send(JSON.stringify(startMessage));
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    receivedMessages.push(message.type);
    
    switch (message.type) {
      case 'ready':
        sessionReady = true;
        logEvent('Session ready', 'Starting audio test in 2 seconds', true);
        setTimeout(startAudioTest, 2000);
        break;
        
      case 'session.created':
        logEvent('OpenAI session created', `ID: ${message.session?.id}`, true);
        break;
        
      case 'session.updated':
        const vadType = message.session?.turn_detection?.type || 'none';
        logEvent('Session configured', `VAD: ${vadType}`, true);
        break;
        
      case 'input_audio_buffer.speech_started':
        speechDetected = true;
        logEvent('SPEECH DETECTED', 'OpenAI VAD working', true);
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        transcriptionReceived = true;
        logEvent('USER TRANSCRIBED', `"${message.transcript}"`, true);
        break;
        
      case 'response.audio_transcript.done':
        const transcript = message.transcript || '';
        if (transcript && !greetingReceived) {
          greetingReceived = true;
          logEvent('AI GREETING', `"${transcript}"`, true);
        } else if (transcript) {
          responseReceived = true;
          logEvent('AI RESPONSE', `"${transcript}"`, true);
        }
        break;
        
      case 'response.audio.delta':
        logEvent('AI AUDIO', `${message.delta?.length || 0} chars`);
        break;
        
      case 'error':
        logEvent('ERROR', message.message, true);
        break;
    }
  } catch (error) {
    logEvent('Parse error', error.message, true);
  }
});

function startAudioTest() {
  logEvent('Starting audio test', 'Sending 8 chunks of realistic audio', true);
  
  let chunkCount = 0;
  const sendChunk = () => {
    if (chunkCount >= 8) {
      logEvent('Audio test complete', 'Waiting for response', true);
      setTimeout(generateReport, 5000);
      return;
    }
    
    const audioData = generateTestAudioChunk(0.1);
    ws.send(JSON.stringify({
      type: 'input_audio_buffer.append',
      audio: audioData
    }));
    
    chunkCount++;
    logEvent(`Audio chunk ${chunkCount}/8`, `${audioData.length} chars`);
    
    setTimeout(sendChunk, 100);
  };
  
  sendChunk();
}

function generateReport() {
  console.log('\n' + '='.repeat(50));
  console.log('🔧 VOICE CONVERSATION FIX TEST RESULTS');
  console.log('='.repeat(50));
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`Session Ready:        ${formatResult(sessionReady)}`);
  console.log(`AI Greeting:          ${formatResult(greetingReceived)}`);
  console.log(`Speech Detection:     ${formatResult(speechDetected)}`);
  console.log(`Transcription:        ${formatResult(transcriptionReceived)}`);
  console.log(`AI Response:          ${formatResult(responseReceived)}`);
  
  console.log('\n📋 MESSAGE FLOW:');
  console.log(`Total Messages:       ${messageCount}`);
  console.log(`Message Types:        ${[...new Set(receivedMessages)].join(', ')}`);
  
  console.log('\n🔧 DIAGNOSIS:');
  if (sessionReady && greetingReceived && speechDetected && transcriptionReceived) {
    console.log('✅ VOICE CONVERSATION SYSTEM IS WORKING!');
    console.log('✅ All core components are functioning correctly');
    console.log('✅ The fixes have resolved the voice interaction issues');
  } else {
    console.log('❌ Some issues remain:');
    if (!sessionReady) console.log('  - Session initialization failed');
    if (!greetingReceived) console.log('  - AI greeting not received');
    if (!speechDetected) console.log('  - Speech detection not working');
    if (!transcriptionReceived) console.log('  - Audio transcription failed');
  }
  
  console.log('\n💡 NEXT STEPS:');
  if (sessionReady && greetingReceived && speechDetected) {
    console.log('1. Test the Admin AI Test Center in the browser');
    console.log('2. Verify microphone permissions are granted');
    console.log('3. Check that audio levels are visible in the UI');
    console.log('4. Speak clearly and check for speech detection events');
  } else {
    console.log('1. Check server logs for any errors');
    console.log('2. Verify OpenAI API key has Realtime API access');
    console.log('3. Test with different audio settings');
  }
  
  ws.close();
}

function formatResult(result) {
  return result ? '✅ PASS' : '❌ FAIL';
}

ws.on('close', () => {
  logEvent('WebSocket closed', '', true);
});

ws.on('error', (error) => {
  logEvent('WebSocket error', error.message, true);
});

// Timeout
setTimeout(() => {
  logEvent('TEST TIMEOUT', 'Ending test', true);
  generateReport();
}, 25000);
