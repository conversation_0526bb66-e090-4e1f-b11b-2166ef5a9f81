nohup: ignoring input
OpenAI SDK Version: OpenAI
Using model: gpt-4o-mini for regular calls
API Key configured: Yes
Warning: connect.session() MemoryStore is not
designed for a production environment, as it will leak
memory, and will not scale past a single process.
PostgreSQL direct connection established
Admin user already exists
[STARTUP] Admin user check complete
Warning: connect.session() MemoryStore is not
designed for a production environment, as it will leak
memory, and will not scale past a single process.
[STARTUP] Direct login route registered at /api/direct-login
/home/<USER>/workspace/server/vite.ts:77
    throw new Error(
          ^


Error: Could not find the build directory: /home/<USER>/workspace/server/public, make sure to build the client first
    at serveStatic (/home/<USER>/workspace/server/vite.ts:77:11)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:83:5)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)

Node.js v20.18.1
