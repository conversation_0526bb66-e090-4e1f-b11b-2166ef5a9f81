<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microphone Test for Real-Time Voice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .level-meter {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .level-bar {
            height: 100%;
            background: linear-gradient(to right, #28a745, #ffc107, #dc3545);
            width: 0%;
            transition: width 0.1s ease;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Real-Time Voice Conversation Test</h1>
        <p>This test will verify that your microphone works correctly with the real-time voice system.</p>
        
        <div id="status-container">
            <div id="browser-support" class="status info">Checking browser support...</div>
            <div id="mic-permission" class="status warning">Microphone permission not requested</div>
            <div id="audio-context" class="status warning">Audio context not initialized</div>
            <div id="websocket-status" class="status warning">WebSocket not connected</div>
        </div>
        
        <div>
            <button id="test-mic" onclick="testMicrophone()">Test Microphone</button>
            <button id="test-websocket" onclick="testWebSocket()">Test WebSocket</button>
            <button id="test-full-flow" onclick="testFullFlow()" disabled>Test Full Voice Flow</button>
        </div>
        
        <div>
            <h3>Microphone Level</h3>
            <div class="level-meter">
                <div id="level-bar" class="level-bar"></div>
            </div>
            <span id="level-text">0%</span>
        </div>
        
        <div>
            <h3>Test Log</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        let audioContext = null;
        let mediaStream = null;
        let analyzer = null;
        let processor = null;
        let websocket = null;
        let isRecording = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        // Check browser support
        function checkBrowserSupport() {
            log('🔍 Checking browser support...');
            
            const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
            const hasAudioContext = !!(window.AudioContext || window.webkitAudioContext);
            const hasWebSocket = !!window.WebSocket;
            
            if (hasGetUserMedia && hasAudioContext && hasWebSocket) {
                updateStatus('browser-support', 'success', '✅ Browser fully supported');
                log('✅ Browser support: getUserMedia, AudioContext, and WebSocket available');
                return true;
            } else {
                updateStatus('browser-support', 'error', '❌ Browser not supported');
                log(`❌ Browser support missing: getUserMedia=${hasGetUserMedia}, AudioContext=${hasAudioContext}, WebSocket=${hasWebSocket}`);
                return false;
            }
        }

        // Test microphone access and audio processing
        async function testMicrophone() {
            try {
                log('🎤 Testing microphone access...');
                
                // Request microphone permission
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: false,
                        autoGainControl: true,
                        sampleRate: { ideal: 24000, min: 16000, max: 48000 },
                        channelCount: 1
                    }
                });
                
                updateStatus('mic-permission', 'success', '✅ Microphone access granted');
                log('✅ Microphone access granted');
                
                // Create audio context
                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                audioContext = new AudioContextClass({ sampleRate: 24000 });
                
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                }
                
                updateStatus('audio-context', 'success', `✅ Audio context ready (${audioContext.sampleRate}Hz)`);
                log(`✅ Audio context created: ${audioContext.sampleRate}Hz, state: ${audioContext.state}`);
                
                // Set up audio processing
                const source = audioContext.createMediaStreamSource(mediaStream);
                analyzer = audioContext.createAnalyser();
                analyzer.fftSize = 256;
                source.connect(analyzer);
                
                processor = audioContext.createScriptProcessor(4096, 1, 1);
                let frameCount = 0;
                
                processor.onaudioprocess = (e) => {
                    frameCount++;
                    
                    // Update visual level meter
                    const bufferLength = analyzer.frequencyBinCount;
                    const dataArray = new Uint8Array(bufferLength);
                    analyzer.getByteFrequencyData(dataArray);
                    
                    let sum = 0;
                    for (let i = 0; i < bufferLength; i++) {
                        sum += dataArray[i];
                    }
                    const level = (sum / bufferLength / 255) * 100;
                    
                    document.getElementById('level-bar').style.width = `${Math.min(100, level * 3)}%`;
                    document.getElementById('level-text').textContent = `${level.toFixed(1)}%`;
                    
                    // Log first few frames
                    if (frameCount <= 3) {
                        log(`🔄 Audio processor frame ${frameCount}: level=${level.toFixed(2)}%`);
                    }
                    
                    // Test audio data conversion
                    if (frameCount === 5) {
                        const inputData = e.inputBuffer.getChannelData(0);
                        const int16Data = new Int16Array(inputData.length);
                        
                        for (let i = 0; i < inputData.length; i++) {
                            let sample = inputData[i] * 2.0; // Gain
                            sample = Math.max(-1, Math.min(1, sample));
                            int16Data[i] = Math.round(sample * 32767);
                        }
                        
                        const buffer = new ArrayBuffer(int16Data.length * 2);
                        const view = new DataView(buffer);
                        for (let i = 0; i < int16Data.length; i++) {
                            view.setInt16(i * 2, int16Data[i], true);
                        }
                        const uint8Array = new Uint8Array(buffer);
                        const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
                        
                        log(`✅ Audio conversion test: ${base64Audio.length} chars of base64 audio data`);
                    }
                };
                
                source.connect(processor);
                // Don't connect to destination to avoid feedback
                
                log('✅ Audio processing pipeline established');
                isRecording = true;
                
                // Enable full flow test
                document.getElementById('test-full-flow').disabled = false;
                
            } catch (error) {
                updateStatus('mic-permission', 'error', '❌ Microphone access failed');
                updateStatus('audio-context', 'error', '❌ Audio context failed');
                log(`❌ Microphone test failed: ${error.message}`);
            }
        }

        // Test WebSocket connection
        function testWebSocket() {
            try {
                log('🌐 Testing WebSocket connection...');
                
                websocket = new WebSocket('ws://localhost:5000/ws');
                
                websocket.onopen = () => {
                    updateStatus('websocket-status', 'success', '✅ WebSocket connected');
                    log('✅ WebSocket connected to server');
                    
                    // Send test message
                    websocket.send(JSON.stringify({
                        type: 'ping',
                        timestamp: new Date().toISOString()
                    }));
                };
                
                websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    log(`📨 WebSocket message: ${data.type}`);
                };
                
                websocket.onclose = () => {
                    updateStatus('websocket-status', 'warning', '⚠️ WebSocket disconnected');
                    log('⚠️ WebSocket connection closed');
                };
                
                websocket.onerror = (error) => {
                    updateStatus('websocket-status', 'error', '❌ WebSocket connection failed');
                    log(`❌ WebSocket error: ${error}`);
                };
                
            } catch (error) {
                updateStatus('websocket-status', 'error', '❌ WebSocket test failed');
                log(`❌ WebSocket test failed: ${error.message}`);
            }
        }

        // Test full voice flow
        function testFullFlow() {
            if (!isRecording || !websocket || websocket.readyState !== WebSocket.OPEN) {
                log('❌ Cannot test full flow: microphone or WebSocket not ready');
                return;
            }
            
            log('🚀 Starting full voice flow test...');
            
            // Send start message
            websocket.send(JSON.stringify({
                type: "start",
                userId: "test-user",
                clientId: "test-client",
                useRealtimeAPI: true,
                mode: "realtime",
                behavior: {
                    model: "gpt-4o-realtime-preview-2024-10-01",
                    temperature: 0.7,
                    voice: { voice: "shimmer", speed: 1.0 }
                },
                instructions: "You are a test assistant. Respond briefly to test the voice system."
            }));
            
            log('📤 Sent session start message');
            
            // Set up message handling for the test
            websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                
                switch (data.type) {
                    case 'ready':
                        log('✅ Session ready - voice conversation active');
                        break;
                    case 'session.created':
                        log(`✅ OpenAI session created: ${data.session?.id}`);
                        break;
                    case 'session.updated':
                        log(`✅ Session configured with VAD: ${data.session?.turn_detection?.type}`);
                        break;
                    case 'input_audio_buffer.speech_started':
                        log('🎤 Speech detection started');
                        break;
                    case 'input_audio_buffer.speech_stopped':
                        log('🔇 Speech detection stopped');
                        break;
                    case 'conversation.item.input_audio_transcription.completed':
                        log(`📝 User transcribed: "${data.transcript}"`);
                        break;
                    case 'response.audio_transcript.done':
                        log(`🤖 AI response: "${data.transcript}"`);
                        break;
                    case 'response.audio.delta':
                        log(`🔊 AI audio chunk: ${data.delta?.length || 0} chars`);
                        break;
                    default:
                        // Log other important events
                        if (['response.created', 'response.done'].includes(data.type)) {
                            log(`📋 ${data.type}`);
                        }
                }
            };
        }

        // Initialize on page load
        window.onload = () => {
            checkBrowserSupport();
        };
    </script>
</body>
</html>
