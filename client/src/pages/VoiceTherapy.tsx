import { useState } from 'react';
import { useAuth } from '@/hooks/use-auth';
import Layout from '@/components/Layout';
import RealTimeVoiceChat from '@/components/RealTimeVoiceChat';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Client } from '@shared/schema';

export default function VoiceTherapy() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeSession, setActiveSession] = useState(false);
  const [sessionSummary, setSessionSummary] = useState<string | null>(null);
  
  // Get the client profile if user is a client
  const { data: clientProfile, isLoading: isLoadingClient } = useQuery<Client>({
    queryKey: ['/api/clients/me'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/clients/me');
        return await res.json();
      } catch (error) {
        console.error('Error fetching client profile:', error);
        return null;
      }
    },
    enabled: !!user && user.userRole === 'client',
  });
  
  const handleConversationCreated = (conversationId: number) => {
    console.log('New conversation created:', conversationId);
    setActiveSession(true);
    
    toast({
      title: 'Session Started',
      description: 'Your voice therapy session has begun',
      variant: 'default',
    });
  };
  
  const handleConversationEnded = (summary: string, conversationId: number) => {
    console.log('Conversation ended:', conversationId);
    setActiveSession(false);
    setSessionSummary(summary);
    
    toast({
      title: 'Session Ended',
      description: 'Your voice therapy session has been summarized',
      variant: 'default',
    });
  };
  
  const startNewSession = () => {
    setSessionSummary(null);
    setActiveSession(true);
  };
  
  if (isLoadingClient) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[600px]">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      </Layout>
    );
  }
  
  // Ensure user is logged in and is a client or admin
  if (!user || (user.userRole !== 'client' && user.userRole !== 'admin') || (!clientProfile && user.userRole === 'client')) {
    return (
      <Layout>
        <div className="max-w-3xl mx-auto py-10 px-4">
          <h1 className="text-3xl font-bold text-center mb-8">Voice Therapy</h1>
          <Card>
            <CardContent className="py-10 text-center">
              <p className="mb-4">
                You need to be logged in as a client to access voice therapy sessions.
                {user?.userRole === 'admin' ? " As an admin, you can proceed with testing this feature." : ""}
              </p>
              {!user ? (
                <Button variant="default" onClick={() => window.location.href = '/auth'}>
                  Go to Login
                </Button>
              ) : null}
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <div className="max-w-4xl mx-auto py-10 px-4">
        <h1 className="text-3xl font-bold text-center mb-2">
          <span className="bg-gradient-to-r from-blue-600 via-cyan-500 to-purple-500 text-transparent bg-clip-text">
            Voice Therapy Session
          </span>
        </h1>
        <p className="text-center text-muted-foreground mb-8">
          Ancient Wisdom Meets Modern Therapy • AI-Powered Voice Interactions
        </p>
        
        {sessionSummary ? (
          <div className="mb-8">
            <Card>
              <CardContent className="py-6">
                <h2 className="text-xl font-semibold mb-4">Session Summary</h2>
                <div className="prose max-w-none">
                  <p>{sessionSummary}</p>
                </div>
                <div className="mt-6 text-center">
                  <Button onClick={startNewSession}>Start New Session</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : activeSession ? (
          <RealTimeVoiceChat
            userId={user.id}
            clientId={user.userRole === 'admin' ? 1 : clientProfile?.id || 1} // Use ID 1 for admin testing
            onConversationCreated={handleConversationCreated}
            onConversationEnded={handleConversationEnded}
          />
        ) : (
          <div className="text-center py-10">
            <h2 className="text-2xl font-semibold mb-4">Ready for a therapy session?</h2>
            <p className="text-muted-foreground mb-6">
              Experience the fusion of ancient wisdom and modern AI technology with our 
              voice assistant. 
            </p>
            <Button 
              size="lg" 
              onClick={startNewSession} 
              className="bg-gradient-to-r from-blue-600 via-cyan-500 to-purple-500 hover:from-blue-700 hover:via-cyan-600 hover:to-purple-600"
            >
              Begin Voice Therapy Session
            </Button>
          </div>
        )}
      </div>
    </Layout>
  );
}