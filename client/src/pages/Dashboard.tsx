import { useQuery } from "@tanstack/react-query";
import { DashboardStats, Client } from "@shared/schema";
import StatsCards from "@/components/StatsCards";
import ClientsList from "@/components/ClientsList";
import SessionNotesList from "@/components/SessionNotesList";
import InsightsSidebar from "@/components/InsightsSidebar";
import NewNoteForm from "@/components/NewNoteForm";
import { useLocation } from "wouter";

export default function Dashboard() {
  const [, setLocation] = useLocation();

  // Fetch dashboard stats
  const { data: stats, isLoading: isLoadingStats } = useQuery<DashboardStats>({
    queryKey: ["/api/dashboard/stats"],
  });

  // Fetch clients
  const { data: clients, isLoading: isLoadingClients } = useQuery<(Client & { themes: any[]; lastSession: Date | null })[]>({
    queryKey: ["/api/clients"],
  });

  // Fetch recent notes
  const { data: notes, isLoading: isLoadingNotes } = useQuery({
    queryKey: ["/api/notes"],
  });

  const handleClientClick = (clientId: number) => {
    setLocation(`/clients/${clientId}`);
  };

  return (
    <div className="flex h-full">
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="md:flex md:items-center md:justify-between mb-6">
            <div className="flex-1 min-w-0">
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl">
                Dashboard
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Your client insights and recent activities
              </p>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button
                type="button"
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                onClick={() => setLocation("/notes")}
              >
                <span className="material-icons text-sm mr-2">add</span>
                New Session
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <StatsCards stats={stats} isLoading={isLoadingStats} />

          {/* Recent Clients */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Clients</h3>
            <ClientsList
              clients={clients}
              isLoading={isLoadingClients}
              onClientClick={handleClientClick}
            />
            <div className="mt-4 text-right">
              <a href="/clients" className="text-sm font-medium text-blue-600 hover:text-blue-700">
                View all clients →
              </a>
            </div>
          </div>

          {/* Recent Session Notes */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Session Notes</h3>
            <SessionNotesList notes={notes as any} isLoading={isLoadingNotes} />

            {/* New Note Entry Form */}
            <div className="mt-6">
              <NewNoteForm />
            </div>
          </div>
        </div>
      </div>

      {/* Insights Sidebar */}
      <InsightsSidebar
        clients={clients}
        stats={stats}
        className="flex-shrink-0"
      />
    </div>
  );
}
