import { useQuery } from "@tanstack/react-query";
import { useParams, useLocation } from "wouter";
import { SessionNote, ClientTheme } from "@shared/schema";
import SessionNotesList from "@/components/SessionNotesList";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { getInitials, getStatusColor, getThemeColor } from "@/lib/utils";
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from "recharts";

interface ClientDetailParams {
  id: string;
}

export default function ClientDetail() {
  const { id } = useParams<ClientDetailParams>();
  const [, setLocation] = useLocation();
  const clientId = parseInt(id);

  // Fetch client details
  const { data: client, isLoading: isLoadingClient } = useQuery({
    queryKey: [`/api/clients/${clientId}`],
  });

  // Fetch client notes
  const { data: notes, isLoading: isLoadingNotes } = useQuery<SessionNote[]>({
    queryKey: [`/api/clients/${clientId}/notes`],
  });

  // Fetch client themes
  const { data: themes, isLoading: isLoadingThemes } = useQuery<ClientTheme[]>({
    queryKey: [`/api/clients/${clientId}/themes`],
  });

  if (isLoadingClient) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-neutral-dark">Client not found</h3>
        <Button
          className="mt-4"
          onClick={() => setLocation("/")}
        >
          Return to Dashboard
        </Button>
      </div>
    );
  }

  const status = getStatusColor((client as any)?.status || 'stable');

  // Prepare data for charts
  const themesChartData = themes?.map((theme, index) => ({
    name: theme.name,
    value: theme.occurrences,
    color: getThemeColor(index).split(" ")[0]
  })) || [];

  const progressData = notes?.map(note => {
    const date = new Date(note.sessionDate || note.createdAt);
    return {
      date: `${date.getMonth() + 1}/${date.getDate()}`,
      value: 0 // Themes are now stored separately
    };
  }).reverse() || [];

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-16 w-16 rounded-full bg-primary-light flex items-center justify-center text-white font-medium text-xl">
            {getInitials((client as any)?.name || 'Unknown')}
          </div>
          <div className="ml-4">
            <h2 className="text-2xl font-bold leading-7 text-neutral-dark">
              {(client as any)?.name || 'Unknown Client'}
            </h2>
            <div className="mt-1 flex items-center">
              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bg} ${status.text}`}>
                {status.label}
              </div>
              <span className="ml-2 text-sm text-gray-500">
                Client ID: {(client as any)?.id || 'Unknown'}
              </span>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Button
            variant="outline"
            onClick={() => setLocation("/")}
          >
            <span className="material-icons text-sm mr-2">arrow_back</span>
            Back to Dashboard
          </Button>
          <Button
            onClick={() => setLocation("/notes")}
          >
            <span className="material-icons text-sm mr-2">add</span>
            New Session
          </Button>
        </div>
      </div>

      {/* Client overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium text-neutral-dark mb-4">Client Overview</h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <p className={`text-sm ${status.text} font-medium`}>{status.label}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Sessions</p>
                <p className="text-sm text-neutral-dark">{notes?.length || 0} total sessions</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Key Themes</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {isLoadingThemes ? (
                    <Skeleton className="h-6 w-24" />
                  ) : (
                    themes?.slice(0, 3).map((theme, index) => (
                      <span 
                        key={theme.id} 
                        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getThemeColor(index)}`}
                      >
                        {theme.name}
                      </span>
                    ))
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium text-neutral-dark mb-4">Theme Distribution</h3>
            {isLoadingThemes ? (
              <div className="h-40 flex items-center justify-center">
                <Skeleton className="h-32 w-32 rounded-full" />
              </div>
            ) : (
              <div className="h-40">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={themesChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={30}
                      outerRadius={60}
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name }) => name}
                    >
                      {themesChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium text-neutral-dark mb-4">Session History</h3>
            {isLoadingNotes ? (
              <div className="h-40 flex items-center justify-center">
                <Skeleton className="h-32 w-full" />
              </div>
            ) : (
              <div className="h-40">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={progressData}>
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#4A6FA5" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Session notes */}
      <div>
        <h3 className="text-lg font-medium text-neutral-dark mb-4">Session Notes</h3>
        <SessionNotesList 
          notes={notes} 
          isLoading={isLoadingNotes} 
          clientId={clientId} 
        />
      </div>
    </div>
  );
}
