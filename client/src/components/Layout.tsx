import { ReactNode, useState } from "react";
import Sidebar from "./Sidebar";
import MobileNav from "./MobileNav";
import InsightsSidebar from "./InsightsSidebar";
import { useQuery } from "@tanstack/react-query";
import { User } from "@shared/schema";
import logoImage from "../assets/lamaMind-logo.png";

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const [mobileNavOpen, setMobileNavOpen] = useState(false);

  // Fetch user profile
  const { data: user } = useQuery<User>({
    queryKey: ["/api/user"],
  });

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar - desktop only */}
      <Sidebar user={user} className="hidden md:flex md:flex-shrink-0" />
      
      {/* Mobile Header */}
      <div className="md:hidden bg-white w-full fixed top-0 left-0 z-10 border-b border-gray-200">
        <div className="flex items-center justify-between h-16 px-4">
          <div className="flex items-center">
            <img src={logoImage} alt="LamaMind Logo" className="h-8 w-8 mr-2" />
          </div>
          <button
            className="text-gray-700"
            onClick={() => setMobileNavOpen(!mobileNavOpen)}
          >
            <span className="material-icons">menu</span>
          </button>
        </div>
      </div>
      
      {/* Mobile Navigation Overlay */}
      {mobileNavOpen && (
        <div 
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-20"
          onClick={() => setMobileNavOpen(false)}
        >
          <div 
            className="bg-white w-64 h-full"
            onClick={(e) => e.stopPropagation()}
          >
            <Sidebar user={user} onLinkClick={() => setMobileNavOpen(false)} />
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <main className="flex-1 overflow-y-auto pt-16 md:pt-0">
        <div className="py-6 mx-auto px-4 sm:px-6 lg:px-8">
          {children}
        </div>
      </main>
      
      {/* Mobile Nav */}
      <MobileNav />
    </div>
  );
}
