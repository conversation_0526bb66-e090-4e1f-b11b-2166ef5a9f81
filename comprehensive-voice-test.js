#!/usr/bin/env node

import { WebSocket } from 'ws';
import fs from 'fs';

console.log('🔍 COMPREHENSIVE REAL-TIME VOICE CONVERSATION DEBUG');
console.log('=' * 60);

const ws = new WebSocket('ws://localhost:5000/ws');

let messageCount = 0;
let audioChunksSent = 0;
let speechStartDetected = false;
let speechStopDetected = false;
let transcriptionReceived = false;
let responseGenerated = false;
let sessionReady = false;
let audioResponseReceived = false;
let greetingReceived = false;
const sentMessages = [];
const receivedMessages = [];
const debugLog = [];

function logEvent(event, details = '', important = false) {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const logLine = `[${timestamp}] ${event}${details ? ': ' + details : ''}`;
  console.log(important ? `🔥 ${logLine}` : logLine);
  debugLog.push(logLine);
}

// Generate realistic test audio data (simulated speech)
function generateTestAudioChunk(duration = 0.1) {
  const sampleRate = 24000;
  const samples = Math.floor(sampleRate * duration);
  const buffer = new ArrayBuffer(samples * 2); // 16-bit PCM
  const view = new DataView(buffer);
  
  // Generate speech-like audio with varying amplitude and frequency
  for (let i = 0; i < samples; i++) {
    // Create a complex waveform that resembles speech
    const t = i / sampleRate;
    const fundamental = 150; // Base frequency around human speech
    const harmonics = Math.sin(2 * Math.PI * fundamental * t) * 0.3 +
                     Math.sin(2 * Math.PI * fundamental * 2 * t) * 0.2 +
                     Math.sin(2 * Math.PI * fundamental * 3 * t) * 0.1;
    
    // Add some noise and amplitude variation to make it more speech-like
    const noise = (Math.random() - 0.5) * 0.1;
    const envelope = Math.sin(t * 10) * 0.5 + 0.5; // Amplitude modulation
    
    const sample = (harmonics + noise) * envelope * 0.3; // Scale to reasonable level
    const int16Sample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));
    view.setInt16(i * 2, int16Sample, true); // little-endian
  }
  
  const uint8Array = new Uint8Array(buffer);
  return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
}

ws.on('open', () => {
  logEvent('WebSocket connected', '', true);
  
  // Send start message to initialize Realtime API session
  const startMessage = {
    type: "start",
    userId: "debug-user",
    clientId: "debug-client",
    useRealtimeAPI: true,
    mode: "realtime",
    behavior: {
      model: "gpt-4o-realtime-preview-2024-10-01",
      temperature: 0.7,
      voice: {
        voice: "shimmer",
        speed: 1.0
      }
    },
    instructions: "You are Vale, an empathetic AI therapeutic assistant. When the session starts, greet the user warmly and ask how they're feeling today. Keep responses concise and natural for voice conversation."
  };
  
  logEvent('Sending start message', '', true);
  ws.send(JSON.stringify(startMessage));
  sentMessages.push('start');
});

ws.on('message', (data) => {
  messageCount++;
  try {
    const message = JSON.parse(data.toString());
    receivedMessages.push(message.type);
    
    logEvent(`Message ${messageCount}`, `${message.type}`, true);
    
    switch (message.type) {
      case 'ready':
        sessionReady = true;
        logEvent('Session ready', 'Starting audio test in 2 seconds', true);
        
        // Wait for initial greeting, then start sending audio
        setTimeout(() => {
          if (!greetingReceived) {
            logEvent('No greeting received', 'Starting audio test anyway', true);
          }
          startAudioTest();
        }, 2000);
        break;
        
      case 'session.created':
        logEvent('OpenAI session created', `ID: ${message.session?.id || 'unknown'}`, true);
        break;
        
      case 'session.updated':
        const vadConfig = message.session?.turn_detection;
        const vadType = vadConfig?.type || 'none';
        logEvent('Session configured', `VAD: ${vadType}, threshold: ${vadConfig?.threshold || 'unknown'}`, true);
        break;
        
      case 'input_audio_buffer.speech_started':
        speechStartDetected = true;
        logEvent('SPEECH STARTED', 'OpenAI detected speech in audio buffer', true);
        break;
        
      case 'input_audio_buffer.speech_stopped':
        speechStopDetected = true;
        logEvent('SPEECH STOPPED', 'OpenAI detected end of speech', true);
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        transcriptionReceived = true;
        const transcript = message.transcript || '';
        logEvent('USER TRANSCRIBED', `"${transcript}"`, true);
        break;
        
      case 'response.created':
        responseGenerated = true;
        logEvent('AI RESPONSE STARTED', 'OpenAI generating response', true);
        break;
        
      case 'response.audio.delta':
        audioResponseReceived = true;
        logEvent('AI AUDIO CHUNK', `${message.delta?.length || 0} chars`);
        break;
        
      case 'response.audio_transcript.done':
        const aiTranscript = message.transcript || '';
        if (aiTranscript && !greetingReceived) {
          greetingReceived = true;
          logEvent('AI GREETING RECEIVED', `"${aiTranscript}"`, true);
        } else if (aiTranscript) {
          logEvent('AI RESPONSE TRANSCRIBED', `"${aiTranscript}"`, true);
        }
        break;
        
      case 'response.done':
        logEvent('RESPONSE COMPLETE', 'AI finished responding', true);
        break;
        
      case 'error':
        logEvent('ERROR', message.message || 'Unknown error', true);
        break;
        
      default:
        logEvent('Other message', message.type);
    }
  } catch (error) {
    logEvent('Parse error', error.message, true);
  }
});

function startAudioTest() {
  logEvent('Starting audio test', 'Sending simulated speech audio', true);
  
  // Send multiple chunks of realistic audio data
  const sendAudioChunk = (chunkIndex) => {
    if (chunkIndex >= 10) {
      logEvent('Audio test complete', `Sent ${audioChunksSent} chunks`, true);
      
      // Wait for response, then end test
      setTimeout(() => {
        endTest();
      }, 5000);
      return;
    }
    
    const audioData = generateTestAudioChunk(0.1); // 100ms chunks
    audioChunksSent++;
    
    ws.send(JSON.stringify({
      type: 'input_audio_buffer.append',
      audio: audioData
    }));
    
    logEvent(`Audio chunk ${chunkIndex + 1}/10`, `${audioData.length} chars`);
    
    // Send next chunk after 100ms
    setTimeout(() => sendAudioChunk(chunkIndex + 1), 100);
  };
  
  sendAudioChunk(0);
}

function endTest() {
  logEvent('Ending test', 'Generating final report', true);
  
  // Generate comprehensive report
  const report = {
    timestamp: new Date().toISOString(),
    testResults: {
      sessionReady,
      greetingReceived,
      audioChunksSent,
      speechStartDetected,
      speechStopDetected,
      transcriptionReceived,
      responseGenerated,
      audioResponseReceived
    },
    messageFlow: {
      totalMessages: messageCount,
      sentMessages,
      receivedMessages
    },
    debugLog
  };
  
  console.log('\n' + '='.repeat(60));
  console.log('🔍 COMPREHENSIVE TEST RESULTS');
  console.log('='.repeat(60));
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`Session Ready:        ${formatResult(sessionReady)}`);
  console.log(`AI Greeting:          ${formatResult(greetingReceived)}`);
  console.log(`Audio Chunks Sent:    ${audioChunksSent}`);
  console.log(`Speech Start:         ${formatResult(speechStartDetected)}`);
  console.log(`Speech Stop:          ${formatResult(speechStopDetected)}`);
  console.log(`Transcription:        ${formatResult(transcriptionReceived)}`);
  console.log(`AI Response:          ${formatResult(responseGenerated)}`);
  console.log(`Audio Response:       ${formatResult(audioResponseReceived)}`);
  
  console.log('\n📋 MESSAGE FLOW:');
  console.log(`Total Messages:       ${messageCount}`);
  console.log(`Sent:                 ${sentMessages.join(', ')}`);
  console.log(`Received:             ${receivedMessages.slice(0, 10).join(', ')}${receivedMessages.length > 10 ? '...' : ''}`);
  
  // Save detailed report
  fs.writeFileSync('voice-test-report.json', JSON.stringify(report, null, 2));
  console.log('\n💾 Detailed report saved to: voice-test-report.json');
  
  // Diagnose issues
  console.log('\n🔧 DIAGNOSIS:');
  if (!sessionReady) {
    console.log('❌ Session failed to initialize - check server connection');
  } else if (!greetingReceived) {
    console.log('❌ AI greeting not received - check OpenAI API configuration');
  } else if (audioChunksSent === 0) {
    console.log('❌ No audio sent - check audio generation');
  } else if (!speechStartDetected) {
    console.log('❌ Speech not detected - check audio format or VAD settings');
  } else if (!transcriptionReceived) {
    console.log('❌ No transcription - check audio quality or OpenAI API');
  } else if (!responseGenerated) {
    console.log('❌ No AI response - check conversation flow');
  } else {
    console.log('✅ All systems working - conversation flow successful!');
  }
  
  ws.close();
}

function formatResult(result) {
  return result ? '✅ PASS' : '❌ FAIL';
}

ws.on('close', (code, reason) => {
  logEvent('WebSocket closed', `${code} ${reason.toString()}`, true);
});

ws.on('error', (error) => {
  logEvent('WebSocket error', error.message, true);
});

// Timeout to prevent hanging
setTimeout(() => {
  logEvent('TEST TIMEOUT', 'Ending test due to timeout', true);
  endTest();
}, 30000); // 30 second timeout
