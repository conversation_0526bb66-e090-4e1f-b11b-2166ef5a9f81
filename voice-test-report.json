{"timestamp": "2025-06-07T00:46:52.848Z", "testResults": {"sessionReady": true, "greetingReceived": true, "audioChunksSent": 10, "speechStartDetected": true, "speechStopDetected": true, "transcriptionReceived": true, "responseGenerated": true, "audioResponseReceived": true}, "messageFlow": {"totalMessages": 93, "sentMessages": ["start"], "receivedMessages": ["session.created", "ready", "session.updated", "conversation.item.created", "response.created", "response.output_item.added", "conversation.item.created", "response.content_part.added", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio.done", "response.audio_transcript.done", "response.content_part.done", "response.output_item.done", "response.done", "rate_limits.updated", "input_audio_buffer.speech_started", "input_audio_buffer.speech_stopped", "input_audio_buffer.committed", "conversation.item.created", "response.created", "response.output_item.added", "conversation.item.created", "response.content_part.added", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "conversation.item.input_audio_transcription.delta", "conversation.item.input_audio_transcription.completed", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "rate_limits.updated", "response.audio.done", "response.audio_transcript.done", "response.content_part.done", "response.output_item.done", "response.done"]}, "debugLog": ["[00:46:22] WebSocket connected", "[00:46:22] Sending start message", "[00:46:23] Message 1: session.created", "[00:46:23] OpenAI session created: ID: sess_BfbzTZuWWDrG6uehP1uIb", "[00:46:23] Message 2: ready", "[00:46:23] Session ready: Starting audio test in 2 seconds", "[00:46:23] Message 3: session.updated", "[00:46:23] Session configured: VAD: server_vad, threshold: 0.5", "[00:46:23] Message 4: conversation.item.created", "[00:46:23] Other message: conversation.item.created", "[00:46:23] Message 5: response.created", "[00:46:23] AI RESPONSE STARTED: OpenAI generating response", "[00:46:23] Message 6: response.output_item.added", "[00:46:23] Other message: response.output_item.added", "[00:46:23] Message 7: conversation.item.created", "[00:46:23] Other message: conversation.item.created", "[00:46:23] Message 8: response.content_part.added", "[00:46:23] Other message: response.content_part.added", "[00:46:23] Message 9: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 10: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 11: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 12: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 13: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 14: response.audio.delta", "[00:46:23] AI AUDIO CHUNK: 6400 chars", "[00:46:23] Message 15: response.audio.delta", "[00:46:23] AI AUDIO CHUNK: 9600 chars", "[00:46:23] Message 16: response.audio.delta", "[00:46:23] AI AUDIO CHUNK: 16000 chars", "[00:46:23] Message 17: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 18: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 19: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 20: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 21: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 22: response.audio.delta", "[00:46:23] AI AUDIO CHUNK: 16000 chars", "[00:46:23] Message 23: response.audio.delta", "[00:46:23] AI AUDIO CHUNK: 16000 chars", "[00:46:23] Message 24: response.audio_transcript.delta", "[00:46:23] Other message: response.audio_transcript.delta", "[00:46:23] Message 25: response.audio.delta", "[00:46:23] AI AUDIO CHUNK: 16000 chars", "[00:46:24] Message 26: response.audio.delta", "[00:46:24] AI AUDIO CHUNK: 32000 chars", "[00:46:24] Message 27: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 28: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 29: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 30: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 31: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 32: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 33: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 34: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 35: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 36: response.audio.delta", "[00:46:24] AI AUDIO CHUNK: 48000 chars", "[00:46:24] Message 37: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 38: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 39: response.audio_transcript.delta", "[00:46:24] Other message: response.audio_transcript.delta", "[00:46:24] Message 40: response.audio.delta", "[00:46:24] AI AUDIO CHUNK: 64000 chars", "[00:46:24] Message 41: response.audio.delta", "[00:46:24] AI AUDIO CHUNK: 64000 chars", "[00:46:24] Message 42: response.audio.delta", "[00:46:24] AI AUDIO CHUNK: 59864 chars", "[00:46:24] Message 43: response.audio.done", "[00:46:24] Other message: response.audio.done", "[00:46:24] Message 44: response.audio_transcript.done", "[00:46:24] AI GREETING RECEIVED: \"Hello! It's great to have you here today. How are you feeling? What would you like to talk about?\"", "[00:46:24] Message 45: response.content_part.done", "[00:46:24] Other message: response.content_part.done", "[00:46:24] Message 46: response.output_item.done", "[00:46:24] Other message: response.output_item.done", "[00:46:24] Message 47: response.done", "[00:46:24] RESPONSE COMPLETE: AI finished responding", "[00:46:24] Message 48: rate_limits.updated", "[00:46:24] Other message: rate_limits.updated", "[00:46:25] Starting audio test: Sending simulated speech audio", "[00:46:25] Audio chunk 1/10: 6400 chars", "[00:46:25] Message 49: input_audio_buffer.speech_started", "[00:46:25] SPEECH STARTED: OpenAI detected speech in audio buffer", "[00:46:25] Audio chunk 2/10: 6400 chars", "[00:46:25] Audio chunk 3/10: 6400 chars", "[00:46:25] Audio chunk 4/10: 6400 chars", "[00:46:25] Audio chunk 5/10: 6400 chars", "[00:46:25] Audio chunk 6/10: 6400 chars", "[00:46:25] Audio chunk 7/10: 6400 chars", "[00:46:25] Audio chunk 8/10: 6400 chars", "[00:46:25] Audio chunk 9/10: 6400 chars", "[00:46:26] Audio chunk 10/10: 6400 chars", "[00:46:26] Message 50: input_audio_buffer.speech_stopped", "[00:46:26] SPEECH STOPPED: OpenAI detected end of speech", "[00:46:26] Message 51: input_audio_buffer.committed", "[00:46:26] Other message: input_audio_buffer.committed", "[00:46:26] Message 52: conversation.item.created", "[00:46:26] Other message: conversation.item.created", "[00:46:26] Message 53: response.created", "[00:46:26] AI RESPONSE STARTED: OpenAI generating response", "[00:46:26] Audio test complete: Sent 10 chunks", "[00:46:26] Message 54: response.output_item.added", "[00:46:26] Other message: response.output_item.added", "[00:46:26] Message 55: conversation.item.created", "[00:46:26] Other message: conversation.item.created", "[00:46:26] Message 56: response.content_part.added", "[00:46:26] Other message: response.content_part.added", "[00:46:26] Message 57: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 58: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 59: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 60: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 61: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 62: response.audio.delta", "[00:46:26] AI AUDIO CHUNK: 6400 chars", "[00:46:26] Message 63: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 64: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 65: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 66: response.audio.delta", "[00:46:26] AI AUDIO CHUNK: 9600 chars", "[00:46:26] Message 67: conversation.item.input_audio_transcription.delta", "[00:46:26] Other message: conversation.item.input_audio_transcription.delta", "[00:46:26] Message 68: conversation.item.input_audio_transcription.completed", "[00:46:26] USER TRANSCRIBED: \"you\n\"", "[00:46:26] Message 69: response.audio.delta", "[00:46:26] AI AUDIO CHUNK: 16000 chars", "[00:46:26] Message 70: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 71: response.audio_transcript.delta", "[00:46:26] Other message: response.audio_transcript.delta", "[00:46:26] Message 72: response.audio.delta", "[00:46:26] AI AUDIO CHUNK: 16000 chars", "[00:46:26] Message 73: response.audio.delta", "[00:46:26] AI AUDIO CHUNK: 16000 chars", "[00:46:27] Message 74: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 75: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 76: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 77: response.audio.delta", "[00:46:27] AI AUDIO CHUNK: 16000 chars", "[00:46:27] Message 78: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 79: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 80: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 81: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 82: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 83: response.audio_transcript.delta", "[00:46:27] Other message: response.audio_transcript.delta", "[00:46:27] Message 84: response.audio.delta", "[00:46:27] AI AUDIO CHUNK: 32000 chars", "[00:46:27] Message 85: response.audio.delta", "[00:46:27] AI AUDIO CHUNK: 48000 chars", "[00:46:27] Message 86: response.audio.delta", "[00:46:27] AI AUDIO CHUNK: 64000 chars", "[00:46:27] Message 87: response.audio.delta", "[00:46:27] AI AUDIO CHUNK: 85464 chars", "[00:46:27] Message 88: rate_limits.updated", "[00:46:27] Other message: rate_limits.updated", "[00:46:27] Message 89: response.audio.done", "[00:46:27] Other message: response.audio.done", "[00:46:27] Message 90: response.audio_transcript.done", "[00:46:27] AI RESPONSE TRANSCRIBED: \"I'm here to listen whenever you're ready. Take your time. What's been on your mind lately?\"", "[00:46:27] Message 91: response.content_part.done", "[00:46:27] Other message: response.content_part.done", "[00:46:27] Message 92: response.output_item.done", "[00:46:27] Other message: response.output_item.done", "[00:46:27] Message 93: response.done", "[00:46:27] RESPONSE COMPLETE: AI finished responding", "[00:46:31] Ending test: Generating final report", "[00:46:31] WebSocket closed: 1005 ", "[00:46:52] TEST TIMEOUT: Ending test due to timeout", "[00:46:52] Ending test: Generating final report"]}