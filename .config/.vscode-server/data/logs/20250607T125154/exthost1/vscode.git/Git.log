2025-06-07 12:51:56.099 [info] [main] Log level: Info
2025-06-07 12:51:56.100 [info] [main] Validating found git in: "git"
2025-06-07 12:51:56.100 [info] [main] Using git "2.47.2" from "git"
2025-06-07 12:51:56.100 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 12:51:56.100 [info] > git rev-parse --show-toplevel [68ms]
2025-06-07 12:51:56.101 [info] > git rev-parse --git-dir --git-common-dir [9ms]
2025-06-07 12:51:56.102 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 12:51:56.102 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 12:51:56.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 12:51:56.105 [info] > git config --get commit.template [13ms]
2025-06-07 12:51:56.121 [info] > git status -z -uall [6ms]
2025-06-07 12:51:56.122 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 12:51:56.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 12:51:56.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 12:51:56.170 [info] > git rev-parse --show-toplevel [43ms]
2025-06-07 12:51:56.187 [info] > git config --get --local branch.main.vscode-merge-base [17ms]
2025-06-07 12:51:56.634 [info] > git config --get commit.template [454ms]
2025-06-07 12:51:56.647 [info] > git rev-parse --show-toplevel [453ms]
2025-06-07 12:51:56.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [456ms]
2025-06-07 12:51:56.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-07 12:51:56.661 [info] > git merge-base refs/heads/main refs/remotes/origin/main [10ms]
2025-06-07 12:51:56.667 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 12:51:56.681 [info] > git diff --name-status -z --diff-filter=ADMR 3d8b3a5165d260649a750d23d7e5b1cc85844257...refs/remotes/origin/main [15ms]
2025-06-07 12:51:56.699 [info] > git status -z -uall [12ms]
2025-06-07 12:51:56.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 12:51:56.704 [info] > git rev-parse --show-toplevel [24ms]
2025-06-07 12:51:56.718 [info] > git check-ignore -v -z --stdin [20ms]
2025-06-07 12:51:56.790 [info] > git rev-parse --show-toplevel [82ms]
2025-06-07 12:51:56.799 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 12:51:56.826 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 12:51:56.831 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:51:56.851 [info] > git rev-parse --show-toplevel [0ms]
2025-06-07 12:51:56.860 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 12:51:56.869 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 12:51:56.882 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 12:51:56.890 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 12:52:01.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:01.316 [info] > git config --get commit.template [7ms]
2025-06-07 12:52:01.326 [info] > git status -z -uall [4ms]
2025-06-07 12:52:01.328 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:52:06.336 [info] > git config --get commit.template [3ms]
2025-06-07 12:52:06.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:06.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:52:06.348 [info] > git status -z -uall [8ms]
2025-06-07 12:52:11.357 [info] > git config --get commit.template [1ms]
2025-06-07 12:52:11.364 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:52:11.379 [info] > git status -z -uall [11ms]
2025-06-07 12:52:11.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:52:16.408 [info] > git config --get commit.template [2ms]
2025-06-07 12:52:16.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:16.444 [info] > git status -z -uall [14ms]
2025-06-07 12:52:16.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 12:52:16.662 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [274ms]
2025-06-07 12:52:22.362 [info] > git config --get commit.template [4ms]
2025-06-07 12:52:22.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:22.369 [info] > git status -z -uall [3ms]
2025-06-07 12:52:22.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:52:27.378 [info] > git config --get commit.template [4ms]
2025-06-07 12:52:27.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:27.384 [info] > git status -z -uall [3ms]
2025-06-07 12:52:27.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:52:27.526 [info] > git diff-tree -r --name-status -z --diff-filter=ADMR --find-renames=50% 567a13994be8c9ce6824ff80fa7a3c5c719edfbf 3d8b3a5165d260649a750d23d7e5b1cc85844257 [2ms]
2025-06-07 12:52:27.789 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:browser-test.js [148ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 567a13994be8c9ce6824ff80fa7a3c5c719edfbf:client/src/pages/AdminAITest.tsx [146ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:client/src/pages/AdminAITest.tsx [144ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:comprehensive-voice-test.js [141ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 567a13994be8c9ce6824ff80fa7a3c5c719edfbf:package.json [134ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:package.json [131ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 567a13994be8c9ce6824ff80fa7a3c5c719edfbf:server/openai-realtime-proxy.ts [129ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:server/openai-realtime-proxy.ts [120ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 567a13994be8c9ce6824ff80fa7a3c5c719edfbf:server/vite.ts [118ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:server/vite.ts [116ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 567a13994be8c9ce6824ff80fa7a3c5c719edfbf:server/websocket-handler.ts [114ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:server/websocket-handler.ts [112ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:test-microphone-browser.html [110ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:test-voice-fixes.js [108ms]
2025-06-07 12:52:27.790 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:voice-test-report.json [105ms]
2025-06-07 12:52:27.790 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- voice-test-report.json [60ms]
2025-06-07 12:52:27.791 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- test-voice-fixes.js [64ms]
2025-06-07 12:52:27.791 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- test-microphone-browser.html [66ms]
2025-06-07 12:52:27.792 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- server/websocket-handler.ts [70ms]
2025-06-07 12:52:27.792 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- server/websocket-handler.ts [76ms]
2025-06-07 12:52:27.792 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- server/vite.ts [78ms]
2025-06-07 12:52:27.792 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- server/vite.ts [80ms]
2025-06-07 12:52:27.793 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- server/openai-realtime-proxy.ts [84ms]
2025-06-07 12:52:27.793 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- server/openai-realtime-proxy.ts [87ms]
2025-06-07 12:52:27.793 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- package.json [89ms]
2025-06-07 12:52:27.794 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- package.json [92ms]
2025-06-07 12:52:27.794 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- package-lock.json [95ms]
2025-06-07 12:52:27.794 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- package-lock.json [97ms]
2025-06-07 12:52:27.794 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- comprehensive-voice-test.js [99ms]
2025-06-07 12:52:27.794 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [102ms]
2025-06-07 12:52:27.795 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [104ms]
2025-06-07 12:52:27.795 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [107ms]
2025-06-07 12:52:27.799 [info] > git show --textconv 567a13994be8c9ce6824ff80fa7a3c5c719edfbf:package-lock.json [148ms]
2025-06-07 12:52:27.799 [info] > git show --textconv 3d8b3a5165d260649a750d23d7e5b1cc85844257:package-lock.json [145ms]
2025-06-07 12:52:28.237 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 12:52:32.398 [info] > git config --get commit.template [4ms]
2025-06-07 12:52:32.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:32.411 [info] > git status -z -uall [8ms]
2025-06-07 12:52:32.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:52:37.423 [info] > git config --get commit.template [5ms]
2025-06-07 12:52:37.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:52:37.432 [info] > git status -z -uall [4ms]
2025-06-07 12:52:37.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:52:42.440 [info] > git config --get commit.template [3ms]
2025-06-07 12:52:42.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:42.449 [info] > git status -z -uall [4ms]
2025-06-07 12:52:42.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:52:47.460 [info] > git config --get commit.template [4ms]
2025-06-07 12:52:47.461 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:47.468 [info] > git status -z -uall [4ms]
2025-06-07 12:52:47.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:52:52.479 [info] > git config --get commit.template [5ms]
2025-06-07 12:52:52.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:52.490 [info] > git status -z -uall [3ms]
2025-06-07 12:52:52.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:52:58.218 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 12:52:58.218 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [11ms]
2025-06-07 12:52:58.218 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [15ms]
2025-06-07 12:52:58.219 [info] > git config --get commit.template [2ms]
2025-06-07 12:52:58.219 [info] > git blame --root --incremental 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [6ms]
2025-06-07 12:52:58.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:52:58.233 [info] > git status -z -uall [4ms]
2025-06-07 12:52:58.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:53:16.918 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 12:53:16.918 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [10ms]
2025-06-07 12:53:16.919 [info] > git config --get commit.template [1ms]
2025-06-07 12:53:16.919 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [6ms]
2025-06-07 12:53:16.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:16.930 [info] > git status -z -uall [3ms]
2025-06-07 12:53:16.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:21.939 [info] > git config --get commit.template [3ms]
2025-06-07 12:53:21.940 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:21.946 [info] > git status -z -uall [3ms]
2025-06-07 12:53:21.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:26.958 [info] > git config --get commit.template [4ms]
2025-06-07 12:53:26.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:26.969 [info] > git status -z -uall [5ms]
2025-06-07 12:53:26.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:31.980 [info] > git config --get commit.template [4ms]
2025-06-07 12:53:31.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:53:31.986 [info] > git status -z -uall [2ms]
2025-06-07 12:53:31.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:36.996 [info] > git config --get commit.template [4ms]
2025-06-07 12:53:36.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:53:37.004 [info] > git status -z -uall [3ms]
2025-06-07 12:53:37.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:42.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:42.021 [info] > git config --get commit.template [8ms]
2025-06-07 12:53:42.036 [info] > git status -z -uall [10ms]
2025-06-07 12:53:42.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:47.049 [info] > git config --get commit.template [5ms]
2025-06-07 12:53:47.050 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:47.057 [info] > git status -z -uall [4ms]
2025-06-07 12:53:47.058 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:53:52.065 [info] > git config --get commit.template [1ms]
2025-06-07 12:53:52.070 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:52.076 [info] > git status -z -uall [4ms]
2025-06-07 12:53:52.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:53:57.086 [info] > git config --get commit.template [4ms]
2025-06-07 12:53:57.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:53:57.095 [info] > git status -z -uall [4ms]
2025-06-07 12:53:57.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:54:02.106 [info] > git config --get commit.template [2ms]
2025-06-07 12:54:02.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:02.113 [info] > git status -z -uall [3ms]
2025-06-07 12:54:02.114 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:54:07.125 [info] > git config --get commit.template [4ms]
2025-06-07 12:54:07.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:07.136 [info] > git status -z -uall [5ms]
2025-06-07 12:54:07.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:54:12.147 [info] > git config --get commit.template [4ms]
2025-06-07 12:54:12.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:12.156 [info] > git status -z -uall [5ms]
2025-06-07 12:54:12.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:54:17.165 [info] > git config --get commit.template [4ms]
2025-06-07 12:54:17.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:17.173 [info] > git status -z -uall [3ms]
2025-06-07 12:54:17.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:54:33.398 [info] > git config --get commit.template [1ms]
2025-06-07 12:54:33.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:54:33.409 [info] > git status -z -uall [4ms]
2025-06-07 12:54:33.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:54:38.424 [info] > git config --get commit.template [4ms]
2025-06-07 12:54:38.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:38.436 [info] > git status -z -uall [8ms]
2025-06-07 12:54:38.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-07 12:54:43.447 [info] > git config --get commit.template [4ms]
2025-06-07 12:54:43.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:54:43.456 [info] > git status -z -uall [3ms]
2025-06-07 12:54:43.457 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:54:48.467 [info] > git config --get commit.template [4ms]
2025-06-07 12:54:48.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:48.475 [info] > git status -z -uall [3ms]
2025-06-07 12:54:48.476 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:54:53.487 [info] > git config --get commit.template [3ms]
2025-06-07 12:54:53.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:54:53.493 [info] > git status -z -uall [3ms]
2025-06-07 12:54:53.494 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:54:58.506 [info] > git config --get commit.template [5ms]
2025-06-07 12:54:58.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:54:58.517 [info] > git status -z -uall [6ms]
2025-06-07 12:54:58.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:55:03.529 [info] > git config --get commit.template [5ms]
2025-06-07 12:55:03.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:55:03.538 [info] > git status -z -uall [4ms]
2025-06-07 12:55:03.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:55:08.566 [info] > git config --get commit.template [3ms]
2025-06-07 12:55:08.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:55:08.574 [info] > git status -z -uall [4ms]
2025-06-07 12:55:08.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 12:55:13.589 [info] > git config --get commit.template [5ms]
2025-06-07 12:55:13.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:55:13.599 [info] > git status -z -uall [5ms]
2025-06-07 12:55:13.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:55:37.225 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 12:55:37.225 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [12ms]
2025-06-07 12:55:37.226 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [17ms]
2025-06-07 12:55:37.226 [info] > git config --get commit.template [1ms]
2025-06-07 12:55:37.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:55:37.240 [info] > git status -z -uall [4ms]
2025-06-07 12:55:37.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:55:42.255 [info] > git config --get commit.template [6ms]
2025-06-07 12:55:42.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:55:42.265 [info] > git status -z -uall [5ms]
2025-06-07 12:55:42.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:55:47.280 [info] > git config --get commit.template [6ms]
2025-06-07 12:55:47.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:55:47.288 [info] > git status -z -uall [3ms]
2025-06-07 12:55:47.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:55:52.304 [info] > git config --get commit.template [8ms]
2025-06-07 12:55:52.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 12:55:52.328 [info] > git status -z -uall [10ms]
2025-06-07 12:55:52.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 12:55:57.379 [info] > git config --get commit.template [2ms]
2025-06-07 12:55:57.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 12:55:57.407 [info] > git status -z -uall [6ms]
2025-06-07 12:55:57.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:02.420 [info] > git config --get commit.template [2ms]
2025-06-07 12:56:02.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:02.441 [info] > git status -z -uall [4ms]
2025-06-07 12:56:02.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:56:07.452 [info] > git config --get commit.template [3ms]
2025-06-07 12:56:07.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:07.459 [info] > git status -z -uall [3ms]
2025-06-07 12:56:07.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:12.473 [info] > git config --get commit.template [6ms]
2025-06-07 12:56:12.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:12.486 [info] > git status -z -uall [6ms]
2025-06-07 12:56:12.487 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:56:17.500 [info] > git config --get commit.template [6ms]
2025-06-07 12:56:17.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:17.513 [info] > git status -z -uall [5ms]
2025-06-07 12:56:17.514 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:22.530 [info] > git config --get commit.template [7ms]
2025-06-07 12:56:22.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:22.540 [info] > git status -z -uall [4ms]
2025-06-07 12:56:22.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:27.553 [info] > git config --get commit.template [5ms]
2025-06-07 12:56:27.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:56:27.568 [info] > git status -z -uall [7ms]
2025-06-07 12:56:27.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:32.584 [info] > git config --get commit.template [7ms]
2025-06-07 12:56:32.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:32.598 [info] > git status -z -uall [7ms]
2025-06-07 12:56:32.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:37.610 [info] > git config --get commit.template [1ms]
2025-06-07 12:56:37.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:37.626 [info] > git status -z -uall [4ms]
2025-06-07 12:56:37.627 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:42.639 [info] > git config --get commit.template [5ms]
2025-06-07 12:56:42.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:56:42.711 [info] > git status -z -uall [66ms]
2025-06-07 12:56:42.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 12:56:47.727 [info] > git config --get commit.template [6ms]
2025-06-07 12:56:47.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:56:47.739 [info] > git status -z -uall [5ms]
2025-06-07 12:56:47.740 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:56:52.756 [info] > git config --get commit.template [9ms]
2025-06-07 12:56:52.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:56:52.786 [info] > git status -z -uall [20ms]
2025-06-07 12:56:52.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 12:56:57.809 [info] > git config --get commit.template [12ms]
2025-06-07 12:56:57.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:56:57.825 [info] > git status -z -uall [9ms]
2025-06-07 12:56:57.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:57:02.840 [info] > git config --get commit.template [4ms]
2025-06-07 12:57:02.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:57:02.852 [info] > git status -z -uall [7ms]
2025-06-07 12:57:02.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:57:07.923 [info] > git config --get commit.template [59ms]
2025-06-07 12:57:07.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [54ms]
2025-06-07 12:57:07.936 [info] > git status -z -uall [6ms]
2025-06-07 12:57:07.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:57:12.948 [info] > git config --get commit.template [3ms]
2025-06-07 12:57:12.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:57:12.966 [info] > git status -z -uall [7ms]
2025-06-07 12:57:12.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:57:17.982 [info] > git config --get commit.template [6ms]
2025-06-07 12:57:17.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:57:17.998 [info] > git status -z -uall [9ms]
2025-06-07 12:57:17.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:57:23.010 [info] > git config --get commit.template [4ms]
2025-06-07 12:57:23.012 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:57:23.024 [info] > git status -z -uall [8ms]
2025-06-07 12:57:23.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:57:28.038 [info] > git config --get commit.template [5ms]
2025-06-07 12:57:28.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:57:28.048 [info] > git status -z -uall [4ms]
2025-06-07 12:57:28.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:57:33.065 [info] > git config --get commit.template [8ms]
2025-06-07 12:57:33.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:57:33.080 [info] > git status -z -uall [6ms]
2025-06-07 12:57:33.082 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:57:38.100 [info] > git config --get commit.template [9ms]
2025-06-07 12:57:38.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:57:38.113 [info] > git status -z -uall [7ms]
2025-06-07 12:57:38.114 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:57:43.127 [info] > git config --get commit.template [5ms]
2025-06-07 12:57:43.127 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:57:43.136 [info] > git status -z -uall [4ms]
2025-06-07 12:57:43.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:57:48.152 [info] > git config --get commit.template [6ms]
2025-06-07 12:57:48.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:57:48.164 [info] > git status -z -uall [7ms]
2025-06-07 12:57:48.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:57:53.179 [info] > git config --get commit.template [6ms]
2025-06-07 12:57:53.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:57:53.192 [info] > git status -z -uall [5ms]
2025-06-07 12:57:53.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:58:04.694 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [13ms]
2025-06-07 12:58:04.694 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [17ms]
2025-06-07 12:58:04.695 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [22ms]
2025-06-07 12:58:04.695 [info] > git config --get commit.template [3ms]
2025-06-07 12:58:04.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:58:04.713 [info] > git status -z -uall [7ms]
2025-06-07 12:58:04.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:58:09.732 [info] > git config --get commit.template [4ms]
2025-06-07 12:58:09.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 12:58:09.760 [info] > git status -z -uall [5ms]
2025-06-07 12:58:09.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 12:58:14.781 [info] > git config --get commit.template [4ms]
2025-06-07 12:58:14.782 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:58:14.791 [info] > git status -z -uall [4ms]
2025-06-07 12:58:14.792 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:58:46.279 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 12:58:46.280 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [19ms]
2025-06-07 12:58:46.280 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [30ms]
2025-06-07 12:58:46.280 [info] > git config --get commit.template [2ms]
2025-06-07 12:58:46.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:58:46.309 [info] > git status -z -uall [7ms]
2025-06-07 12:58:46.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:58:51.329 [info] > git config --get commit.template [8ms]
2025-06-07 12:58:51.330 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:58:51.342 [info] > git status -z -uall [6ms]
2025-06-07 12:58:51.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:58:55.647 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 12:58:55.647 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [12ms]
2025-06-07 12:58:55.648 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [2ms]
2025-06-07 12:58:56.356 [info] > git config --get commit.template [2ms]
2025-06-07 12:58:56.365 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:58:56.380 [info] > git status -z -uall [7ms]
2025-06-07 12:58:56.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:59:01.394 [info] > git config --get commit.template [5ms]
2025-06-07 12:59:01.395 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:59:01.405 [info] > git status -z -uall [6ms]
2025-06-07 12:59:01.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:59:06.479 [info] > git config --get commit.template [2ms]
2025-06-07 12:59:06.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:59:06.499 [info] > git status -z -uall [5ms]
2025-06-07 12:59:06.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:59:11.515 [info] > git config --get commit.template [5ms]
2025-06-07 12:59:11.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:59:11.524 [info] > git status -z -uall [5ms]
2025-06-07 12:59:11.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:59:16.538 [info] > git config --get commit.template [5ms]
2025-06-07 12:59:16.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:59:16.548 [info] > git status -z -uall [4ms]
2025-06-07 12:59:16.549 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:59:24.900 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [11ms]
2025-06-07 12:59:24.900 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [18ms]
2025-06-07 12:59:24.900 [info] > git config --get commit.template [1ms]
2025-06-07 12:59:24.901 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 12:59:24.906 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:59:24.916 [info] > git status -z -uall [5ms]
2025-06-07 12:59:24.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:59:29.943 [info] > git config --get commit.template [11ms]
2025-06-07 12:59:29.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:59:29.958 [info] > git status -z -uall [7ms]
2025-06-07 12:59:29.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:59:34.980 [info] > git config --get commit.template [10ms]
2025-06-07 12:59:34.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:59:35.012 [info] > git status -z -uall [12ms]
2025-06-07 12:59:35.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:00:22.988 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [15ms]
2025-06-07 13:00:22.988 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [21ms]
2025-06-07 13:00:22.988 [info] > git config --get commit.template [1ms]
2025-06-07 13:00:22.988 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 13:00:22.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:00:23.010 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:00:23.014 [info] > git status -z -uall [11ms]
2025-06-07 13:00:28.025 [info] > git config --get commit.template [4ms]
2025-06-07 13:00:28.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:00:28.034 [info] > git status -z -uall [4ms]
2025-06-07 13:00:28.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:00:36.584 [info] > git config --get commit.template [2ms]
2025-06-07 13:00:36.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:00:36.609 [info] > git status -z -uall [7ms]
2025-06-07 13:00:36.610 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:01:58.699 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [92ms]
2025-06-07 13:01:58.700 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [100ms]
2025-06-07 13:01:58.701 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [64ms]
2025-06-07 13:01:58.715 [info] > git config --get commit.template [18ms]
2025-06-07 13:01:58.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:01:58.732 [info] > git status -z -uall [7ms]
2025-06-07 13:01:58.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:02:03.866 [info] > git config --get commit.template [2ms]
2025-06-07 13:02:03.885 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 13:02:03.901 [info] > git status -z -uall [5ms]
2025-06-07 13:02:03.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:02:08.918 [info] > git config --get commit.template [5ms]
2025-06-07 13:02:08.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:08.948 [info] > git status -z -uall [17ms]
2025-06-07 13:02:08.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:02:13.967 [info] > git config --get commit.template [7ms]
2025-06-07 13:02:13.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:13.984 [info] > git status -z -uall [10ms]
2025-06-07 13:02:13.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:02:18.997 [info] > git config --get commit.template [4ms]
2025-06-07 13:02:18.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:02:19.008 [info] > git status -z -uall [6ms]
2025-06-07 13:02:19.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:02:24.022 [info] > git config --get commit.template [5ms]
2025-06-07 13:02:24.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:02:24.039 [info] > git status -z -uall [9ms]
2025-06-07 13:02:24.040 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:02:29.056 [info] > git config --get commit.template [8ms]
2025-06-07 13:02:29.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:29.068 [info] > git status -z -uall [5ms]
2025-06-07 13:02:29.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:02:34.086 [info] > git config --get commit.template [8ms]
2025-06-07 13:02:34.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:34.106 [info] > git status -z -uall [9ms]
2025-06-07 13:02:34.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:02:39.123 [info] > git config --get commit.template [7ms]
2025-06-07 13:02:39.124 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:39.141 [info] > git status -z -uall [6ms]
2025-06-07 13:02:39.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:02:44.160 [info] > git config --get commit.template [8ms]
2025-06-07 13:02:44.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:44.178 [info] > git status -z -uall [9ms]
2025-06-07 13:02:44.179 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:02:49.196 [info] > git config --get commit.template [6ms]
2025-06-07 13:02:49.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:02:49.209 [info] > git status -z -uall [7ms]
2025-06-07 13:02:49.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:02:54.220 [info] > git config --get commit.template [1ms]
2025-06-07 13:02:54.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:02:54.259 [info] > git status -z -uall [17ms]
2025-06-07 13:02:54.259 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:02:59.273 [info] > git config --get commit.template [5ms]
2025-06-07 13:02:59.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:02:59.284 [info] > git status -z -uall [6ms]
2025-06-07 13:02:59.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:04.300 [info] > git config --get commit.template [5ms]
2025-06-07 13:03:04.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:04.315 [info] > git status -z -uall [8ms]
2025-06-07 13:03:04.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:03:09.333 [info] > git config --get commit.template [6ms]
2025-06-07 13:03:09.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:09.349 [info] > git status -z -uall [8ms]
2025-06-07 13:03:09.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:14.369 [info] > git config --get commit.template [8ms]
2025-06-07 13:03:14.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:14.390 [info] > git status -z -uall [6ms]
2025-06-07 13:03:14.393 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:19.413 [info] > git config --get commit.template [5ms]
2025-06-07 13:03:19.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:19.425 [info] > git status -z -uall [6ms]
2025-06-07 13:03:19.426 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:24.442 [info] > git config --get commit.template [5ms]
2025-06-07 13:03:24.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:03:24.453 [info] > git status -z -uall [5ms]
2025-06-07 13:03:24.457 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:03:29.475 [info] > git config --get commit.template [7ms]
2025-06-07 13:03:29.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:03:29.487 [info] > git status -z -uall [5ms]
2025-06-07 13:03:29.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:34.502 [info] > git config --get commit.template [5ms]
2025-06-07 13:03:34.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:34.513 [info] > git status -z -uall [5ms]
2025-06-07 13:03:34.514 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:39.562 [info] > git config --get commit.template [29ms]
2025-06-07 13:03:39.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-07 13:03:39.600 [info] > git status -z -uall [18ms]
2025-06-07 13:03:39.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-07 13:03:44.617 [info] > git config --get commit.template [6ms]
2025-06-07 13:03:44.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:44.692 [info] > git status -z -uall [70ms]
2025-06-07 13:03:44.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [69ms]
2025-06-07 13:03:49.711 [info] > git config --get commit.template [7ms]
2025-06-07 13:03:49.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:03:49.723 [info] > git status -z -uall [6ms]
2025-06-07 13:03:49.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:54.738 [info] > git config --get commit.template [4ms]
2025-06-07 13:03:54.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:03:54.752 [info] > git status -z -uall [6ms]
2025-06-07 13:03:54.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:03:59.771 [info] > git config --get commit.template [8ms]
2025-06-07 13:03:59.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:03:59.785 [info] > git status -z -uall [5ms]
2025-06-07 13:03:59.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:04:04.808 [info] > git config --get commit.template [9ms]
2025-06-07 13:04:04.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:04:04.827 [info] > git status -z -uall [9ms]
2025-06-07 13:04:04.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:04:09.915 [info] > git config --get commit.template [3ms]
2025-06-07 13:04:09.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 13:04:09.956 [info] > git status -z -uall [9ms]
2025-06-07 13:04:09.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:04:14.972 [info] > git config --get commit.template [6ms]
2025-06-07 13:04:14.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:04:14.988 [info] > git status -z -uall [8ms]
2025-06-07 13:04:14.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:04:20.010 [info] > git config --get commit.template [8ms]
2025-06-07 13:04:20.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:04:20.026 [info] > git status -z -uall [10ms]
2025-06-07 13:04:20.027 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:04:25.045 [info] > git config --get commit.template [9ms]
2025-06-07 13:04:25.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:04:25.056 [info] > git status -z -uall [5ms]
2025-06-07 13:04:25.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:04:30.078 [info] > git config --get commit.template [7ms]
2025-06-07 13:04:30.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:04:30.098 [info] > git status -z -uall [13ms]
2025-06-07 13:04:30.098 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 13:04:35.112 [info] > git config --get commit.template [5ms]
2025-06-07 13:04:35.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:04:35.123 [info] > git status -z -uall [5ms]
2025-06-07 13:04:35.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:04:40.142 [info] > git config --get commit.template [6ms]
2025-06-07 13:04:40.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:04:40.158 [info] > git status -z -uall [8ms]
2025-06-07 13:04:40.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:04:45.182 [info] > git config --get commit.template [11ms]
2025-06-07 13:04:45.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:04:45.205 [info] > git status -z -uall [11ms]
2025-06-07 13:04:45.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:04:50.217 [info] > git config --get commit.template [4ms]
2025-06-07 13:04:50.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:04:50.232 [info] > git status -z -uall [6ms]
2025-06-07 13:04:50.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:04:55.249 [info] > git config --get commit.template [7ms]
2025-06-07 13:04:55.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:04:55.260 [info] > git status -z -uall [6ms]
2025-06-07 13:04:55.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:00.692 [info] > git config --get commit.template [2ms]
2025-06-07 13:05:00.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:05:00.718 [info] > git status -z -uall [8ms]
2025-06-07 13:05:00.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:05.734 [info] > git config --get commit.template [6ms]
2025-06-07 13:05:05.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:05:05.745 [info] > git status -z -uall [7ms]
2025-06-07 13:05:05.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:05:10.763 [info] > git config --get commit.template [8ms]
2025-06-07 13:05:10.764 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:05:10.779 [info] > git status -z -uall [8ms]
2025-06-07 13:05:10.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:05:15.801 [info] > git config --get commit.template [10ms]
2025-06-07 13:05:15.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:05:15.818 [info] > git status -z -uall [6ms]
2025-06-07 13:05:15.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:20.844 [info] > git config --get commit.template [12ms]
2025-06-07 13:05:20.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 13:05:20.880 [info] > git status -z -uall [10ms]
2025-06-07 13:05:20.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:05:25.902 [info] > git config --get commit.template [9ms]
2025-06-07 13:05:25.904 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:05:25.915 [info] > git status -z -uall [5ms]
2025-06-07 13:05:25.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:30.933 [info] > git config --get commit.template [0ms]
2025-06-07 13:05:30.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:05:30.968 [info] > git status -z -uall [10ms]
2025-06-07 13:05:30.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:35.987 [info] > git config --get commit.template [1ms]
2025-06-07 13:05:36.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:05:36.021 [info] > git status -z -uall [10ms]
2025-06-07 13:05:36.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:05:41.044 [info] > git config --get commit.template [10ms]
2025-06-07 13:05:41.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:05:41.064 [info] > git status -z -uall [8ms]
2025-06-07 13:05:41.067 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:05:46.079 [info] > git config --get commit.template [1ms]
2025-06-07 13:05:46.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:05:46.098 [info] > git status -z -uall [7ms]
2025-06-07 13:05:46.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:51.144 [info] > git config --get commit.template [19ms]
2025-06-07 13:05:51.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:05:51.173 [info] > git status -z -uall [13ms]
2025-06-07 13:05:51.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:05:56.213 [info] > git config --get commit.template [22ms]
2025-06-07 13:05:56.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:05:56.237 [info] > git status -z -uall [11ms]
2025-06-07 13:05:56.239 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:06:01.256 [info] > git config --get commit.template [1ms]
2025-06-07 13:06:01.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:06:01.276 [info] > git status -z -uall [7ms]
2025-06-07 13:06:01.277 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:06:06.299 [info] > git config --get commit.template [9ms]
2025-06-07 13:06:06.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:06:06.318 [info] > git status -z -uall [9ms]
2025-06-07 13:06:06.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:06:11.334 [info] > git config --get commit.template [6ms]
2025-06-07 13:06:11.335 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:06:11.344 [info] > git status -z -uall [5ms]
2025-06-07 13:06:11.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:06:16.359 [info] > git config --get commit.template [2ms]
2025-06-07 13:06:16.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:06:16.381 [info] > git status -z -uall [6ms]
2025-06-07 13:06:16.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:06:21.399 [info] > git config --get commit.template [7ms]
2025-06-07 13:06:21.401 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:06:21.411 [info] > git status -z -uall [5ms]
2025-06-07 13:06:21.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:06:26.438 [info] > git config --get commit.template [10ms]
2025-06-07 13:06:26.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:06:26.463 [info] > git status -z -uall [10ms]
2025-06-07 13:06:26.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:06:31.483 [info] > git config --get commit.template [7ms]
2025-06-07 13:06:31.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:06:31.498 [info] > git status -z -uall [6ms]
2025-06-07 13:06:31.499 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:06:36.526 [info] > git config --get commit.template [18ms]
2025-06-07 13:06:36.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:06:36.540 [info] > git status -z -uall [5ms]
2025-06-07 13:06:36.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:07:38.786 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:07:38.786 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:07:38.786 [info] > git config --get commit.template [1ms]
2025-06-07 13:07:38.787 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [5ms]
2025-06-07 13:07:38.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:07:38.802 [info] > git status -z -uall [5ms]
2025-06-07 13:07:38.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:07:43.816 [info] > git config --get commit.template [5ms]
2025-06-07 13:07:43.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:07:43.827 [info] > git status -z -uall [5ms]
2025-06-07 13:07:43.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:07:48.844 [info] > git config --get commit.template [5ms]
2025-06-07 13:07:48.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:07:48.855 [info] > git status -z -uall [6ms]
2025-06-07 13:07:48.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-06-07 13:07:53.925 [info] > git config --get commit.template [7ms]
2025-06-07 13:07:53.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:07:53.934 [info] > git status -z -uall [4ms]
2025-06-07 13:07:53.935 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:07:58.955 [info] > git config --get commit.template [8ms]
2025-06-07 13:07:58.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:07:58.968 [info] > git status -z -uall [6ms]
2025-06-07 13:07:58.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:08:03.982 [info] > git config --get commit.template [4ms]
2025-06-07 13:08:03.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:08:03.991 [info] > git status -z -uall [5ms]
2025-06-07 13:08:03.992 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:08:09.005 [info] > git config --get commit.template [5ms]
2025-06-07 13:08:09.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:08:09.016 [info] > git status -z -uall [5ms]
2025-06-07 13:08:09.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:08:14.029 [info] > git config --get commit.template [5ms]
2025-06-07 13:08:14.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:08:14.042 [info] > git status -z -uall [6ms]
2025-06-07 13:08:14.043 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:09:13.813 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:09:13.813 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [15ms]
2025-06-07 13:09:13.814 [info] > git config --get commit.template [2ms]
2025-06-07 13:09:13.814 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:09:13.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:09:13.828 [info] > git status -z -uall [5ms]
2025-06-07 13:09:13.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:09:18.843 [info] > git config --get commit.template [6ms]
2025-06-07 13:09:18.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:09:18.855 [info] > git status -z -uall [5ms]
2025-06-07 13:09:18.856 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:09:23.876 [info] > git config --get commit.template [7ms]
2025-06-07 13:09:23.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:09:23.889 [info] > git status -z -uall [5ms]
2025-06-07 13:09:23.890 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:09:28.905 [info] > git config --get commit.template [6ms]
2025-06-07 13:09:28.906 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:09:28.917 [info] > git status -z -uall [5ms]
2025-06-07 13:09:28.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:09:33.996 [info] > git config --get commit.template [16ms]
2025-06-07 13:09:33.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:09:34.042 [info] > git status -z -uall [19ms]
2025-06-07 13:09:34.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 13:09:39.059 [info] > git config --get commit.template [7ms]
2025-06-07 13:09:39.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:09:39.072 [info] > git status -z -uall [5ms]
2025-06-07 13:09:39.073 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:09:47.850 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [11ms]
2025-06-07 13:09:47.850 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [15ms]
2025-06-07 13:09:47.851 [info] > git config --get commit.template [2ms]
2025-06-07 13:09:47.851 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:09:47.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:09:47.867 [info] > git status -z -uall [5ms]
2025-06-07 13:09:47.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:09:52.883 [info] > git config --get commit.template [5ms]
2025-06-07 13:09:52.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:09:52.893 [info] > git status -z -uall [4ms]
2025-06-07 13:09:52.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:09:57.938 [info] > git config --get commit.template [6ms]
2025-06-07 13:09:57.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:09:57.951 [info] > git status -z -uall [7ms]
2025-06-07 13:09:57.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:10:02.963 [info] > git config --get commit.template [4ms]
2025-06-07 13:10:02.964 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:10:02.973 [info] > git status -z -uall [5ms]
2025-06-07 13:10:02.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:10:07.987 [info] > git config --get commit.template [4ms]
2025-06-07 13:10:07.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:10:07.995 [info] > git status -z -uall [4ms]
2025-06-07 13:10:07.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:10:56.666 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [11ms]
2025-06-07 13:10:56.666 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [15ms]
2025-06-07 13:10:56.667 [info] > git config --get commit.template [2ms]
2025-06-07 13:10:56.667 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 13:10:56.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:10:56.681 [info] > git status -z -uall [5ms]
2025-06-07 13:10:56.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 13:11:01.704 [info] > git config --get commit.template [6ms]
2025-06-07 13:11:01.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:11:01.714 [info] > git status -z -uall [5ms]
2025-06-07 13:11:01.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:11:06.727 [info] > git config --get commit.template [5ms]
2025-06-07 13:11:06.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:11:06.737 [info] > git status -z -uall [5ms]
2025-06-07 13:11:06.738 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:11:11.752 [info] > git config --get commit.template [5ms]
2025-06-07 13:11:11.753 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:11.761 [info] > git status -z -uall [4ms]
2025-06-07 13:11:11.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:11:16.778 [info] > git config --get commit.template [5ms]
2025-06-07 13:11:16.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:16.789 [info] > git status -z -uall [5ms]
2025-06-07 13:11:16.790 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:11:21.805 [info] > git config --get commit.template [6ms]
2025-06-07 13:11:21.806 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:21.815 [info] > git status -z -uall [4ms]
2025-06-07 13:11:21.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:11:26.863 [info] > git config --get commit.template [10ms]
2025-06-07 13:11:26.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:11:26.886 [info] > git status -z -uall [9ms]
2025-06-07 13:11:26.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:11:31.904 [info] > git config --get commit.template [5ms]
2025-06-07 13:11:31.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:11:31.915 [info] > git status -z -uall [5ms]
2025-06-07 13:11:31.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:11:36.931 [info] > git config --get commit.template [5ms]
2025-06-07 13:11:36.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:36.948 [info] > git status -z -uall [8ms]
2025-06-07 13:11:36.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:11:41.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:41.968 [info] > git config --get commit.template [9ms]
2025-06-07 13:11:41.980 [info] > git status -z -uall [5ms]
2025-06-07 13:11:41.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:11:46.999 [info] > git config --get commit.template [5ms]
2025-06-07 13:11:47.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:47.010 [info] > git status -z -uall [5ms]
2025-06-07 13:11:47.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:11:51.979 [info] > git check-ignore -v -z --stdin [21ms]
2025-06-07 13:11:51.980 [info] > git show --textconv :client/src/pages/VoiceTherapy.tsx [12ms]
2025-06-07 13:11:51.980 [info] > git ls-files --stage -- client/src/pages/VoiceTherapy.tsx [6ms]
2025-06-07 13:11:52.014 [info] > git cat-file -s 8e4bf76a1bf6ab88161169086379dd4521274eb6 [14ms]
2025-06-07 13:11:55.248 [info] > git config --get commit.template [1ms]
2025-06-07 13:11:55.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:11:55.267 [info] > git status -z -uall [4ms]
2025-06-07 13:11:55.268 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:11:55.987 [info] > git blame --root --incremental 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/VoiceTherapy.tsx [29ms]
2025-06-07 13:11:59.341 [info] > git show --textconv :client/src/components/RealTimeVoiceChat.tsx [5ms]
2025-06-07 13:11:59.342 [info] > git ls-files --stage -- client/src/components/RealTimeVoiceChat.tsx [2ms]
2025-06-07 13:11:59.349 [info] > git cat-file -s 1a3281906de51c02fa70194887cfdd2fed82d12f [3ms]
2025-06-07 13:11:59.480 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 13:12:00.286 [info] > git config --get commit.template [6ms]
2025-06-07 13:12:00.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:12:00.298 [info] > git status -z -uall [6ms]
2025-06-07 13:12:00.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:12:02.897 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 13:12:06.259 [info] > git show --textconv :client/src/components/VoiceChat.tsx [6ms]
2025-06-07 13:12:06.259 [info] > git ls-files --stage -- client/src/components/VoiceChat.tsx [1ms]
2025-06-07 13:12:06.266 [info] > git cat-file -s 2e1a4f583b1d9226187e16949d34dd18686c699e [2ms]
2025-06-07 13:12:08.959 [info] > git show --textconv :client/src/components/RealTimeVoiceChat.tsx [4ms]
2025-06-07 13:12:08.960 [info] > git ls-files --stage -- client/src/components/RealTimeVoiceChat.tsx [1ms]
2025-06-07 13:12:08.966 [info] > git cat-file -s 1a3281906de51c02fa70194887cfdd2fed82d12f [1ms]
2025-06-07 13:12:09.256 [info] > git config --get commit.template [6ms]
2025-06-07 13:12:09.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:12:09.266 [info] > git status -z -uall [4ms]
2025-06-07 13:12:09.268 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:12:11.713 [info] > git show --textconv :client/src/pages/VoiceTherapy.tsx [6ms]
2025-06-07 13:12:11.713 [info] > git ls-files --stage -- client/src/pages/VoiceTherapy.tsx [2ms]
2025-06-07 13:12:11.726 [info] > git cat-file -s 8e4bf76a1bf6ab88161169086379dd4521274eb6 [2ms]
2025-06-07 13:12:12.076 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 13:12:14.287 [info] > git config --get commit.template [1ms]
2025-06-07 13:12:14.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:12:14.330 [info] > git status -z -uall [18ms]
2025-06-07 13:12:14.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:12:19.437 [info] > git config --get commit.template [22ms]
2025-06-07 13:12:19.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-07 13:12:19.474 [info] > git status -z -uall [8ms]
2025-06-07 13:12:19.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:12:24.488 [info] > git config --get commit.template [5ms]
2025-06-07 13:12:24.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:12:24.500 [info] > git status -z -uall [6ms]
2025-06-07 13:12:24.501 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:12:30.399 [info] > git config --get commit.template [7ms]
2025-06-07 13:12:30.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:12:30.429 [info] > git status -z -uall [7ms]
2025-06-07 13:12:30.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 13:12:37.395 [info] > git config --get commit.template [5ms]
2025-06-07 13:12:37.402 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:12:37.410 [info] > git status -z -uall [4ms]
2025-06-07 13:12:37.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:12:42.424 [info] > git config --get commit.template [5ms]
2025-06-07 13:12:42.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:12:42.433 [info] > git status -z -uall [4ms]
2025-06-07 13:12:42.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:16:41.495 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:16:41.495 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [16ms]
2025-06-07 13:16:41.495 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [23ms]
2025-06-07 13:16:41.496 [info] > git config --get commit.template [2ms]
2025-06-07 13:16:41.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:16:41.529 [info] > git status -z -uall [18ms]
2025-06-07 13:16:41.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 13:16:46.544 [info] > git config --get commit.template [5ms]
2025-06-07 13:16:46.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:16:46.558 [info] > git status -z -uall [8ms]
2025-06-07 13:16:46.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:17:04.420 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [19ms]
2025-06-07 13:17:04.422 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [29ms]
2025-06-07 13:17:04.423 [info] > git config --get commit.template [4ms]
2025-06-07 13:17:04.423 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [15ms]
2025-06-07 13:17:04.438 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:17:04.474 [info] > git status -z -uall [15ms]
2025-06-07 13:17:04.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:17:09.498 [info] > git config --get commit.template [10ms]
2025-06-07 13:17:09.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:17:09.525 [info] > git status -z -uall [11ms]
2025-06-07 13:17:09.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 13:17:14.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 13:17:14.553 [info] > git config --get commit.template [9ms]
2025-06-07 13:17:14.566 [info] > git status -z -uall [6ms]
2025-06-07 13:17:14.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:17:19.582 [info] > git config --get commit.template [6ms]
2025-06-07 13:17:19.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:17:19.597 [info] > git status -z -uall [9ms]
2025-06-07 13:17:19.598 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:17:24.612 [info] > git config --get commit.template [6ms]
2025-06-07 13:17:24.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:17:24.621 [info] > git status -z -uall [4ms]
2025-06-07 13:17:24.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:17:29.637 [info] > git config --get commit.template [5ms]
2025-06-07 13:17:29.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:17:29.648 [info] > git status -z -uall [5ms]
2025-06-07 13:17:29.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:17:34.668 [info] > git config --get commit.template [6ms]
2025-06-07 13:17:34.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:17:34.687 [info] > git status -z -uall [7ms]
2025-06-07 13:17:34.688 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:17:39.711 [info] > git config --get commit.template [10ms]
2025-06-07 13:17:39.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:17:39.738 [info] > git status -z -uall [12ms]
2025-06-07 13:17:39.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:17:44.763 [info] > git config --get commit.template [8ms]
2025-06-07 13:17:44.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:17:44.782 [info] > git status -z -uall [7ms]
2025-06-07 13:17:44.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:17:49.797 [info] > git config --get commit.template [5ms]
2025-06-07 13:17:49.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:17:49.813 [info] > git status -z -uall [9ms]
2025-06-07 13:17:49.813 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:17:55.765 [info] > git config --get commit.template [4ms]
2025-06-07 13:17:55.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:17:55.778 [info] > git status -z -uall [7ms]
2025-06-07 13:17:55.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:18:33.713 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [11ms]
2025-06-07 13:18:33.714 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [19ms]
2025-06-07 13:18:33.715 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [30ms]
2025-06-07 13:18:33.716 [info] > git config --get commit.template [4ms]
2025-06-07 13:18:33.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:18:33.747 [info] > git status -z -uall [7ms]
2025-06-07 13:18:33.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:18:38.762 [info] > git config --get commit.template [5ms]
2025-06-07 13:18:38.763 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:18:38.774 [info] > git status -z -uall [6ms]
2025-06-07 13:18:38.775 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:18:43.792 [info] > git config --get commit.template [6ms]
2025-06-07 13:18:43.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:18:43.803 [info] > git status -z -uall [5ms]
2025-06-07 13:18:43.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:18:48.819 [info] > git config --get commit.template [4ms]
2025-06-07 13:18:48.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:18:48.831 [info] > git status -z -uall [5ms]
2025-06-07 13:18:48.833 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:18:53.848 [info] > git config --get commit.template [5ms]
2025-06-07 13:18:53.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:18:53.868 [info] > git status -z -uall [5ms]
2025-06-07 13:18:53.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:18:58.898 [info] > git config --get commit.template [11ms]
2025-06-07 13:18:58.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:18:58.920 [info] > git status -z -uall [10ms]
2025-06-07 13:18:58.923 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:19:03.961 [info] > git config --get commit.template [14ms]
2025-06-07 13:19:03.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:19:03.986 [info] > git status -z -uall [11ms]
2025-06-07 13:19:03.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:19:17.552 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [23ms]
2025-06-07 13:19:17.553 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [34ms]
2025-06-07 13:19:17.553 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [43ms]
2025-06-07 13:19:17.554 [info] > git config --get commit.template [2ms]
2025-06-07 13:19:17.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:19:17.588 [info] > git status -z -uall [8ms]
2025-06-07 13:19:17.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:19:22.605 [info] > git config --get commit.template [1ms]
2025-06-07 13:19:22.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:19:22.625 [info] > git status -z -uall [5ms]
2025-06-07 13:19:22.626 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:20:12.042 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 13:20:12.043 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [14ms]
2025-06-07 13:20:12.043 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [18ms]
2025-06-07 13:20:12.045 [info] > git config --get commit.template [4ms]
2025-06-07 13:20:12.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:20:12.063 [info] > git status -z -uall [5ms]
2025-06-07 13:20:12.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:20:17.076 [info] > git config --get commit.template [4ms]
2025-06-07 13:20:17.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:20:17.087 [info] > git status -z -uall [5ms]
2025-06-07 13:20:17.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:20:42.359 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:20:42.359 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [13ms]
2025-06-07 13:20:42.359 [info] > git config --get commit.template [1ms]
2025-06-07 13:20:42.360 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:20:42.365 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:20:42.375 [info] > git status -z -uall [5ms]
2025-06-07 13:20:42.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:20:47.391 [info] > git config --get commit.template [5ms]
2025-06-07 13:20:47.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:20:47.410 [info] > git status -z -uall [9ms]
2025-06-07 13:20:47.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:20:52.427 [info] > git config --get commit.template [5ms]
2025-06-07 13:20:52.427 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:20:52.437 [info] > git status -z -uall [5ms]
2025-06-07 13:20:52.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:20:57.461 [info] > git config --get commit.template [6ms]
2025-06-07 13:20:57.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [92ms]
2025-06-07 13:20:57.609 [info] > git status -z -uall [13ms]
2025-06-07 13:20:57.610 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:21:02.650 [info] > git config --get commit.template [18ms]
2025-06-07 13:21:02.651 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:21:02.661 [info] > git status -z -uall [4ms]
2025-06-07 13:21:02.662 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:21:07.733 [info] > git config --get commit.template [6ms]
2025-06-07 13:21:07.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:21:07.748 [info] > git status -z -uall [9ms]
2025-06-07 13:21:07.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 13:21:12.764 [info] > git config --get commit.template [7ms]
2025-06-07 13:21:12.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:21:12.778 [info] > git status -z -uall [7ms]
2025-06-07 13:21:12.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:21:17.793 [info] > git config --get commit.template [5ms]
2025-06-07 13:21:17.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:21:17.807 [info] > git status -z -uall [7ms]
2025-06-07 13:21:17.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:22:05.781 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:22:05.782 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:22:05.782 [info] > git config --get commit.template [1ms]
2025-06-07 13:22:05.782 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:22:05.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:22:05.797 [info] > git status -z -uall [5ms]
2025-06-07 13:22:05.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:22:10.826 [info] > git config --get commit.template [18ms]
2025-06-07 13:22:10.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:22:10.843 [info] > git status -z -uall [6ms]
2025-06-07 13:22:10.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:22:15.859 [info] > git config --get commit.template [5ms]
2025-06-07 13:22:15.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:22:15.869 [info] > git status -z -uall [5ms]
2025-06-07 13:22:15.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:22:20.887 [info] > git config --get commit.template [5ms]
2025-06-07 13:22:20.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:22:20.897 [info] > git status -z -uall [5ms]
2025-06-07 13:22:20.898 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:22:25.912 [info] > git config --get commit.template [5ms]
2025-06-07 13:22:25.913 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:22:25.923 [info] > git status -z -uall [5ms]
2025-06-07 13:22:25.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:22:30.936 [info] > git config --get commit.template [5ms]
2025-06-07 13:22:30.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:22:30.945 [info] > git status -z -uall [4ms]
2025-06-07 13:22:30.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:22:35.957 [info] > git config --get commit.template [4ms]
2025-06-07 13:22:35.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:22:35.966 [info] > git status -z -uall [4ms]
2025-06-07 13:22:35.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:04.307 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [14ms]
2025-06-07 13:23:04.308 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [22ms]
2025-06-07 13:23:04.308 [info] > git config --get commit.template [2ms]
2025-06-07 13:23:04.308 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:23:04.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:23:04.331 [info] > git status -z -uall [8ms]
2025-06-07 13:23:04.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:09.346 [info] > git config --get commit.template [6ms]
2025-06-07 13:23:09.347 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:23:09.358 [info] > git status -z -uall [5ms]
2025-06-07 13:23:09.359 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:14.370 [info] > git config --get commit.template [4ms]
2025-06-07 13:23:14.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:23:14.380 [info] > git status -z -uall [5ms]
2025-06-07 13:23:14.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:19.394 [info] > git config --get commit.template [6ms]
2025-06-07 13:23:19.395 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:23:19.404 [info] > git status -z -uall [4ms]
2025-06-07 13:23:19.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:23:24.417 [info] > git config --get commit.template [4ms]
2025-06-07 13:23:24.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:23:24.429 [info] > git status -z -uall [6ms]
2025-06-07 13:23:24.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:29.442 [info] > git config --get commit.template [5ms]
2025-06-07 13:23:29.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:23:29.453 [info] > git status -z -uall [4ms]
2025-06-07 13:23:29.454 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:35.125 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:23:35.125 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:23:35.126 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:23:35.126 [info] > git config --get commit.template [1ms]
2025-06-07 13:23:35.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 13:23:35.145 [info] > git status -z -uall [5ms]
2025-06-07 13:23:35.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:40.189 [info] > git config --get commit.template [9ms]
2025-06-07 13:23:40.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:23:40.210 [info] > git status -z -uall [6ms]
2025-06-07 13:23:40.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:23:45.223 [info] > git config --get commit.template [4ms]
2025-06-07 13:23:45.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:23:45.233 [info] > git status -z -uall [4ms]
2025-06-07 13:23:45.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:23:55.636 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [12ms]
2025-06-07 13:23:55.636 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [18ms]
2025-06-07 13:23:55.637 [info] > git config --get commit.template [2ms]
2025-06-07 13:23:55.637 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 13:23:55.649 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:23:55.665 [info] > git status -z -uall [8ms]
2025-06-07 13:23:55.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:24:00.676 [info] > git config --get commit.template [3ms]
2025-06-07 13:24:00.682 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:24:00.691 [info] > git status -z -uall [5ms]
2025-06-07 13:24:00.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:24:22.928 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 13:24:22.928 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [12ms]
2025-06-07 13:24:22.929 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [5ms]
2025-06-07 13:24:22.929 [info] > git config --get commit.template [1ms]
2025-06-07 13:24:22.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:24:22.941 [info] > git status -z -uall [4ms]
2025-06-07 13:24:22.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:24:43.788 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:24:43.788 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [13ms]
2025-06-07 13:24:43.789 [info] > git config --get commit.template [2ms]
2025-06-07 13:24:43.789 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [6ms]
2025-06-07 13:24:43.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:24:43.803 [info] > git status -z -uall [5ms]
2025-06-07 13:24:43.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:24:48.816 [info] > git config --get commit.template [4ms]
2025-06-07 13:24:48.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:24:48.824 [info] > git status -z -uall [4ms]
2025-06-07 13:24:48.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:03.678 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [16ms]
2025-06-07 13:25:03.678 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [23ms]
2025-06-07 13:25:03.679 [info] > git config --get commit.template [3ms]
2025-06-07 13:25:03.679 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [12ms]
2025-06-07 13:25:03.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:25:03.704 [info] > git status -z -uall [8ms]
2025-06-07 13:25:03.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:25:03.720 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-07 13:25:03.729 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [1ms]
2025-06-07 13:25:08.732 [info] > git config --get commit.template [5ms]
2025-06-07 13:25:08.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:25:08.748 [info] > git status -z -uall [6ms]
2025-06-07 13:25:08.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:13.762 [info] > git config --get commit.template [6ms]
2025-06-07 13:25:13.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:13.779 [info] > git status -z -uall [9ms]
2025-06-07 13:25:13.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:19.856 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [14ms]
2025-06-07 13:25:19.857 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [29ms]
2025-06-07 13:25:19.857 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [36ms]
2025-06-07 13:25:19.859 [info] > git config --get commit.template [4ms]
2025-06-07 13:25:19.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:19.881 [info] > git status -z -uall [8ms]
2025-06-07 13:25:19.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:24.895 [info] > git config --get commit.template [4ms]
2025-06-07 13:25:24.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:24.906 [info] > git status -z -uall [6ms]
2025-06-07 13:25:24.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:29.666 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [4ms]
2025-06-07 13:25:29.666 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [8ms]
2025-06-07 13:25:29.667 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [1ms]
2025-06-07 13:25:29.919 [info] > git config --get commit.template [5ms]
2025-06-07 13:25:29.920 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:29.930 [info] > git status -z -uall [5ms]
2025-06-07 13:25:29.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:34.942 [info] > git config --get commit.template [5ms]
2025-06-07 13:25:34.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:34.958 [info] > git status -z -uall [8ms]
2025-06-07 13:25:34.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:25:39.972 [info] > git config --get commit.template [5ms]
2025-06-07 13:25:39.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:25:39.980 [info] > git status -z -uall [4ms]
2025-06-07 13:25:39.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:25:44.993 [info] > git config --get commit.template [5ms]
2025-06-07 13:25:44.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:45.004 [info] > git status -z -uall [5ms]
2025-06-07 13:25:45.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:25:50.016 [info] > git config --get commit.template [5ms]
2025-06-07 13:25:50.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:50.025 [info] > git status -z -uall [4ms]
2025-06-07 13:25:50.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:25:55.040 [info] > git config --get commit.template [4ms]
2025-06-07 13:25:55.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:25:55.050 [info] > git status -z -uall [5ms]
2025-06-07 13:25:55.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:26:00.062 [info] > git config --get commit.template [4ms]
2025-06-07 13:26:00.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:26:00.072 [info] > git status -z -uall [5ms]
2025-06-07 13:26:00.073 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:26:05.087 [info] > git config --get commit.template [5ms]
2025-06-07 13:26:05.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:26:05.099 [info] > git status -z -uall [7ms]
2025-06-07 13:26:05.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:26:08.677 [info] > git show --textconv :client/src/pages/VoiceTherapy.tsx [6ms]
2025-06-07 13:26:08.678 [info] > git ls-files --stage -- client/src/pages/VoiceTherapy.tsx [1ms]
2025-06-07 13:26:08.684 [info] > git cat-file -s f49d0842d9fb56981fb7245aca0e40b2ecb4bfda [1ms]
2025-06-07 13:26:08.804 [info] > git blame --root --incremental afcc68bbfc7a9840fe8bc9258ce0aab95444c453 -- client/src/pages/VoiceTherapy.tsx [16ms]
2025-06-07 13:26:14.572 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:26:14.573 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [16ms]
2025-06-07 13:26:14.573 [info] > git config --get commit.template [2ms]
2025-06-07 13:26:14.573 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:26:14.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:26:14.595 [info] > git status -z -uall [5ms]
2025-06-07 13:26:14.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:26:19.604 [info] > git config --get commit.template [1ms]
2025-06-07 13:26:19.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:26:19.619 [info] > git status -z -uall [5ms]
2025-06-07 13:26:19.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:26:31.531 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [8ms]
2025-06-07 13:26:31.531 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [14ms]
2025-06-07 13:26:31.532 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [20ms]
2025-06-07 13:26:31.534 [info] > git config --get commit.template [2ms]
2025-06-07 13:26:31.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:26:31.558 [info] > git status -z -uall [6ms]
2025-06-07 13:26:31.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:26:36.572 [info] > git config --get commit.template [2ms]
2025-06-07 13:26:36.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:26:36.601 [info] > git status -z -uall [9ms]
2025-06-07 13:26:36.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:26:41.617 [info] > git config --get commit.template [2ms]
2025-06-07 13:26:41.624 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:26:41.633 [info] > git status -z -uall [5ms]
2025-06-07 13:26:41.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:26:46.649 [info] > git config --get commit.template [7ms]
2025-06-07 13:26:46.650 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:26:46.662 [info] > git status -z -uall [6ms]
2025-06-07 13:26:46.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:26:51.676 [info] > git config --get commit.template [4ms]
2025-06-07 13:26:51.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:26:51.687 [info] > git status -z -uall [5ms]
2025-06-07 13:26:51.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:26:56.700 [info] > git config --get commit.template [4ms]
2025-06-07 13:26:56.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:26:56.713 [info] > git status -z -uall [5ms]
2025-06-07 13:26:56.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:27:00.158 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [4ms]
2025-06-07 13:27:00.158 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [8ms]
2025-06-07 13:27:00.159 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [1ms]
2025-06-07 13:27:01.729 [info] > git config --get commit.template [6ms]
2025-06-07 13:27:01.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:27:01.738 [info] > git status -z -uall [4ms]
2025-06-07 13:27:01.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:27:32.664 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [14ms]
2025-06-07 13:27:32.664 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [23ms]
2025-06-07 13:27:32.665 [info] > git config --get commit.template [2ms]
2025-06-07 13:27:32.665 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:27:32.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:27:32.697 [info] > git status -z -uall [8ms]
2025-06-07 13:27:32.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:27:37.713 [info] > git config --get commit.template [1ms]
2025-06-07 13:27:37.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:27:37.749 [info] > git status -z -uall [12ms]
2025-06-07 13:27:37.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:27:48.897 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [20ms]
2025-06-07 13:27:48.898 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [29ms]
2025-06-07 13:27:48.898 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [37ms]
2025-06-07 13:27:48.898 [info] > git config --get commit.template [3ms]
2025-06-07 13:27:48.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:27:48.927 [info] > git status -z -uall [11ms]
2025-06-07 13:27:48.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:27:53.947 [info] > git config --get commit.template [2ms]
2025-06-07 13:27:53.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:27:53.988 [info] > git status -z -uall [13ms]
2025-06-07 13:27:53.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:28:09.868 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [16ms]
2025-06-07 13:28:09.869 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [28ms]
2025-06-07 13:28:09.870 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [39ms]
2025-06-07 13:28:09.887 [info] > git config --get commit.template [19ms]
2025-06-07 13:28:09.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:28:09.913 [info] > git status -z -uall [11ms]
2025-06-07 13:28:09.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:28:14.927 [info] > git config --get commit.template [1ms]
2025-06-07 13:28:14.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:28:14.943 [info] > git status -z -uall [5ms]
2025-06-07 13:28:14.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:28:19.958 [info] > git config --get commit.template [5ms]
2025-06-07 13:28:19.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:28:19.975 [info] > git status -z -uall [10ms]
2025-06-07 13:28:19.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:28:24.991 [info] > git config --get commit.template [5ms]
2025-06-07 13:28:24.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:28:25.005 [info] > git status -z -uall [7ms]
2025-06-07 13:28:25.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:28:30.018 [info] > git config --get commit.template [4ms]
2025-06-07 13:28:30.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:28:30.029 [info] > git status -z -uall [5ms]
2025-06-07 13:28:30.030 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:28:35.041 [info] > git config --get commit.template [4ms]
2025-06-07 13:28:35.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:28:35.051 [info] > git status -z -uall [4ms]
2025-06-07 13:28:35.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:28:51.139 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [26ms]
2025-06-07 13:28:51.140 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [41ms]
2025-06-07 13:28:51.142 [info] > git config --get commit.template [3ms]
2025-06-07 13:28:51.142 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [16ms]
2025-06-07 13:28:51.154 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:28:51.173 [info] > git status -z -uall [11ms]
2025-06-07 13:28:51.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 13:28:56.187 [info] > git config --get commit.template [5ms]
2025-06-07 13:28:56.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:28:56.197 [info] > git status -z -uall [4ms]
2025-06-07 13:28:56.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:01.212 [info] > git config --get commit.template [6ms]
2025-06-07 13:29:01.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:29:01.223 [info] > git status -z -uall [5ms]
2025-06-07 13:29:01.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:06.236 [info] > git config --get commit.template [4ms]
2025-06-07 13:29:06.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:29:06.247 [info] > git status -z -uall [4ms]
2025-06-07 13:29:06.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:11.260 [info] > git config --get commit.template [5ms]
2025-06-07 13:29:11.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:29:11.269 [info] > git status -z -uall [4ms]
2025-06-07 13:29:11.270 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:29:16.449 [info] > git config --get commit.template [3ms]
2025-06-07 13:29:16.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:29:16.476 [info] > git status -z -uall [8ms]
2025-06-07 13:29:16.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:21.509 [info] > git config --get commit.template [11ms]
2025-06-07 13:29:21.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:29:21.538 [info] > git status -z -uall [7ms]
2025-06-07 13:29:21.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:29:26.554 [info] > git config --get commit.template [5ms]
2025-06-07 13:29:26.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:29:26.564 [info] > git status -z -uall [4ms]
2025-06-07 13:29:26.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:29:31.577 [info] > git config --get commit.template [5ms]
2025-06-07 13:29:31.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:29:31.587 [info] > git status -z -uall [5ms]
2025-06-07 13:29:31.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:29:36.616 [info] > git config --get commit.template [19ms]
2025-06-07 13:29:36.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 13:29:36.635 [info] > git status -z -uall [7ms]
2025-06-07 13:29:36.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:41.663 [info] > git config --get commit.template [12ms]
2025-06-07 13:29:41.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:29:41.681 [info] > git status -z -uall [7ms]
2025-06-07 13:29:41.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:46.697 [info] > git config --get commit.template [2ms]
2025-06-07 13:29:46.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:29:46.716 [info] > git status -z -uall [6ms]
2025-06-07 13:29:46.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:51.733 [info] > git config --get commit.template [4ms]
2025-06-07 13:29:51.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:29:51.744 [info] > git status -z -uall [5ms]
2025-06-07 13:29:51.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:29:56.766 [info] > git config --get commit.template [1ms]
2025-06-07 13:29:56.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:29:56.797 [info] > git status -z -uall [7ms]
2025-06-07 13:29:56.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:30:01.815 [info] > git config --get commit.template [1ms]
2025-06-07 13:30:01.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:30:01.850 [info] > git status -z -uall [11ms]
2025-06-07 13:30:01.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:30:06.864 [info] > git config --get commit.template [1ms]
2025-06-07 13:30:06.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:30:06.900 [info] > git status -z -uall [15ms]
2025-06-07 13:30:06.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:30:11.916 [info] > git config --get commit.template [5ms]
2025-06-07 13:30:11.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:30:11.925 [info] > git status -z -uall [4ms]
2025-06-07 13:30:11.926 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:32:18.712 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:32:18.712 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:32:18.715 [info] > git config --get commit.template [4ms]
2025-06-07 13:32:18.715 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:32:18.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [78ms]
2025-06-07 13:32:18.819 [info] > git status -z -uall [5ms]
2025-06-07 13:32:18.821 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:40:07.811 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [5ms]
2025-06-07 13:40:07.812 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [10ms]
2025-06-07 13:40:07.812 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:40:07.813 [info] > git config --get commit.template [3ms]
2025-06-07 13:40:07.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:40:07.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:40:07.832 [info] > git status -z -uall [6ms]
2025-06-07 13:40:12.848 [info] > git config --get commit.template [6ms]
2025-06-07 13:40:12.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:40:12.868 [info] > git status -z -uall [10ms]
2025-06-07 13:40:12.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:41:11.414 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [23ms]
2025-06-07 13:41:11.415 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [33ms]
2025-06-07 13:41:11.415 [info] > git config --get commit.template [1ms]
2025-06-07 13:41:11.416 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [17ms]
2025-06-07 13:41:11.427 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:41:11.440 [info] > git status -z -uall [5ms]
2025-06-07 13:41:11.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:41:16.457 [info] > git config --get commit.template [6ms]
2025-06-07 13:41:16.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:41:16.481 [info] > git status -z -uall [11ms]
2025-06-07 13:41:16.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:41:21.493 [info] > git config --get commit.template [1ms]
2025-06-07 13:41:21.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:41:21.506 [info] > git status -z -uall [4ms]
2025-06-07 13:41:21.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:41:26.520 [info] > git config --get commit.template [5ms]
2025-06-07 13:41:26.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:41:26.531 [info] > git status -z -uall [6ms]
2025-06-07 13:41:26.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:41:31.544 [info] > git config --get commit.template [5ms]
2025-06-07 13:41:31.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:41:31.553 [info] > git status -z -uall [4ms]
2025-06-07 13:41:31.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:41:36.570 [info] > git config --get commit.template [5ms]
2025-06-07 13:41:36.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:41:36.584 [info] > git status -z -uall [6ms]
2025-06-07 13:41:36.585 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:41:41.597 [info] > git config --get commit.template [4ms]
2025-06-07 13:41:41.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:41:41.608 [info] > git status -z -uall [5ms]
2025-06-07 13:41:41.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:41:46.709 [info] > git config --get commit.template [2ms]
2025-06-07 13:41:46.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:41:46.725 [info] > git status -z -uall [5ms]
2025-06-07 13:41:46.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:41:51.738 [info] > git config --get commit.template [4ms]
2025-06-07 13:41:51.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:41:51.750 [info] > git status -z -uall [7ms]
2025-06-07 13:41:51.751 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:41:56.764 [info] > git config --get commit.template [5ms]
2025-06-07 13:41:56.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:41:56.774 [info] > git status -z -uall [5ms]
2025-06-07 13:41:56.775 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:42:01.789 [info] > git config --get commit.template [5ms]
2025-06-07 13:42:01.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:42:01.801 [info] > git status -z -uall [5ms]
2025-06-07 13:42:01.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:42:06.821 [info] > git config --get commit.template [10ms]
2025-06-07 13:42:06.822 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:42:06.836 [info] > git status -z -uall [8ms]
2025-06-07 13:42:06.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:42:11.851 [info] > git config --get commit.template [4ms]
2025-06-07 13:42:11.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:42:11.862 [info] > git status -z -uall [5ms]
2025-06-07 13:42:11.864 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:42:16.875 [info] > git config --get commit.template [4ms]
2025-06-07 13:42:16.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:42:16.891 [info] > git status -z -uall [7ms]
2025-06-07 13:42:16.892 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:42:21.905 [info] > git config --get commit.template [4ms]
2025-06-07 13:42:21.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:42:21.918 [info] > git status -z -uall [7ms]
2025-06-07 13:42:21.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:42:26.936 [info] > git config --get commit.template [7ms]
2025-06-07 13:42:26.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:42:26.956 [info] > git status -z -uall [10ms]
2025-06-07 13:42:26.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:42:31.987 [info] > git config --get commit.template [15ms]
2025-06-07 13:42:31.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:42:32.003 [info] > git status -z -uall [7ms]
2025-06-07 13:42:32.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:42:37.033 [info] > git config --get commit.template [17ms]
2025-06-07 13:42:37.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:42:37.051 [info] > git status -z -uall [9ms]
2025-06-07 13:42:37.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:42:42.078 [info] > git config --get commit.template [13ms]
2025-06-07 13:42:42.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:42:42.097 [info] > git status -z -uall [10ms]
2025-06-07 13:42:42.098 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:42:47.134 [info] > git config --get commit.template [15ms]
2025-06-07 13:42:47.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 13:42:47.176 [info] > git status -z -uall [11ms]
2025-06-07 13:42:47.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:42:52.195 [info] > git config --get commit.template [1ms]
2025-06-07 13:42:52.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:42:52.227 [info] > git status -z -uall [12ms]
2025-06-07 13:42:52.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:42:57.244 [info] > git config --get commit.template [0ms]
2025-06-07 13:42:57.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 13:42:57.347 [info] > git status -z -uall [33ms]
2025-06-07 13:42:57.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [24ms]
2025-06-07 13:43:02.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:43:02.409 [info] > git config --get commit.template [32ms]
2025-06-07 13:43:02.481 [info] > git status -z -uall [25ms]
2025-06-07 13:43:02.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:44:59.470 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [22ms]
2025-06-07 13:44:59.470 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [31ms]
2025-06-07 13:44:59.471 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [39ms]
2025-06-07 13:44:59.472 [info] > git config --get commit.template [3ms]
2025-06-07 13:44:59.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:44:59.526 [info] > git status -z -uall [20ms]
2025-06-07 13:44:59.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:45:28.698 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:45:28.699 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [13ms]
2025-06-07 13:45:28.699 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [17ms]
2025-06-07 13:45:28.699 [info] > git config --get commit.template [2ms]
2025-06-07 13:45:28.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:45:28.720 [info] > git status -z -uall [5ms]
2025-06-07 13:45:28.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:45:33.739 [info] > git config --get commit.template [7ms]
2025-06-07 13:45:33.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:45:33.751 [info] > git status -z -uall [5ms]
2025-06-07 13:45:33.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:46:09.412 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:46:09.413 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:46:09.413 [info] > git config --get commit.template [1ms]
2025-06-07 13:46:09.413 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:46:09.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:46:09.434 [info] > git status -z -uall [5ms]
2025-06-07 13:46:09.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:46:14.447 [info] > git config --get commit.template [5ms]
2025-06-07 13:46:14.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:46:14.460 [info] > git status -z -uall [7ms]
2025-06-07 13:46:14.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:46:49.765 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [12ms]
2025-06-07 13:46:49.765 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [16ms]
2025-06-07 13:46:49.766 [info] > git config --get commit.template [4ms]
2025-06-07 13:46:49.766 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:46:49.773 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:46:49.784 [info] > git status -z -uall [5ms]
2025-06-07 13:46:49.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:46:49.793 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-07 13:46:49.800 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [1ms]
2025-06-07 13:46:54.805 [info] > git config --get commit.template [1ms]
2025-06-07 13:46:54.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 13:46:54.840 [info] > git status -z -uall [10ms]
2025-06-07 13:46:54.841 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:46:59.861 [info] > git config --get commit.template [6ms]
2025-06-07 13:46:59.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:46:59.886 [info] > git status -z -uall [17ms]
2025-06-07 13:46:59.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:47:04.904 [info] > git config --get commit.template [1ms]
2025-06-07 13:47:04.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:47:04.932 [info] > git status -z -uall [7ms]
2025-06-07 13:47:04.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:47:09.950 [info] > git config --get commit.template [7ms]
2025-06-07 13:47:09.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:47:09.969 [info] > git status -z -uall [10ms]
2025-06-07 13:47:09.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:47:31.478 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [70ms]
2025-06-07 13:47:31.478 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [78ms]
2025-06-07 13:47:31.479 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [84ms]
2025-06-07 13:47:31.480 [info] > git config --get commit.template [3ms]
2025-06-07 13:47:31.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:47:31.500 [info] > git status -z -uall [4ms]
2025-06-07 13:47:31.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:47:36.514 [info] > git config --get commit.template [4ms]
2025-06-07 13:47:36.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:47:36.525 [info] > git status -z -uall [5ms]
2025-06-07 13:47:36.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:47:42.498 [info] > git config --get commit.template [7ms]
2025-06-07 13:47:42.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-07 13:47:42.528 [info] > git status -z -uall [6ms]
2025-06-07 13:47:42.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:47:47.544 [info] > git config --get commit.template [5ms]
2025-06-07 13:47:47.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:47:47.554 [info] > git status -z -uall [5ms]
2025-06-07 13:47:47.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:47:52.568 [info] > git config --get commit.template [4ms]
2025-06-07 13:47:52.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:47:52.579 [info] > git status -z -uall [4ms]
2025-06-07 13:47:52.580 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:48:02.505 [info] > git config --get commit.template [4ms]
2025-06-07 13:48:02.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:48:02.518 [info] > git status -z -uall [7ms]
2025-06-07 13:48:02.519 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:48:07.533 [info] > git config --get commit.template [4ms]
2025-06-07 13:48:07.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:48:07.544 [info] > git status -z -uall [6ms]
2025-06-07 13:48:07.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:48:19.982 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:48:19.982 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [14ms]
2025-06-07 13:48:19.983 [info] > git config --get commit.template [2ms]
2025-06-07 13:48:19.983 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:48:19.990 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:48:20.000 [info] > git status -z -uall [5ms]
2025-06-07 13:48:20.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:48:25.017 [info] > git config --get commit.template [7ms]
2025-06-07 13:48:25.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:48:25.031 [info] > git status -z -uall [6ms]
2025-06-07 13:48:25.032 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:48:59.841 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [13ms]
2025-06-07 13:48:59.841 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [17ms]
2025-06-07 13:48:59.842 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [22ms]
2025-06-07 13:48:59.842 [info] > git config --get commit.template [1ms]
2025-06-07 13:48:59.847 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:48:59.856 [info] > git status -z -uall [4ms]
2025-06-07 13:48:59.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:49:04.869 [info] > git config --get commit.template [4ms]
2025-06-07 13:49:04.870 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:49:04.881 [info] > git status -z -uall [5ms]
2025-06-07 13:49:04.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:49:09.912 [info] > git config --get commit.template [10ms]
2025-06-07 13:49:09.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:49:09.932 [info] > git status -z -uall [8ms]
2025-06-07 13:49:09.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:49:16.527 [info] > git config --get commit.template [10ms]
2025-06-07 13:49:16.527 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:49:16.551 [info] > git status -z -uall [10ms]
2025-06-07 13:49:16.553 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:49:21.566 [info] > git config --get commit.template [4ms]
2025-06-07 13:49:21.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:49:21.576 [info] > git status -z -uall [5ms]
2025-06-07 13:49:21.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:49:26.590 [info] > git config --get commit.template [5ms]
2025-06-07 13:49:26.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:49:26.607 [info] > git status -z -uall [7ms]
2025-06-07 13:49:26.610 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:49:31.629 [info] > git config --get commit.template [9ms]
2025-06-07 13:49:31.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:49:31.645 [info] > git status -z -uall [5ms]
2025-06-07 13:49:31.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:49:36.671 [info] > git config --get commit.template [12ms]
2025-06-07 13:49:36.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:49:36.700 [info] > git status -z -uall [13ms]
2025-06-07 13:49:36.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:49:41.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:49:41.720 [info] > git config --get commit.template [7ms]
2025-06-07 13:49:41.741 [info] > git status -z -uall [13ms]
2025-06-07 13:49:41.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:49:47.514 [info] > git config --get commit.template [5ms]
2025-06-07 13:49:47.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:49:47.525 [info] > git status -z -uall [5ms]
2025-06-07 13:49:47.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:49:52.543 [info] > git config --get commit.template [8ms]
2025-06-07 13:49:52.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:49:52.563 [info] > git status -z -uall [8ms]
2025-06-07 13:49:52.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:49:57.578 [info] > git config --get commit.template [5ms]
2025-06-07 13:49:57.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:49:57.588 [info] > git status -z -uall [4ms]
2025-06-07 13:49:57.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:50:02.609 [info] > git config --get commit.template [8ms]
2025-06-07 13:50:02.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:02.624 [info] > git status -z -uall [8ms]
2025-06-07 13:50:02.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:50:07.648 [info] > git config --get commit.template [10ms]
2025-06-07 13:50:07.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:07.672 [info] > git status -z -uall [11ms]
2025-06-07 13:50:07.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 13:50:12.688 [info] > git config --get commit.template [1ms]
2025-06-07 13:50:12.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:50:12.722 [info] > git status -z -uall [8ms]
2025-06-07 13:50:12.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:50:17.736 [info] > git config --get commit.template [5ms]
2025-06-07 13:50:17.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:17.748 [info] > git status -z -uall [5ms]
2025-06-07 13:50:17.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:50:22.763 [info] > git config --get commit.template [4ms]
2025-06-07 13:50:22.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:22.773 [info] > git status -z -uall [4ms]
2025-06-07 13:50:22.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 13:50:27.796 [info] > git config --get commit.template [5ms]
2025-06-07 13:50:27.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:27.808 [info] > git status -z -uall [4ms]
2025-06-07 13:50:27.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:50:32.830 [info] > git config --get commit.template [8ms]
2025-06-07 13:50:32.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:32.850 [info] > git status -z -uall [10ms]
2025-06-07 13:50:32.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:50:37.863 [info] > git config --get commit.template [5ms]
2025-06-07 13:50:37.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:50:37.873 [info] > git status -z -uall [4ms]
2025-06-07 13:50:37.874 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:50:42.888 [info] > git config --get commit.template [5ms]
2025-06-07 13:50:42.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:42.901 [info] > git status -z -uall [7ms]
2025-06-07 13:50:42.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:50:47.920 [info] > git config --get commit.template [5ms]
2025-06-07 13:50:47.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:47.930 [info] > git status -z -uall [4ms]
2025-06-07 13:50:47.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:50:52.947 [info] > git config --get commit.template [1ms]
2025-06-07 13:50:52.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:52.969 [info] > git status -z -uall [6ms]
2025-06-07 13:50:52.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:50:57.987 [info] > git config --get commit.template [5ms]
2025-06-07 13:50:57.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:50:58.004 [info] > git status -z -uall [6ms]
2025-06-07 13:50:58.004 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:51:03.017 [info] > git config --get commit.template [5ms]
2025-06-07 13:51:03.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:03.029 [info] > git status -z -uall [5ms]
2025-06-07 13:51:03.030 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:51:08.045 [info] > git config --get commit.template [5ms]
2025-06-07 13:51:08.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:08.054 [info] > git status -z -uall [4ms]
2025-06-07 13:51:08.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:51:13.071 [info] > git config --get commit.template [7ms]
2025-06-07 13:51:13.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:13.080 [info] > git status -z -uall [4ms]
2025-06-07 13:51:13.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:51:18.095 [info] > git config --get commit.template [5ms]
2025-06-07 13:51:18.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:51:18.106 [info] > git status -z -uall [5ms]
2025-06-07 13:51:18.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:51:27.336 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:51:27.338 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [15ms]
2025-06-07 13:51:27.338 [info] > git config --get commit.template [2ms]
2025-06-07 13:51:27.338 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [7ms]
2025-06-07 13:51:27.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:27.353 [info] > git status -z -uall [4ms]
2025-06-07 13:51:27.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:51:32.369 [info] > git config --get commit.template [5ms]
2025-06-07 13:51:32.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:32.381 [info] > git status -z -uall [5ms]
2025-06-07 13:51:32.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:51:37.451 [info] > git config --get commit.template [60ms]
2025-06-07 13:51:37.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [57ms]
2025-06-07 13:51:37.474 [info] > git status -z -uall [10ms]
2025-06-07 13:51:37.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 13:51:42.488 [info] > git config --get commit.template [4ms]
2025-06-07 13:51:42.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:42.498 [info] > git status -z -uall [5ms]
2025-06-07 13:51:42.499 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:51:47.512 [info] > git config --get commit.template [6ms]
2025-06-07 13:51:47.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:47.524 [info] > git status -z -uall [6ms]
2025-06-07 13:51:47.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:51:52.537 [info] > git config --get commit.template [4ms]
2025-06-07 13:51:52.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:51:52.548 [info] > git status -z -uall [5ms]
2025-06-07 13:51:52.549 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:51:57.571 [info] > git config --get commit.template [10ms]
2025-06-07 13:51:57.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:51:57.587 [info] > git status -z -uall [7ms]
2025-06-07 13:51:57.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:52:02.603 [info] > git config --get commit.template [5ms]
2025-06-07 13:52:02.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:52:02.613 [info] > git status -z -uall [4ms]
2025-06-07 13:52:02.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:52:07.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 13:52:07.632 [info] > git config --get commit.template [7ms]
2025-06-07 13:52:07.643 [info] > git status -z -uall [5ms]
2025-06-07 13:52:07.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:52:12.656 [info] > git config --get commit.template [5ms]
2025-06-07 13:52:12.658 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:52:12.666 [info] > git status -z -uall [4ms]
2025-06-07 13:52:12.668 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 13:52:36.057 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [13ms]
2025-06-07 13:52:36.057 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [18ms]
2025-06-07 13:52:36.058 [info] > git config --get commit.template [2ms]
2025-06-07 13:52:36.058 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [9ms]
2025-06-07 13:52:36.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:52:36.076 [info] > git status -z -uall [5ms]
2025-06-07 13:52:36.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:53:13.503 [info] > git ls-tree -l 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- client/src/pages/AdminAITest.tsx [20ms]
2025-06-07 13:53:13.503 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- browser-test.js [27ms]
2025-06-07 13:53:13.504 [info] > git config --get commit.template [7ms]
2025-06-07 13:53:13.504 [info] > git ls-tree -l 3d8b3a5165d260649a750d23d7e5b1cc85844257 -- client/src/pages/AdminAITest.tsx [15ms]
2025-06-07 13:53:13.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 13:53:13.544 [info] > git status -z -uall [16ms]
2025-06-07 13:53:13.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 13:53:18.557 [info] > git config --get commit.template [5ms]
2025-06-07 13:53:18.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 13:53:18.569 [info] > git status -z -uall [7ms]
2025-06-07 13:53:18.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 13:53:23.582 [info] > git config --get commit.template [4ms]
2025-06-07 13:53:23.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 13:53:23.591 [info] > git status -z -uall [4ms]
2025-06-07 13:53:23.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
