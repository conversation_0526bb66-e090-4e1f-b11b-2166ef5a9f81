2025-06-07 12:51:56.822 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 12:51:56.822 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-07 12:51:56.822 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-07 12:51:56.878 [info] 'AugmentExtension' Retrieving model config
2025-06-07 12:51:57.181 [info] 'AugmentExtension' Retrieved model config
2025-06-07 12:51:57.181 [info] 'AugmentExtension' Returning model config
2025-06-07 12:51:57.220 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-07 12:51:57.220 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 12:51:57.220 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-07 12:51:57.220 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-07 12:51:57.221 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-07 12:51:57.221 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-07 12:51:57.221 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 12:51:57.239 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 12:51:57.239 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-07 12:51:57.240 [info] 'ToolsModel' Loaded saved chat mode: CHAT
2025-06-07 12:51:57.242 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-07 12:51:57.537 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-07 12:51:57.539 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-07 12:51:57.539 [info] 'OpenFileManager' Opened source folder 100
2025-06-07 12:51:57.540 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 12:51:57.548 [info] 'MtimeCache[workspace]' read 1710 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 12:51:57.782 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-07 12:52:02.146 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T125154/exthost1/vscode.json-language-features
2025-06-07 12:52:09.713 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-07 12:52:09.713 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 685
  - files emitted: 2206
  - other paths emitted: 4
  - total paths emitted: 2895
  - timing stats:
    - readDir: 15 ms
    - filter: 88 ms
    - yield: 19 ms
    - total: 129 ms
2025-06-07 12:52:09.713 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1760
  - paths not accessible: 0
  - not plain files: 0
  - large files: 38
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1698
  - mtime cache misses: 62
  - probe batches: 5
  - blob names probed: 1783
  - files read: 511
  - blobs uploaded: 20
  - timing stats:
    - ingestPath: 3 ms
    - probe: 3031 ms
    - stat: 30 ms
    - read: 628 ms
    - upload: 1254 ms
2025-06-07 12:52:09.713 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 3 ms
  - read MtimeCache: 9 ms
  - pre-populate PathMap: 71 ms
  - create PathFilter: 171 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 132 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 11784 ms
  - enable persist: 5 ms
  - total: 12176 ms
2025-06-07 12:52:09.713 [info] 'WorkspaceManager' Workspace startup complete in 12510 ms
2025-06-07 12:52:16.402 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 12:52:28.571 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T125154/exthost1/output_logging_20250607T125155
2025-06-07 12:52:58.772 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T125154/exthost1/vscode.typescript-language-features
2025-06-07 12:53:57.815 [error] 'AugmentExtension' API request 65699b70-4900-4863-9c82-ff6e9db1fb4d to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-07 12:53:58.043 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 12:53:58.392 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 12:54:01.088 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 12:54:02.633 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-07 12:54:02.634 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-07 12:54:02.648 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-07 12:54:02.648 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 12:54:02.648 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 12:54:02.762 [info] 'TaskManager' Setting current root task UUID to 7654dc6b-0544-4805-8c84-dccdb1050436
2025-06-07 12:54:03.158 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 12:54:03.159 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 12:54:18.531 [info] 'StallDetector' Recent work: [{"name":"open-confirmation-modal","durationMs":1829.304978,"timestamp":"2025-06-07T12:54:18.494Z"}]
2025-06-07 12:54:25.593 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 12:54:25.782 [info] 'TaskManager' Setting current root task UUID to 1367e5ab-50f8-466f-a7cf-cbf171c70833
2025-06-07 12:54:25.782 [info] 'TaskManager' Setting current root task UUID to 1367e5ab-50f8-466f-a7cf-cbf171c70833
2025-06-07 12:54:30.370 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-07 12:59:28.711 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 12:59:33.324 [info] 'ViewTool' Tool called with path: client/src/pages/AdminDashboard.tsx and view_range: undefined
2025-06-07 12:59:48.156 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 12:59:52.046 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 12:59:56.528 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 13:00:00.814 [info] 'ViewTool' Tool called with path: client/src/pages/VoiceTherapy.tsx and view_range: undefined
2025-06-07 13:00:05.858 [info] 'ViewTool' Tool called with path: client/src/components/VoiceChat.tsx and view_range: undefined
2025-06-07 13:00:10.414 [info] 'ViewTool' Tool called with path: client/src/components/RealTimeVoiceChat.tsx and view_range: [1,50]
2025-06-07 13:00:28.001 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 13:00:28.522 [info] 'ToolFileUtils' File not found: client/src/lib/debug-config.ts. No similar files found
2025-06-07 13:00:28.523 [error] 'StrReplaceEditorTool' Error in tool call: File not found: client/src/lib/debug-config.ts
2025-06-07 13:00:44.338 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:00:44.470 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/checkpoint-documents/0526bb66-e090-4e1f-b11b-2166ef5a9f81
2025-06-07 13:00:55.346 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:00:55.347 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (24887 bytes)
2025-06-07 13:00:56.162 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-19413615
2025-06-07 13:00:56.978 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:00:56.978 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (28779 bytes)
2025-06-07 13:01:00.355 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:01:19.154 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:01:19.334 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30415 bytes)
2025-06-07 13:01:20.210 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-15bdf676
2025-06-07 13:01:21.024 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:01:21.024 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30588 bytes)
2025-06-07 13:01:24.342 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:01:32.358 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:01:32.358 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30588 bytes)
2025-06-07 13:01:33.934 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:01:33.934 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30642 bytes)
2025-06-07 13:01:37.437 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:01:44.920 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:01:44.920 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30642 bytes)
2025-06-07 13:01:46.433 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:01:46.433 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30823 bytes)
2025-06-07 13:01:49.928 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:01:58.787 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:01:58.787 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (28779 bytes)
2025-06-07 13:02:00.464 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:02:00.464 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (29516 bytes)
2025-06-07 13:02:03.904 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:02:10.248 [info] 'ToolFileUtils' Reading file: client/src/pages/VoiceTherapy.tsx
2025-06-07 13:02:10.249 [info] 'ToolFileUtils' Successfully read file: client/src/pages/VoiceTherapy.tsx (5238 bytes)
2025-06-07 13:02:11.232 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/76606708
2025-06-07 13:02:12.009 [info] 'ToolFileUtils' Reading file: client/src/pages/VoiceTherapy.tsx
2025-06-07 13:02:12.010 [info] 'ToolFileUtils' Successfully read file: client/src/pages/VoiceTherapy.tsx (5254 bytes)
2025-06-07 13:02:15.258 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:02:21.845 [info] 'ToolFileUtils' Reading file: client/src/pages/VoiceTherapy.tsx
2025-06-07 13:02:21.846 [info] 'ToolFileUtils' Successfully read file: client/src/pages/VoiceTherapy.tsx (5254 bytes)
2025-06-07 13:02:23.397 [info] 'ToolFileUtils' Reading file: client/src/pages/VoiceTherapy.tsx
2025-06-07 13:02:23.397 [info] 'ToolFileUtils' Successfully read file: client/src/pages/VoiceTherapy.tsx (5261 bytes)
2025-06-07 13:02:26.851 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:02:47.579 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:02:47.821 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87355 bytes)
2025-06-07 13:02:49.673 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:02:49.673 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87465 bytes)
2025-06-07 13:02:52.830 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:02:59.834 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:02:59.835 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87465 bytes)
2025-06-07 13:03:01.427 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:01.427 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87541 bytes)
2025-06-07 13:03:04.842 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:03:11.688 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:11.689 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87541 bytes)
2025-06-07 13:03:13.289 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:13.290 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87662 bytes)
2025-06-07 13:03:16.696 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:03:22.742 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:22.743 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87662 bytes)
2025-06-07 13:03:24.462 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:24.462 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87704 bytes)
2025-06-07 13:03:27.846 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:03:32.031 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [1800,1900]
2025-06-07 13:03:39.406 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:39.406 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87704 bytes)
2025-06-07 13:03:41.156 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:41.157 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87778 bytes)
2025-06-07 13:03:44.413 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:03:56.454 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:56.454 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (87778 bytes)
2025-06-07 13:03:58.062 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:03:58.062 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (92343 bytes)
2025-06-07 13:04:01.464 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:04:10.742 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:04:10.742 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (92343 bytes)
2025-06-07 13:04:12.350 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:04:12.350 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (92725 bytes)
2025-06-07 13:04:15.751 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:04:38.562 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 13:04:42.829 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [1285,1320]
2025-06-07 13:04:53.976 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:04:53.976 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (92725 bytes)
2025-06-07 13:04:55.571 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:04:55.571 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93129 bytes)
2025-06-07 13:04:58.986 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:05:07.983 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:05:08.152 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (9542 bytes)
2025-06-07 13:05:09.089 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-5bb39c94
2025-06-07 13:05:09.883 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:05:09.884 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (9762 bytes)
2025-06-07 13:05:13.333 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:05:20.326 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:05:20.326 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (9762 bytes)
2025-06-07 13:05:21.906 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:05:21.906 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10426 bytes)
2025-06-07 13:05:25.337 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:05:33.419 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:05:33.419 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10426 bytes)
2025-06-07 13:05:35.026 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:05:35.026 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10724 bytes)
2025-06-07 13:05:38.435 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:05:42.537 [info] 'ViewTool' Tool called with path: client/src/components/ui and view_range: undefined
2025-06-07 13:05:42.591 [info] 'ViewTool' Listing directory: client/src/components/ui (depth: 2, showHidden: false)
2025-06-07 13:05:50.459 [error] 'AugmentExtension' API request 10335c3a-14d8-4ccb-81cc-87de98b6d46d to https://i0.api.augmentcode.com/memorize failed: This operation was aborted
2025-06-07 13:05:50.724 [info] 'OpenFileManager' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:05:50.825 [info] 'OpenFileManager' Operation succeeded after 1 transient failures
2025-06-07 13:07:47.411 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-07 13:08:44.055 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:08:44.314 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93129 bytes)
2025-06-07 13:08:46.316 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:08:46.317 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93386 bytes)
2025-06-07 13:08:49.325 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:09:00.146 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:00.147 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93386 bytes)
2025-06-07 13:09:01.700 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:01.700 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93461 bytes)
2025-06-07 13:09:05.154 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:09:18.237 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:18.237 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93461 bytes)
2025-06-07 13:09:19.821 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:19.821 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93885 bytes)
2025-06-07 13:09:23.245 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:09:29.463 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:29.463 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (93885 bytes)
2025-06-07 13:09:31.126 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:31.126 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94015 bytes)
2025-06-07 13:09:34.553 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:09:37.348 [error] 'AugmentExtension' API request 5709f208-9b90-497b-9e1f-805e9c604ea7 to https://i0.api.augmentcode.com/record-request-events failed: This operation was aborted
2025-06-07 13:09:37.678 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.logToolUseRequestEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:35458)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 13:09:37.678 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:09:37.944 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-07 13:09:38.552 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [1470,1520]
2025-06-07 13:09:53.026 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:53.026 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94015 bytes)
2025-06-07 13:09:54.669 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:09:54.669 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94274 bytes)
2025-06-07 13:09:58.032 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:10:14.253 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:14.253 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94274 bytes)
2025-06-07 13:10:15.950 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:15.950 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94347 bytes)
2025-06-07 13:10:19.267 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:10:28.577 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:28.578 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94347 bytes)
2025-06-07 13:10:30.213 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:30.214 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94368 bytes)
2025-06-07 13:10:33.597 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:10:43.947 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:43.947 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94368 bytes)
2025-06-07 13:10:45.540 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:45.540 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94567 bytes)
2025-06-07 13:10:49.006 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:10:57.193 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:57.194 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94567 bytes)
2025-06-07 13:10:58.861 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:10:58.861 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94599 bytes)
2025-06-07 13:11:02.203 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:11:22.341 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:11:22.341 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94599 bytes)
2025-06-07 13:11:23.974 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:11:23.974 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94679 bytes)
2025-06-07 13:11:27.372 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:11:34.225 [error] 'AugmentExtension' API request d0e9d989-aaec-4bd3-a38a-87a2552a4ace to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-07 13:11:34.518 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:11:34.982 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:11:43.199 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:11:43.199 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94679 bytes)
2025-06-07 13:11:45.056 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:11:45.337 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94864 bytes)
2025-06-07 13:11:48.245 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:12:00.308 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:12:00.308 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94864 bytes)
2025-06-07 13:12:00.689 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 2af4e96b3e0c9e42c664d8418882eebdac755277bc03cc2bb87a82eb25787d51: deleted
2025-06-07 13:12:02.229 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:12:02.230 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94879 bytes)
2025-06-07 13:12:05.315 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:12:12.939 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:12:12.939 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94879 bytes)
2025-06-07 13:12:14.651 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:12:14.652 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94884 bytes)
2025-06-07 13:12:17.289 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-07 13:12:18.056 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:12:31.020 [error] 'AugmentExtension' API request a90e36ea-15de-4f30-a392-12bf67ebe859 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:12:31.210 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:12:31.210 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94884 bytes)
2025-06-07 13:12:31.256 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:12:31.805 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:12:32.899 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 13:12:32.899 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94899 bytes)
2025-06-07 13:12:36.246 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:12:44.099 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:12:44.272 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10724 bytes)
2025-06-07 13:12:49.307 [info] 'ViewTool' Tool called with path: client/src/components/RealTimeVoiceChat.tsx and view_range: [1,10]
2025-06-07 13:12:56.788 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:12:56.789 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10724 bytes)
2025-06-07 13:12:58.716 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:12:58.716 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10775 bytes)
2025-06-07 13:13:01.793 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:13:07.105 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:13:07.105 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10775 bytes)
2025-06-07 13:13:08.748 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:13:08.748 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10799 bytes)
2025-06-07 13:13:12.112 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:13:15.630 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:13:15.630 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10799 bytes)
2025-06-07 13:13:17.090 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceChat.tsx
2025-06-07 13:13:17.090 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceChat.tsx (10823 bytes)
2025-06-07 13:13:20.639 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:13:37.527 [error] 'AugmentExtension' API request 5fef4fc6-570d-4d91-bffa-8b66ceedfa6e to https://i0.api.augmentcode.com/record-request-events failed: This operation was aborted
2025-06-07 13:13:37.710 [error] 'AugmentExtension' API request 962bd4d0-80a9-41ff-853a-d69594a2f007 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:13:37.810 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.logToolUseRequestEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:35458)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 13:13:37.810 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:13:37.957 [info] 'OpenFileManager' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:13:38.071 [info] 'OpenFileManager' Operation succeeded after 1 transient failures
2025-06-07 13:13:38.132 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-07 13:16:47.943 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-07 13:18:28.047 [error] 'AugmentExtension' API request 85572682-fbcd-4acb-8274-60d236b22b01 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:18:28.334 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:18:28.663 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:18:31.266 [info] 'ViewTool' Tool called with path: dist/index.js and view_range: [1,50]
2025-06-07 13:18:37.750 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-06-07 13:19:27.442 [error] 'AugmentExtension' API request d879dfce-13bc-47a2-88bf-56ee1b2f34cc to https://i0.api.augmentcode.com/record-request-events failed: This operation was aborted
2025-06-07 13:19:27.649 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.logToolUseRequestEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:35458)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 13:19:27.649 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:19:27.920 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-07 13:20:01.238 [info] 'ViewTool' Tool called with path: server/db/schema.ts and view_range: undefined
2025-06-07 13:20:06.804 [info] 'ViewTool' Tool called with path: server/db/schema.ts and view_range: undefined
2025-06-07 13:20:11.963 [info] 'ViewTool' Tool called with path: server/db/schema.ts and view_range: undefined
2025-06-07 13:20:37.642 [error] 'AugmentExtension' API request 6f215cfa-b1ef-4ed0-a6c0-9936bb94c23f to https://i0.api.augmentcode.com/record-request-events failed: This operation was aborted
2025-06-07 13:20:37.831 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.logToolUseRequestEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:35458)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 13:20:37.831 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:20:38.218 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-07 13:21:56.742 [info] 'AugmentExtension' Retrieving model config
2025-06-07 13:21:56.972 [info] 'AugmentExtension' Retrieved model config
2025-06-07 13:21:56.972 [info] 'AugmentExtension' Returning model config
2025-06-07 13:22:24.323 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: [1,50]
2025-06-07 13:29:28.124 [error] 'AugmentExtension' API request f54bee39-253a-4255-afc1-6d9d8238ca89 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:29:28.336 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:29:28.742 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:29:51.429 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:30:10.447 [info] 'ToolFileUtils' Reading file: client/src/pages/Settings.tsx
2025-06-07 13:30:10.617 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Settings.tsx (9834 bytes)
2025-06-07 13:30:16.669 [info] 'ViewTool' Tool called with path: client/src/pages/Settings.tsx and view_range: [31,90]
2025-06-07 13:30:30.721 [info] 'ToolFileUtils' Reading file: client/src/pages/Settings.tsx
2025-06-07 13:30:30.721 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Settings.tsx (9834 bytes)
2025-06-07 13:30:31.651 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/16a62954
2025-06-07 13:30:32.462 [info] 'ToolFileUtils' Reading file: client/src/pages/Settings.tsx
2025-06-07 13:30:32.462 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Settings.tsx (7632 bytes)
2025-06-07 13:30:35.727 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:30:47.015 [info] 'ToolFileUtils' Reading file: client/src/pages/Settings.tsx
2025-06-07 13:30:47.015 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Settings.tsx (7632 bytes)
2025-06-07 13:30:54.175 [info] 'ToolFileUtils' Reading file: client/src/pages/Settings.tsx
2025-06-07 13:30:54.175 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Settings.tsx (7632 bytes)
2025-06-07 13:30:55.705 [info] 'ToolFileUtils' Reading file: client/src/pages/Settings.tsx
2025-06-07 13:30:55.705 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Settings.tsx (7762 bytes)
2025-06-07 13:30:59.182 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:31:15.795 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:31:15.973 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (29516 bytes)
2025-06-07 13:31:17.826 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:31:17.826 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (31225 bytes)
2025-06-07 13:31:21.003 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:31:52.345 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:31:52.345 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (31225 bytes)
2025-06-07 13:31:58.088 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [1040,-1]
2025-06-07 13:32:18.573 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:32:18.573 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (31225 bytes)
2025-06-07 13:32:20.115 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:32:20.115 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (35499 bytes)
2025-06-07 13:32:23.590 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:32:46.947 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:32:46.947 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (35499 bytes)
2025-06-07 13:32:48.416 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:32:48.416 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40227 bytes)
2025-06-07 13:32:51.954 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:33:11.744 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:33:11.923 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (30823 bytes)
2025-06-07 13:33:13.629 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:33:13.629 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (31562 bytes)
2025-06-07 13:33:16.928 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:33:24.467 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:33:24.467 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (31562 bytes)
2025-06-07 13:33:25.966 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:33:25.966 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (31718 bytes)
2025-06-07 13:33:29.475 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:33:37.483 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:33:37.483 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (31718 bytes)
2025-06-07 13:33:39.135 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:33:39.135 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32348 bytes)
2025-06-07 13:33:42.505 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:33:57.854 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:33:58.028 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7609 bytes)
2025-06-07 13:33:59.376 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-6b63ff95
2025-06-07 13:34:00.189 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:34:00.189 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7568 bytes)
2025-06-07 13:34:03.041 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:34:11.300 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:34:11.300 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7568 bytes)
2025-06-07 13:34:12.848 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:34:12.848 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7564 bytes)
2025-06-07 13:34:16.348 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:34:23.569 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:34:23.570 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7564 bytes)
2025-06-07 13:34:29.155 [info] 'ViewTool' Tool called with path: client/src/pages/Analytics.tsx and view_range: [140,170]
2025-06-07 13:34:37.142 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:34:37.142 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7564 bytes)
2025-06-07 13:34:38.736 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 13:34:38.736 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7500 bytes)
2025-06-07 13:34:42.147 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:35:50.201 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:35:50.383 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40227 bytes)
2025-06-07 13:35:52.191 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:35:52.191 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40231 bytes)
2025-06-07 13:35:52.291 [error] 'AugmentExtension' API request b292faf0-3406-4dbb-9e0e-47bbcdc6a368 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:35:52.491 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:35:52.823 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:35:55.396 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:36:01.058 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:36:01.058 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40231 bytes)
2025-06-07 13:36:02.571 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:36:02.571 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40235 bytes)
2025-06-07 13:36:06.063 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:36:11.495 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:36:11.496 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40235 bytes)
2025-06-07 13:36:13.047 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:36:13.048 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40239 bytes)
2025-06-07 13:36:16.508 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:46:01.020 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 13:46:07.027 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 13:46:12.164 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [400,430]
2025-06-07 13:46:27.914 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:46:27.914 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (40239 bytes)
2025-06-07 13:46:29.719 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:46:29.719 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (41170 bytes)
2025-06-07 13:46:32.921 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:46:44.822 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:46:44.822 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (41170 bytes)
2025-06-07 13:46:54.880 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [255,295]
2025-06-07 13:47:05.709 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:47:05.709 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (41170 bytes)
2025-06-07 13:47:07.339 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:47:07.339 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (42165 bytes)
2025-06-07 13:47:10.814 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:47:16.208 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 13:47:21.831 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [1000,1030]
2025-06-07 13:47:37.680 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:47:37.681 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (42165 bytes)
2025-06-07 13:47:39.256 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:47:39.257 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (43231 bytes)
2025-06-07 13:47:42.730 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:47:52.466 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:47:52.466 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (43231 bytes)
2025-06-07 13:47:54.432 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:47:54.432 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (44318 bytes)
2025-06-07 13:47:57.476 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:48:09.107 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:48:09.107 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (44318 bytes)
2025-06-07 13:48:11.384 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 13:48:11.384 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (44746 bytes)
2025-06-07 13:48:14.116 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:49:11.968 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-07 13:49:52.681 [error] 'AugmentExtension' API request 0e3ad0f6-5afa-4c51-88d6-1dc7ef0eb012 to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-07 13:49:52.899 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:49:53.288 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:51:15.017 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [70,90]
2025-06-07 13:51:31.438 [error] 'AugmentExtension' API request bdae1568-6fca-4bcb-8a8e-4ccd33d492b0 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:51:31.635 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:51:31.933 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:51:32.299 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:51:32.299 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32348 bytes)
2025-06-07 13:51:34.051 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:51:34.051 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32343 bytes)
2025-06-07 13:51:37.304 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:51:44.980 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:51:44.980 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32343 bytes)
2025-06-07 13:51:50.354 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1,20]
2025-06-07 13:51:56.742 [info] 'AugmentExtension' Retrieving model config
2025-06-07 13:51:57.100 [info] 'AugmentExtension' Retrieved model config
2025-06-07 13:51:57.100 [info] 'AugmentExtension' Returning model config
2025-06-07 13:51:58.903 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:51:58.904 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32343 bytes)
2025-06-07 13:52:00.491 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:00.492 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32357 bytes)
2025-06-07 13:52:03.910 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:52:11.071 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:11.071 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32357 bytes)
2025-06-07 13:52:12.601 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:12.601 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32356 bytes)
2025-06-07 13:52:16.079 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:52:27.041 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:27.041 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32356 bytes)
2025-06-07 13:52:28.536 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:28.536 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32446 bytes)
2025-06-07 13:52:32.046 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:52:41.411 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:41.411 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32446 bytes)
2025-06-07 13:52:42.973 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:52:42.974 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32557 bytes)
2025-06-07 13:52:46.458 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:52:47.855 [error] 'AugmentExtension' API request 5dd32976-fb5f-4083-9b35-f3fdc3199e04 to https://i0.api.augmentcode.com/record-request-events failed: This operation was aborted
2025-06-07 13:52:48.062 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at processTicksAndRejections (node:internal/process/task_queues:95:5)
	at runNextTicks (node:internal/process/task_queues:64:3)
	at listOnTimeout (node:internal/timers:545:9)
	at process.processTimers (node:internal/timers:519:7)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.logToolUseRequestEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:35458)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 13:52:48.063 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:52:48.318 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-07 13:53:44.000 [info] 'ViewTool' Tool called with path: shared/schema.ts and view_range: undefined
2025-06-07 13:53:51.900 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 13:53:51.900 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9205 bytes)
2025-06-07 13:53:52.844 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/2ddf2d6
2025-06-07 13:53:53.636 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 13:53:53.637 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9230 bytes)
2025-06-07 13:53:56.947 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:54:04.023 [info] 'ViewTool' Tool called with path: shared/schema.ts and view_range: undefined
2025-06-07 13:54:16.558 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 13:54:16.558 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9230 bytes)
2025-06-07 13:54:18.203 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 13:54:18.203 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9563 bytes)
2025-06-07 13:54:21.567 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:54:29.639 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 13:54:29.639 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9563 bytes)
2025-06-07 13:54:31.140 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 13:54:31.140 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9584 bytes)
2025-06-07 13:54:34.657 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:54:46.698 [error] 'AugmentExtension' API request 84bf5586-9d13-44d7-b79f-ca7368dc8f33 to https://i0.api.augmentcode.com/memorize failed: This operation was aborted
2025-06-07 13:54:47.266 [info] 'OpenFileManager' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:54:47.366 [info] 'OpenFileManager' Operation succeeded after 1 transient failures
2025-06-07 13:55:14.700 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:55:14.881 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32557 bytes)
2025-06-07 13:55:16.577 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:55:16.578 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32683 bytes)
2025-06-07 13:55:19.886 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:55:26.455 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T125154/exthost1/vscode.markdown-language-features
2025-06-07 13:55:35.547 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:55:35.547 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32683 bytes)
2025-06-07 13:55:37.102 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 13:55:37.103 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32710 bytes)
2025-06-07 13:55:40.689 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:55:43.653 [error] 'AugmentExtension' API request 93c8db1b-d919-4c56-acdd-7335213bfa5e to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-07 13:55:43.903 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:55:44.287 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 13:55:48.951 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:55:49.130 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6383 bytes)
2025-06-07 13:55:55.855 [info] 'ViewTool' Tool called with path: client/src/components/NewNoteForm.tsx and view_range: [20,50]
2025-06-07 13:56:07.713 [error] 'AugmentExtension' API request 4ecd1f22-d9e6-4c43-bf8f-f329b81da18a to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 13:56:08.026 [info] 'FileUploader#BlobStatusExecutor' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 13:56:08.327 [info] 'FileUploader#BlobStatusExecutor' Operation succeeded after 1 transient failures
2025-06-07 13:56:09.410 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:09.416 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6383 bytes)
2025-06-07 13:56:10.378 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-2447e35d
2025-06-07 13:56:11.186 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:11.187 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6382 bytes)
2025-06-07 13:56:14.439 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:56:27.285 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:27.286 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6382 bytes)
2025-06-07 13:56:28.844 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:28.844 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6381 bytes)
2025-06-07 13:56:32.352 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:56:39.083 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:39.083 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6381 bytes)
2025-06-07 13:56:40.624 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:40.624 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6379 bytes)
2025-06-07 13:56:44.089 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:56:54.166 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:54.167 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6379 bytes)
2025-06-07 13:56:55.746 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:56:55.746 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6377 bytes)
2025-06-07 13:56:59.179 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:57:15.736 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:57:15.737 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6377 bytes)
2025-06-07 13:57:17.285 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 13:57:17.285 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6375 bytes)
2025-06-07 13:57:20.742 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:57:29.215 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:57:29.392 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6952 bytes)
2025-06-07 13:57:30.380 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/3b719d92
2025-06-07 13:57:31.164 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:57:31.164 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6970 bytes)
2025-06-07 13:57:34.409 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:57:42.927 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:57:42.928 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6970 bytes)
2025-06-07 13:57:44.560 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:57:44.561 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6987 bytes)
2025-06-07 13:57:48.052 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:57:54.871 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:57:54.872 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6987 bytes)
2025-06-07 13:57:56.512 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:57:56.513 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6985 bytes)
2025-06-07 13:57:59.941 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:58:10.477 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:10.478 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6985 bytes)
2025-06-07 13:58:12.054 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:12.055 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (7000 bytes)
2025-06-07 13:58:15.511 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:58:26.812 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:26.812 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (7000 bytes)
2025-06-07 13:58:35.941 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:35.942 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (7000 bytes)
2025-06-07 13:58:37.545 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:37.545 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6019 bytes)
2025-06-07 13:58:40.949 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:58:48.312 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:48.312 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6019 bytes)
2025-06-07 13:58:49.910 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:58:49.910 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6015 bytes)
2025-06-07 13:58:53.369 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:59:00.546 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:59:00.546 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6015 bytes)
2025-06-07 13:59:02.074 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:59:02.074 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6013 bytes)
2025-06-07 13:59:05.551 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:59:11.983 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:59:11.983 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6013 bytes)
2025-06-07 13:59:13.552 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 13:59:13.553 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6012 bytes)
2025-06-07 13:59:17.001 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:59:24.500 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 13:59:24.673 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7699 bytes)
2025-06-07 13:59:25.551 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-2d64c653
2025-06-07 13:59:26.366 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 13:59:26.366 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7717 bytes)
2025-06-07 13:59:29.678 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 13:59:36.716 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 13:59:36.717 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7717 bytes)
2025-06-07 13:59:38.248 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 13:59:38.248 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7727 bytes)
2025-06-07 13:59:41.723 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:00:10.717 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 14:00:10.896 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9584 bytes)
2025-06-07 14:00:12.773 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 14:00:12.773 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9643 bytes)
2025-06-07 14:00:15.903 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:00:23.173 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:00:23.350 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6375 bytes)
2025-06-07 14:00:25.070 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:00:25.070 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6388 bytes)
2025-06-07 14:00:28.358 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:00:38.180 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:00:38.180 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6388 bytes)
2025-06-07 14:00:39.749 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:00:39.750 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6386 bytes)
2025-06-07 14:00:41.324 [error] 'AugmentExtension' API request 5977f83d-d440-435c-8c1e-85befbe2bf2c to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 14:00:41.468 [info] 'FileUploader#BlobStatusExecutor' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 14:00:41.814 [info] 'FileUploader#BlobStatusExecutor' Operation succeeded after 1 transient failures
2025-06-07 14:00:43.193 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:00:50.444 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:00:50.445 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6386 bytes)
2025-06-07 14:01:01.570 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:01:01.571 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6386 bytes)
2025-06-07 14:01:08.170 [info] 'ViewTool' Tool called with path: client/src/components/NewNoteForm.tsx and view_range: [135,145]
2025-06-07 14:01:15.799 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:01:15.800 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6386 bytes)
2025-06-07 14:01:17.578 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 14:01:17.579 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6410 bytes)
2025-06-07 14:01:20.825 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:01:30.370 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:01:30.548 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32710 bytes)
2025-06-07 14:01:47.971 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:01:47.971 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32710 bytes)
2025-06-07 14:01:49.826 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:01:49.826 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32872 bytes)
2025-06-07 14:01:52.978 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:02:00.257 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:02:00.257 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32872 bytes)
2025-06-07 14:02:01.800 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:02:01.800 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32927 bytes)
2025-06-07 14:02:05.262 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:02:11.434 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:02:11.434 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32927 bytes)
2025-06-07 14:02:12.899 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 14:02:12.899 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (32968 bytes)
2025-06-07 14:02:16.440 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:02:49.464 [info] 'ToolFileUtils' Reading file: client/src/components/ConfigManagement.tsx
2025-06-07 14:02:49.698 [info] 'ToolFileUtils' Successfully read file: client/src/components/ConfigManagement.tsx (8092 bytes)
2025-06-07 14:02:50.843 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-1e26f452
2025-06-07 14:02:51.609 [info] 'ToolFileUtils' Reading file: client/src/components/ConfigManagement.tsx
2025-06-07 14:02:51.609 [info] 'ToolFileUtils' Successfully read file: client/src/components/ConfigManagement.tsx (8188 bytes)
2025-06-07 14:02:54.781 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:03:02.664 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceTest.tsx
2025-06-07 14:03:02.935 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceTest.tsx (10270 bytes)
2025-06-07 14:03:03.948 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/a44e7a6
2025-06-07 14:03:04.889 [info] 'ToolFileUtils' Reading file: client/src/components/RealTimeVoiceTest.tsx
2025-06-07 14:03:04.889 [info] 'ToolFileUtils' Successfully read file: client/src/components/RealTimeVoiceTest.tsx (10366 bytes)
2025-06-07 14:03:08.083 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:03:15.768 [info] 'ToolFileUtils' Reading file: server/real-time-audio.ts
2025-06-07 14:03:15.939 [info] 'ToolFileUtils' Successfully read file: server/real-time-audio.ts (8634 bytes)
2025-06-07 14:03:26.357 [info] 'ToolFileUtils' Reading file: server/real-time-audio.ts
2025-06-07 14:03:26.358 [info] 'ToolFileUtils' Successfully read file: server/real-time-audio.ts (8634 bytes)
2025-06-07 14:03:27.323 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/4b37db60
2025-06-07 14:03:28.105 [info] 'ToolFileUtils' Reading file: server/real-time-audio.ts
2025-06-07 14:03:28.106 [info] 'ToolFileUtils' Successfully read file: server/real-time-audio.ts (8741 bytes)
2025-06-07 14:03:31.365 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:09:14.078 [info] 'ViewTool' Tool called with path: server/auth.ts and view_range: undefined
2025-06-07 14:09:35.646 [info] 'ViewTool' Tool called with path: server/temp-login-fix.ts and view_range: [30,50]
2025-06-07 14:09:45.317 [info] 'ToolFileUtils' Reading file: server/temp-login-fix.ts
2025-06-07 14:09:45.319 [info] 'ToolFileUtils' Successfully read file: server/temp-login-fix.ts (2564 bytes)
2025-06-07 14:09:47.072 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/7672917d
2025-06-07 14:09:47.904 [info] 'ToolFileUtils' Reading file: server/temp-login-fix.ts
2025-06-07 14:09:47.905 [info] 'ToolFileUtils' Successfully read file: server/temp-login-fix.ts (2649 bytes)
2025-06-07 14:09:50.334 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:10:58.948 [info] 'ViewTool' Tool called with path: server/auth.ts and view_range: [32,45]
2025-06-07 14:11:07.879 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 14:11:07.879 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6352 bytes)
2025-06-07 14:11:08.774 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/26ad70cd
2025-06-07 14:11:09.665 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 14:11:09.665 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6360 bytes)
2025-06-07 14:11:12.888 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-__NEW_AGENT__.json'
2025-06-07 14:15:55.658 [error] 'AugmentExtension' API request b96357d7-79a4-4030-a537-31c3f24f6f5e to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 14:15:55.885 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 14:15:56.170 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 14:16:39.462 [error] 'AugmentExtensionSidecar' API request da048c31-81e4-414f-88d4-f687b17744c3 to https://i0.api.augmentcode.com/chat-stream failed: The operation was aborted due to timeout
2025-06-07 14:16:39.462 [error] 'AugmentExtensionSidecar' TimeoutError: The operation was aborted due to timeout
	at node:internal/deps/undici/undici:13502:13
	at processTicksAndRejections (node:internal/process/task_queues:95:5)
	at runNextTicks (node:internal/process/task_queues:64:3)
	at listOnTimeout (node:internal/timers:545:9)
	at process.processTimers (node:internal/timers:519:7)
	at async VG.globalThis.fetch (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:172:20755)
	at async Ky (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12322)
	at async q2.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1192:3996)
	at async q2.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:56319)
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async q2.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1192:3149)
	at async e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1415:35435)
	at async e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1415:33816)
	at async LR.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1770:3124)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1211:5047
2025-06-07 14:16:39.684 [info] 'AugmentExtensionSidecar' Operation failed with error Error: The operation was aborted due to timeout, retrying in 250 ms; retries = 0
2025-06-07 14:16:39.685 [error] 'ChatApp' Chat stream failed: Error: The operation was aborted due to timeout
Error: The operation was aborted due to timeout
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1192:4229)
	at processTicksAndRejections (node:internal/process/task_queues:95:5)
	at runNextTicks (node:internal/process/task_queues:64:3)
	at listOnTimeout (node:internal/timers:545:9)
	at process.processTimers (node:internal/timers:519:7)
	at async q2.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:56319)
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async q2.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1192:3149)
	at async e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1415:35435)
	at async e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1415:33816)
	at async LR.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1770:3124)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1211:5047
2025-06-07 14:20:01.730 [error] 'AugmentExtension' API request 8bb6b8dc-7b9f-4bc1-962d-18a83d0b0d13 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 14:20:01.957 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 14:20:02.375 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 14:21:56.742 [info] 'AugmentExtension' Retrieving model config
2025-06-07 14:21:56.899 [info] 'AugmentExtension' Retrieved model config
2025-06-07 14:21:56.899 [info] 'AugmentExtension' Returning model config
2025-06-07 14:24:43.785 [error] 'AugmentExtension' API request 7df5df1d-7b28-4b36-a8a0-94b693e06084 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 14:24:44.010 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 14:24:44.324 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 14:26:28.137 [error] 'AugmentExtension' API request 73cea56e-8aa0-44a3-8def-40cbad31934e to https://i0.api.augmentcode.com/client-metrics failed: This operation was aborted
2025-06-07 14:26:28.365 [error] 'ClientMetricsReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at processTicksAndRejections (node:internal/process/task_queues:95:5)
	at runNextTicks (node:internal/process/task_queues:64:3)
	at listOnTimeout (node:internal/timers:545:9)
	at process.processTimers (node:internal/timers:519:7)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.clientMetrics (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:50199)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 14:26:28.365 [info] 'ClientMetricsReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 14:26:28.613 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-06-07 14:26:43.725 [error] 'AugmentExtension' API request 6e374c4a-4d28-427a-bb8f-37c32084ba43 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 14:26:43.941 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 14:26:44.190 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
