2025-06-07 12:28:43.628 [info] [main] Log level: Info
2025-06-07 12:28:43.628 [info] [main] Validating found git in: "git"
2025-06-07 12:28:43.628 [info] [main] Using git "2.47.2" from "git"
2025-06-07 12:28:43.628 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 12:28:43.628 [info] > git rev-parse --show-toplevel [27ms]
2025-06-07 12:28:43.628 [info] > git rev-parse --git-dir --git-common-dir [5ms]
2025-06-07 12:28:43.628 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 12:28:43.628 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 12:28:43.628 [info] > git config --get commit.template [2ms]
2025-06-07 12:28:43.628 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 12:28:43.628 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 12:28:43.629 [info] > git status -z -uall [5ms]
2025-06-07 12:28:43.630 [info] > git rev-parse --show-toplevel [9ms]
2025-06-07 12:28:43.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:28:43.662 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 12:28:43.668 [info] > git config --get commit.template [8ms]
2025-06-07 12:28:43.668 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 12:28:43.671 [info] > git config --get --local branch.main.vscode-merge-base [6ms]
2025-06-07 12:28:43.678 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 12:28:43.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 12:28:43.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [7ms]
2025-06-07 12:28:43.686 [info] > git rev-parse --show-toplevel [5ms]
2025-06-07 12:28:43.686 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-07 12:28:44.089 [info] > git rev-parse --show-toplevel [397ms]
2025-06-07 12:28:44.089 [info] > git diff --name-status -z --diff-filter=ADMR 3d8b3a5165d260649a750d23d7e5b1cc85844257...refs/remotes/origin/main [400ms]
2025-06-07 12:28:44.136 [info] > git status -z -uall [43ms]
2025-06-07 12:28:44.137 [info] > git rev-parse --show-toplevel [28ms]
2025-06-07 12:28:44.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [42ms]
2025-06-07 12:28:44.217 [info] > git rev-parse --show-toplevel [60ms]
2025-06-07 12:28:44.226 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 12:28:44.232 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 12:28:44.243 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 12:28:44.250 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:28:44.258 [info] > git rev-parse --show-toplevel [0ms]
2025-06-07 12:28:44.262 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 12:28:44.644 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-07 12:29:09.458 [info] > git config --get commit.template [4ms]
2025-06-07 12:29:09.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:29:09.468 [info] > git status -z -uall [5ms]
2025-06-07 12:29:09.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
