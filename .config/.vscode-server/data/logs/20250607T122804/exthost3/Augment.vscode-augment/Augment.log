2025-06-07 12:29:24.480 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 12:29:24.480 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-07 12:29:24.480 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-07 12:29:24.555 [info] 'AugmentExtension' Retrieving model config
2025-06-07 12:29:24.808 [info] 'AugmentExtension' Retrieved model config
2025-06-07 12:29:24.808 [info] 'AugmentExtension' Returning model config
2025-06-07 12:29:24.844 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-07 12:29:24.844 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 12:29:24.844 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-07 12:29:24.845 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-07 12:29:24.845 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-07 12:29:24.845 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-07 12:29:24.845 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 12:29:24.861 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 12:29:24.862 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-07 12:29:24.863 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-07 12:29:24.880 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 12:29:24.881 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 12:29:25.247 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-07 12:29:25.255 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-07 12:29:25.255 [info] 'OpenFileManager' Opened source folder 100
2025-06-07 12:29:25.256 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 12:29:25.257 [info] 'MtimeCache[workspace]' no blob name cache found at /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json'
2025-06-07 12:29:25.631 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 12:29:25.631 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 12:29:25.631 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 12:29:25.632 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 12:29:25.652 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 12:29:25.652 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 12:29:26.032 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 12:29:27.616 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-07 12:29:27.616 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-07 12:29:27.688 [info] 'TaskManager' Setting current root task UUID to 7654dc6b-0544-4805-8c84-dccdb1050436
2025-06-07 12:29:27.884 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets
2025-06-07 12:29:27.885 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets/agent-edits
2025-06-07 12:29:27.886 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets/agent-edits/manifest
2025-06-07 12:29:27.886 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets/agent-edits/shards
2025-06-07 12:29:27.887 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets/task-storage
2025-06-07 12:29:27.887 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets/task-storage/manifest
2025-06-07 12:29:27.887 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/augment-user-assets/task-storage/tasks
2025-06-07 12:29:28.479 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/14d6801d
2025-06-07 12:29:30.075 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T122804/exthost3/vscode.json-language-features
2025-06-07 12:34:34.472 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-07 12:34:34.472 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 670
  - files emitted: 2193
  - other paths emitted: 4
  - total paths emitted: 2867
  - timing stats:
    - readDir: 21 ms
    - filter: 132 ms
    - yield: 41 ms
    - total: 250 ms
2025-06-07 12:34:34.472 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2631
  - paths not accessible: 0
  - not plain files: 0
  - large files: 36
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 2631
  - probe batches: 104
  - blob names probed: 2664
  - files read: 3092
  - blobs uploaded: 32
  - timing stats:
    - ingestPath: 59 ms
    - probe: 22370 ms
    - stat: 43 ms
    - read: 1595 ms
    - upload: 2611 ms
2025-06-07 12:34:34.472 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 10 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 107 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 254 ms
  - purge stale PathMap entries: 3 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 308845 ms
  - enable persist: 5 ms
  - total: 309225 ms
2025-06-07 12:34:34.472 [info] 'WorkspaceManager' Workspace startup complete in 309642 ms
2025-06-07 12:34:34.595 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1
2025-06-07 12:35:14.776 [info] 'PathMap' Closed source folder /home/<USER>/workspace with id 100
2025-06-07 12:35:14.776 [info] 'OpenFileManager' Closed source folder 100
