2025-06-07 12:29:23.973 [info] [main] Log level: Info
2025-06-07 12:29:23.973 [info] [main] Validating found git in: "git"
2025-06-07 12:29:23.973 [info] [main] Using git "2.47.2" from "git"
2025-06-07 12:29:23.973 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 12:29:23.973 [info] > git rev-parse --show-toplevel [57ms]
2025-06-07 12:29:23.973 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-07 12:29:23.973 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 12:29:23.973 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 12:29:23.973 [info] > git config --get commit.template [1ms]
2025-06-07 12:29:23.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:29:23.984 [info] > git status -z -uall [5ms]
2025-06-07 12:29:23.985 [info] > git rev-parse --show-toplevel [9ms]
2025-06-07 12:29:23.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:29:24.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 12:29:24.013 [info] > git config --get commit.template [4ms]
2025-06-07 12:29:24.014 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 12:29:24.014 [info] > git config --get --local branch.main.vscode-merge-base [2ms]
2025-06-07 12:29:24.022 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 12:29:24.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-07 12:29:24.028 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 12:29:24.030 [info] > git merge-base refs/heads/main refs/remotes/origin/main [5ms]
2025-06-07 12:29:24.034 [info] > git rev-parse --show-toplevel [7ms]
2025-06-07 12:29:24.034 [info] > git diff --name-status -z --diff-filter=ADMR 3d8b3a5165d260649a750d23d7e5b1cc85844257...refs/remotes/origin/main [1ms]
2025-06-07 12:29:24.042 [info] > git status -z -uall [2ms]
2025-06-07 12:29:24.042 [info] > git rev-parse --show-toplevel [5ms]
2025-06-07 12:29:24.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [303ms]
2025-06-07 12:29:24.355 [info] > git rev-parse --show-toplevel [309ms]
2025-06-07 12:29:24.367 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:29:24.372 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 12:29:24.376 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 12:29:24.382 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:29:24.393 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 12:29:24.401 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 12:29:24.405 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 12:29:24.408 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 12:29:24.508 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-07 12:34:25.601 [info] > git config --get commit.template [4ms]
2025-06-07 12:34:25.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:34:25.608 [info] > git status -z -uall [3ms]
2025-06-07 12:34:25.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:34:30.620 [info] > git config --get commit.template [1ms]
2025-06-07 12:34:30.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:34:30.635 [info] > git status -z -uall [4ms]
2025-06-07 12:34:30.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:34:35.649 [info] > git config --get commit.template [5ms]
2025-06-07 12:34:35.651 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:34:35.659 [info] > git status -z -uall [5ms]
2025-06-07 12:34:35.661 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:34:40.671 [info] > git config --get commit.template [4ms]
2025-06-07 12:34:40.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:34:40.680 [info] > git status -z -uall [3ms]
2025-06-07 12:34:40.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:34:59.910 [info] > git config --get commit.template [4ms]
2025-06-07 12:34:59.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:34:59.918 [info] > git status -z -uall [4ms]
2025-06-07 12:34:59.919 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:35:04.925 [info] > git config --get commit.template [1ms]
2025-06-07 12:35:04.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 12:35:04.939 [info] > git status -z -uall [4ms]
2025-06-07 12:35:04.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:35:09.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 12:35:09.953 [info] > git config --get commit.template [7ms]
2025-06-07 12:35:09.962 [info] > git status -z -uall [4ms]
2025-06-07 12:35:09.964 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
