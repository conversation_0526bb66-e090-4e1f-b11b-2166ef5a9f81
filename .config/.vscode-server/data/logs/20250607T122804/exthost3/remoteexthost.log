2025-06-07 12:29:23.031 [info] Extension host with pid 1613 started
2025-06-07 12:29:23.031 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock'
2025-06-07 12:29:23.031 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-07 12:29:23.034 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': The lock does not look stale, elapsed: 766 ms, giving up.
2025-06-07 12:29:23.037 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-1/vscode.lock'
2025-06-07 12:29:23.043 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-1/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-07 12:29:23.044 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-1/vscode.lock': The lock does not look stale, elapsed: 863 ms, giving up.
2025-06-07 12:29:23.048 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/vscode.lock': Lock acquired.
2025-06-07 12:29:23.436 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-07 12:29:23.437 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-07 12:29:23.437 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-07 12:29:23.553 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-07 12:29:23.553 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-07 12:29:23.894 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-07 12:29:24.047 [info] Eager extensions activated
2025-06-07 12:29:24.047 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 12:29:24.048 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 12:29:24.048 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 12:29:29.424 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 12:29:29.424 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 12:29:29.424 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 12:29:31.076 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at n_e.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
2025-06-07 12:35:14.735 [info] Extension host terminating: received terminate message from renderer
2025-06-07 12:35:14.736 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d-2/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-07 12:35:14.779 [info] Extension host with pid 1613 exiting with code 0
