2025-06-07 12:28:07.003 [info] [main] Log level: Info
2025-06-07 12:28:07.003 [info] [main] Validating found git in: "git"
2025-06-07 12:28:07.003 [info] [main] Using git "2.47.2" from "git"
2025-06-07 12:28:07.003 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 12:28:07.003 [info] > git rev-parse --show-toplevel [54ms]
2025-06-07 12:28:07.003 [info] > git rev-parse --git-dir --git-common-dir [4ms]
2025-06-07 12:28:07.003 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 12:28:07.003 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 12:28:07.003 [info] > git config --get commit.template [5ms]
2025-06-07 12:28:07.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 12:28:07.010 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 12:28:07.029 [info] > git status -z -uall [7ms]
2025-06-07 12:28:07.030 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 12:28:07.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-07 12:28:07.090 [info] > git rev-parse --show-toplevel [57ms]
2025-06-07 12:28:07.111 [info] > git config --get commit.template [26ms]
2025-06-07 12:28:07.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-06-07 12:28:07.121 [info] > git config --get --local branch.main.vscode-merge-base [2ms]
2025-06-07 12:28:07.125 [info] > git rev-parse --show-toplevel [16ms]
2025-06-07 12:28:07.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-06-07 12:28:07.156 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [31ms]
2025-06-07 12:28:07.695 [info] > git rev-parse --show-toplevel [555ms]
2025-06-07 12:28:07.709 [info] > git check-ignore -v -z --stdin [8ms]
2025-06-07 12:28:07.710 [info] > git merge-base refs/heads/main refs/remotes/origin/main [540ms]
2025-06-07 12:28:07.716 [info] > git rev-parse --show-toplevel [9ms]
2025-06-07 12:28:07.736 [info] > git status -z -uall [13ms]
2025-06-07 12:28:07.736 [info] > git diff --name-status -z --diff-filter=ADMR 3d8b3a5165d260649a750d23d7e5b1cc85844257...refs/remotes/origin/main [21ms]
2025-06-07 12:28:07.737 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:28:07.765 [info] > git rev-parse --show-toplevel [16ms]
2025-06-07 12:28:07.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [37ms]
2025-06-07 12:28:07.861 [info] > git rev-parse --show-toplevel [77ms]
2025-06-07 12:28:07.871 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 12:28:07.879 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:28:07.894 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 12:28:07.901 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 12:28:07.904 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 12:28:14.386 [info] > git config --get commit.template [4ms]
2025-06-07 12:28:14.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:28:14.411 [info] > git status -z -uall [22ms]
2025-06-07 12:28:14.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-06-07 12:28:21.389 [info] > git config --get commit.template [5ms]
2025-06-07 12:28:21.390 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:28:21.398 [info] > git status -z -uall [5ms]
2025-06-07 12:28:21.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:28:28.524 [info] > git config --get commit.template [7ms]
2025-06-07 12:28:28.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 12:28:28.549 [info] > git status -z -uall [8ms]
2025-06-07 12:28:28.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 12:28:33.562 [info] > git config --get commit.template [4ms]
2025-06-07 12:28:33.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:28:33.571 [info] > git status -z -uall [4ms]
2025-06-07 12:28:33.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:28:38.583 [info] > git config --get commit.template [4ms]
2025-06-07 12:28:38.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 12:28:38.591 [info] > git status -z -uall [4ms]
2025-06-07 12:28:38.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 12:28:43.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 12:28:43.608 [info] > git config --get commit.template [10ms]
2025-06-07 12:28:43.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 12:28:43.626 [info] > git status -z -uall [6ms]
