2025-06-07 16:02:48.051 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 16:02:48.051 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-07 16:02:48.051 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-07 16:02:48.484 [info] 'AugmentExtension' Retrieving model config
2025-06-07 16:02:48.496 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 16:02:48.828 [info] 'AugmentExtension' Retrieved model config
2025-06-07 16:02:48.828 [info] 'AugmentExtension' Returning model config
2025-06-07 16:02:48.893 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-07 16:02:48.894 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 16:02:48.894 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-07 16:02:48.894 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-07 16:02:48.894 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-07 16:02:48.894 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-07 16:02:48.894 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 16:02:48.910 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 16:02:48.910 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 16:02:48.910 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-07 16:02:48.911 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-07 16:02:48.947 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 16:02:48.948 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 16:02:49.269 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-07 16:02:49.279 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-07 16:02:49.279 [info] 'OpenFileManager' Opened source folder 100
2025-06-07 16:02:49.282 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 16:02:49.301 [info] 'MtimeCache[workspace]' read 2110 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 16:02:49.614 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 16:02:49.614 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 16:02:49.616 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 16:02:49.616 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 16:02:49.616 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 16:02:49.616 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 16:02:50.175 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-07 16:02:50.175 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-07 16:02:50.175 [info] 'TaskManager' Setting current root task UUID to 1367e5ab-50f8-466f-a7cf-cbf171c70833
2025-06-07 16:02:51.068 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/0526bb66-e090-4e1f-b11b-2166ef5a9f81/document--1749309404892-53c8c554-da68-46af-9167-b812b9537ea8.json
2025-06-07 16:02:51.068 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/0526bb66-e090-4e1f-b11b-2166ef5a9f81/document--1749309404892-53c8c554-da68-46af-9167-b812b9537ea8.json
2025-06-07 16:02:54.057 [info] 'WorkspaceManager[workspace]' Directory removed: .config/.vscode-server/data/logs/20250605T012639
2025-06-07 16:02:58.491 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-07 16:02:58.491 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 730
  - files emitted: 2588
  - other paths emitted: 4
  - total paths emitted: 3322
  - timing stats:
    - readDir: 18 ms
    - filter: 99 ms
    - yield: 29 ms
    - total: 158 ms
2025-06-07 16:02:58.491 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2131
  - paths not accessible: 0
  - not plain files: 0
  - large files: 40
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 2101
  - mtime cache misses: 30
  - probe batches: 4
  - blob names probed: 2150
  - files read: 478
  - blobs uploaded: 19
  - timing stats:
    - ingestPath: 8 ms
    - probe: 1396 ms
    - stat: 19 ms
    - read: 3393 ms
    - upload: 750 ms
2025-06-07 16:02:58.492 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 13 ms
  - read MtimeCache: 19 ms
  - pre-populate PathMap: 112 ms
  - create PathFilter: 231 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 162 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 8680 ms
  - enable persist: 3 ms
  - total: 9221 ms
2025-06-07 16:02:58.492 [info] 'WorkspaceManager' Workspace startup complete in 9618 ms
2025-06-07 16:05:50.194 [error] 'AugmentExtension' API request a5abba53-2f55-4406-a65c-af53708720dd to https://i0.api.augmentcode.com/subscription-info failed: This operation was aborted
2025-06-07 16:05:50.526 [error] 'ChatApp' Failed to get subscription info: Error: This operation was aborted
2025-06-07 16:05:50.607 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":30394.053896,"timestamp":"2025-06-07T16:05:50.526Z"}]
2025-06-07 16:06:40.656 [info] 'ViewTool' Tool called with path: client/src/components/InsightsSidebar.tsx and view_range: [1,50]
2025-06-07 16:06:49.889 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:06:49.890 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4638 bytes)
2025-06-07 16:06:51.637 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:06:51.637 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4634 bytes)
2025-06-07 16:07:03.990 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:07:03.991 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4634 bytes)
2025-06-07 16:07:05.571 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:07:05.571 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4673 bytes)
2025-06-07 16:07:20.875 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:07:20.875 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4673 bytes)
2025-06-07 16:07:22.451 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:07:22.451 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4960 bytes)
2025-06-07 16:07:40.395 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:07:40.396 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4960 bytes)
2025-06-07 16:07:42.051 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:07:42.051 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4935 bytes)
2025-06-07 16:08:09.847 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 16:08:10.030 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6012 bytes)
2025-06-07 16:08:11.805 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 16:08:11.806 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6665 bytes)
2025-06-07 16:08:27.741 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 16:08:27.742 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (6665 bytes)
2025-06-07 16:08:29.279 [info] 'ToolFileUtils' Reading file: client/src/components/SessionNotesList.tsx
2025-06-07 16:08:29.279 [info] 'ToolFileUtils' Successfully read file: client/src/components/SessionNotesList.tsx (7474 bytes)
2025-06-07 16:08:34.609 [error] 'AugmentExtension' API request f98801b0-5cc4-4ee8-a22e-67e3a22f14a3 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 16:08:34.863 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 16:08:35.145 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 16:08:39.845 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 16:08:46.464 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 16:08:57.419 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:08:57.420 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94977 bytes)
2025-06-07 16:08:59.316 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:08:59.316 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (96085 bytes)
2025-06-07 16:09:30.962 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:09:30.962 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (96085 bytes)
2025-06-07 16:09:32.489 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:09:32.490 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (98015 bytes)
2025-06-07 16:09:46.264 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:09:46.265 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (98015 bytes)
2025-06-07 16:09:49.824 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:09:49.824 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (98648 bytes)
2025-06-07 16:10:00.082 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 16:10:08.418 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 16:10:15.228 [info] 'ViewTool' Tool called with path: client/src/lib/debug-config.ts and view_range: undefined
2025-06-07 16:10:26.217 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 16:10:26.217 [info] 'ToolFileUtils' Successfully read file: client/src/lib/debug-config.ts (2609 bytes)
2025-06-07 16:10:27.155 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/5743aa4c
2025-06-07 16:10:27.963 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 16:10:27.963 [info] 'ToolFileUtils' Successfully read file: client/src/lib/debug-config.ts (2886 bytes)
2025-06-07 16:10:40.265 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 16:10:40.265 [info] 'ToolFileUtils' Successfully read file: client/src/lib/debug-config.ts (2886 bytes)
2025-06-07 16:10:41.835 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 16:10:41.835 [info] 'ToolFileUtils' Successfully read file: client/src/lib/debug-config.ts (3222 bytes)
2025-06-07 16:10:55.218 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 16:10:55.219 [info] 'ToolFileUtils' Successfully read file: client/src/lib/debug-config.ts (3222 bytes)
2025-06-07 16:10:56.744 [info] 'ToolFileUtils' Reading file: client/src/lib/debug-config.ts
2025-06-07 16:10:56.745 [info] 'ToolFileUtils' Successfully read file: client/src/lib/debug-config.ts (3898 bytes)
2025-06-07 16:11:10.323 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:11:10.323 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (98648 bytes)
2025-06-07 16:11:19.011 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-07 16:11:20.196 [error] 'AugmentExtension' API request 6f935ad9-3d62-4f46-a805-1d2217bfdc07 to https://i0.api.augmentcode.com/subscription-info failed: This operation was aborted
2025-06-07 16:11:20.546 [error] 'ChatApp' Failed to get subscription info: Error: This operation was aborted
2025-06-07 16:11:20.613 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":30407.208303,"timestamp":"2025-06-07T16:11:20.546Z"}]
2025-06-07 16:11:30.554 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:11:30.554 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (98648 bytes)
2025-06-07 16:11:32.123 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 16:11:32.123 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (99057 bytes)
2025-06-07 16:16:40.714 [error] 'AugmentExtension' API request c180efcb-11b5-4ad0-bb94-9efb78716516 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 16:16:40.958 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 16:16:41.271 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 16:19:28.432 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-06-07 16:28:28.339 [info] 'ViewTool' Tool called with path: client/src/pages/Dashboard.tsx and view_range: undefined
2025-06-07 16:28:41.354 [info] 'ToolFileUtils' Reading file: client/src/pages/Dashboard.tsx
2025-06-07 16:28:41.354 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Dashboard.tsx (2961 bytes)
2025-06-07 16:28:43.178 [info] 'ToolFileUtils' Reading file: client/src/pages/Dashboard.tsx
2025-06-07 16:28:43.178 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Dashboard.tsx (3159 bytes)
2025-06-07 16:28:58.740 [info] 'ToolFileUtils' Reading file: client/src/pages/Dashboard.tsx
2025-06-07 16:28:58.740 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Dashboard.tsx (3159 bytes)
2025-06-07 16:29:00.273 [info] 'ToolFileUtils' Reading file: client/src/pages/Dashboard.tsx
2025-06-07 16:29:00.273 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Dashboard.tsx (3419 bytes)
2025-06-07 16:29:09.657 [info] 'ViewTool' Tool called with path: client/src/components/StatsCards.tsx and view_range: [1,50]
2025-06-07 16:29:29.104 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:29:29.104 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3502 bytes)
2025-06-07 16:29:30.057 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-70f9fc73
2025-06-07 16:29:30.876 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:29:30.877 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3486 bytes)
2025-06-07 16:29:42.533 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:29:42.539 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3486 bytes)
2025-06-07 16:29:44.125 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:29:44.125 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3480 bytes)
2025-06-07 16:29:56.195 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:29:56.195 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3480 bytes)
2025-06-07 16:29:57.804 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:29:57.804 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3478 bytes)
2025-06-07 16:30:09.722 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:30:09.722 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3478 bytes)
2025-06-07 16:30:11.238 [info] 'ToolFileUtils' Reading file: client/src/components/StatsCards.tsx
2025-06-07 16:30:11.238 [info] 'ToolFileUtils' Successfully read file: client/src/components/StatsCards.tsx (3469 bytes)
2025-06-07 16:30:21.877 [info] 'ViewTool' Tool called with path: client/src/components/ClientsList.tsx and view_range: undefined
2025-06-07 16:30:29.901 [info] 'ToolFileUtils' Reading file: client/src/components/ClientsList.tsx
2025-06-07 16:30:29.901 [info] 'ToolFileUtils' Successfully read file: client/src/components/ClientsList.tsx (4436 bytes)
2025-06-07 16:30:30.913 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-7306ea2d
2025-06-07 16:30:31.718 [info] 'ToolFileUtils' Reading file: client/src/components/ClientsList.tsx
2025-06-07 16:30:31.718 [info] 'ToolFileUtils' Successfully read file: client/src/components/ClientsList.tsx (4432 bytes)
2025-06-07 16:30:43.542 [info] 'ToolFileUtils' Reading file: client/src/components/ClientsList.tsx
2025-06-07 16:30:43.543 [info] 'ToolFileUtils' Successfully read file: client/src/components/ClientsList.tsx (4432 bytes)
2025-06-07 16:30:45.123 [info] 'ToolFileUtils' Reading file: client/src/components/ClientsList.tsx
2025-06-07 16:30:45.124 [info] 'ToolFileUtils' Successfully read file: client/src/components/ClientsList.tsx (4423 bytes)
2025-06-07 16:32:47.425 [info] 'AugmentExtension' Retrieving model config
2025-06-07 16:32:47.582 [info] 'AugmentExtension' Retrieved model config
2025-06-07 16:32:47.582 [info] 'AugmentExtension' Returning model config
2025-06-07 16:35:46.209 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:35:46.392 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4935 bytes)
2025-06-07 16:35:48.112 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:35:48.112 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5090 bytes)
2025-06-07 16:36:02.142 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:36:02.143 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5090 bytes)
2025-06-07 16:36:12.438 [info] 'ViewTool' Tool called with path: client/src/components/InsightsSidebar.tsx and view_range: [68,95]
2025-06-07 16:36:23.975 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:36:23.976 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5090 bytes)
2025-06-07 16:36:25.504 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:36:25.504 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5329 bytes)
2025-06-07 16:36:42.130 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:36:42.131 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5329 bytes)
2025-06-07 16:36:43.712 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:36:43.712 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5793 bytes)
2025-06-07 16:36:55.607 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:36:55.608 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5793 bytes)
2025-06-07 16:37:01.030 [info] 'ViewTool' Tool called with path: client/src/components/InsightsSidebar.tsx and view_range: [40,50]
2025-06-07 16:37:09.001 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:37:09.005 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5793 bytes)
2025-06-07 16:37:10.736 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 16:37:10.736 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (5793 bytes)
2025-06-07 16:37:37.459 [info] 'ViewTool' Tool called with path: client/src/components/Sidebar.tsx and view_range: undefined
2025-06-07 16:37:47.495 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:37:47.495 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5116 bytes)
2025-06-07 16:37:48.595 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-27dd9f97
2025-06-07 16:37:49.408 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:37:49.409 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5141 bytes)
2025-06-07 16:37:59.954 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:37:59.954 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5141 bytes)
2025-06-07 16:38:01.474 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:01.474 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5132 bytes)
2025-06-07 16:38:13.954 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:13.954 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5132 bytes)
2025-06-07 16:38:15.620 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:15.620 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5238 bytes)
2025-06-07 16:38:25.377 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:25.377 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5238 bytes)
2025-06-07 16:38:26.850 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:26.850 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5249 bytes)
2025-06-07 16:38:37.556 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:37.556 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5249 bytes)
2025-06-07 16:38:39.086 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:39.086 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5274 bytes)
2025-06-07 16:38:48.580 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:48.580 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5274 bytes)
2025-06-07 16:38:50.101 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:38:50.101 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5308 bytes)
2025-06-07 16:39:11.222 [error] 'AugmentExtension' API request eea19ea3-7f0a-47cd-b3bd-13681cb4ffd4 to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-07 16:39:11.405 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 16:39:11.769 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 16:43:04.416 [info] 'ViewTool' Tool called with path: client/src/components/Layout.tsx and view_range: undefined
2025-06-07 16:43:17.786 [info] 'ToolFileUtils' Reading file: client/src/components/Layout.tsx
2025-06-07 16:43:17.786 [info] 'ToolFileUtils' Successfully read file: client/src/components/Layout.tsx (2082 bytes)
2025-06-07 16:43:18.798 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-4f4b434d
2025-06-07 16:43:19.574 [info] 'ToolFileUtils' Reading file: client/src/components/Layout.tsx
2025-06-07 16:43:19.574 [info] 'ToolFileUtils' Successfully read file: client/src/components/Layout.tsx (2077 bytes)
2025-06-07 16:43:29.655 [info] 'ViewTool' Tool called with path: client/src/index.css and view_range: [1,50]
2025-06-07 16:43:34.680 [info] 'ViewTool' Tool called with path: index.html and view_range: undefined
2025-06-07 16:43:34.736 [info] 'ViewTool' Path does not exist: index.html
2025-06-07 16:43:35.223 [info] 'ToolFileUtils' File not found: index.html. Similar files found:
/home/<USER>/workspace/client/index.html
/home/<USER>/workspace/dist/public/index.html
/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/index.html
/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/media/index.html
/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/media/index.html
2025-06-07 16:43:40.614 [info] 'ViewTool' Tool called with path: client/index.html and view_range: undefined
2025-06-07 16:43:41.318 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T160243/exthost1/output_logging_20250607T160246
2025-06-07 16:43:52.997 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:43:53.318 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5308 bytes)
2025-06-07 16:43:55.414 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:43:55.415 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5245 bytes)
2025-06-07 16:44:07.655 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:44:07.655 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5245 bytes)
2025-06-07 16:44:09.166 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 16:44:09.166 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5211 bytes)
2025-06-07 16:52:53.120 [error] 'AugmentExtension' API request 9867e0e1-2f2a-47fc-924e-b15be3361450 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 16:52:53.401 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 16:52:53.707 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 17:02:47.426 [info] 'AugmentExtension' Retrieving model config
2025-06-07 17:02:47.609 [info] 'AugmentExtension' Retrieved model config
2025-06-07 17:02:47.609 [info] 'AugmentExtension' Returning model config
2025-06-07 17:04:25.930 [info] 'ViewTool' Tool called with path: client/src/components/Sidebar.tsx and view_range: [90,115]
2025-06-07 17:04:38.900 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:04:38.901 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5211 bytes)
2025-06-07 17:04:40.627 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:04:40.627 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5293 bytes)
2025-06-07 17:04:51.165 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:04:51.165 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5293 bytes)
2025-06-07 17:04:52.796 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:04:52.796 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5332 bytes)
2025-06-07 17:05:09.277 [info] 'ViewTool' Tool called with path: tailwind.config.js and view_range: undefined
2025-06-07 17:05:09.336 [info] 'ViewTool' Path does not exist: tailwind.config.js
2025-06-07 17:05:09.591 [info] 'ViewTool' Corrected path to: /home/<USER>/workspace/node_modules/tailwindcss/stubs/tailwind.config.js
2025-06-07 17:05:14.892 [info] 'ViewTool' Tool called with path: client/tailwind.config.js and view_range: undefined
2025-06-07 17:05:14.979 [info] 'ViewTool' Path does not exist: client/tailwind.config.js
2025-06-07 17:05:15.187 [info] 'ToolFileUtils' File not found: client/tailwind.config.js. Similar files found:
/home/<USER>/workspace/node_modules/tailwindcss/stubs/tailwind.config.js
2025-06-07 17:05:21.837 [info] 'ViewTool' Tool called with path: tailwind.config.ts and view_range: undefined
2025-06-07 17:05:33.141 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:05:33.142 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5332 bytes)
2025-06-07 17:05:34.742 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:05:34.743 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5828 bytes)
2025-06-07 17:06:29.166 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:06:29.167 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (5828 bytes)
2025-06-07 17:06:30.698 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:06:30.698 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6057 bytes)
2025-06-07 17:06:48.993 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:06:48.993 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6057 bytes)
2025-06-07 17:06:50.541 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:06:50.541 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6447 bytes)
2025-06-07 17:08:43.901 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:08:44.078 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6447 bytes)
2025-06-07 17:08:45.754 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:08:45.754 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6690 bytes)
2025-06-07 17:09:14.020 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:09:14.021 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6690 bytes)
2025-06-07 17:09:15.553 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:09:15.553 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6923 bytes)
2025-06-07 17:09:26.005 [info] 'ToolFileUtils' Reading file: client/src/components/Layout.tsx
2025-06-07 17:09:26.191 [info] 'ToolFileUtils' Successfully read file: client/src/components/Layout.tsx (2077 bytes)
2025-06-07 17:09:27.956 [info] 'ToolFileUtils' Reading file: client/src/components/Layout.tsx
2025-06-07 17:09:27.956 [info] 'ToolFileUtils' Successfully read file: client/src/components/Layout.tsx (2109 bytes)
2025-06-07 17:13:11.302 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:13:11.574 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6923 bytes)
2025-06-07 17:13:13.422 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:13:13.422 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6774 bytes)
2025-06-07 17:13:24.327 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:13:24.327 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6774 bytes)
2025-06-07 17:13:25.871 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:13:25.871 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6674 bytes)
2025-06-07 17:13:36.696 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:13:36.697 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6674 bytes)
2025-06-07 17:13:38.219 [info] 'ToolFileUtils' Reading file: client/src/components/Sidebar.tsx
2025-06-07 17:13:38.219 [info] 'ToolFileUtils' Successfully read file: client/src/components/Sidebar.tsx (6594 bytes)
2025-06-07 17:13:48.987 [info] 'ToolFileUtils' Reading file: client/src/components/Layout.tsx
2025-06-07 17:13:49.160 [info] 'ToolFileUtils' Successfully read file: client/src/components/Layout.tsx (2109 bytes)
2025-06-07 17:13:50.908 [info] 'ToolFileUtils' Reading file: client/src/components/Layout.tsx
2025-06-07 17:13:50.909 [info] 'ToolFileUtils' Successfully read file: client/src/components/Layout.tsx (2077 bytes)
2025-06-07 17:19:29.673 [error] 'AugmentExtension' API request 33434902-b26d-41f7-8d9f-11931c4c56c4 to https://i0.api.augmentcode.com/client-metrics failed: This operation was aborted
2025-06-07 17:19:29.858 [error] 'ClientMetricsReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
	at e.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:9113)
	at q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:13053)
	at processTicksAndRejections (node:internal/process/task_queues:95:5)
	at runNextTicks (node:internal/process/task_queues:64:3)
	at listOnTimeout (node:internal/timers:545:9)
	at process.processTimers (node:internal/timers:519:7)
	at async q2.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:55490)
	at async q2.clientMetrics (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:1196:50199)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13706
	at async rs (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:11918)
	at async e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:13605)
	at async /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/out/extension.js:237:12928
2025-06-07 17:19:29.859 [info] 'ClientMetricsReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 17:19:30.146 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
