2025-06-07 16:02:48.630 [info] [main] Log level: Info
2025-06-07 16:02:48.630 [info] [main] Validating found git in: "git"
2025-06-07 16:02:48.630 [info] [main] Using git "2.47.2" from "git"
2025-06-07 16:02:48.630 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --git-dir --git-common-dir [4ms]
2025-06-07 16:02:48.630 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 16:02:48.630 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.630 [info] > git config --get commit.template [7ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [5ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [23ms]
2025-06-07 16:02:48.630 [info] > git status -z -uall [31ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms]
2025-06-07 16:02:48.630 [info] > git config --get commit.template [24ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [28ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [118ms]
2025-06-07 16:02:48.630 [info] > git config --get --local branch.main.vscode-merge-base [16ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [20ms]
2025-06-07 16:02:48.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [61ms]
2025-06-07 16:02:48.703 [info] > git rev-parse --show-toplevel [116ms]
2025-06-07 16:02:48.730 [info] > git status -z -uall [24ms]
2025-06-07 16:02:48.730 [info] > git merge-base refs/heads/main refs/remotes/origin/main [94ms]
2025-06-07 16:02:48.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [36ms]
2025-06-07 16:02:48.751 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 16:02:48.751 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [15ms]
2025-06-07 16:02:48.755 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-07 16:02:48.768 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 16:02:48.768 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [10ms]
2025-06-07 16:02:48.774 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.777 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 16:02:53.730 [info] > git config --get commit.template [2ms]
2025-06-07 16:02:53.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:02:53.747 [info] > git status -z -uall [7ms]
2025-06-07 16:02:53.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:03:01.733 [info] > git config --get commit.template [4ms]
2025-06-07 16:03:01.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:01.743 [info] > git status -z -uall [5ms]
2025-06-07 16:03:01.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:06.756 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:06.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:06.777 [info] > git status -z -uall [7ms]
2025-06-07 16:03:06.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:03:11.791 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:11.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:11.811 [info] > git status -z -uall [4ms]
2025-06-07 16:03:11.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:03:16.823 [info] > git config --get commit.template [0ms]
2025-06-07 16:03:16.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:16.837 [info] > git status -z -uall [6ms]
2025-06-07 16:03:16.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:21.855 [info] > git config --get commit.template [8ms]
2025-06-07 16:03:21.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:21.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:21.884 [info] > git status -z -uall [16ms]
2025-06-07 16:03:26.902 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:26.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:03:26.943 [info] > git status -z -uall [11ms]
2025-06-07 16:03:26.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:03:31.961 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:31.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:31.976 [info] > git status -z -uall [6ms]
2025-06-07 16:03:31.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:36.995 [info] > git config --get commit.template [6ms]
2025-06-07 16:03:36.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:03:37.014 [info] > git status -z -uall [9ms]
2025-06-07 16:03:37.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:42.064 [info] > git config --get commit.template [13ms]
2025-06-07 16:03:42.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:42.106 [info] > git status -z -uall [11ms]
2025-06-07 16:03:42.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:47.127 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:47.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 16:03:47.149 [info] > git status -z -uall [6ms]
2025-06-07 16:03:47.150 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:52.163 [info] > git config --get commit.template [3ms]
2025-06-07 16:03:52.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:52.197 [info] > git status -z -uall [9ms]
2025-06-07 16:03:52.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:03:57.216 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:57.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:03:57.274 [info] > git status -z -uall [23ms]
2025-06-07 16:03:57.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:02.290 [info] > git config --get commit.template [7ms]
2025-06-07 16:04:02.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:04:02.316 [info] > git status -z -uall [9ms]
2025-06-07 16:04:02.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:07.335 [info] > git config --get commit.template [7ms]
2025-06-07 16:04:07.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:07.357 [info] > git status -z -uall [10ms]
2025-06-07 16:04:07.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:04:12.375 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:12.384 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:12.419 [info] > git status -z -uall [20ms]
2025-06-07 16:04:12.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:19.856 [info] > git config --get commit.template [11ms]
2025-06-07 16:04:19.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:04:19.881 [info] > git status -z -uall [13ms]
2025-06-07 16:04:19.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:24.893 [info] > git config --get commit.template [3ms]
2025-06-07 16:04:24.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:24.901 [info] > git status -z -uall [4ms]
2025-06-07 16:04:24.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:29.917 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:29.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:29.931 [info] > git status -z -uall [7ms]
2025-06-07 16:04:29.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:34.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:34.946 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:34.953 [info] > git status -z -uall [4ms]
2025-06-07 16:04:34.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:39.966 [info] > git config --get commit.template [4ms]
2025-06-07 16:04:39.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:39.979 [info] > git status -z -uall [5ms]
2025-06-07 16:04:39.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:46.044 [info] > git config --get commit.template [4ms]
2025-06-07 16:04:46.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:46.085 [info] > git status -z -uall [18ms]
2025-06-07 16:04:46.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:51.099 [info] > git config --get commit.template [2ms]
2025-06-07 16:04:51.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:51.113 [info] > git status -z -uall [6ms]
2025-06-07 16:04:51.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:05:45.210 [info] > git config --get commit.template [3ms]
2025-06-07 16:05:45.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:05:45.218 [info] > git status -z -uall [3ms]
2025-06-07 16:05:45.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:05:50.309 [info] > git config --get commit.template [3ms]
2025-06-07 16:05:50.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:05:50.322 [info] > git status -z -uall [4ms]
2025-06-07 16:05:50.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:05:55.335 [info] > git config --get commit.template [4ms]
2025-06-07 16:05:55.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:05:55.343 [info] > git status -z -uall [3ms]
2025-06-07 16:05:55.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:00.357 [info] > git config --get commit.template [6ms]
2025-06-07 16:06:00.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:00.365 [info] > git status -z -uall [4ms]
2025-06-07 16:06:00.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:05.378 [info] > git config --get commit.template [1ms]
2025-06-07 16:06:05.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:05.396 [info] > git status -z -uall [6ms]
2025-06-07 16:06:05.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:10.410 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:10.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:10.563 [info] > git status -z -uall [146ms]
2025-06-07 16:06:10.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [182ms]
2025-06-07 16:06:15.622 [info] > git config --get commit.template [3ms]
2025-06-07 16:06:15.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:15.634 [info] > git status -z -uall [5ms]
2025-06-07 16:06:15.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:20.647 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:20.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:20.656 [info] > git status -z -uall [4ms]
2025-06-07 16:06:20.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:25.668 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:25.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:25.676 [info] > git status -z -uall [3ms]
2025-06-07 16:06:25.678 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:30.690 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:30.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:30.698 [info] > git status -z -uall [3ms]
2025-06-07 16:06:30.699 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:35.712 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:35.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:35.721 [info] > git status -z -uall [4ms]
2025-06-07 16:06:35.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:40.734 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:40.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:40.742 [info] > git status -z -uall [4ms]
2025-06-07 16:06:40.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:45.756 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:45.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:45.767 [info] > git status -z -uall [7ms]
2025-06-07 16:06:45.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:50.784 [info] > git config --get commit.template [7ms]
2025-06-07 16:06:50.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:06:50.802 [info] > git status -z -uall [9ms]
2025-06-07 16:06:50.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:57.516 [info] > git config --get commit.template [14ms]
2025-06-07 16:06:57.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:57.528 [info] > git status -z -uall [4ms]
2025-06-07 16:06:57.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:07:02.544 [info] > git config --get commit.template [5ms]
2025-06-07 16:07:02.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:07:02.553 [info] > git status -z -uall [4ms]
2025-06-07 16:07:02.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:18:25.721 [info] > git config --get commit.template [2ms]
2025-06-07 16:18:25.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:18:25.850 [info] > git status -z -uall [97ms]
2025-06-07 16:18:25.851 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [80ms]
2025-06-07 16:18:30.885 [info] > git config --get commit.template [3ms]
2025-06-07 16:18:30.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:18:30.981 [info] > git status -z -uall [51ms]
2025-06-07 16:18:30.982 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [36ms]
2025-06-07 16:18:36.037 [info] > git config --get commit.template [24ms]
2025-06-07 16:18:36.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:18:36.078 [info] > git status -z -uall [18ms]
2025-06-07 16:18:36.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-06-07 16:18:41.141 [info] > git config --get commit.template [25ms]
2025-06-07 16:18:41.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:18:41.175 [info] > git status -z -uall [15ms]
2025-06-07 16:18:41.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:18:46.204 [info] > git config --get commit.template [9ms]
2025-06-07 16:18:46.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:18:46.230 [info] > git status -z -uall [13ms]
2025-06-07 16:18:46.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:18:51.276 [info] > git config --get commit.template [9ms]
2025-06-07 16:18:51.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:18:51.289 [info] > git status -z -uall [6ms]
2025-06-07 16:18:51.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:18:56.309 [info] > git config --get commit.template [6ms]
2025-06-07 16:18:56.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:18:56.323 [info] > git status -z -uall [5ms]
2025-06-07 16:18:56.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:01.350 [info] > git config --get commit.template [10ms]
2025-06-07 16:19:01.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:19:01.365 [info] > git status -z -uall [5ms]
2025-06-07 16:19:01.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:06.383 [info] > git config --get commit.template [5ms]
2025-06-07 16:19:06.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:06.398 [info] > git status -z -uall [6ms]
2025-06-07 16:19:06.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:11.416 [info] > git config --get commit.template [6ms]
2025-06-07 16:19:11.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:11.432 [info] > git status -z -uall [8ms]
2025-06-07 16:19:11.433 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:16.452 [info] > git config --get commit.template [6ms]
2025-06-07 16:19:16.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:16.484 [info] > git status -z -uall [25ms]
2025-06-07 16:19:16.485 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:21.516 [info] > git config --get commit.template [15ms]
2025-06-07 16:19:21.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:19:21.538 [info] > git status -z -uall [9ms]
2025-06-07 16:19:21.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:19:26.569 [info] > git config --get commit.template [11ms]
2025-06-07 16:19:26.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:19:26.599 [info] > git status -z -uall [15ms]
2025-06-07 16:19:26.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:31.633 [info] > git config --get commit.template [15ms]
2025-06-07 16:19:31.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:31.664 [info] > git status -z -uall [15ms]
2025-06-07 16:19:31.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:36.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:19:36.716 [info] > git config --get commit.template [29ms]
2025-06-07 16:19:36.772 [info] > git status -z -uall [35ms]
2025-06-07 16:19:36.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:19:41.793 [info] > git config --get commit.template [5ms]
2025-06-07 16:19:41.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:41.806 [info] > git status -z -uall [6ms]
2025-06-07 16:19:41.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:46.822 [info] > git config --get commit.template [5ms]
2025-06-07 16:19:46.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:19:46.836 [info] > git status -z -uall [7ms]
2025-06-07 16:19:46.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:19:51.855 [info] > git config --get commit.template [2ms]
2025-06-07 16:19:51.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:19:51.893 [info] > git status -z -uall [13ms]
2025-06-07 16:19:51.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:19:56.918 [info] > git config --get commit.template [7ms]
2025-06-07 16:19:56.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:19:56.934 [info] > git status -z -uall [7ms]
2025-06-07 16:19:56.935 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:20:01.954 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:01.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:01.966 [info] > git status -z -uall [5ms]
2025-06-07 16:20:01.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:20:06.986 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:06.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:06.997 [info] > git status -z -uall [5ms]
2025-06-07 16:20:06.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:12.018 [info] > git config --get commit.template [8ms]
2025-06-07 16:20:12.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 16:20:12.039 [info] > git status -z -uall [6ms]
2025-06-07 16:20:12.040 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:17.058 [info] > git config --get commit.template [7ms]
2025-06-07 16:20:17.059 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:17.075 [info] > git status -z -uall [7ms]
2025-06-07 16:20:17.075 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:20:22.096 [info] > git config --get commit.template [7ms]
2025-06-07 16:20:22.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:22.111 [info] > git status -z -uall [7ms]
2025-06-07 16:20:22.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:27.129 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:27.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:27.143 [info] > git status -z -uall [6ms]
2025-06-07 16:20:27.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:32.173 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:32.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:32.202 [info] > git status -z -uall [6ms]
2025-06-07 16:20:32.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:37.225 [info] > git config --get commit.template [9ms]
2025-06-07 16:20:37.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:37.248 [info] > git status -z -uall [9ms]
2025-06-07 16:20:37.249 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:42.269 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:42.270 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:20:42.283 [info] > git status -z -uall [6ms]
2025-06-07 16:20:42.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:47.301 [info] > git config --get commit.template [6ms]
2025-06-07 16:20:47.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:47.314 [info] > git status -z -uall [6ms]
2025-06-07 16:20:47.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:52.331 [info] > git config --get commit.template [5ms]
2025-06-07 16:20:52.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:20:52.344 [info] > git status -z -uall [6ms]
2025-06-07 16:20:52.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:20:57.389 [info] > git config --get commit.template [20ms]
2025-06-07 16:20:57.397 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-07 16:20:57.434 [info] > git status -z -uall [13ms]
2025-06-07 16:20:57.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:02.458 [info] > git config --get commit.template [7ms]
2025-06-07 16:21:02.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:02.473 [info] > git status -z -uall [7ms]
2025-06-07 16:21:02.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:07.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 16:21:07.495 [info] > git config --get commit.template [11ms]
2025-06-07 16:21:07.508 [info] > git status -z -uall [5ms]
2025-06-07 16:21:07.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:12.527 [info] > git config --get commit.template [7ms]
2025-06-07 16:21:12.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:12.541 [info] > git status -z -uall [7ms]
2025-06-07 16:21:12.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:17.557 [info] > git config --get commit.template [5ms]
2025-06-07 16:21:17.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:17.569 [info] > git status -z -uall [6ms]
2025-06-07 16:21:17.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:22.593 [info] > git config --get commit.template [6ms]
2025-06-07 16:21:22.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:22.606 [info] > git status -z -uall [6ms]
2025-06-07 16:21:22.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:27.624 [info] > git config --get commit.template [6ms]
2025-06-07 16:21:27.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:27.637 [info] > git status -z -uall [5ms]
2025-06-07 16:21:27.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:32.654 [info] > git config --get commit.template [5ms]
2025-06-07 16:21:32.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:32.668 [info] > git status -z -uall [6ms]
2025-06-07 16:21:32.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:37.688 [info] > git config --get commit.template [7ms]
2025-06-07 16:21:37.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:37.699 [info] > git status -z -uall [5ms]
2025-06-07 16:21:37.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:42.741 [info] > git config --get commit.template [18ms]
2025-06-07 16:21:42.741 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:21:42.765 [info] > git status -z -uall [12ms]
2025-06-07 16:21:42.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:47.786 [info] > git config --get commit.template [8ms]
2025-06-07 16:21:47.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:47.800 [info] > git status -z -uall [6ms]
2025-06-07 16:21:47.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:21:52.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 16:21:52.866 [info] > git config --get commit.template [25ms]
2025-06-07 16:21:52.886 [info] > git status -z -uall [8ms]
2025-06-07 16:21:52.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:21:57.916 [info] > git config --get commit.template [14ms]
2025-06-07 16:21:57.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:21:57.935 [info] > git status -z -uall [9ms]
2025-06-07 16:21:57.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:02.954 [info] > git config --get commit.template [6ms]
2025-06-07 16:22:02.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:02.976 [info] > git status -z -uall [9ms]
2025-06-07 16:22:02.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:07.998 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:08.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:08.012 [info] > git status -z -uall [5ms]
2025-06-07 16:22:08.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:13.032 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:13.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:13.044 [info] > git status -z -uall [5ms]
2025-06-07 16:22:13.045 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:18.081 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:18.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:18.094 [info] > git status -z -uall [5ms]
2025-06-07 16:22:18.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:23.115 [info] > git config --get commit.template [8ms]
2025-06-07 16:22:23.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:22:23.131 [info] > git status -z -uall [6ms]
2025-06-07 16:22:23.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:28.156 [info] > git config --get commit.template [1ms]
2025-06-07 16:22:28.170 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:28.203 [info] > git status -z -uall [10ms]
2025-06-07 16:22:28.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:33.223 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:33.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:22:33.237 [info] > git status -z -uall [6ms]
2025-06-07 16:22:33.239 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:38.260 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:38.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:38.273 [info] > git status -z -uall [7ms]
2025-06-07 16:22:38.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:22:43.293 [info] > git config --get commit.template [7ms]
2025-06-07 16:22:43.295 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:43.307 [info] > git status -z -uall [7ms]
2025-06-07 16:22:43.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:22:48.336 [info] > git config --get commit.template [10ms]
2025-06-07 16:22:48.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:48.360 [info] > git status -z -uall [14ms]
2025-06-07 16:22:48.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:53.380 [info] > git config --get commit.template [8ms]
2025-06-07 16:22:53.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:22:53.402 [info] > git status -z -uall [9ms]
2025-06-07 16:22:53.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:22:58.416 [info] > git config --get commit.template [2ms]
2025-06-07 16:22:58.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:22:58.437 [info] > git status -z -uall [8ms]
2025-06-07 16:22:58.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:03.462 [info] > git config --get commit.template [9ms]
2025-06-07 16:23:03.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:03.487 [info] > git status -z -uall [13ms]
2025-06-07 16:23:03.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:08.526 [info] > git config --get commit.template [19ms]
2025-06-07 16:23:08.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:23:08.559 [info] > git status -z -uall [15ms]
2025-06-07 16:23:08.562 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:23:13.603 [info] > git config --get commit.template [20ms]
2025-06-07 16:23:13.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:13.654 [info] > git status -z -uall [23ms]
2025-06-07 16:23:13.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:23:18.676 [info] > git config --get commit.template [9ms]
2025-06-07 16:23:18.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:18.693 [info] > git status -z -uall [7ms]
2025-06-07 16:23:18.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:23.713 [info] > git config --get commit.template [7ms]
2025-06-07 16:23:23.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:23.727 [info] > git status -z -uall [6ms]
2025-06-07 16:23:23.728 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:23:28.817 [info] > git config --get commit.template [73ms]
2025-06-07 16:23:28.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:28.844 [info] > git status -z -uall [8ms]
2025-06-07 16:23:28.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:23:33.865 [info] > git config --get commit.template [7ms]
2025-06-07 16:23:33.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:33.879 [info] > git status -z -uall [7ms]
2025-06-07 16:23:33.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:38.899 [info] > git config --get commit.template [7ms]
2025-06-07 16:23:38.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:38.913 [info] > git status -z -uall [6ms]
2025-06-07 16:23:38.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:43.942 [info] > git config --get commit.template [14ms]
2025-06-07 16:23:43.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:23:43.971 [info] > git status -z -uall [10ms]
2025-06-07 16:23:43.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:49.000 [info] > git config --get commit.template [14ms]
2025-06-07 16:23:49.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:23:49.031 [info] > git status -z -uall [16ms]
2025-06-07 16:23:49.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:23:54.061 [info] > git config --get commit.template [9ms]
2025-06-07 16:23:54.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:54.086 [info] > git status -z -uall [12ms]
2025-06-07 16:23:54.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:23:59.182 [info] > git config --get commit.template [76ms]
2025-06-07 16:23:59.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:23:59.224 [info] > git status -z -uall [14ms]
2025-06-07 16:23:59.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:24:04.242 [info] > git config --get commit.template [1ms]
2025-06-07 16:24:04.254 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:04.269 [info] > git status -z -uall [7ms]
2025-06-07 16:24:04.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:24:09.292 [info] > git config --get commit.template [8ms]
2025-06-07 16:24:09.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:09.306 [info] > git status -z -uall [6ms]
2025-06-07 16:24:09.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:14.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 16:24:14.345 [info] > git config --get commit.template [23ms]
2025-06-07 16:24:14.371 [info] > git status -z -uall [12ms]
2025-06-07 16:24:14.371 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:19.389 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:19.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:19.407 [info] > git status -z -uall [8ms]
2025-06-07 16:24:19.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:24.425 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:24.426 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:24.437 [info] > git status -z -uall [5ms]
2025-06-07 16:24:24.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:29.466 [info] > git config --get commit.template [12ms]
2025-06-07 16:24:29.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:24:29.487 [info] > git status -z -uall [11ms]
2025-06-07 16:24:29.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:34.508 [info] > git config --get commit.template [2ms]
2025-06-07 16:24:34.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:34.540 [info] > git status -z -uall [8ms]
2025-06-07 16:24:34.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:39.557 [info] > git config --get commit.template [5ms]
2025-06-07 16:24:39.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:39.570 [info] > git status -z -uall [5ms]
2025-06-07 16:24:39.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:44.587 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:44.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:44.600 [info] > git status -z -uall [6ms]
2025-06-07 16:24:44.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:49.623 [info] > git config --get commit.template [8ms]
2025-06-07 16:24:49.624 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:24:49.636 [info] > git status -z -uall [6ms]
2025-06-07 16:24:49.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:24:54.655 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:54.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:54.671 [info] > git status -z -uall [7ms]
2025-06-07 16:24:54.672 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:24:59.689 [info] > git config --get commit.template [6ms]
2025-06-07 16:24:59.690 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:24:59.702 [info] > git status -z -uall [5ms]
2025-06-07 16:24:59.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:04.721 [info] > git config --get commit.template [7ms]
2025-06-07 16:25:04.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:04.735 [info] > git status -z -uall [6ms]
2025-06-07 16:25:04.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:09.753 [info] > git config --get commit.template [7ms]
2025-06-07 16:25:09.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:09.766 [info] > git status -z -uall [6ms]
2025-06-07 16:25:09.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:14.791 [info] > git config --get commit.template [10ms]
2025-06-07 16:25:14.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:14.806 [info] > git status -z -uall [7ms]
2025-06-07 16:25:14.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:19.911 [info] > git config --get commit.template [33ms]
2025-06-07 16:25:19.914 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 16:25:19.955 [info] > git status -z -uall [18ms]
2025-06-07 16:25:19.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:25:24.989 [info] > git config --get commit.template [14ms]
2025-06-07 16:25:24.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:25.025 [info] > git status -z -uall [22ms]
2025-06-07 16:25:25.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 16:25:30.053 [info] > git config --get commit.template [10ms]
2025-06-07 16:25:30.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:30.086 [info] > git status -z -uall [18ms]
2025-06-07 16:25:30.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:25:35.110 [info] > git config --get commit.template [0ms]
2025-06-07 16:25:35.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 16:25:35.211 [info] > git status -z -uall [29ms]
2025-06-07 16:25:35.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:40.239 [info] > git config --get commit.template [9ms]
2025-06-07 16:25:40.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:40.262 [info] > git status -z -uall [12ms]
2025-06-07 16:25:40.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:25:45.282 [info] > git config --get commit.template [0ms]
2025-06-07 16:25:45.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:45.322 [info] > git status -z -uall [12ms]
2025-06-07 16:25:45.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:50.343 [info] > git config --get commit.template [5ms]
2025-06-07 16:25:50.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:25:50.358 [info] > git status -z -uall [8ms]
2025-06-07 16:25:50.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:25:57.442 [info] > git config --get commit.template [9ms]
2025-06-07 16:25:57.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:25:57.475 [info] > git status -z -uall [17ms]
2025-06-07 16:25:57.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:26:02.498 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:02.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:02.510 [info] > git status -z -uall [5ms]
2025-06-07 16:26:02.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:26:08.893 [info] > git config --get commit.template [10ms]
2025-06-07 16:26:08.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:08.905 [info] > git status -z -uall [5ms]
2025-06-07 16:26:08.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:13.928 [info] > git config --get commit.template [1ms]
2025-06-07 16:26:13.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:13.966 [info] > git status -z -uall [8ms]
2025-06-07 16:26:13.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:26:20.043 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:20.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:26:20.056 [info] > git status -z -uall [6ms]
2025-06-07 16:26:20.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:25.074 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:25.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:25.089 [info] > git status -z -uall [7ms]
2025-06-07 16:26:25.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:30.111 [info] > git config --get commit.template [8ms]
2025-06-07 16:26:30.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:30.123 [info] > git status -z -uall [5ms]
2025-06-07 16:26:30.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:26:35.141 [info] > git config --get commit.template [5ms]
2025-06-07 16:26:35.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:26:35.158 [info] > git status -z -uall [9ms]
2025-06-07 16:26:35.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:26:40.177 [info] > git config --get commit.template [7ms]
2025-06-07 16:26:40.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:26:40.192 [info] > git status -z -uall [7ms]
2025-06-07 16:26:40.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:27:40.019 [info] > git config --get commit.template [8ms]
2025-06-07 16:27:40.021 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:27:40.036 [info] > git status -z -uall [7ms]
2025-06-07 16:27:40.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:27:45.059 [info] > git config --get commit.template [9ms]
2025-06-07 16:27:45.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:27:45.090 [info] > git status -z -uall [13ms]
2025-06-07 16:27:45.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:27:50.119 [info] > git config --get commit.template [13ms]
2025-06-07 16:27:50.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:27:50.141 [info] > git status -z -uall [9ms]
2025-06-07 16:27:50.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:27:55.166 [info] > git config --get commit.template [8ms]
2025-06-07 16:27:55.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:27:55.183 [info] > git status -z -uall [8ms]
2025-06-07 16:27:55.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:28:00.201 [info] > git config --get commit.template [7ms]
2025-06-07 16:28:00.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:00.215 [info] > git status -z -uall [7ms]
2025-06-07 16:28:00.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:28:05.243 [info] > git config --get commit.template [12ms]
2025-06-07 16:28:05.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:28:05.288 [info] > git status -z -uall [19ms]
2025-06-07 16:28:05.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 16:28:19.668 [info] > git config --get commit.template [5ms]
2025-06-07 16:28:19.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:19.681 [info] > git status -z -uall [5ms]
2025-06-07 16:28:19.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:28:24.700 [info] > git config --get commit.template [5ms]
2025-06-07 16:28:24.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:24.712 [info] > git status -z -uall [5ms]
2025-06-07 16:28:24.716 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:28:44.433 [info] > git config --get commit.template [6ms]
2025-06-07 16:28:44.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:44.446 [info] > git status -z -uall [6ms]
2025-06-07 16:28:44.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:28:44.458 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-07 16:28:44.467 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [2ms]
2025-06-07 16:28:49.472 [info] > git config --get commit.template [1ms]
2025-06-07 16:28:49.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:28:49.505 [info] > git status -z -uall [11ms]
2025-06-07 16:28:49.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:28:54.522 [info] > git config --get commit.template [6ms]
2025-06-07 16:28:54.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:28:54.538 [info] > git status -z -uall [6ms]
2025-06-07 16:28:54.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:28:59.560 [info] > git config --get commit.template [7ms]
2025-06-07 16:28:59.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:28:59.574 [info] > git status -z -uall [7ms]
2025-06-07 16:28:59.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:07.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:07.155 [info] > git config --get commit.template [16ms]
2025-06-07 16:29:07.178 [info] > git status -z -uall [11ms]
2025-06-07 16:29:07.179 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:16.148 [info] > git config --get commit.template [9ms]
2025-06-07 16:29:16.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:29:16.163 [info] > git status -z -uall [7ms]
2025-06-07 16:29:16.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:21.188 [info] > git config --get commit.template [4ms]
2025-06-07 16:29:21.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:21.222 [info] > git status -z -uall [10ms]
2025-06-07 16:29:21.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:26.249 [info] > git config --get commit.template [8ms]
2025-06-07 16:29:26.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:26.264 [info] > git status -z -uall [7ms]
2025-06-07 16:29:26.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:29:31.289 [info] > git config --get commit.template [11ms]
2025-06-07 16:29:31.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:29:31.307 [info] > git status -z -uall [8ms]
2025-06-07 16:29:31.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:29:36.329 [info] > git config --get commit.template [6ms]
2025-06-07 16:29:36.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:29:36.356 [info] > git status -z -uall [12ms]
2025-06-07 16:29:36.356 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:29:41.374 [info] > git config --get commit.template [6ms]
2025-06-07 16:29:41.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:41.388 [info] > git status -z -uall [6ms]
2025-06-07 16:29:41.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:46.415 [info] > git config --get commit.template [8ms]
2025-06-07 16:29:46.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:46.426 [info] > git status -z -uall [5ms]
2025-06-07 16:29:46.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:29:51.449 [info] > git config --get commit.template [9ms]
2025-06-07 16:29:51.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:29:51.465 [info] > git status -z -uall [6ms]
2025-06-07 16:29:51.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:29:56.486 [info] > git config --get commit.template [2ms]
2025-06-07 16:29:56.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-06-07 16:29:56.591 [info] > git status -z -uall [15ms]
2025-06-07 16:29:56.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:01.613 [info] > git config --get commit.template [7ms]
2025-06-07 16:30:01.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:30:01.628 [info] > git status -z -uall [8ms]
2025-06-07 16:30:01.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:06.650 [info] > git config --get commit.template [7ms]
2025-06-07 16:30:06.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:06.663 [info] > git status -z -uall [5ms]
2025-06-07 16:30:06.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:30:11.688 [info] > git config --get commit.template [4ms]
2025-06-07 16:30:11.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:11.722 [info] > git status -z -uall [11ms]
2025-06-07 16:30:11.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:30:16.745 [info] > git config --get commit.template [6ms]
2025-06-07 16:30:16.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:30:16.786 [info] > git status -z -uall [10ms]
2025-06-07 16:30:16.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:30:21.818 [info] > git config --get commit.template [16ms]
2025-06-07 16:30:21.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:21.838 [info] > git status -z -uall [9ms]
2025-06-07 16:30:21.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:26.861 [info] > git config --get commit.template [7ms]
2025-06-07 16:30:26.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:30:26.880 [info] > git status -z -uall [11ms]
2025-06-07 16:30:26.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:30:31.937 [info] > git config --get commit.template [6ms]
2025-06-07 16:30:31.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:31.952 [info] > git status -z -uall [8ms]
2025-06-07 16:30:31.952 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:30:36.977 [info] > git config --get commit.template [11ms]
2025-06-07 16:30:36.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:36.995 [info] > git status -z -uall [7ms]
2025-06-07 16:30:36.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:42.030 [info] > git config --get commit.template [12ms]
2025-06-07 16:30:42.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:42.077 [info] > git status -z -uall [21ms]
2025-06-07 16:30:42.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:30:47.096 [info] > git config --get commit.template [5ms]
2025-06-07 16:30:47.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:47.174 [info] > git status -z -uall [67ms]
2025-06-07 16:30:47.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [59ms]
2025-06-07 16:30:52.192 [info] > git config --get commit.template [5ms]
2025-06-07 16:30:52.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:30:52.204 [info] > git status -z -uall [5ms]
2025-06-07 16:30:52.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:30:57.248 [info] > git config --get commit.template [19ms]
2025-06-07 16:30:57.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:30:57.291 [info] > git status -z -uall [16ms]
2025-06-07 16:30:57.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:31:02.323 [info] > git config --get commit.template [8ms]
2025-06-07 16:31:02.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:02.349 [info] > git status -z -uall [10ms]
2025-06-07 16:31:02.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:07.380 [info] > git config --get commit.template [13ms]
2025-06-07 16:31:07.383 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:31:07.434 [info] > git status -z -uall [34ms]
2025-06-07 16:31:07.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 16:31:12.461 [info] > git config --get commit.template [6ms]
2025-06-07 16:31:12.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:12.473 [info] > git status -z -uall [5ms]
2025-06-07 16:31:12.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:31:17.492 [info] > git config --get commit.template [1ms]
2025-06-07 16:31:17.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 16:31:17.526 [info] > git status -z -uall [7ms]
2025-06-07 16:31:17.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:22.545 [info] > git config --get commit.template [6ms]
2025-06-07 16:31:22.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:31:22.569 [info] > git status -z -uall [13ms]
2025-06-07 16:31:22.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:27.588 [info] > git config --get commit.template [2ms]
2025-06-07 16:31:27.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:27.631 [info] > git status -z -uall [13ms]
2025-06-07 16:31:27.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:32.646 [info] > git config --get commit.template [2ms]
2025-06-07 16:31:32.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:31:32.666 [info] > git status -z -uall [6ms]
2025-06-07 16:31:32.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:31:37.729 [info] > git config --get commit.template [17ms]
2025-06-07 16:31:37.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:31:37.769 [info] > git status -z -uall [17ms]
2025-06-07 16:31:37.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:31:42.858 [info] > git config --get commit.template [40ms]
2025-06-07 16:31:42.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:31:42.916 [info] > git status -z -uall [22ms]
2025-06-07 16:31:42.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:31:47.941 [info] > git config --get commit.template [1ms]
2025-06-07 16:31:47.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:31:47.977 [info] > git status -z -uall [11ms]
2025-06-07 16:31:47.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:31:53.019 [info] > git config --get commit.template [25ms]
2025-06-07 16:31:53.021 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:31:53.069 [info] > git status -z -uall [24ms]
2025-06-07 16:31:53.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:31:58.091 [info] > git config --get commit.template [9ms]
2025-06-07 16:31:58.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:31:58.103 [info] > git status -z -uall [6ms]
2025-06-07 16:31:58.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:33:38.205 [info] > git config --get commit.template [10ms]
2025-06-07 16:33:38.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:33:38.234 [info] > git status -z -uall [17ms]
2025-06-07 16:33:38.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:33:50.309 [info] > git config --get commit.template [11ms]
2025-06-07 16:33:50.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:33:50.338 [info] > git status -z -uall [15ms]
2025-06-07 16:33:50.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:33:55.358 [info] > git config --get commit.template [8ms]
2025-06-07 16:33:55.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:33:55.373 [info] > git status -z -uall [7ms]
2025-06-07 16:33:55.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:35:09.036 [info] > git config --get commit.template [2ms]
2025-06-07 16:35:09.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:35:09.060 [info] > git status -z -uall [8ms]
2025-06-07 16:35:09.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:14.078 [info] > git config --get commit.template [3ms]
2025-06-07 16:35:14.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:35:14.115 [info] > git status -z -uall [9ms]
2025-06-07 16:35:14.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:19.134 [info] > git config --get commit.template [6ms]
2025-06-07 16:35:19.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:35:19.158 [info] > git status -z -uall [6ms]
2025-06-07 16:35:19.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:24.180 [info] > git config --get commit.template [7ms]
2025-06-07 16:35:24.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:35:24.200 [info] > git status -z -uall [7ms]
2025-06-07 16:35:24.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:29.244 [info] > git config --get commit.template [6ms]
2025-06-07 16:35:29.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:35:29.261 [info] > git status -z -uall [10ms]
2025-06-07 16:35:29.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 16:35:34.281 [info] > git config --get commit.template [6ms]
2025-06-07 16:35:34.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:35:34.293 [info] > git status -z -uall [6ms]
2025-06-07 16:35:34.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:35:39.317 [info] > git config --get commit.template [10ms]
2025-06-07 16:35:39.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:35:39.333 [info] > git status -z -uall [6ms]
2025-06-07 16:35:39.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:35:44.359 [info] > git config --get commit.template [9ms]
2025-06-07 16:35:44.360 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:35:44.379 [info] > git status -z -uall [10ms]
2025-06-07 16:35:44.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [75ms]
2025-06-07 16:36:29.507 [info] > git config --get commit.template [3ms]
2025-06-07 16:36:29.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 16:36:29.588 [info] > git status -z -uall [29ms]
2025-06-07 16:36:29.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 16:36:34.611 [info] > git config --get commit.template [9ms]
2025-06-07 16:36:34.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:34.631 [info] > git status -z -uall [8ms]
2025-06-07 16:36:34.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:36:39.654 [info] > git config --get commit.template [9ms]
2025-06-07 16:36:39.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:39.669 [info] > git status -z -uall [6ms]
2025-06-07 16:36:39.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:36:44.690 [info] > git config --get commit.template [6ms]
2025-06-07 16:36:44.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:44.705 [info] > git status -z -uall [7ms]
2025-06-07 16:36:44.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:36:49.736 [info] > git config --get commit.template [14ms]
2025-06-07 16:36:49.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:36:49.757 [info] > git status -z -uall [9ms]
2025-06-07 16:36:49.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:36:54.791 [info] > git config --get commit.template [6ms]
2025-06-07 16:36:54.818 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 16:36:54.843 [info] > git status -z -uall [12ms]
2025-06-07 16:36:54.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:36:59.870 [info] > git config --get commit.template [2ms]
2025-06-07 16:36:59.891 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:36:59.967 [info] > git status -z -uall [50ms]
2025-06-07 16:36:59.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:37:05.106 [info] > git config --get commit.template [33ms]
2025-06-07 16:37:05.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 16:37:05.165 [info] > git status -z -uall [24ms]
2025-06-07 16:37:05.169 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:37:10.293 [info] > git config --get commit.template [111ms]
2025-06-07 16:37:10.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [85ms]
2025-06-07 16:37:10.324 [info] > git status -z -uall [14ms]
2025-06-07 16:37:10.325 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:16.494 [info] > git config --get commit.template [12ms]
2025-06-07 16:37:16.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:37:16.531 [info] > git status -z -uall [22ms]
2025-06-07 16:37:16.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:37:21.557 [info] > git config --get commit.template [8ms]
2025-06-07 16:37:21.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:37:21.575 [info] > git status -z -uall [8ms]
2025-06-07 16:37:21.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:26.616 [info] > git config --get commit.template [19ms]
2025-06-07 16:37:26.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:37:26.657 [info] > git status -z -uall [16ms]
2025-06-07 16:37:26.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:37:31.685 [info] > git config --get commit.template [10ms]
2025-06-07 16:37:31.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:37:31.719 [info] > git status -z -uall [20ms]
2025-06-07 16:37:31.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:36.743 [info] > git config --get commit.template [2ms]
2025-06-07 16:37:36.763 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:37:36.797 [info] > git status -z -uall [22ms]
2025-06-07 16:37:36.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:37:41.823 [info] > git config --get commit.template [1ms]
2025-06-07 16:37:41.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:37:41.845 [info] > git status -z -uall [8ms]
2025-06-07 16:37:41.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:37:46.884 [info] > git config --get commit.template [16ms]
2025-06-07 16:37:46.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:37:46.908 [info] > git status -z -uall [10ms]
2025-06-07 16:37:46.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:06.019 [info] > git config --get commit.template [17ms]
2025-06-07 16:42:06.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:42:06.056 [info] > git status -z -uall [17ms]
2025-06-07 16:42:06.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:42:11.096 [info] > git config --get commit.template [3ms]
2025-06-07 16:42:11.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:42:11.141 [info] > git status -z -uall [15ms]
2025-06-07 16:42:11.142 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:42:16.182 [info] > git config --get commit.template [20ms]
2025-06-07 16:42:16.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:42:16.230 [info] > git status -z -uall [18ms]
2025-06-07 16:42:16.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:42:21.267 [info] > git config --get commit.template [14ms]
2025-06-07 16:42:21.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:21.293 [info] > git status -z -uall [11ms]
2025-06-07 16:42:21.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:42:26.312 [info] > git config --get commit.template [4ms]
2025-06-07 16:42:26.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:26.349 [info] > git status -z -uall [11ms]
2025-06-07 16:42:26.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:42:31.368 [info] > git config --get commit.template [6ms]
2025-06-07 16:42:31.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:31.382 [info] > git status -z -uall [6ms]
2025-06-07 16:42:31.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:36.406 [info] > git config --get commit.template [9ms]
2025-06-07 16:42:36.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:42:36.422 [info] > git status -z -uall [7ms]
2025-06-07 16:42:36.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:41.440 [info] > git config --get commit.template [2ms]
2025-06-07 16:42:41.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:41.466 [info] > git status -z -uall [6ms]
2025-06-07 16:42:41.467 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:42:46.485 [info] > git config --get commit.template [5ms]
2025-06-07 16:42:46.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:42:46.501 [info] > git status -z -uall [9ms]
2025-06-07 16:42:46.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:42:51.521 [info] > git config --get commit.template [6ms]
2025-06-07 16:42:51.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:51.536 [info] > git status -z -uall [7ms]
2025-06-07 16:42:51.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:42:56.558 [info] > git config --get commit.template [8ms]
2025-06-07 16:42:56.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:42:56.571 [info] > git status -z -uall [6ms]
2025-06-07 16:42:56.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:02:31.113 [info] > git config --get commit.template [5ms]
2025-06-07 17:02:31.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:02:31.129 [info] > git status -z -uall [8ms]
2025-06-07 17:02:31.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:03:01.444 [info] > git config --get commit.template [6ms]
2025-06-07 17:03:01.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:03:01.460 [info] > git status -z -uall [9ms]
2025-06-07 17:03:01.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:03:06.481 [info] > git config --get commit.template [7ms]
2025-06-07 17:03:06.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:06.498 [info] > git status -z -uall [6ms]
2025-06-07 17:03:06.499 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:03:11.518 [info] > git config --get commit.template [7ms]
2025-06-07 17:03:11.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:11.533 [info] > git status -z -uall [7ms]
2025-06-07 17:03:11.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:03:16.559 [info] > git config --get commit.template [11ms]
2025-06-07 17:03:16.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:16.582 [info] > git status -z -uall [11ms]
2025-06-07 17:03:16.583 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:03:21.601 [info] > git config --get commit.template [6ms]
2025-06-07 17:03:21.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:21.613 [info] > git status -z -uall [6ms]
2025-06-07 17:03:21.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:03:26.653 [info] > git config --get commit.template [16ms]
2025-06-07 17:03:26.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:26.678 [info] > git status -z -uall [12ms]
2025-06-07 17:03:26.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:03:31.719 [info] > git config --get commit.template [18ms]
2025-06-07 17:03:31.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:03:31.753 [info] > git status -z -uall [16ms]
2025-06-07 17:03:31.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:11:51.538 [info] > git config --get commit.template [6ms]
2025-06-07 17:11:51.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 17:11:51.557 [info] > git status -z -uall [8ms]
2025-06-07 17:11:51.558 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:11:56.573 [info] > git config --get commit.template [6ms]
2025-06-07 17:11:56.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:11:56.588 [info] > git status -z -uall [7ms]
2025-06-07 17:11:56.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:01.607 [info] > git config --get commit.template [6ms]
2025-06-07 17:12:01.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:01.620 [info] > git status -z -uall [6ms]
2025-06-07 17:12:01.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:06.644 [info] > git config --get commit.template [7ms]
2025-06-07 17:12:06.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:06.665 [info] > git status -z -uall [10ms]
2025-06-07 17:12:06.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:11.693 [info] > git config --get commit.template [1ms]
2025-06-07 17:12:11.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 17:12:11.745 [info] > git status -z -uall [14ms]
2025-06-07 17:12:11.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:16.778 [info] > git config --get commit.template [11ms]
2025-06-07 17:12:16.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:16.808 [info] > git status -z -uall [15ms]
2025-06-07 17:12:16.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 17:12:21.828 [info] > git config --get commit.template [1ms]
2025-06-07 17:12:21.847 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:21.865 [info] > git status -z -uall [6ms]
2025-06-07 17:12:21.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:26.898 [info] > git config --get commit.template [8ms]
2025-06-07 17:12:26.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 17:12:26.952 [info] > git status -z -uall [16ms]
2025-06-07 17:12:26.956 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 17:12:31.979 [info] > git config --get commit.template [8ms]
2025-06-07 17:12:31.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:31.991 [info] > git status -z -uall [6ms]
2025-06-07 17:12:31.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 17:12:37.018 [info] > git config --get commit.template [11ms]
2025-06-07 17:12:37.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:37.043 [info] > git status -z -uall [12ms]
2025-06-07 17:12:37.044 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:42.071 [info] > git config --get commit.template [14ms]
2025-06-07 17:12:42.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:42.104 [info] > git status -z -uall [19ms]
2025-06-07 17:12:42.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 17:12:47.905 [info] > git config --get commit.template [10ms]
2025-06-07 17:12:47.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 17:12:47.925 [info] > git status -z -uall [11ms]
2025-06-07 17:12:47.927 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
