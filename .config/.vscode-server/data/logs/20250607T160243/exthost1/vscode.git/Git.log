2025-06-07 16:02:48.630 [info] [main] Log level: Info
2025-06-07 16:02:48.630 [info] [main] Validating found git in: "git"
2025-06-07 16:02:48.630 [info] [main] Using git "2.47.2" from "git"
2025-06-07 16:02:48.630 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --git-dir --git-common-dir [4ms]
2025-06-07 16:02:48.630 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 16:02:48.630 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.630 [info] > git config --get commit.template [7ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [5ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [23ms]
2025-06-07 16:02:48.630 [info] > git status -z -uall [31ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms]
2025-06-07 16:02:48.630 [info] > git config --get commit.template [24ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [28ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [118ms]
2025-06-07 16:02:48.630 [info] > git config --get --local branch.main.vscode-merge-base [16ms]
2025-06-07 16:02:48.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 16:02:48.630 [info] > git rev-parse --show-toplevel [20ms]
2025-06-07 16:02:48.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [61ms]
2025-06-07 16:02:48.703 [info] > git rev-parse --show-toplevel [116ms]
2025-06-07 16:02:48.730 [info] > git status -z -uall [24ms]
2025-06-07 16:02:48.730 [info] > git merge-base refs/heads/main refs/remotes/origin/main [94ms]
2025-06-07 16:02:48.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [36ms]
2025-06-07 16:02:48.751 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 16:02:48.751 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [15ms]
2025-06-07 16:02:48.755 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-07 16:02:48.768 [info] > git rev-parse --show-toplevel [13ms]
2025-06-07 16:02:48.768 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [10ms]
2025-06-07 16:02:48.774 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 16:02:48.777 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 16:02:53.730 [info] > git config --get commit.template [2ms]
2025-06-07 16:02:53.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:02:53.747 [info] > git status -z -uall [7ms]
2025-06-07 16:02:53.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:03:01.733 [info] > git config --get commit.template [4ms]
2025-06-07 16:03:01.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:01.743 [info] > git status -z -uall [5ms]
2025-06-07 16:03:01.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:06.756 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:06.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:06.777 [info] > git status -z -uall [7ms]
2025-06-07 16:03:06.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:03:11.791 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:11.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:11.811 [info] > git status -z -uall [4ms]
2025-06-07 16:03:11.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:03:16.823 [info] > git config --get commit.template [0ms]
2025-06-07 16:03:16.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:16.837 [info] > git status -z -uall [6ms]
2025-06-07 16:03:16.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:21.855 [info] > git config --get commit.template [8ms]
2025-06-07 16:03:21.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:21.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:21.884 [info] > git status -z -uall [16ms]
2025-06-07 16:03:26.902 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:26.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 16:03:26.943 [info] > git status -z -uall [11ms]
2025-06-07 16:03:26.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:03:31.961 [info] > git config --get commit.template [2ms]
2025-06-07 16:03:31.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:03:31.976 [info] > git status -z -uall [6ms]
2025-06-07 16:03:31.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:36.995 [info] > git config --get commit.template [6ms]
2025-06-07 16:03:36.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:03:37.014 [info] > git status -z -uall [9ms]
2025-06-07 16:03:37.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:42.064 [info] > git config --get commit.template [13ms]
2025-06-07 16:03:42.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:42.106 [info] > git status -z -uall [11ms]
2025-06-07 16:03:42.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:03:47.127 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:47.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 16:03:47.149 [info] > git status -z -uall [6ms]
2025-06-07 16:03:47.150 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:03:52.163 [info] > git config --get commit.template [3ms]
2025-06-07 16:03:52.172 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:03:52.197 [info] > git status -z -uall [9ms]
2025-06-07 16:03:52.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 16:03:57.216 [info] > git config --get commit.template [1ms]
2025-06-07 16:03:57.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:03:57.274 [info] > git status -z -uall [23ms]
2025-06-07 16:03:57.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:02.290 [info] > git config --get commit.template [7ms]
2025-06-07 16:04:02.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:04:02.316 [info] > git status -z -uall [9ms]
2025-06-07 16:04:02.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:07.335 [info] > git config --get commit.template [7ms]
2025-06-07 16:04:07.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:07.357 [info] > git status -z -uall [10ms]
2025-06-07 16:04:07.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 16:04:12.375 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:12.384 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:12.419 [info] > git status -z -uall [20ms]
2025-06-07 16:04:12.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:19.856 [info] > git config --get commit.template [11ms]
2025-06-07 16:04:19.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 16:04:19.881 [info] > git status -z -uall [13ms]
2025-06-07 16:04:19.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:04:24.893 [info] > git config --get commit.template [3ms]
2025-06-07 16:04:24.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:24.901 [info] > git status -z -uall [4ms]
2025-06-07 16:04:24.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:29.917 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:29.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:29.931 [info] > git status -z -uall [7ms]
2025-06-07 16:04:29.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:34.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:34.946 [info] > git config --get commit.template [5ms]
2025-06-07 16:04:34.953 [info] > git status -z -uall [4ms]
2025-06-07 16:04:34.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:04:39.966 [info] > git config --get commit.template [4ms]
2025-06-07 16:04:39.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:04:39.979 [info] > git status -z -uall [5ms]
2025-06-07 16:04:39.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:46.044 [info] > git config --get commit.template [4ms]
2025-06-07 16:04:46.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:46.085 [info] > git status -z -uall [18ms]
2025-06-07 16:04:46.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:04:51.099 [info] > git config --get commit.template [2ms]
2025-06-07 16:04:51.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:04:51.113 [info] > git status -z -uall [6ms]
2025-06-07 16:04:51.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:05:45.210 [info] > git config --get commit.template [3ms]
2025-06-07 16:05:45.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:05:45.218 [info] > git status -z -uall [3ms]
2025-06-07 16:05:45.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:05:50.309 [info] > git config --get commit.template [3ms]
2025-06-07 16:05:50.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:05:50.322 [info] > git status -z -uall [4ms]
2025-06-07 16:05:50.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:05:55.335 [info] > git config --get commit.template [4ms]
2025-06-07 16:05:55.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:05:55.343 [info] > git status -z -uall [3ms]
2025-06-07 16:05:55.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:00.357 [info] > git config --get commit.template [6ms]
2025-06-07 16:06:00.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:00.365 [info] > git status -z -uall [4ms]
2025-06-07 16:06:00.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:06:05.378 [info] > git config --get commit.template [1ms]
2025-06-07 16:06:05.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:05.396 [info] > git status -z -uall [6ms]
2025-06-07 16:06:05.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:10.410 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:10.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:10.563 [info] > git status -z -uall [146ms]
2025-06-07 16:06:10.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [182ms]
2025-06-07 16:06:15.622 [info] > git config --get commit.template [3ms]
2025-06-07 16:06:15.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:15.634 [info] > git status -z -uall [5ms]
2025-06-07 16:06:15.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:06:20.647 [info] > git config --get commit.template [5ms]
2025-06-07 16:06:20.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:06:20.656 [info] > git status -z -uall [4ms]
2025-06-07 16:06:20.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:06:25.668 [info] > git config --get commit.template [4ms]
2025-06-07 16:06:25.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:06:25.676 [info] > git status -z -uall [3ms]
2025-06-07 16:06:25.678 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
