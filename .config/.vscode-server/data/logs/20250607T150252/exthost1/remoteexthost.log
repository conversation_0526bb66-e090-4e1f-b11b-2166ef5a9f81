2025-06-07 15:03:07.131 [info] Extension host with pid 210 started
2025-06-07 15:03:07.131 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock'
2025-06-07 15:03:07.131 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-07 15:03:07.135 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': The pid 1981 appears to be gone.
2025-06-07 15:03:07.135 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Deleting a stale lock.
2025-06-07 15:03:07.147 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Lock acquired.
2025-06-07 15:03:07.367 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-06-07 15:03:07.368 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-07 15:03:07.369 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-07 15:03:07.369 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-07 15:03:07.374 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-07 15:03:07.375 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-07 15:03:08.149 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-07 15:03:08.149 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-07 15:03:08.436 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-07 15:03:08.997 [info] Eager extensions activated
2025-06-07 15:03:08.997 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 15:03:08.998 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 15:03:09.498 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-07 15:03:14.447 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 15:03:15.359 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at n_e.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
