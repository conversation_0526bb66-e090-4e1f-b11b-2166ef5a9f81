2025-06-07 15:03:09.051 [info] [main] Log level: Info
2025-06-07 15:03:09.051 [info] [main] Validating found git in: "git"
2025-06-07 15:03:09.053 [info] [main] Using git "2.47.2" from "git"
2025-06-07 15:03:09.053 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [25ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-07 15:03:09.053 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 15:03:09.053 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 15:03:09.053 [info] > git config --get commit.template [5ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 15:03:09.053 [info] > git status -z -uall [9ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [14ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [80ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [135ms]
2025-06-07 15:03:09.053 [info] > git config --get --local branch.main.vscode-merge-base [51ms]
2025-06-07 15:03:09.053 [info] > git config --get commit.template [137ms]
2025-06-07 15:03:09.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [13ms]
2025-06-07 15:03:09.072 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 15:03:09.086 [info] > git rev-parse --show-toplevel [9ms]
2025-06-07 15:03:09.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 15:03:09.087 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-07 15:03:09.106 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 15:03:09.106 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [16ms]
2025-06-07 15:03:09.120 [info] > git status -z -uall [4ms]
2025-06-07 15:03:09.121 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 15:03:09.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:03:09.166 [info] > git rev-parse --show-toplevel [36ms]
2025-06-07 15:03:09.262 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:03:09.267 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:03:09.287 [info] > git rev-parse --show-toplevel [14ms]
2025-06-07 15:03:09.292 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:03:09.296 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 15:03:30.291 [info] > git config --get commit.template [3ms]
2025-06-07 15:03:30.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:03:30.299 [info] > git status -z -uall [4ms]
2025-06-07 15:03:30.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:03:35.311 [info] > git config --get commit.template [4ms]
2025-06-07 15:03:35.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:03:35.320 [info] > git status -z -uall [3ms]
2025-06-07 15:03:35.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:03:40.329 [info] > git config --get commit.template [1ms]
2025-06-07 15:03:40.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:03:40.401 [info] > git status -z -uall [64ms]
2025-06-07 15:03:40.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 15:07:30.063 [info] > git config --get commit.template [12ms]
2025-06-07 15:07:30.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:07:30.093 [info] > git status -z -uall [14ms]
2025-06-07 15:07:30.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:35.109 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:35.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:35.121 [info] > git status -z -uall [6ms]
2025-06-07 15:07:35.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
