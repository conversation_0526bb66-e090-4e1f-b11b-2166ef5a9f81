2025-06-07 15:03:09.051 [info] [main] Log level: Info
2025-06-07 15:03:09.051 [info] [main] Validating found git in: "git"
2025-06-07 15:03:09.053 [info] [main] Using git "2.47.2" from "git"
2025-06-07 15:03:09.053 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [25ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-07 15:03:09.053 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 15:03:09.053 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 15:03:09.053 [info] > git config --get commit.template [5ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 15:03:09.053 [info] > git status -z -uall [9ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [14ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [80ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [135ms]
2025-06-07 15:03:09.053 [info] > git config --get --local branch.main.vscode-merge-base [51ms]
2025-06-07 15:03:09.053 [info] > git config --get commit.template [137ms]
2025-06-07 15:03:09.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [13ms]
2025-06-07 15:03:09.072 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 15:03:09.086 [info] > git rev-parse --show-toplevel [9ms]
2025-06-07 15:03:09.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 15:03:09.087 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-07 15:03:09.106 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 15:03:09.106 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [16ms]
2025-06-07 15:03:09.120 [info] > git status -z -uall [4ms]
2025-06-07 15:03:09.121 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 15:03:09.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:03:09.166 [info] > git rev-parse --show-toplevel [36ms]
2025-06-07 15:03:09.262 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:03:09.267 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:03:09.287 [info] > git rev-parse --show-toplevel [14ms]
2025-06-07 15:03:09.292 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:03:09.296 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 15:03:30.291 [info] > git config --get commit.template [3ms]
2025-06-07 15:03:30.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:03:30.299 [info] > git status -z -uall [4ms]
2025-06-07 15:03:30.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:03:35.311 [info] > git config --get commit.template [4ms]
2025-06-07 15:03:35.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:03:35.320 [info] > git status -z -uall [3ms]
2025-06-07 15:03:35.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:03:40.329 [info] > git config --get commit.template [1ms]
2025-06-07 15:03:40.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:03:40.401 [info] > git status -z -uall [64ms]
2025-06-07 15:03:40.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 15:07:30.063 [info] > git config --get commit.template [12ms]
2025-06-07 15:07:30.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:07:30.093 [info] > git status -z -uall [14ms]
2025-06-07 15:07:30.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:35.109 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:35.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:35.121 [info] > git status -z -uall [6ms]
2025-06-07 15:07:35.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:07:40.139 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:40.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:40.152 [info] > git status -z -uall [5ms]
2025-06-07 15:07:40.153 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:45.168 [info] > git config --get commit.template [6ms]
2025-06-07 15:07:45.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:45.179 [info] > git status -z -uall [5ms]
2025-06-07 15:07:45.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:50.196 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:50.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:50.210 [info] > git status -z -uall [8ms]
2025-06-07 15:07:50.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:07:55.228 [info] > git config --get commit.template [6ms]
2025-06-07 15:07:55.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:55.242 [info] > git status -z -uall [6ms]
2025-06-07 15:07:55.244 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:01.596 [info] > git config --get commit.template [8ms]
2025-06-07 15:15:01.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:01.610 [info] > git status -z -uall [8ms]
2025-06-07 15:15:01.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:06.635 [info] > git config --get commit.template [9ms]
2025-06-07 15:15:06.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:15:06.658 [info] > git status -z -uall [10ms]
2025-06-07 15:15:06.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:11.677 [info] > git config --get commit.template [7ms]
2025-06-07 15:15:11.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:15:11.689 [info] > git status -z -uall [5ms]
2025-06-07 15:15:11.690 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:15:20.828 [info] > git config --get commit.template [6ms]
2025-06-07 15:15:20.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:20.839 [info] > git status -z -uall [5ms]
2025-06-07 15:15:20.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:25.855 [info] > git config --get commit.template [5ms]
2025-06-07 15:15:25.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:25.870 [info] > git status -z -uall [7ms]
2025-06-07 15:15:25.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:15:30.895 [info] > git config --get commit.template [9ms]
2025-06-07 15:15:30.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:30.915 [info] > git status -z -uall [8ms]
2025-06-07 15:15:30.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:35.951 [info] > git config --get commit.template [11ms]
2025-06-07 15:15:35.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 15:15:36.018 [info] > git status -z -uall [8ms]
2025-06-07 15:15:36.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:41.036 [info] > git config --get commit.template [6ms]
2025-06-07 15:15:41.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:41.048 [info] > git status -z -uall [4ms]
2025-06-07 15:15:41.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:15:46.067 [info] > git config --get commit.template [5ms]
2025-06-07 15:15:46.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:46.083 [info] > git status -z -uall [8ms]
2025-06-07 15:15:46.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:17:52.235 [info] > git config --get commit.template [5ms]
2025-06-07 15:17:52.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:17:52.252 [info] > git status -z -uall [9ms]
2025-06-07 15:17:52.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:17:57.278 [info] > git config --get commit.template [8ms]
2025-06-07 15:17:57.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:17:57.301 [info] > git status -z -uall [14ms]
2025-06-07 15:17:57.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:18:02.325 [info] > git config --get commit.template [10ms]
2025-06-07 15:18:02.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:02.340 [info] > git status -z -uall [9ms]
2025-06-07 15:18:02.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:07.370 [info] > git config --get commit.template [14ms]
2025-06-07 15:18:07.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:07.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:18:07.421 [info] > git status -z -uall [41ms]
2025-06-07 15:18:12.445 [info] > git config --get commit.template [7ms]
2025-06-07 15:18:12.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:12.457 [info] > git status -z -uall [5ms]
2025-06-07 15:18:12.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:17.478 [info] > git config --get commit.template [7ms]
2025-06-07 15:18:17.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:17.494 [info] > git status -z -uall [7ms]
2025-06-07 15:18:17.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:22.514 [info] > git config --get commit.template [7ms]
2025-06-07 15:18:22.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:22.525 [info] > git status -z -uall [6ms]
2025-06-07 15:18:22.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:27.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:27.547 [info] > git config --get commit.template [8ms]
2025-06-07 15:18:27.559 [info] > git status -z -uall [5ms]
2025-06-07 15:18:27.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:18:32.581 [info] > git config --get commit.template [9ms]
2025-06-07 15:18:32.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:32.598 [info] > git status -z -uall [7ms]
2025-06-07 15:18:32.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:37.620 [info] > git config --get commit.template [10ms]
2025-06-07 15:18:37.621 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:37.640 [info] > git status -z -uall [10ms]
2025-06-07 15:18:37.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:18:42.664 [info] > git config --get commit.template [10ms]
2025-06-07 15:18:42.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:42.679 [info] > git status -z -uall [8ms]
2025-06-07 15:18:42.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:18:47.700 [info] > git config --get commit.template [9ms]
2025-06-07 15:18:47.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:47.717 [info] > git status -z -uall [7ms]
2025-06-07 15:18:47.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:18:52.735 [info] > git config --get commit.template [5ms]
2025-06-07 15:18:52.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:52.748 [info] > git status -z -uall [7ms]
2025-06-07 15:18:52.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:57.785 [info] > git config --get commit.template [14ms]
2025-06-07 15:18:57.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:18:57.807 [info] > git status -z -uall [9ms]
2025-06-07 15:18:57.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:19:02.831 [info] > git config --get commit.template [9ms]
2025-06-07 15:19:02.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:02.853 [info] > git status -z -uall [13ms]
2025-06-07 15:19:02.856 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:19:07.875 [info] > git config --get commit.template [7ms]
2025-06-07 15:19:07.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:07.890 [info] > git status -z -uall [8ms]
2025-06-07 15:19:07.891 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:12.911 [info] > git config --get commit.template [10ms]
2025-06-07 15:19:12.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:19:12.929 [info] > git status -z -uall [5ms]
2025-06-07 15:19:12.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:17.967 [info] > git config --get commit.template [19ms]
2025-06-07 15:19:17.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 15:19:18.047 [info] > git status -z -uall [32ms]
2025-06-07 15:19:18.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 15:19:23.071 [info] > git config --get commit.template [9ms]
2025-06-07 15:19:23.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:23.088 [info] > git status -z -uall [8ms]
2025-06-07 15:19:23.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:19:28.112 [info] > git config --get commit.template [6ms]
2025-06-07 15:19:28.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:28.131 [info] > git status -z -uall [9ms]
2025-06-07 15:19:28.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:33.150 [info] > git config --get commit.template [7ms]
2025-06-07 15:19:33.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:33.169 [info] > git status -z -uall [8ms]
2025-06-07 15:19:33.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:38.202 [info] > git config --get commit.template [11ms]
2025-06-07 15:19:38.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:38.218 [info] > git status -z -uall [10ms]
2025-06-07 15:19:38.218 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:19:43.228 [info] > git config --get commit.template [2ms]
2025-06-07 15:19:43.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:43.249 [info] > git status -z -uall [5ms]
2025-06-07 15:19:43.250 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:48.429 [info] > git config --get commit.template [4ms]
2025-06-07 15:19:48.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:48.448 [info] > git status -z -uall [10ms]
2025-06-07 15:19:48.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:19:53.466 [info] > git config --get commit.template [9ms]
2025-06-07 15:19:53.467 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:53.486 [info] > git status -z -uall [9ms]
2025-06-07 15:19:53.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:58.514 [info] > git config --get commit.template [10ms]
2025-06-07 15:19:58.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:58.544 [info] > git status -z -uall [20ms]
2025-06-07 15:19:58.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-07 15:20:03.578 [info] > git config --get commit.template [9ms]
2025-06-07 15:20:03.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:20:03.596 [info] > git status -z -uall [12ms]
2025-06-07 15:20:03.597 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:08.659 [info] > git config --get commit.template [27ms]
2025-06-07 15:20:08.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 15:20:08.708 [info] > git status -z -uall [18ms]
2025-06-07 15:20:08.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-07 15:20:13.740 [info] > git config --get commit.template [9ms]
2025-06-07 15:20:13.742 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:20:13.761 [info] > git status -z -uall [10ms]
2025-06-07 15:20:13.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:20:18.799 [info] > git config --get commit.template [13ms]
2025-06-07 15:20:18.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:20:18.836 [info] > git status -z -uall [19ms]
2025-06-07 15:20:18.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:27.516 [info] > git config --get commit.template [4ms]
2025-06-07 15:20:27.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:20:27.555 [info] > git status -z -uall [10ms]
2025-06-07 15:20:27.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 15:20:34.559 [info] > git config --get commit.template [2ms]
2025-06-07 15:20:34.574 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:20:34.590 [info] > git status -z -uall [8ms]
2025-06-07 15:20:34.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:39.627 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:20:39.627 [info] > git config --get commit.template [20ms]
2025-06-07 15:20:39.656 [info] > git status -z -uall [18ms]
2025-06-07 15:20:39.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:44.687 [info] > git config --get commit.template [16ms]
2025-06-07 15:20:44.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 15:20:44.738 [info] > git status -z -uall [28ms]
2025-06-07 15:20:44.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 15:20:54.611 [info] > git config --get commit.template [5ms]
2025-06-07 15:20:54.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:20:54.623 [info] > git status -z -uall [5ms]
2025-06-07 15:20:54.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:00.655 [info] > git config --get commit.template [5ms]
2025-06-07 15:21:00.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:00.664 [info] > git status -z -uall [4ms]
2025-06-07 15:21:00.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:21:05.696 [info] > git config --get commit.template [12ms]
2025-06-07 15:21:05.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:05.710 [info] > git status -z -uall [7ms]
2025-06-07 15:21:05.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:21:10.730 [info] > git config --get commit.template [8ms]
2025-06-07 15:21:10.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:10.749 [info] > git status -z -uall [8ms]
2025-06-07 15:21:10.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:21:15.769 [info] > git config --get commit.template [9ms]
2025-06-07 15:21:15.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:15.779 [info] > git status -z -uall [4ms]
2025-06-07 15:21:15.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:21:25.664 [info] > git config --get commit.template [8ms]
2025-06-07 15:21:25.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:25.679 [info] > git status -z -uall [8ms]
2025-06-07 15:21:25.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:30.698 [info] > git config --get commit.template [8ms]
2025-06-07 15:21:30.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:21:30.717 [info] > git status -z -uall [9ms]
2025-06-07 15:21:30.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:35.737 [info] > git config --get commit.template [9ms]
2025-06-07 15:21:35.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:35.754 [info] > git status -z -uall [8ms]
2025-06-07 15:21:35.755 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:40.770 [info] > git config --get commit.template [5ms]
2025-06-07 15:21:40.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:40.781 [info] > git status -z -uall [5ms]
2025-06-07 15:21:40.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:45.801 [info] > git config --get commit.template [9ms]
2025-06-07 15:21:45.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:45.814 [info] > git status -z -uall [5ms]
2025-06-07 15:21:45.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:50.827 [info] > git config --get commit.template [4ms]
2025-06-07 15:21:50.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:21:50.841 [info] > git status -z -uall [8ms]
2025-06-07 15:21:50.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:55.858 [info] > git config --get commit.template [7ms]
2025-06-07 15:21:55.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:55.877 [info] > git status -z -uall [9ms]
2025-06-07 15:21:55.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:00.902 [info] > git config --get commit.template [11ms]
2025-06-07 15:22:00.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:00.914 [info] > git status -z -uall [5ms]
2025-06-07 15:22:00.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:22:05.935 [info] > git config --get commit.template [10ms]
2025-06-07 15:22:05.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:05.952 [info] > git status -z -uall [8ms]
2025-06-07 15:22:05.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:10.973 [info] > git config --get commit.template [9ms]
2025-06-07 15:22:10.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:10.990 [info] > git status -z -uall [8ms]
2025-06-07 15:22:10.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:16.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 15:22:16.680 [info] > git config --get commit.template [12ms]
2025-06-07 15:22:16.693 [info] > git status -z -uall [6ms]
2025-06-07 15:22:16.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:21.714 [info] > git config --get commit.template [6ms]
2025-06-07 15:22:21.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:21.726 [info] > git status -z -uall [6ms]
2025-06-07 15:22:21.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:26.747 [info] > git config --get commit.template [10ms]
2025-06-07 15:22:26.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:26.764 [info] > git status -z -uall [7ms]
2025-06-07 15:22:26.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:31.787 [info] > git config --get commit.template [10ms]
2025-06-07 15:22:31.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:31.799 [info] > git status -z -uall [5ms]
2025-06-07 15:22:31.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:36.817 [info] > git config --get commit.template [6ms]
2025-06-07 15:22:36.818 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:36.830 [info] > git status -z -uall [5ms]
2025-06-07 15:22:36.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:41.854 [info] > git config --get commit.template [9ms]
2025-06-07 15:22:41.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:41.873 [info] > git status -z -uall [11ms]
2025-06-07 15:22:41.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:46.892 [info] > git config --get commit.template [8ms]
2025-06-07 15:22:46.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:46.906 [info] > git status -z -uall [8ms]
2025-06-07 15:22:46.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:51.919 [info] > git config --get commit.template [4ms]
2025-06-07 15:22:51.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:51.936 [info] > git status -z -uall [9ms]
2025-06-07 15:22:51.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:22:56.955 [info] > git config --get commit.template [6ms]
2025-06-07 15:22:56.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:22:56.977 [info] > git status -z -uall [12ms]
2025-06-07 15:22:56.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:23:01.992 [info] > git config --get commit.template [0ms]
2025-06-07 15:23:02.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:02.023 [info] > git status -z -uall [11ms]
2025-06-07 15:23:02.024 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:07.086 [info] > git config --get commit.template [9ms]
2025-06-07 15:23:07.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:07.109 [info] > git status -z -uall [13ms]
2025-06-07 15:23:07.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 15:23:12.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:12.135 [info] > git config --get commit.template [13ms]
2025-06-07 15:23:12.155 [info] > git status -z -uall [10ms]
2025-06-07 15:23:12.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:23:17.173 [info] > git config --get commit.template [2ms]
2025-06-07 15:23:17.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:23:17.202 [info] > git status -z -uall [7ms]
2025-06-07 15:23:17.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
