2025-06-07 15:03:09.051 [info] [main] Log level: Info
2025-06-07 15:03:09.051 [info] [main] Validating found git in: "git"
2025-06-07 15:03:09.053 [info] [main] Using git "2.47.2" from "git"
2025-06-07 15:03:09.053 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [25ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-07 15:03:09.053 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 15:03:09.053 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 15:03:09.053 [info] > git config --get commit.template [5ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 15:03:09.053 [info] > git status -z -uall [9ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [14ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-06-07 15:03:09.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [80ms]
2025-06-07 15:03:09.053 [info] > git rev-parse --show-toplevel [135ms]
2025-06-07 15:03:09.053 [info] > git config --get --local branch.main.vscode-merge-base [51ms]
2025-06-07 15:03:09.053 [info] > git config --get commit.template [137ms]
2025-06-07 15:03:09.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [13ms]
2025-06-07 15:03:09.072 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 15:03:09.086 [info] > git rev-parse --show-toplevel [9ms]
2025-06-07 15:03:09.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 15:03:09.087 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-07 15:03:09.106 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 15:03:09.106 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [16ms]
2025-06-07 15:03:09.120 [info] > git status -z -uall [4ms]
2025-06-07 15:03:09.121 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 15:03:09.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:03:09.166 [info] > git rev-parse --show-toplevel [36ms]
2025-06-07 15:03:09.262 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:03:09.267 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:03:09.287 [info] > git rev-parse --show-toplevel [14ms]
2025-06-07 15:03:09.292 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:03:09.296 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 15:03:30.291 [info] > git config --get commit.template [3ms]
2025-06-07 15:03:30.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:03:30.299 [info] > git status -z -uall [4ms]
2025-06-07 15:03:30.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:03:35.311 [info] > git config --get commit.template [4ms]
2025-06-07 15:03:35.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:03:35.320 [info] > git status -z -uall [3ms]
2025-06-07 15:03:35.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:03:40.329 [info] > git config --get commit.template [1ms]
2025-06-07 15:03:40.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:03:40.401 [info] > git status -z -uall [64ms]
2025-06-07 15:03:40.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [63ms]
2025-06-07 15:07:30.063 [info] > git config --get commit.template [12ms]
2025-06-07 15:07:30.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:07:30.093 [info] > git status -z -uall [14ms]
2025-06-07 15:07:30.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:35.109 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:35.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:35.121 [info] > git status -z -uall [6ms]
2025-06-07 15:07:35.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:07:40.139 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:40.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:40.152 [info] > git status -z -uall [5ms]
2025-06-07 15:07:40.153 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:45.168 [info] > git config --get commit.template [6ms]
2025-06-07 15:07:45.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:45.179 [info] > git status -z -uall [5ms]
2025-06-07 15:07:45.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:07:50.196 [info] > git config --get commit.template [5ms]
2025-06-07 15:07:50.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:50.210 [info] > git status -z -uall [8ms]
2025-06-07 15:07:50.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:07:55.228 [info] > git config --get commit.template [6ms]
2025-06-07 15:07:55.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:07:55.242 [info] > git status -z -uall [6ms]
2025-06-07 15:07:55.244 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:01.596 [info] > git config --get commit.template [8ms]
2025-06-07 15:15:01.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:01.610 [info] > git status -z -uall [8ms]
2025-06-07 15:15:01.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:06.635 [info] > git config --get commit.template [9ms]
2025-06-07 15:15:06.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:15:06.658 [info] > git status -z -uall [10ms]
2025-06-07 15:15:06.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:11.677 [info] > git config --get commit.template [7ms]
2025-06-07 15:15:11.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:15:11.689 [info] > git status -z -uall [5ms]
2025-06-07 15:15:11.690 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:15:20.828 [info] > git config --get commit.template [6ms]
2025-06-07 15:15:20.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:20.839 [info] > git status -z -uall [5ms]
2025-06-07 15:15:20.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:25.855 [info] > git config --get commit.template [5ms]
2025-06-07 15:15:25.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:25.870 [info] > git status -z -uall [7ms]
2025-06-07 15:15:25.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:15:30.895 [info] > git config --get commit.template [9ms]
2025-06-07 15:15:30.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:30.915 [info] > git status -z -uall [8ms]
2025-06-07 15:15:30.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:35.951 [info] > git config --get commit.template [11ms]
2025-06-07 15:15:35.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 15:15:36.018 [info] > git status -z -uall [8ms]
2025-06-07 15:15:36.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:15:41.036 [info] > git config --get commit.template [6ms]
2025-06-07 15:15:41.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:41.048 [info] > git status -z -uall [4ms]
2025-06-07 15:15:41.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:15:46.067 [info] > git config --get commit.template [5ms]
2025-06-07 15:15:46.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:15:46.083 [info] > git status -z -uall [8ms]
2025-06-07 15:15:46.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:17:52.235 [info] > git config --get commit.template [5ms]
2025-06-07 15:17:52.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:17:52.252 [info] > git status -z -uall [9ms]
2025-06-07 15:17:52.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:17:57.278 [info] > git config --get commit.template [8ms]
2025-06-07 15:17:57.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:17:57.301 [info] > git status -z -uall [14ms]
2025-06-07 15:17:57.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:18:02.325 [info] > git config --get commit.template [10ms]
2025-06-07 15:18:02.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:02.340 [info] > git status -z -uall [9ms]
2025-06-07 15:18:02.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:07.370 [info] > git config --get commit.template [14ms]
2025-06-07 15:18:07.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:07.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:18:07.421 [info] > git status -z -uall [41ms]
2025-06-07 15:18:12.445 [info] > git config --get commit.template [7ms]
2025-06-07 15:18:12.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:12.457 [info] > git status -z -uall [5ms]
2025-06-07 15:18:12.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:17.478 [info] > git config --get commit.template [7ms]
2025-06-07 15:18:17.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:17.494 [info] > git status -z -uall [7ms]
2025-06-07 15:18:17.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:22.514 [info] > git config --get commit.template [7ms]
2025-06-07 15:18:22.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:22.525 [info] > git status -z -uall [6ms]
2025-06-07 15:18:22.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:27.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:27.547 [info] > git config --get commit.template [8ms]
2025-06-07 15:18:27.559 [info] > git status -z -uall [5ms]
2025-06-07 15:18:27.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:18:32.581 [info] > git config --get commit.template [9ms]
2025-06-07 15:18:32.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:32.598 [info] > git status -z -uall [7ms]
2025-06-07 15:18:32.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:37.620 [info] > git config --get commit.template [10ms]
2025-06-07 15:18:37.621 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:37.640 [info] > git status -z -uall [10ms]
2025-06-07 15:18:37.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:18:42.664 [info] > git config --get commit.template [10ms]
2025-06-07 15:18:42.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:42.679 [info] > git status -z -uall [8ms]
2025-06-07 15:18:42.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:18:47.700 [info] > git config --get commit.template [9ms]
2025-06-07 15:18:47.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:18:47.717 [info] > git status -z -uall [7ms]
2025-06-07 15:18:47.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:18:52.735 [info] > git config --get commit.template [5ms]
2025-06-07 15:18:52.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:18:52.748 [info] > git status -z -uall [7ms]
2025-06-07 15:18:52.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:18:57.785 [info] > git config --get commit.template [14ms]
2025-06-07 15:18:57.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:18:57.807 [info] > git status -z -uall [9ms]
2025-06-07 15:18:57.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:19:02.831 [info] > git config --get commit.template [9ms]
2025-06-07 15:19:02.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:02.853 [info] > git status -z -uall [13ms]
2025-06-07 15:19:02.856 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:19:07.875 [info] > git config --get commit.template [7ms]
2025-06-07 15:19:07.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:07.890 [info] > git status -z -uall [8ms]
2025-06-07 15:19:07.891 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:12.911 [info] > git config --get commit.template [10ms]
2025-06-07 15:19:12.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:19:12.929 [info] > git status -z -uall [5ms]
2025-06-07 15:19:12.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:17.967 [info] > git config --get commit.template [19ms]
2025-06-07 15:19:17.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 15:19:18.047 [info] > git status -z -uall [32ms]
2025-06-07 15:19:18.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 15:19:23.071 [info] > git config --get commit.template [9ms]
2025-06-07 15:19:23.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:23.088 [info] > git status -z -uall [8ms]
2025-06-07 15:19:23.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:19:28.112 [info] > git config --get commit.template [6ms]
2025-06-07 15:19:28.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:28.131 [info] > git status -z -uall [9ms]
2025-06-07 15:19:28.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:33.150 [info] > git config --get commit.template [7ms]
2025-06-07 15:19:33.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:33.169 [info] > git status -z -uall [8ms]
2025-06-07 15:19:33.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:38.202 [info] > git config --get commit.template [11ms]
2025-06-07 15:19:38.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:38.218 [info] > git status -z -uall [10ms]
2025-06-07 15:19:38.218 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:19:43.228 [info] > git config --get commit.template [2ms]
2025-06-07 15:19:43.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:43.249 [info] > git status -z -uall [5ms]
2025-06-07 15:19:43.250 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:48.429 [info] > git config --get commit.template [4ms]
2025-06-07 15:19:48.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:48.448 [info] > git status -z -uall [10ms]
2025-06-07 15:19:48.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:19:53.466 [info] > git config --get commit.template [9ms]
2025-06-07 15:19:53.467 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:19:53.486 [info] > git status -z -uall [9ms]
2025-06-07 15:19:53.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:19:58.514 [info] > git config --get commit.template [10ms]
2025-06-07 15:19:58.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:19:58.544 [info] > git status -z -uall [20ms]
2025-06-07 15:19:58.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-07 15:20:03.578 [info] > git config --get commit.template [9ms]
2025-06-07 15:20:03.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:20:03.596 [info] > git status -z -uall [12ms]
2025-06-07 15:20:03.597 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:08.659 [info] > git config --get commit.template [27ms]
2025-06-07 15:20:08.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-07 15:20:08.708 [info] > git status -z -uall [18ms]
2025-06-07 15:20:08.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-07 15:20:13.740 [info] > git config --get commit.template [9ms]
2025-06-07 15:20:13.742 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:20:13.761 [info] > git status -z -uall [10ms]
2025-06-07 15:20:13.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:20:18.799 [info] > git config --get commit.template [13ms]
2025-06-07 15:20:18.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:20:18.836 [info] > git status -z -uall [19ms]
2025-06-07 15:20:18.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:27.516 [info] > git config --get commit.template [4ms]
2025-06-07 15:20:27.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:20:27.555 [info] > git status -z -uall [10ms]
2025-06-07 15:20:27.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 15:20:34.559 [info] > git config --get commit.template [2ms]
2025-06-07 15:20:34.574 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:20:34.590 [info] > git status -z -uall [8ms]
2025-06-07 15:20:34.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:39.627 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:20:39.627 [info] > git config --get commit.template [20ms]
2025-06-07 15:20:39.656 [info] > git status -z -uall [18ms]
2025-06-07 15:20:39.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:20:44.687 [info] > git config --get commit.template [16ms]
2025-06-07 15:20:44.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-07 15:20:44.738 [info] > git status -z -uall [28ms]
2025-06-07 15:20:44.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 15:20:54.611 [info] > git config --get commit.template [5ms]
2025-06-07 15:20:54.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:20:54.623 [info] > git status -z -uall [5ms]
2025-06-07 15:20:54.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:00.655 [info] > git config --get commit.template [5ms]
2025-06-07 15:21:00.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:00.664 [info] > git status -z -uall [4ms]
2025-06-07 15:21:00.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:21:05.696 [info] > git config --get commit.template [12ms]
2025-06-07 15:21:05.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:05.710 [info] > git status -z -uall [7ms]
2025-06-07 15:21:05.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:21:10.730 [info] > git config --get commit.template [8ms]
2025-06-07 15:21:10.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:10.749 [info] > git status -z -uall [8ms]
2025-06-07 15:21:10.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:21:15.769 [info] > git config --get commit.template [9ms]
2025-06-07 15:21:15.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:15.779 [info] > git status -z -uall [4ms]
2025-06-07 15:21:15.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:21:25.664 [info] > git config --get commit.template [8ms]
2025-06-07 15:21:25.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:25.679 [info] > git status -z -uall [8ms]
2025-06-07 15:21:25.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:30.698 [info] > git config --get commit.template [8ms]
2025-06-07 15:21:30.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:21:30.717 [info] > git status -z -uall [9ms]
2025-06-07 15:21:30.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:35.737 [info] > git config --get commit.template [9ms]
2025-06-07 15:21:35.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:35.754 [info] > git status -z -uall [8ms]
2025-06-07 15:21:35.755 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:40.770 [info] > git config --get commit.template [5ms]
2025-06-07 15:21:40.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:40.781 [info] > git status -z -uall [5ms]
2025-06-07 15:21:40.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:45.801 [info] > git config --get commit.template [9ms]
2025-06-07 15:21:45.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:45.814 [info] > git status -z -uall [5ms]
2025-06-07 15:21:45.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:50.827 [info] > git config --get commit.template [4ms]
2025-06-07 15:21:50.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:21:50.841 [info] > git status -z -uall [8ms]
2025-06-07 15:21:50.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:21:55.858 [info] > git config --get commit.template [7ms]
2025-06-07 15:21:55.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:21:55.877 [info] > git status -z -uall [9ms]
2025-06-07 15:21:55.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:00.902 [info] > git config --get commit.template [11ms]
2025-06-07 15:22:00.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:00.914 [info] > git status -z -uall [5ms]
2025-06-07 15:22:00.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:22:05.935 [info] > git config --get commit.template [10ms]
2025-06-07 15:22:05.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:05.952 [info] > git status -z -uall [8ms]
2025-06-07 15:22:05.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:10.973 [info] > git config --get commit.template [9ms]
2025-06-07 15:22:10.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:10.990 [info] > git status -z -uall [8ms]
2025-06-07 15:22:10.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:16.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 15:22:16.680 [info] > git config --get commit.template [12ms]
2025-06-07 15:22:16.693 [info] > git status -z -uall [6ms]
2025-06-07 15:22:16.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:21.714 [info] > git config --get commit.template [6ms]
2025-06-07 15:22:21.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:22:21.726 [info] > git status -z -uall [6ms]
2025-06-07 15:22:21.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:26.747 [info] > git config --get commit.template [10ms]
2025-06-07 15:22:26.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:26.764 [info] > git status -z -uall [7ms]
2025-06-07 15:22:26.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:31.787 [info] > git config --get commit.template [10ms]
2025-06-07 15:22:31.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:31.799 [info] > git status -z -uall [5ms]
2025-06-07 15:22:31.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:36.817 [info] > git config --get commit.template [6ms]
2025-06-07 15:22:36.818 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:36.830 [info] > git status -z -uall [5ms]
2025-06-07 15:22:36.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:22:41.854 [info] > git config --get commit.template [9ms]
2025-06-07 15:22:41.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:41.873 [info] > git status -z -uall [11ms]
2025-06-07 15:22:41.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:46.892 [info] > git config --get commit.template [8ms]
2025-06-07 15:22:46.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:46.906 [info] > git status -z -uall [8ms]
2025-06-07 15:22:46.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:22:51.919 [info] > git config --get commit.template [4ms]
2025-06-07 15:22:51.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:22:51.936 [info] > git status -z -uall [9ms]
2025-06-07 15:22:51.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:22:56.955 [info] > git config --get commit.template [6ms]
2025-06-07 15:22:56.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:22:56.977 [info] > git status -z -uall [12ms]
2025-06-07 15:22:56.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:23:01.992 [info] > git config --get commit.template [0ms]
2025-06-07 15:23:02.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:02.023 [info] > git status -z -uall [11ms]
2025-06-07 15:23:02.024 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:07.086 [info] > git config --get commit.template [9ms]
2025-06-07 15:23:07.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:07.109 [info] > git status -z -uall [13ms]
2025-06-07 15:23:07.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 15:23:12.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:12.135 [info] > git config --get commit.template [13ms]
2025-06-07 15:23:12.155 [info] > git status -z -uall [10ms]
2025-06-07 15:23:12.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:23:17.173 [info] > git config --get commit.template [2ms]
2025-06-07 15:23:17.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:23:17.202 [info] > git status -z -uall [7ms]
2025-06-07 15:23:17.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:23:22.224 [info] > git config --get commit.template [9ms]
2025-06-07 15:23:22.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:22.256 [info] > git status -z -uall [24ms]
2025-06-07 15:23:22.257 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:23:27.283 [info] > git config --get commit.template [11ms]
2025-06-07 15:23:27.284 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 15:23:27.305 [info] > git status -z -uall [9ms]
2025-06-07 15:23:27.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:32.330 [info] > git config --get commit.template [10ms]
2025-06-07 15:23:32.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:32.350 [info] > git status -z -uall [10ms]
2025-06-07 15:23:32.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:37.369 [info] > git config --get commit.template [6ms]
2025-06-07 15:23:37.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:23:37.389 [info] > git status -z -uall [10ms]
2025-06-07 15:23:37.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:42.407 [info] > git config --get commit.template [6ms]
2025-06-07 15:23:42.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:23:42.420 [info] > git status -z -uall [6ms]
2025-06-07 15:23:42.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:47.443 [info] > git config --get commit.template [11ms]
2025-06-07 15:23:47.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:23:47.459 [info] > git status -z -uall [8ms]
2025-06-07 15:23:47.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:23:52.477 [info] > git config --get commit.template [5ms]
2025-06-07 15:23:52.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:23:52.498 [info] > git status -z -uall [10ms]
2025-06-07 15:23:52.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:23:57.511 [info] > git config --get commit.template [2ms]
2025-06-07 15:23:57.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:23:57.530 [info] > git status -z -uall [6ms]
2025-06-07 15:23:57.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:24:02.557 [info] > git config --get commit.template [11ms]
2025-06-07 15:24:02.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:02.569 [info] > git status -z -uall [5ms]
2025-06-07 15:24:02.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:24:07.601 [info] > git config --get commit.template [12ms]
2025-06-07 15:24:07.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:24:07.621 [info] > git status -z -uall [8ms]
2025-06-07 15:24:07.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:24:12.649 [info] > git config --get commit.template [13ms]
2025-06-07 15:24:12.650 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:12.669 [info] > git status -z -uall [12ms]
2025-06-07 15:24:12.671 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:24:17.693 [info] > git config --get commit.template [11ms]
2025-06-07 15:24:17.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:17.708 [info] > git status -z -uall [7ms]
2025-06-07 15:24:17.709 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:24:22.734 [info] > git config --get commit.template [9ms]
2025-06-07 15:24:22.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:22.750 [info] > git status -z -uall [7ms]
2025-06-07 15:24:22.751 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:24:27.796 [info] > git config --get commit.template [7ms]
2025-06-07 15:24:27.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:24:27.879 [info] > git status -z -uall [31ms]
2025-06-07 15:24:27.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-07 15:24:32.908 [info] > git config --get commit.template [13ms]
2025-06-07 15:24:32.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:32.930 [info] > git status -z -uall [9ms]
2025-06-07 15:24:32.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:24:37.957 [info] > git config --get commit.template [12ms]
2025-06-07 15:24:37.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:37.975 [info] > git status -z -uall [11ms]
2025-06-07 15:24:37.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:24:42.996 [info] > git config --get commit.template [6ms]
2025-06-07 15:24:42.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:43.011 [info] > git status -z -uall [7ms]
2025-06-07 15:24:43.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:24:48.033 [info] > git config --get commit.template [5ms]
2025-06-07 15:24:48.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:24:48.047 [info] > git status -z -uall [7ms]
2025-06-07 15:24:48.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:24:53.073 [info] > git config --get commit.template [10ms]
2025-06-07 15:24:53.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:24:53.094 [info] > git status -z -uall [5ms]
2025-06-07 15:24:53.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:24:58.146 [info] > git config --get commit.template [12ms]
2025-06-07 15:24:58.146 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:24:58.163 [info] > git status -z -uall [7ms]
2025-06-07 15:24:58.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:25:03.190 [info] > git config --get commit.template [11ms]
2025-06-07 15:25:03.191 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:03.209 [info] > git status -z -uall [9ms]
2025-06-07 15:25:03.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:25:08.286 [info] > git config --get commit.template [63ms]
2025-06-07 15:25:08.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:08.321 [info] > git status -z -uall [12ms]
2025-06-07 15:25:08.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 15:25:13.369 [info] > git config --get commit.template [7ms]
2025-06-07 15:25:13.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:25:13.386 [info] > git status -z -uall [8ms]
2025-06-07 15:25:13.387 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:25:18.413 [info] > git config --get commit.template [11ms]
2025-06-07 15:25:18.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:18.428 [info] > git status -z -uall [8ms]
2025-06-07 15:25:18.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:25:23.446 [info] > git config --get commit.template [6ms]
2025-06-07 15:25:23.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:23.464 [info] > git status -z -uall [8ms]
2025-06-07 15:25:23.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:25:28.482 [info] > git config --get commit.template [5ms]
2025-06-07 15:25:28.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:28.508 [info] > git status -z -uall [15ms]
2025-06-07 15:25:28.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 15:25:33.532 [info] > git config --get commit.template [7ms]
2025-06-07 15:25:33.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:33.554 [info] > git status -z -uall [8ms]
2025-06-07 15:25:33.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:25:38.590 [info] > git config --get commit.template [17ms]
2025-06-07 15:25:38.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:38.608 [info] > git status -z -uall [7ms]
2025-06-07 15:25:38.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:25:43.631 [info] > git config --get commit.template [7ms]
2025-06-07 15:25:43.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:43.652 [info] > git status -z -uall [9ms]
2025-06-07 15:25:43.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-07 15:25:48.707 [info] > git config --get commit.template [37ms]
2025-06-07 15:25:48.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 15:25:48.736 [info] > git status -z -uall [16ms]
2025-06-07 15:25:48.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:25:53.775 [info] > git config --get commit.template [14ms]
2025-06-07 15:25:53.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:53.796 [info] > git status -z -uall [12ms]
2025-06-07 15:25:53.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:25:58.814 [info] > git config --get commit.template [6ms]
2025-06-07 15:25:58.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:25:58.834 [info] > git status -z -uall [9ms]
2025-06-07 15:25:58.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:26:03.863 [info] > git config --get commit.template [13ms]
2025-06-07 15:26:03.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:26:03.880 [info] > git status -z -uall [8ms]
2025-06-07 15:26:03.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:26:08.904 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:26:08.904 [info] > git config --get commit.template [12ms]
2025-06-07 15:26:08.925 [info] > git status -z -uall [9ms]
2025-06-07 15:26:08.927 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:26:13.948 [info] > git config --get commit.template [10ms]
2025-06-07 15:26:13.950 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:26:13.961 [info] > git status -z -uall [5ms]
2025-06-07 15:26:13.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:26:18.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:26:18.988 [info] > git config --get commit.template [15ms]
2025-06-07 15:26:19.021 [info] > git status -z -uall [17ms]
2025-06-07 15:26:19.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:26:24.044 [info] > git config --get commit.template [9ms]
2025-06-07 15:26:24.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:26:24.067 [info] > git status -z -uall [11ms]
2025-06-07 15:26:24.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:26:30.838 [info] > git config --get commit.template [12ms]
2025-06-07 15:26:30.838 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:26:30.854 [info] > git status -z -uall [7ms]
2025-06-07 15:26:30.854 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:26:35.882 [info] > git config --get commit.template [11ms]
2025-06-07 15:26:35.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:26:35.910 [info] > git status -z -uall [11ms]
2025-06-07 15:26:35.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:26:40.933 [info] > git config --get commit.template [8ms]
2025-06-07 15:26:40.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:26:40.966 [info] > git status -z -uall [19ms]
2025-06-07 15:26:40.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:26:45.983 [info] > git config --get commit.template [1ms]
2025-06-07 15:26:45.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:26:46.015 [info] > git status -z -uall [11ms]
2025-06-07 15:26:46.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:26:51.048 [info] > git config --get commit.template [16ms]
2025-06-07 15:26:51.050 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:26:51.074 [info] > git status -z -uall [7ms]
2025-06-07 15:26:51.076 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:26:56.096 [info] > git config --get commit.template [8ms]
2025-06-07 15:26:56.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:26:56.112 [info] > git status -z -uall [7ms]
2025-06-07 15:26:56.113 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:27:01.149 [info] > git config --get commit.template [6ms]
2025-06-07 15:27:01.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:27:01.166 [info] > git status -z -uall [12ms]
2025-06-07 15:27:01.166 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:27:06.189 [info] > git config --get commit.template [9ms]
2025-06-07 15:27:06.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:27:06.211 [info] > git status -z -uall [10ms]
2025-06-07 15:27:06.213 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:27:11.234 [info] > git config --get commit.template [10ms]
2025-06-07 15:27:11.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:27:11.253 [info] > git status -z -uall [9ms]
2025-06-07 15:27:11.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:27:16.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:27:16.287 [info] > git config --get commit.template [13ms]
2025-06-07 15:27:16.305 [info] > git status -z -uall [8ms]
2025-06-07 15:27:16.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:27:21.326 [info] > git config --get commit.template [5ms]
2025-06-07 15:27:21.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:27:21.350 [info] > git status -z -uall [13ms]
2025-06-07 15:27:21.352 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:27:26.375 [info] > git config --get commit.template [8ms]
2025-06-07 15:27:26.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:27:26.447 [info] > git status -z -uall [61ms]
2025-06-07 15:27:26.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [49ms]
2025-06-07 15:27:31.470 [info] > git config --get commit.template [14ms]
2025-06-07 15:27:31.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:27:31.490 [info] > git status -z -uall [6ms]
2025-06-07 15:27:31.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:27:36.513 [info] > git config --get commit.template [8ms]
2025-06-07 15:27:36.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:27:36.525 [info] > git status -z -uall [4ms]
2025-06-07 15:27:36.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:27:41.547 [info] > git config --get commit.template [10ms]
2025-06-07 15:27:41.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:27:41.561 [info] > git status -z -uall [5ms]
2025-06-07 15:27:41.562 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:27:46.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:27:46.594 [info] > git config --get commit.template [16ms]
2025-06-07 15:27:46.633 [info] > git status -z -uall [18ms]
2025-06-07 15:27:46.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:27:51.660 [info] > git config --get commit.template [11ms]
2025-06-07 15:27:51.662 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:27:51.678 [info] > git status -z -uall [7ms]
2025-06-07 15:27:51.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:27:56.702 [info] > git config --get commit.template [9ms]
2025-06-07 15:27:56.703 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:27:56.716 [info] > git status -z -uall [5ms]
2025-06-07 15:27:56.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:28:01.730 [info] > git config --get commit.template [5ms]
2025-06-07 15:28:01.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:01.740 [info] > git status -z -uall [4ms]
2025-06-07 15:28:01.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:28:06.759 [info] > git config --get commit.template [8ms]
2025-06-07 15:28:06.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:06.776 [info] > git status -z -uall [7ms]
2025-06-07 15:28:06.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:28:11.795 [info] > git config --get commit.template [9ms]
2025-06-07 15:28:11.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:11.822 [info] > git status -z -uall [7ms]
2025-06-07 15:28:11.823 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:28:16.843 [info] > git config --get commit.template [9ms]
2025-06-07 15:28:16.844 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:16.861 [info] > git status -z -uall [9ms]
2025-06-07 15:28:16.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:28:21.888 [info] > git config --get commit.template [13ms]
2025-06-07 15:28:21.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:28:21.910 [info] > git status -z -uall [9ms]
2025-06-07 15:28:21.911 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:28:26.928 [info] > git config --get commit.template [7ms]
2025-06-07 15:28:26.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:26.943 [info] > git status -z -uall [6ms]
2025-06-07 15:28:26.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:28:31.964 [info] > git config --get commit.template [3ms]
2025-06-07 15:28:31.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:31.994 [info] > git status -z -uall [8ms]
2025-06-07 15:28:31.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:28:37.012 [info] > git config --get commit.template [6ms]
2025-06-07 15:28:37.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:28:37.022 [info] > git status -z -uall [4ms]
2025-06-07 15:28:37.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:28:42.050 [info] > git config --get commit.template [13ms]
2025-06-07 15:28:42.051 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:28:42.063 [info] > git status -z -uall [7ms]
2025-06-07 15:28:42.065 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:28:47.081 [info] > git config --get commit.template [6ms]
2025-06-07 15:28:47.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:47.091 [info] > git status -z -uall [5ms]
2025-06-07 15:28:47.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:28:52.109 [info] > git config --get commit.template [8ms]
2025-06-07 15:28:52.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:28:52.127 [info] > git status -z -uall [10ms]
2025-06-07 15:28:52.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:28:57.153 [info] > git config --get commit.template [10ms]
2025-06-07 15:28:57.156 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:28:57.179 [info] > git status -z -uall [11ms]
2025-06-07 15:28:57.182 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:29:02.209 [info] > git config --get commit.template [13ms]
2025-06-07 15:29:02.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:02.232 [info] > git status -z -uall [11ms]
2025-06-07 15:29:02.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:29:07.257 [info] > git config --get commit.template [9ms]
2025-06-07 15:29:07.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:29:07.277 [info] > git status -z -uall [12ms]
2025-06-07 15:29:07.277 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:29:12.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:29:12.300 [info] > git config --get commit.template [13ms]
2025-06-07 15:29:12.316 [info] > git status -z -uall [8ms]
2025-06-07 15:29:12.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:29:17.340 [info] > git config --get commit.template [10ms]
2025-06-07 15:29:17.341 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:17.367 [info] > git status -z -uall [9ms]
2025-06-07 15:29:17.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:29:22.392 [info] > git config --get commit.template [11ms]
2025-06-07 15:29:22.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:22.407 [info] > git status -z -uall [6ms]
2025-06-07 15:29:22.408 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:29:27.448 [info] > git config --get commit.template [24ms]
2025-06-07 15:29:27.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:27.483 [info] > git status -z -uall [21ms]
2025-06-07 15:29:27.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 15:29:32.506 [info] > git config --get commit.template [9ms]
2025-06-07 15:29:32.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:32.526 [info] > git status -z -uall [8ms]
2025-06-07 15:29:32.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:29:37.545 [info] > git config --get commit.template [5ms]
2025-06-07 15:29:37.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [27ms]
2025-06-07 15:29:37.588 [info] > git status -z -uall [7ms]
2025-06-07 15:29:37.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:29:42.614 [info] > git config --get commit.template [11ms]
2025-06-07 15:29:42.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:42.633 [info] > git status -z -uall [12ms]
2025-06-07 15:29:42.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:29:47.669 [info] > git config --get commit.template [11ms]
2025-06-07 15:29:47.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 15:29:47.737 [info] > git status -z -uall [10ms]
2025-06-07 15:29:47.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:29:52.767 [info] > git config --get commit.template [14ms]
2025-06-07 15:29:52.767 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:29:52.785 [info] > git status -z -uall [9ms]
2025-06-07 15:29:52.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:29:57.808 [info] > git config --get commit.template [10ms]
2025-06-07 15:29:57.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:29:57.830 [info] > git status -z -uall [15ms]
2025-06-07 15:29:57.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:30:02.862 [info] > git config --get commit.template [16ms]
2025-06-07 15:30:02.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:30:02.886 [info] > git status -z -uall [10ms]
2025-06-07 15:30:02.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:30:07.907 [info] > git config --get commit.template [5ms]
2025-06-07 15:30:07.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:07.920 [info] > git status -z -uall [6ms]
2025-06-07 15:30:07.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:30:12.947 [info] > git config --get commit.template [11ms]
2025-06-07 15:30:12.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:12.974 [info] > git status -z -uall [10ms]
2025-06-07 15:30:12.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:30:17.997 [info] > git config --get commit.template [10ms]
2025-06-07 15:30:17.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:18.012 [info] > git status -z -uall [6ms]
2025-06-07 15:30:18.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:30:23.034 [info] > git config --get commit.template [9ms]
2025-06-07 15:30:23.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:30:23.054 [info] > git status -z -uall [9ms]
2025-06-07 15:30:23.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:30:28.134 [info] > git config --get commit.template [20ms]
2025-06-07 15:30:28.135 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:28.153 [info] > git status -z -uall [11ms]
2025-06-07 15:30:28.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:30:33.172 [info] > git config --get commit.template [7ms]
2025-06-07 15:30:33.173 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:33.182 [info] > git status -z -uall [5ms]
2025-06-07 15:30:33.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:30:38.203 [info] > git config --get commit.template [8ms]
2025-06-07 15:30:38.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:38.225 [info] > git status -z -uall [12ms]
2025-06-07 15:30:38.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:30:43.245 [info] > git config --get commit.template [9ms]
2025-06-07 15:30:43.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 15:30:43.269 [info] > git status -z -uall [11ms]
2025-06-07 15:30:43.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:30:48.290 [info] > git config --get commit.template [6ms]
2025-06-07 15:30:48.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:48.308 [info] > git status -z -uall [8ms]
2025-06-07 15:30:48.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:30:53.326 [info] > git config --get commit.template [6ms]
2025-06-07 15:30:53.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:30:53.336 [info] > git status -z -uall [5ms]
2025-06-07 15:30:53.337 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:30:58.357 [info] > git config --get commit.template [8ms]
2025-06-07 15:30:58.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:30:58.375 [info] > git status -z -uall [9ms]
2025-06-07 15:30:58.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:03.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 15:31:03.405 [info] > git config --get commit.template [15ms]
2025-06-07 15:31:03.423 [info] > git status -z -uall [11ms]
2025-06-07 15:31:03.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:31:08.451 [info] > git config --get commit.template [12ms]
2025-06-07 15:31:08.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 15:31:08.484 [info] > git status -z -uall [14ms]
2025-06-07 15:31:08.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:13.505 [info] > git config --get commit.template [10ms]
2025-06-07 15:31:13.506 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:13.522 [info] > git status -z -uall [8ms]
2025-06-07 15:31:13.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:18.545 [info] > git config --get commit.template [8ms]
2025-06-07 15:31:18.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:18.567 [info] > git status -z -uall [13ms]
2025-06-07 15:31:18.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:31:23.587 [info] > git config --get commit.template [9ms]
2025-06-07 15:31:23.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:23.611 [info] > git status -z -uall [13ms]
2025-06-07 15:31:23.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:31:28.634 [info] > git config --get commit.template [10ms]
2025-06-07 15:31:28.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:31:28.653 [info] > git status -z -uall [9ms]
2025-06-07 15:31:28.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:33.672 [info] > git config --get commit.template [9ms]
2025-06-07 15:31:33.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:33.692 [info] > git status -z -uall [8ms]
2025-06-07 15:31:33.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:38.715 [info] > git config --get commit.template [9ms]
2025-06-07 15:31:38.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:38.732 [info] > git status -z -uall [7ms]
2025-06-07 15:31:38.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:43.759 [info] > git config --get commit.template [14ms]
2025-06-07 15:31:43.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:43.772 [info] > git status -z -uall [7ms]
2025-06-07 15:31:43.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:48.791 [info] > git config --get commit.template [7ms]
2025-06-07 15:31:48.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:48.812 [info] > git status -z -uall [9ms]
2025-06-07 15:31:48.813 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:31:53.836 [info] > git config --get commit.template [10ms]
2025-06-07 15:31:53.838 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:31:53.851 [info] > git status -z -uall [5ms]
2025-06-07 15:31:53.853 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:31:58.876 [info] > git config --get commit.template [11ms]
2025-06-07 15:31:58.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:31:58.895 [info] > git status -z -uall [9ms]
2025-06-07 15:31:58.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:32:03.919 [info] > git config --get commit.template [0ms]
2025-06-07 15:32:03.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:32:03.944 [info] > git status -z -uall [6ms]
2025-06-07 15:32:03.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:32:08.966 [info] > git config --get commit.template [10ms]
2025-06-07 15:32:08.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:32:08.992 [info] > git status -z -uall [15ms]
2025-06-07 15:32:08.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:32:14.008 [info] > git config --get commit.template [5ms]
2025-06-07 15:32:14.009 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:32:14.022 [info] > git status -z -uall [7ms]
2025-06-07 15:32:14.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:32:19.043 [info] > git config --get commit.template [9ms]
2025-06-07 15:32:19.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:32:19.063 [info] > git status -z -uall [9ms]
2025-06-07 15:32:19.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:32:24.105 [info] > git config --get commit.template [23ms]
2025-06-07 15:32:24.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:32:24.132 [info] > git status -z -uall [13ms]
2025-06-07 15:32:24.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
