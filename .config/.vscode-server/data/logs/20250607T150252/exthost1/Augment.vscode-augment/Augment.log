2025-06-07 15:03:08.493 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 15:03:08.493 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-07 15:03:08.493 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-07 15:03:08.818 [info] 'AugmentExtension' Retrieving model config
2025-06-07 15:03:09.172 [info] 'AugmentExtension' Retrieved model config
2025-06-07 15:03:09.172 [info] 'AugmentExtension' Returning model config
2025-06-07 15:03:09.220 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-07 15:03:09.220 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 15:03:09.220 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-07 15:03:09.220 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-07 15:03:09.220 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-07 15:03:09.220 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-07 15:03:09.220 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 15:03:09.233 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 15:03:09.233 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 15:03:09.233 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-07 15:03:09.233 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-07 15:03:09.247 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 15:03:09.247 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 15:03:09.336 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 15:03:09.559 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-07 15:03:09.563 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-07 15:03:09.563 [info] 'OpenFileManager' Opened source folder 100
2025-06-07 15:03:09.563 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 15:03:09.577 [info] 'MtimeCache[workspace]' read 1993 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 15:03:09.994 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 15:03:09.994 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 15:03:10.000 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 15:03:10.000 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 15:03:10.000 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 15:03:10.000 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 15:03:10.278 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-07 15:03:10.278 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-07 15:03:10.281 [info] 'TaskManager' Setting current root task UUID to 1367e5ab-50f8-466f-a7cf-cbf171c70833
2025-06-07 15:03:18.804 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-07 15:03:18.804 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 711
  - files emitted: 2469
  - other paths emitted: 4
  - total paths emitted: 3184
  - timing stats:
    - readDir: 13 ms
    - filter: 108 ms
    - yield: 18 ms
    - total: 149 ms
2025-06-07 15:03:18.804 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2012
  - paths not accessible: 0
  - not plain files: 0
  - large files: 39
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1984
  - mtime cache misses: 28
  - probe batches: 5
  - blob names probed: 2031
  - files read: 474
  - blobs uploaded: 17
  - timing stats:
    - ingestPath: 4 ms
    - probe: 1790 ms
    - stat: 29 ms
    - read: 1674 ms
    - upload: 1039 ms
2025-06-07 15:03:18.804 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 4 ms
  - read MtimeCache: 15 ms
  - pre-populate PathMap: 59 ms
  - create PathFilter: 117 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 152 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 8893 ms
  - enable persist: 2 ms
  - total: 9244 ms
2025-06-07 15:03:18.804 [info] 'WorkspaceManager' Workspace startup complete in 9596 ms
2025-06-07 15:07:52.531 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:07:52.738 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4550 bytes)
2025-06-07 15:07:53.679 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-6d535872
2025-06-07 15:07:54.464 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:07:54.464 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4568 bytes)
2025-06-07 15:08:06.425 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:06.426 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4568 bytes)
2025-06-07 15:08:08.038 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:08.039 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4575 bytes)
2025-06-07 15:08:21.331 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:21.332 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4575 bytes)
2025-06-07 15:08:22.861 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:22.861 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4584 bytes)
2025-06-07 15:08:40.100 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:40.100 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4584 bytes)
2025-06-07 15:08:41.730 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:41.731 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4620 bytes)
2025-06-07 15:08:53.943 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:53.943 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4620 bytes)
2025-06-07 15:08:55.482 [info] 'ToolFileUtils' Reading file: client/src/components/InsightsSidebar.tsx
2025-06-07 15:08:55.483 [info] 'ToolFileUtils' Successfully read file: client/src/components/InsightsSidebar.tsx (4638 bytes)
2025-06-07 15:09:08.262 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 15:09:08.432 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6410 bytes)
2025-06-07 15:09:10.169 [info] 'ToolFileUtils' Reading file: client/src/components/NewNoteForm.tsx
2025-06-07 15:09:10.170 [info] 'ToolFileUtils' Successfully read file: client/src/components/NewNoteForm.tsx (6426 bytes)
2025-06-07 15:09:21.608 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:09:21.861 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94899 bytes)
2025-06-07 15:09:23.769 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:09:23.769 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94910 bytes)
2025-06-07 15:09:41.494 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:09:41.495 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94910 bytes)
2025-06-07 15:09:43.101 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:09:43.102 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94914 bytes)
2025-06-07 15:09:54.918 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:09:54.919 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94914 bytes)
2025-06-07 15:09:56.505 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:09:56.505 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94925 bytes)
2025-06-07 15:10:18.897 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:10:19.069 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7500 bytes)
2025-06-07 15:10:20.831 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:10:20.831 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7518 bytes)
2025-06-07 15:10:32.867 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:10:32.867 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7518 bytes)
2025-06-07 15:10:34.400 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:10:34.400 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7525 bytes)
2025-06-07 15:10:46.512 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:10:46.512 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7525 bytes)
2025-06-07 15:10:48.003 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:10:48.003 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7534 bytes)
2025-06-07 15:11:01.026 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:11:01.026 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7534 bytes)
2025-06-07 15:11:02.647 [info] 'ToolFileUtils' Reading file: client/src/pages/Analytics.tsx
2025-06-07 15:11:02.647 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Analytics.tsx (7543 bytes)
2025-06-07 15:11:15.828 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:15.998 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7727 bytes)
2025-06-07 15:11:17.689 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:17.689 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7749 bytes)
2025-06-07 15:11:29.073 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:29.073 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7749 bytes)
2025-06-07 15:11:30.658 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:30.660 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7772 bytes)
2025-06-07 15:11:42.679 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:42.679 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7772 bytes)
2025-06-07 15:11:44.202 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:44.203 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7802 bytes)
2025-06-07 15:11:58.069 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:58.069 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7802 bytes)
2025-06-07 15:11:59.585 [info] 'ToolFileUtils' Reading file: client/src/pages/ClientDetail.tsx
2025-06-07 15:11:59.586 [info] 'ToolFileUtils' Successfully read file: client/src/pages/ClientDetail.tsx (7825 bytes)
2025-06-07 15:12:14.028 [info] 'ToolFileUtils' Reading file: client/src/pages/Dashboard.tsx
2025-06-07 15:12:14.202 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Dashboard.tsx (2954 bytes)
2025-06-07 15:12:15.078 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-44e9e787
2025-06-07 15:12:15.890 [info] 'ToolFileUtils' Reading file: client/src/pages/Dashboard.tsx
2025-06-07 15:12:15.891 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Dashboard.tsx (2961 bytes)
2025-06-07 15:12:28.535 [info] 'ToolFileUtils' Reading file: server/api.ts
2025-06-07 15:12:28.704 [info] 'ToolFileUtils' Successfully read file: server/api.ts (1609 bytes)
2025-06-07 15:12:29.738 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/5bc9d58b
2025-06-07 15:12:30.561 [info] 'ToolFileUtils' Reading file: server/api.ts
2025-06-07 15:12:30.561 [info] 'ToolFileUtils' Successfully read file: server/api.ts (1608 bytes)
2025-06-07 15:12:44.327 [info] 'ToolFileUtils' Reading file: server/schema.ts
2025-06-07 15:12:44.524 [info] 'ToolFileUtils' Successfully read file: server/schema.ts (3554 bytes)
2025-06-07 15:12:45.579 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-2c6c07cc
2025-06-07 15:12:46.396 [info] 'ToolFileUtils' Reading file: server/schema.ts
2025-06-07 15:12:46.396 [info] 'ToolFileUtils' Successfully read file: server/schema.ts (3569 bytes)
2025-06-07 15:13:02.246 [info] 'ToolFileUtils' Reading file: server/pg-direct.ts
2025-06-07 15:13:02.415 [info] 'ToolFileUtils' Successfully read file: server/pg-direct.ts (3533 bytes)
2025-06-07 15:13:03.411 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/2c5e6b06
2025-06-07 15:13:04.165 [info] 'ToolFileUtils' Reading file: server/pg-direct.ts
2025-06-07 15:13:04.165 [info] 'ToolFileUtils' Successfully read file: server/pg-direct.ts (3545 bytes)
2025-06-07 15:13:21.159 [info] 'ToolFileUtils' Reading file: server/real-time-audio.ts
2025-06-07 15:13:21.331 [info] 'ToolFileUtils' Successfully read file: server/real-time-audio.ts (8741 bytes)
2025-06-07 15:13:23.040 [info] 'ToolFileUtils' Reading file: server/real-time-audio.ts
2025-06-07 15:13:23.040 [info] 'ToolFileUtils' Successfully read file: server/real-time-audio.ts (8748 bytes)
2025-06-07 15:15:38.127 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-06-07 15:15:38.301 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (9714 bytes)
2025-06-07 15:16:15.720 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:16:16.033 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94925 bytes)
2025-06-07 15:16:17.914 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:16:17.914 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94923 bytes)
2025-06-07 15:16:32.106 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:16:32.106 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94923 bytes)
2025-06-07 15:16:33.736 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:16:33.736 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94921 bytes)
2025-06-07 15:16:58.581 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:16:58.785 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6366 bytes)
2025-06-07 15:17:09.000 [info] 'ViewTool' Tool called with path: server/auth.ts and view_range: [1,20]
2025-06-07 15:17:19.835 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:17:19.836 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6366 bytes)
2025-06-07 15:17:21.532 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:17:21.533 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6421 bytes)
2025-06-07 15:17:36.738 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:17:36.738 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6421 bytes)
2025-06-07 15:17:38.327 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:17:38.328 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6733 bytes)
2025-06-07 15:18:36.409 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:18:36.589 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (44939 bytes)
2025-06-07 15:18:38.406 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:18:38.406 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (44983 bytes)
2025-06-07 15:18:57.335 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:18:57.335 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (44983 bytes)
2025-06-07 15:18:58.840 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:18:58.840 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46014 bytes)
2025-06-07 15:19:13.378 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:19:13.378 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46014 bytes)
2025-06-07 15:19:14.987 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:19:14.987 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46194 bytes)
2025-06-07 15:19:40.977 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:19:40.977 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46194 bytes)
2025-06-07 15:19:42.559 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:19:42.559 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46254 bytes)
2025-06-07 15:20:02.613 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:20:02.613 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46254 bytes)
2025-06-07 15:20:12.222 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 15:20:23.099 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:20:23.099 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46254 bytes)
2025-06-07 15:20:24.699 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:20:24.699 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46596 bytes)
2025-06-07 15:20:55.712 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [1645,1655]
2025-06-07 15:21:07.004 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:21:07.004 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94921 bytes)
2025-06-07 15:21:08.941 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:21:08.941 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94932 bytes)
2025-06-07 15:21:21.914 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:21:21.914 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94932 bytes)
2025-06-07 15:21:23.616 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:21:23.616 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94943 bytes)
2025-06-07 15:21:41.726 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 15:21:41.903 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (33144 bytes)
2025-06-07 15:21:43.629 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-06-07 15:21:43.629 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (33207 bytes)
2025-06-07 15:23:36.111 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:23:36.111 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94943 bytes)
2025-06-07 15:23:37.723 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:23:37.723 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94960 bytes)
2025-06-07 15:23:50.421 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:23:50.421 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94960 bytes)
2025-06-07 15:23:51.993 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 15:23:51.993 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (94977 bytes)
2025-06-07 15:25:21.614 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:25:21.784 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6733 bytes)
2025-06-07 15:25:23.532 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:25:23.532 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6707 bytes)
2025-06-07 15:32:41.063 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:32:41.236 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6707 bytes)
2025-06-07 15:32:43.031 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-06-07 15:32:43.031 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (6773 bytes)
2025-06-07 15:32:55.432 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:32:55.609 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46596 bytes)
2025-06-07 15:32:58.852 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:32:58.852 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46887 bytes)
2025-06-07 15:33:08.071 [info] 'AugmentExtension' Retrieving model config
