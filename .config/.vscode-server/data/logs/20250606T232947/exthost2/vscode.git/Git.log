2025-06-06 23:31:12.057 [info] [main] Log level: Info
2025-06-06 23:31:12.057 [info] [main] Validating found git in: "git"
2025-06-06 23:31:12.057 [info] [main] Using git "2.47.2" from "git"
2025-06-06 23:31:12.057 [info] [Model][doInitialScan] Initial repository scan started
2025-06-06 23:31:12.057 [info] > git rev-parse --show-toplevel [40ms]
2025-06-06 23:31:12.057 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-06 23:31:12.057 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-06 23:31:12.057 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-06 23:31:12.057 [info] > git config --get commit.template [9ms]
2025-06-06 23:31:12.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-06 23:31:12.072 [info] > git rev-parse --show-toplevel [2ms]
2025-06-06 23:31:12.080 [info] > git status -z -uall [5ms]
2025-06-06 23:31:12.082 [info] > git rev-parse --show-toplevel [2ms]
2025-06-06 23:31:12.082 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:31:12.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-06 23:31:12.113 [info] > git rev-parse --show-toplevel [9ms]
2025-06-06 23:31:12.114 [info] > git config --get commit.template [2ms]
2025-06-06 23:31:12.114 [info] > git config --get --local branch.main.vscode-merge-base [5ms]
2025-06-06 23:31:12.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [358ms]
2025-06-06 23:31:12.492 [info] > git rev-parse --show-toplevel [373ms]
2025-06-06 23:31:12.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [375ms]
2025-06-06 23:31:12.500 [info] > git rev-parse --show-toplevel [2ms]
2025-06-06 23:31:12.500 [info] > git merge-base refs/heads/main refs/remotes/origin/main [4ms]
2025-06-06 23:31:12.517 [info] > git status -z -uall [4ms]
2025-06-06 23:31:12.517 [info] > git diff --name-status -z --diff-filter=ADMR 914f82aa2133a3ab9e76799ac1b0b9bf2d295a95...refs/remotes/origin/main [14ms]
2025-06-06 23:31:12.519 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:31:12.523 [info] > git rev-parse --show-toplevel [16ms]
2025-06-06 23:31:12.528 [info] > git rev-parse --show-toplevel [2ms]
2025-06-06 23:31:12.535 [info] > git rev-parse --show-toplevel [5ms]
2025-06-06 23:31:12.542 [info] > git rev-parse --show-toplevel [2ms]
2025-06-06 23:31:12.545 [info] > git rev-parse --show-toplevel [1ms]
2025-06-06 23:31:12.548 [info] > git rev-parse --show-toplevel [1ms]
2025-06-06 23:31:12.551 [info] > git rev-parse --show-toplevel [1ms]
2025-06-06 23:31:12.554 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-06 23:31:15.068 [info] > git config --get commit.template [2ms]
2025-06-06 23:31:15.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:15.085 [info] > git status -z -uall [5ms]
2025-06-06 23:31:15.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:31:22.183 [info] > git config --get commit.template [2ms]
2025-06-06 23:31:22.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:31:22.196 [info] > git status -z -uall [5ms]
2025-06-06 23:31:22.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:31:27.204 [info] > git config --get commit.template [2ms]
2025-06-06 23:31:27.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:27.212 [info] > git status -z -uall [4ms]
2025-06-06 23:31:27.212 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-06 23:31:32.220 [info] > git config --get commit.template [2ms]
2025-06-06 23:31:32.221 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:32.226 [info] > git status -z -uall [2ms]
2025-06-06 23:31:32.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:31:37.235 [info] > git config --get commit.template [2ms]
2025-06-06 23:31:37.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:37.240 [info] > git status -z -uall [2ms]
2025-06-06 23:31:37.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:31:42.249 [info] > git config --get commit.template [3ms]
2025-06-06 23:31:42.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:42.255 [info] > git status -z -uall [2ms]
2025-06-06 23:31:42.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:31:47.263 [info] > git config --get commit.template [3ms]
2025-06-06 23:31:47.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:47.269 [info] > git status -z -uall [2ms]
2025-06-06 23:31:47.270 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:31:52.282 [info] > git config --get commit.template [5ms]
2025-06-06 23:31:52.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:52.289 [info] > git status -z -uall [3ms]
2025-06-06 23:31:52.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:31:57.300 [info] > git config --get commit.template [3ms]
2025-06-06 23:31:57.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:31:57.306 [info] > git status -z -uall [3ms]
2025-06-06 23:31:57.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:32:02.315 [info] > git config --get commit.template [3ms]
2025-06-06 23:32:02.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:32:02.321 [info] > git status -z -uall [3ms]
2025-06-06 23:32:02.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:32:07.331 [info] > git config --get commit.template [3ms]
2025-06-06 23:32:07.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:32:07.338 [info] > git status -z -uall [3ms]
2025-06-06 23:32:07.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:34:19.907 [info] > git config --get commit.template [4ms]
2025-06-06 23:34:19.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:34:19.914 [info] > git status -z -uall [3ms]
2025-06-06 23:34:19.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:34:22.766 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-06 23:34:24.923 [info] > git config --get commit.template [3ms]
2025-06-06 23:34:24.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:34:24.929 [info] > git status -z -uall [3ms]
2025-06-06 23:34:24.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:34:29.938 [info] > git config --get commit.template [3ms]
2025-06-06 23:34:29.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:34:29.944 [info] > git status -z -uall [3ms]
2025-06-06 23:34:29.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:34:32.857 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-06 23:34:35.337 [info] > git show --textconv :server/openai.ts [45ms]
2025-06-06 23:34:35.337 [info] > git config --get commit.template [94ms]
2025-06-06 23:34:35.349 [info] > git ls-files --stage -- server/openai.ts [52ms]
2025-06-06 23:34:35.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-06-06 23:34:35.450 [info] > git cat-file -s ca9b5fe40e6fa626665df86b60e7298ab4367adf [75ms]
2025-06-06 23:34:35.487 [info] > git status -z -uall [18ms]
2025-06-06 23:34:35.490 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:34:35.743 [info] > git blame --root --incremental 914f82aa2133a3ab9e76799ac1b0b9bf2d295a95 -- server/openai.ts [119ms]
2025-06-06 23:34:40.503 [info] > git config --get commit.template [4ms]
2025-06-06 23:34:40.504 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:34:40.509 [info] > git status -z -uall [3ms]
2025-06-06 23:34:40.510 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:34:45.518 [info] > git config --get commit.template [2ms]
2025-06-06 23:34:45.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:34:45.524 [info] > git status -z -uall [2ms]
2025-06-06 23:34:45.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:34:50.534 [info] > git config --get commit.template [3ms]
2025-06-06 23:34:50.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:34:50.541 [info] > git status -z -uall [3ms]
2025-06-06 23:34:50.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:34:55.553 [info] > git config --get commit.template [3ms]
2025-06-06 23:34:55.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:34:55.560 [info] > git status -z -uall [3ms]
2025-06-06 23:34:55.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:35:00.570 [info] > git config --get commit.template [3ms]
2025-06-06 23:35:00.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:00.578 [info] > git status -z -uall [4ms]
2025-06-06 23:35:00.579 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:35:05.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-06 23:35:05.591 [info] > git config --get commit.template [6ms]
2025-06-06 23:35:05.597 [info] > git status -z -uall [3ms]
2025-06-06 23:35:05.598 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:10.607 [info] > git config --get commit.template [3ms]
2025-06-06 23:35:10.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:10.614 [info] > git status -z -uall [3ms]
2025-06-06 23:35:10.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:15.626 [info] > git config --get commit.template [4ms]
2025-06-06 23:35:15.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:15.632 [info] > git status -z -uall [3ms]
2025-06-06 23:35:15.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:35:20.640 [info] > git config --get commit.template [2ms]
2025-06-06 23:35:20.641 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:20.646 [info] > git status -z -uall [2ms]
2025-06-06 23:35:20.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:25.655 [info] > git config --get commit.template [2ms]
2025-06-06 23:35:25.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:25.661 [info] > git status -z -uall [2ms]
2025-06-06 23:35:25.662 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:30.670 [info] > git config --get commit.template [2ms]
2025-06-06 23:35:30.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:30.676 [info] > git status -z -uall [2ms]
2025-06-06 23:35:30.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:35.685 [info] > git config --get commit.template [2ms]
2025-06-06 23:35:35.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:35.692 [info] > git status -z -uall [3ms]
2025-06-06 23:35:35.693 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:40.731 [info] > git config --get commit.template [5ms]
2025-06-06 23:35:40.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:35:40.756 [info] > git status -z -uall [11ms]
2025-06-06 23:35:40.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-06 23:35:45.765 [info] > git config --get commit.template [3ms]
2025-06-06 23:35:45.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:45.771 [info] > git status -z -uall [3ms]
2025-06-06 23:35:45.772 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:35:50.780 [info] > git config --get commit.template [3ms]
2025-06-06 23:35:50.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:35:50.787 [info] > git status -z -uall [3ms]
2025-06-06 23:35:50.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:35:55.797 [info] > git config --get commit.template [3ms]
2025-06-06 23:35:55.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:35:55.805 [info] > git status -z -uall [4ms]
2025-06-06 23:35:55.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:36:00.816 [info] > git config --get commit.template [4ms]
2025-06-06 23:36:00.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:36:00.823 [info] > git status -z -uall [4ms]
2025-06-06 23:36:00.823 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:36:05.835 [info] > git config --get commit.template [3ms]
2025-06-06 23:36:05.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:36:05.841 [info] > git status -z -uall [3ms]
2025-06-06 23:36:05.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:36:10.853 [info] > git config --get commit.template [3ms]
2025-06-06 23:36:10.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:36:10.861 [info] > git status -z -uall [4ms]
2025-06-06 23:36:10.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:36:15.870 [info] > git config --get commit.template [3ms]
2025-06-06 23:36:15.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:36:15.877 [info] > git status -z -uall [4ms]
2025-06-06 23:36:15.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-06-06 23:36:20.938 [info] > git config --get commit.template [3ms]
2025-06-06 23:36:20.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:36:20.949 [info] > git status -z -uall [3ms]
2025-06-06 23:36:20.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:37:28.063 [info] > git config --get commit.template [3ms]
2025-06-06 23:37:28.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:37:28.071 [info] > git status -z -uall [4ms]
2025-06-06 23:37:28.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:37:33.082 [info] > git config --get commit.template [4ms]
2025-06-06 23:37:33.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:37:33.088 [info] > git status -z -uall [2ms]
2025-06-06 23:37:33.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:37:38.097 [info] > git config --get commit.template [3ms]
2025-06-06 23:37:38.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:37:38.104 [info] > git status -z -uall [3ms]
2025-06-06 23:37:38.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:37:43.131 [info] > git config --get commit.template [4ms]
2025-06-06 23:37:43.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:37:43.139 [info] > git status -z -uall [5ms]
2025-06-06 23:37:43.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:37:48.149 [info] > git config --get commit.template [3ms]
2025-06-06 23:37:48.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:37:48.156 [info] > git status -z -uall [4ms]
2025-06-06 23:37:48.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:37:53.167 [info] > git config --get commit.template [3ms]
2025-06-06 23:37:53.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:37:53.175 [info] > git status -z -uall [5ms]
2025-06-06 23:37:53.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:37:58.183 [info] > git config --get commit.template [2ms]
2025-06-06 23:37:58.192 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-06 23:37:58.198 [info] > git status -z -uall [3ms]
2025-06-06 23:37:58.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:38:03.207 [info] > git config --get commit.template [3ms]
2025-06-06 23:38:03.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:38:03.214 [info] > git status -z -uall [4ms]
2025-06-06 23:38:03.214 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:38:08.222 [info] > git config --get commit.template [2ms]
2025-06-06 23:38:08.223 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:38:08.228 [info] > git status -z -uall [3ms]
2025-06-06 23:38:08.229 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:38:13.236 [info] > git config --get commit.template [3ms]
2025-06-06 23:38:13.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:38:13.242 [info] > git status -z -uall [3ms]
2025-06-06 23:38:13.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:38:18.251 [info] > git config --get commit.template [3ms]
2025-06-06 23:38:18.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:38:18.257 [info] > git status -z -uall [2ms]
2025-06-06 23:38:18.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:38:23.266 [info] > git config --get commit.template [2ms]
2025-06-06 23:38:23.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:38:23.273 [info] > git status -z -uall [3ms]
2025-06-06 23:38:23.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:38:28.282 [info] > git config --get commit.template [3ms]
2025-06-06 23:38:28.283 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:38:28.288 [info] > git status -z -uall [3ms]
2025-06-06 23:38:28.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:38:28.985 [info] > git show --textconv :server/openai.ts [3ms]
2025-06-06 23:38:28.986 [info] > git ls-files --stage -- server/openai.ts [2ms]
2025-06-06 23:38:28.989 [info] > git cat-file -s ca9b5fe40e6fa626665df86b60e7298ab4367adf [1ms]
2025-06-06 23:38:33.299 [info] > git config --get commit.template [4ms]
2025-06-06 23:38:33.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:38:33.306 [info] > git status -z -uall [3ms]
2025-06-06 23:38:33.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:38:38.316 [info] > git config --get commit.template [4ms]
2025-06-06 23:38:38.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:38:38.322 [info] > git status -z -uall [2ms]
2025-06-06 23:38:38.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:39:03.249 [info] > git config --get commit.template [4ms]
2025-06-06 23:39:03.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:39:03.257 [info] > git status -z -uall [3ms]
2025-06-06 23:39:03.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-06 23:39:08.273 [info] > git config --get commit.template [3ms]
2025-06-06 23:39:08.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:39:08.279 [info] > git status -z -uall [2ms]
2025-06-06 23:39:08.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:39:13.287 [info] > git config --get commit.template [2ms]
2025-06-06 23:39:13.288 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:39:13.293 [info] > git status -z -uall [2ms]
2025-06-06 23:39:13.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
