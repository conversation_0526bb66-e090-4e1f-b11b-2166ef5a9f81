2025-06-06 23:31:12.618 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-06 23:31:12.618 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-06 23:31:12.618 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-06 23:31:12.691 [info] 'AugmentExtension' Retrieving model config
2025-06-06 23:31:12.971 [info] 'AugmentExtension' Retrieved model config
2025-06-06 23:31:12.971 [info] 'AugmentExtension' Returning model config
2025-06-06 23:31:12.999 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-06 23:31:12.999 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-06 23:31:12.999 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-06 23:31:12.999 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-06 23:31:13.000 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-06 23:31:13.000 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-06 23:31:13.000 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-06 23:31:13.014 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-06 23:31:13.014 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-06 23:31:13.014 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-06 23:31:13.024 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-06 23:31:13.024 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-06 23:31:13.293 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-06 23:31:13.295 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-06 23:31:13.295 [info] 'OpenFileManager' Opened source folder 100
2025-06-06 23:31:13.296 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-06 23:31:13.300 [info] 'MtimeCache[workspace]' read 2071 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-06 23:31:13.749 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-06 23:31:13.750 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-06 23:31:13.750 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-06 23:31:13.750 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-06 23:31:13.753 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-06 23:31:13.754 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-06 23:31:18.352 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250606T232947/exthost2/vscode.json-language-features
2025-06-06 23:31:31.507 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-06 23:31:31.507 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 631
  - files emitted: 2552
  - other paths emitted: 4
  - total paths emitted: 3187
  - timing stats:
    - readDir: 21 ms
    - filter: 106 ms
    - yield: 22 ms
    - total: 169 ms
2025-06-06 23:31:31.507 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2104
  - paths not accessible: 0
  - not plain files: 0
  - large files: 39
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 2062
  - mtime cache misses: 42
  - probe batches: 8
  - blob names probed: 2128
  - files read: 499
  - blobs uploaded: 21
  - timing stats:
    - ingestPath: 3 ms
    - probe: 3669 ms
    - stat: 26 ms
    - read: 582 ms
    - upload: 2394 ms
2025-06-06 23:31:31.507 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 3 ms
  - read MtimeCache: 4 ms
  - pre-populate PathMap: 63 ms
  - create PathFilter: 155 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 172 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 17814 ms
  - enable persist: 2 ms
  - total: 18214 ms
2025-06-06 23:31:31.507 [info] 'WorkspaceManager' Workspace startup complete in 18518 ms
2025-06-06 23:34:35.741 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250606T232947/exthost2/vscode.typescript-language-features
2025-06-06 23:35:03.447 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-06 23:35:05.518 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-06 23:35:05.518 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-06 23:35:05.520 [info] 'TaskManager' Setting current root task UUID to a8952510-18f3-457e-8b74-************
2025-06-06 23:35:12.630 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-06 23:35:12.757 [info] 'TaskManager' Setting current root task UUID to 041097f4-3bc9-42b8-a715-f0e0f98e1b58
2025-06-06 23:35:12.757 [info] 'TaskManager' Setting current root task UUID to 041097f4-3bc9-42b8-a715-f0e0f98e1b58
2025-06-06 23:35:42.260 [info] 'ViewTool' Tool called with path: server/openai.ts and view_range: undefined
2025-06-06 23:35:47.060 [info] 'ViewTool' Tool called with path: server/openai.ts and view_range: undefined
2025-06-06 23:35:58.873 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:35:58.873 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21691 bytes)
2025-06-06 23:35:59.620 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/38f4f843
2025-06-06 23:36:00.400 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:00.400 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21727 bytes)
2025-06-06 23:36:04.131 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e4b5ef20-c99e-4ddf-9565-b6163d3116cb
2025-06-06 23:36:09.002 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:09.002 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21727 bytes)
2025-06-06 23:36:10.531 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:10.531 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21729 bytes)
2025-06-06 23:36:19.993 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:19.994 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21729 bytes)
2025-06-06 23:36:21.533 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:21.533 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21738 bytes)
2025-06-06 23:36:29.048 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:29.048 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21738 bytes)
2025-06-06 23:36:30.488 [info] 'ToolFileUtils' Reading file: server/openai.ts
2025-06-06 23:36:30.489 [info] 'ToolFileUtils' Successfully read file: server/openai.ts (21740 bytes)
2025-06-06 23:36:37.296 [info] 'ViewTool' Tool called with path: server/openai.ts and view_range: [9,20]
2025-06-06 23:36:41.328 [info] 'ViewTool' Tool called with path: server/openai.ts and view_range: [575,585]
2025-06-06 23:36:45.107 [info] 'ViewTool' Tool called with path: server/openai.ts and view_range: undefined
2025-06-06 23:38:28.854 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-06 23:38:39.348 [warning] 'queryNextEditStream' [68a7ac90-1408-40c8-814f-2fe48ee63b52/d61834ab-07e4-4852-b3c1-bf83373bdd07] Found 1 unknown blobs.
2025-06-06 23:38:40.694 [warning] 'queryNextEditStream' [e37587dd-b095-4139-9893-8eb79a158a28/9b625cc1-5586-4cb8-9026-413d14da0aba] Found 1 unknown blobs.
2025-06-06 23:39:13.483 [error] 'AugmentExtension' API request 9502e57a-cd4b-47a3-80b5-47a4b8a39d65 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-06 23:39:13.709 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-06 23:39:13.980 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
