2025-06-07 15:38:39.525 [info] [main] Log level: Info
2025-06-07 15:38:39.525 [info] [main] Validating found git in: "git"
2025-06-07 15:38:39.525 [info] [main] Using git "2.47.2" from "git"
2025-06-07 15:38:39.525 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 15:38:39.525 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 15:38:39.525 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-07 15:38:39.525 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 15:38:39.525 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 15:38:39.525 [info] > git config --get commit.template [7ms]
2025-06-07 15:38:39.525 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 15:38:39.525 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.525 [info] > git status -z -uall [8ms]
2025-06-07 15:38:39.525 [info] > git rev-parse --show-toplevel [12ms]
2025-06-07 15:38:39.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 15:38:39.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-07 15:38:39.526 [info] > git rev-parse --show-toplevel [47ms]
2025-06-07 15:38:39.526 [info] > git config --get commit.template [8ms]
2025-06-07 15:38:39.526 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-06-07 15:38:39.526 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 15:38:39.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [4ms]
2025-06-07 15:38:39.526 [info] > git merge-base refs/heads/main refs/remotes/origin/main [12ms]
2025-06-07 15:38:39.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-07 15:38:39.526 [info] > git rev-parse --show-toplevel [100ms]
2025-06-07 15:38:39.526 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [101ms]
2025-06-07 15:38:39.526 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.526 [info] > git status -z -uall [13ms]
2025-06-07 15:38:39.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 15:38:39.526 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 15:38:39.526 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.530 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.538 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.543 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 15:38:39.547 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.561 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 15:38:39.564 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 15:38:51.495 [info] > git config --get commit.template [4ms]
2025-06-07 15:38:51.496 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:38:51.503 [info] > git status -z -uall [4ms]
2025-06-07 15:38:51.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:38:56.516 [info] > git config --get commit.template [4ms]
2025-06-07 15:38:56.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:38:56.526 [info] > git status -z -uall [5ms]
2025-06-07 15:38:56.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:39:01.539 [info] > git config --get commit.template [5ms]
2025-06-07 15:39:01.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:39:01.548 [info] > git status -z -uall [4ms]
2025-06-07 15:39:01.550 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:39:06.563 [info] > git config --get commit.template [5ms]
2025-06-07 15:39:06.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:39:06.574 [info] > git status -z -uall [6ms]
2025-06-07 15:39:06.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:39:11.586 [info] > git config --get commit.template [1ms]
2025-06-07 15:39:11.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:39:11.598 [info] > git status -z -uall [4ms]
2025-06-07 15:39:11.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:39:16.616 [info] > git config --get commit.template [6ms]
2025-06-07 15:39:16.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:39:16.633 [info] > git status -z -uall [7ms]
2025-06-07 15:39:16.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:39:21.651 [info] > git config --get commit.template [8ms]
2025-06-07 15:39:21.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:39:21.668 [info] > git status -z -uall [7ms]
2025-06-07 15:39:21.668 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:39:26.683 [info] > git config --get commit.template [5ms]
2025-06-07 15:39:26.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:39:26.693 [info] > git status -z -uall [5ms]
2025-06-07 15:39:26.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:39:31.747 [info] > git config --get commit.template [1ms]
2025-06-07 15:39:31.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:39:31.766 [info] > git status -z -uall [4ms]
2025-06-07 15:39:31.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:39:36.788 [info] > git config --get commit.template [9ms]
2025-06-07 15:39:36.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:39:36.804 [info] > git status -z -uall [6ms]
2025-06-07 15:39:36.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:39:45.826 [info] > git config --get commit.template [15ms]
2025-06-07 15:39:45.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:39:45.844 [info] > git status -z -uall [8ms]
2025-06-07 15:39:45.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:39:51.814 [info] > git config --get commit.template [7ms]
2025-06-07 15:39:51.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:39:51.831 [info] > git status -z -uall [8ms]
2025-06-07 15:39:51.833 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:39:56.850 [info] > git config --get commit.template [6ms]
2025-06-07 15:39:56.852 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:39:56.860 [info] > git status -z -uall [4ms]
2025-06-07 15:39:56.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:40:01.873 [info] > git config --get commit.template [4ms]
2025-06-07 15:40:01.875 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:01.888 [info] > git status -z -uall [9ms]
2025-06-07 15:40:01.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:40:06.899 [info] > git config --get commit.template [0ms]
2025-06-07 15:40:06.904 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:06.913 [info] > git status -z -uall [5ms]
2025-06-07 15:40:06.913 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:40:11.933 [info] > git config --get commit.template [9ms]
2025-06-07 15:40:11.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:11.955 [info] > git status -z -uall [9ms]
2025-06-07 15:40:11.958 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:40:16.973 [info] > git config --get commit.template [6ms]
2025-06-07 15:40:16.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:40:16.985 [info] > git status -z -uall [6ms]
2025-06-07 15:40:16.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:40:22.014 [info] > git config --get commit.template [0ms]
2025-06-07 15:40:22.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:22.038 [info] > git status -z -uall [11ms]
2025-06-07 15:40:22.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:40:45.304 [info] > git config --get commit.template [5ms]
2025-06-07 15:40:45.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:45.315 [info] > git status -z -uall [6ms]
2025-06-07 15:40:45.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:40:50.352 [info] > git config --get commit.template [4ms]
2025-06-07 15:40:50.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:50.361 [info] > git status -z -uall [5ms]
2025-06-07 15:40:50.362 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:40:55.378 [info] > git config --get commit.template [0ms]
2025-06-07 15:40:55.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:40:55.400 [info] > git status -z -uall [8ms]
2025-06-07 15:40:55.401 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:41:00.416 [info] > git config --get commit.template [6ms]
2025-06-07 15:41:00.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:41:00.429 [info] > git status -z -uall [6ms]
2025-06-07 15:41:00.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:41:05.443 [info] > git config --get commit.template [6ms]
2025-06-07 15:41:05.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:05.456 [info] > git status -z -uall [7ms]
2025-06-07 15:41:05.457 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:41:10.663 [info] > git config --get commit.template [2ms]
2025-06-07 15:41:10.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 15:41:10.709 [info] > git status -z -uall [9ms]
2025-06-07 15:41:10.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:41:15.730 [info] > git config --get commit.template [10ms]
2025-06-07 15:41:15.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:15.743 [info] > git status -z -uall [7ms]
2025-06-07 15:41:15.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-07 15:41:20.760 [info] > git config --get commit.template [4ms]
2025-06-07 15:41:20.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:41:20.775 [info] > git status -z -uall [9ms]
2025-06-07 15:41:20.777 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:41:25.914 [info] > git config --get commit.template [9ms]
2025-06-07 15:41:25.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:25.935 [info] > git status -z -uall [11ms]
2025-06-07 15:41:25.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 15:41:30.950 [info] > git config --get commit.template [1ms]
2025-06-07 15:41:30.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:30.967 [info] > git status -z -uall [5ms]
2025-06-07 15:41:30.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:41:36.001 [info] > git config --get commit.template [4ms]
2025-06-07 15:41:36.025 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:36.039 [info] > git status -z -uall [8ms]
2025-06-07 15:41:36.040 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:41:41.052 [info] > git config --get commit.template [1ms]
2025-06-07 15:41:41.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:41.079 [info] > git status -z -uall [10ms]
2025-06-07 15:41:41.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:41:46.095 [info] > git config --get commit.template [3ms]
2025-06-07 15:41:46.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:41:46.106 [info] > git status -z -uall [4ms]
2025-06-07 15:41:46.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:41:51.123 [info] > git config --get commit.template [8ms]
2025-06-07 15:41:51.124 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:51.140 [info] > git status -z -uall [9ms]
2025-06-07 15:41:51.141 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:41:56.158 [info] > git config --get commit.template [5ms]
2025-06-07 15:41:56.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:41:56.168 [info] > git status -z -uall [5ms]
2025-06-07 15:41:56.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:42:01.185 [info] > git config --get commit.template [4ms]
2025-06-07 15:42:01.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:01.196 [info] > git status -z -uall [6ms]
2025-06-07 15:42:01.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:42:06.208 [info] > git config --get commit.template [4ms]
2025-06-07 15:42:06.210 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:06.220 [info] > git status -z -uall [5ms]
2025-06-07 15:42:06.222 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:42:11.236 [info] > git config --get commit.template [5ms]
2025-06-07 15:42:11.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:42:11.248 [info] > git status -z -uall [6ms]
2025-06-07 15:42:11.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:42:16.258 [info] > git config --get commit.template [4ms]
2025-06-07 15:42:16.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:16.271 [info] > git status -z -uall [8ms]
2025-06-07 15:42:16.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:42:21.286 [info] > git config --get commit.template [5ms]
2025-06-07 15:42:21.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:42:21.298 [info] > git status -z -uall [5ms]
2025-06-07 15:42:21.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:42:26.314 [info] > git config --get commit.template [1ms]
2025-06-07 15:42:26.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:26.344 [info] > git status -z -uall [19ms]
2025-06-07 15:42:26.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 15:42:34.167 [info] > git config --get commit.template [9ms]
2025-06-07 15:42:34.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:34.177 [info] > git status -z -uall [4ms]
2025-06-07 15:42:34.179 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:42:39.188 [info] > git config --get commit.template [3ms]
2025-06-07 15:42:39.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:42:39.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:42:39.198 [info] > git status -z -uall [6ms]
2025-06-07 15:42:44.205 [info] > git config --get commit.template [1ms]
2025-06-07 15:42:44.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:42:44.217 [info] > git status -z -uall [4ms]
2025-06-07 15:42:44.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:42:49.231 [info] > git config --get commit.template [6ms]
2025-06-07 15:42:49.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:49.241 [info] > git status -z -uall [5ms]
2025-06-07 15:42:49.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:42:54.256 [info] > git config --get commit.template [6ms]
2025-06-07 15:42:54.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:42:54.267 [info] > git status -z -uall [5ms]
2025-06-07 15:42:54.267 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:42:59.285 [info] > git config --get commit.template [8ms]
2025-06-07 15:42:59.286 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:42:59.301 [info] > git status -z -uall [8ms]
2025-06-07 15:42:59.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-07 15:43:04.345 [info] > git config --get commit.template [11ms]
2025-06-07 15:43:04.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:43:04.366 [info] > git status -z -uall [10ms]
2025-06-07 15:43:04.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:43:09.391 [info] > git config --get commit.template [7ms]
2025-06-07 15:43:09.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:43:09.411 [info] > git status -z -uall [9ms]
2025-06-07 15:43:09.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:43:14.425 [info] > git config --get commit.template [3ms]
2025-06-07 15:43:14.426 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:43:14.438 [info] > git status -z -uall [6ms]
2025-06-07 15:43:14.439 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:43:19.450 [info] > git config --get commit.template [1ms]
2025-06-07 15:43:19.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:43:19.477 [info] > git status -z -uall [9ms]
2025-06-07 15:43:19.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:43:24.493 [info] > git config --get commit.template [5ms]
2025-06-07 15:43:24.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:43:24.505 [info] > git status -z -uall [5ms]
2025-06-07 15:43:24.505 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:43:29.516 [info] > git config --get commit.template [4ms]
2025-06-07 15:43:29.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:43:29.527 [info] > git status -z -uall [7ms]
2025-06-07 15:43:29.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:43:34.541 [info] > git config --get commit.template [4ms]
2025-06-07 15:43:34.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-06-07 15:43:34.599 [info] > git status -z -uall [5ms]
2025-06-07 15:43:34.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:43:39.616 [info] > git config --get commit.template [7ms]
2025-06-07 15:43:39.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:43:39.626 [info] > git status -z -uall [5ms]
2025-06-07 15:43:39.627 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:43:44.647 [info] > git config --get commit.template [8ms]
2025-06-07 15:43:44.649 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:43:44.663 [info] > git status -z -uall [7ms]
2025-06-07 15:43:44.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:43:49.676 [info] > git config --get commit.template [4ms]
2025-06-07 15:43:49.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:43:49.684 [info] > git status -z -uall [4ms]
2025-06-07 15:43:49.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:00.186 [info] > git config --get commit.template [4ms]
2025-06-07 15:44:00.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:00.200 [info] > git status -z -uall [6ms]
2025-06-07 15:44:00.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:05.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:44:05.228 [info] > git config --get commit.template [17ms]
2025-06-07 15:44:05.243 [info] > git status -z -uall [4ms]
2025-06-07 15:44:05.244 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:10.257 [info] > git config --get commit.template [6ms]
2025-06-07 15:44:10.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:10.270 [info] > git status -z -uall [6ms]
2025-06-07 15:44:10.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:15.287 [info] > git config --get commit.template [5ms]
2025-06-07 15:44:15.288 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:15.298 [info] > git status -z -uall [5ms]
2025-06-07 15:44:15.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:44:20.311 [info] > git config --get commit.template [4ms]
2025-06-07 15:44:20.313 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:20.319 [info] > git status -z -uall [2ms]
2025-06-07 15:44:20.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:25.332 [info] > git config --get commit.template [4ms]
2025-06-07 15:44:25.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:25.340 [info] > git status -z -uall [3ms]
2025-06-07 15:44:25.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:30.353 [info] > git config --get commit.template [5ms]
2025-06-07 15:44:30.354 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:44:30.368 [info] > git status -z -uall [7ms]
2025-06-07 15:44:30.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:35.378 [info] > git config --get commit.template [3ms]
2025-06-07 15:44:35.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:44:35.390 [info] > git status -z -uall [5ms]
2025-06-07 15:44:35.391 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:44:40.407 [info] > git config --get commit.template [6ms]
2025-06-07 15:44:40.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:40.419 [info] > git status -z -uall [4ms]
2025-06-07 15:44:40.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:47.199 [info] > git config --get commit.template [1ms]
2025-06-07 15:44:47.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:47.218 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:44:47.219 [info] > git status -z -uall [6ms]
2025-06-07 15:44:52.231 [info] > git config --get commit.template [4ms]
2025-06-07 15:44:52.233 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:52.240 [info] > git status -z -uall [4ms]
2025-06-07 15:44:52.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:44:57.257 [info] > git config --get commit.template [6ms]
2025-06-07 15:44:57.258 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:44:57.267 [info] > git status -z -uall [4ms]
2025-06-07 15:44:57.268 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:45:02.279 [info] > git config --get commit.template [4ms]
2025-06-07 15:45:02.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:02.290 [info] > git status -z -uall [4ms]
2025-06-07 15:45:02.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:45:07.301 [info] > git config --get commit.template [4ms]
2025-06-07 15:45:07.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:07.308 [info] > git status -z -uall [3ms]
2025-06-07 15:45:07.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:45:12.319 [info] > git config --get commit.template [3ms]
2025-06-07 15:45:12.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:12.329 [info] > git status -z -uall [5ms]
2025-06-07 15:45:12.330 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:45:17.349 [info] > git config --get commit.template [8ms]
2025-06-07 15:45:17.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:45:17.367 [info] > git status -z -uall [10ms]
2025-06-07 15:45:17.368 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:45:22.380 [info] > git config --get commit.template [4ms]
2025-06-07 15:45:22.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:45:22.392 [info] > git status -z -uall [6ms]
2025-06-07 15:45:22.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:45:27.412 [info] > git config --get commit.template [10ms]
2025-06-07 15:45:27.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:27.423 [info] > git status -z -uall [4ms]
2025-06-07 15:45:27.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:45:32.440 [info] > git config --get commit.template [7ms]
2025-06-07 15:45:32.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:32.453 [info] > git status -z -uall [8ms]
2025-06-07 15:45:32.456 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:45:37.471 [info] > git config --get commit.template [6ms]
2025-06-07 15:45:37.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:45:37.482 [info] > git status -z -uall [4ms]
2025-06-07 15:45:37.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:45:42.495 [info] > git config --get commit.template [4ms]
2025-06-07 15:45:42.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:42.508 [info] > git status -z -uall [6ms]
2025-06-07 15:45:42.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:45:47.520 [info] > git config --get commit.template [4ms]
2025-06-07 15:45:47.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:47.536 [info] > git status -z -uall [9ms]
2025-06-07 15:45:47.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-07 15:45:52.632 [info] > git config --get commit.template [85ms]
2025-06-07 15:45:52.641 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:45:52.660 [info] > git status -z -uall [8ms]
2025-06-07 15:45:52.661 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:45:57.673 [info] > git config --get commit.template [1ms]
2025-06-07 15:45:57.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:45:57.696 [info] > git status -z -uall [5ms]
2025-06-07 15:45:57.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:02.708 [info] > git config --get commit.template [4ms]
2025-06-07 15:46:02.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:46:02.716 [info] > git status -z -uall [4ms]
2025-06-07 15:46:02.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:46:07.737 [info] > git config --get commit.template [11ms]
2025-06-07 15:46:07.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:46:07.755 [info] > git status -z -uall [9ms]
2025-06-07 15:46:07.755 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:12.769 [info] > git config --get commit.template [6ms]
2025-06-07 15:46:12.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:12.789 [info] > git status -z -uall [14ms]
2025-06-07 15:46:12.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:17.807 [info] > git config --get commit.template [5ms]
2025-06-07 15:46:17.808 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:46:17.816 [info] > git status -z -uall [4ms]
2025-06-07 15:46:17.817 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:22.833 [info] > git config --get commit.template [6ms]
2025-06-07 15:46:22.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:22.846 [info] > git status -z -uall [6ms]
2025-06-07 15:46:22.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:46:27.864 [info] > git config --get commit.template [6ms]
2025-06-07 15:46:27.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:27.875 [info] > git status -z -uall [4ms]
2025-06-07 15:46:27.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:32.900 [info] > git config --get commit.template [1ms]
2025-06-07 15:46:32.914 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:46:32.925 [info] > git status -z -uall [4ms]
2025-06-07 15:46:32.926 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:37.949 [info] > git config --get commit.template [10ms]
2025-06-07 15:46:37.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:37.976 [info] > git status -z -uall [9ms]
2025-06-07 15:46:37.978 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:46:42.991 [info] > git config --get commit.template [5ms]
2025-06-07 15:46:42.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:42.998 [info] > git status -z -uall [3ms]
2025-06-07 15:46:42.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:48.010 [info] > git config --get commit.template [4ms]
2025-06-07 15:46:48.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:48.024 [info] > git status -z -uall [9ms]
2025-06-07 15:46:48.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:46:53.045 [info] > git config --get commit.template [7ms]
2025-06-07 15:46:53.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:53.069 [info] > git status -z -uall [9ms]
2025-06-07 15:46:53.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:46:58.086 [info] > git config --get commit.template [5ms]
2025-06-07 15:46:58.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:46:58.099 [info] > git status -z -uall [6ms]
2025-06-07 15:46:58.101 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:03.118 [info] > git config --get commit.template [7ms]
2025-06-07 15:47:03.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:47:03.133 [info] > git status -z -uall [8ms]
2025-06-07 15:47:03.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:08.149 [info] > git config --get commit.template [5ms]
2025-06-07 15:47:08.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:47:08.158 [info] > git status -z -uall [4ms]
2025-06-07 15:47:08.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:47:13.174 [info] > git config --get commit.template [5ms]
2025-06-07 15:47:13.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:47:13.184 [info] > git status -z -uall [5ms]
2025-06-07 15:47:13.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:18.197 [info] > git config --get commit.template [1ms]
2025-06-07 15:47:18.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:47:18.216 [info] > git status -z -uall [7ms]
2025-06-07 15:47:18.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:23.231 [info] > git config --get commit.template [5ms]
2025-06-07 15:47:23.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:47:23.241 [info] > git status -z -uall [4ms]
2025-06-07 15:47:23.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:28.262 [info] > git config --get commit.template [9ms]
2025-06-07 15:47:28.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:47:28.277 [info] > git status -z -uall [7ms]
2025-06-07 15:47:28.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:33.296 [info] > git config --get commit.template [6ms]
2025-06-07 15:47:33.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:47:33.306 [info] > git status -z -uall [4ms]
2025-06-07 15:47:33.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:47:38.327 [info] > git config --get commit.template [9ms]
2025-06-07 15:47:38.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:47:38.347 [info] > git status -z -uall [8ms]
2025-06-07 15:47:38.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:47:43.359 [info] > git config --get commit.template [2ms]
2025-06-07 15:47:43.365 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:47:43.379 [info] > git status -z -uall [10ms]
2025-06-07 15:47:43.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:47:48.403 [info] > git config --get commit.template [12ms]
2025-06-07 15:47:48.404 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:47:48.423 [info] > git status -z -uall [9ms]
2025-06-07 15:47:48.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:47:55.462 [info] > git config --get commit.template [4ms]
2025-06-07 15:47:55.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:47:55.474 [info] > git status -z -uall [7ms]
2025-06-07 15:47:55.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:48:01.466 [info] > git config --get commit.template [7ms]
2025-06-07 15:48:01.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:01.478 [info] > git status -z -uall [6ms]
2025-06-07 15:48:01.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:48:06.495 [info] > git config --get commit.template [0ms]
2025-06-07 15:48:06.506 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:06.528 [info] > git status -z -uall [10ms]
2025-06-07 15:48:06.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:48:11.548 [info] > git config --get commit.template [8ms]
2025-06-07 15:48:11.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:11.565 [info] > git status -z -uall [8ms]
2025-06-07 15:48:11.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:48:16.612 [info] > git config --get commit.template [8ms]
2025-06-07 15:48:16.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:48:16.631 [info] > git status -z -uall [10ms]
2025-06-07 15:48:16.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:48:21.644 [info] > git config --get commit.template [4ms]
2025-06-07 15:48:21.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:48:21.653 [info] > git status -z -uall [4ms]
2025-06-07 15:48:21.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:48:26.670 [info] > git config --get commit.template [7ms]
2025-06-07 15:48:26.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:26.680 [info] > git status -z -uall [5ms]
2025-06-07 15:48:26.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:48:31.700 [info] > git config --get commit.template [7ms]
2025-06-07 15:48:31.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:48:31.714 [info] > git status -z -uall [6ms]
2025-06-07 15:48:31.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:48:36.730 [info] > git config --get commit.template [6ms]
2025-06-07 15:48:36.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:36.739 [info] > git status -z -uall [4ms]
2025-06-07 15:48:36.740 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:48:41.760 [info] > git config --get commit.template [8ms]
2025-06-07 15:48:41.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:41.770 [info] > git status -z -uall [4ms]
2025-06-07 15:48:41.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:48:46.784 [info] > git config --get commit.template [2ms]
2025-06-07 15:48:46.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:46.808 [info] > git status -z -uall [10ms]
2025-06-07 15:48:46.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:48:51.830 [info] > git config --get commit.template [8ms]
2025-06-07 15:48:51.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:48:51.862 [info] > git status -z -uall [21ms]
2025-06-07 15:48:51.865 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-07 15:48:56.888 [info] > git config --get commit.template [9ms]
2025-06-07 15:48:56.891 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:48:56.924 [info] > git status -z -uall [19ms]
2025-06-07 15:48:56.926 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 15:49:01.943 [info] > git config --get commit.template [6ms]
2025-06-07 15:49:01.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:01.960 [info] > git status -z -uall [8ms]
2025-06-07 15:49:01.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:49:06.976 [info] > git config --get commit.template [5ms]
2025-06-07 15:49:06.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:06.990 [info] > git status -z -uall [7ms]
2025-06-07 15:49:06.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:49:12.006 [info] > git config --get commit.template [6ms]
2025-06-07 15:49:12.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:49:12.021 [info] > git status -z -uall [7ms]
2025-06-07 15:49:12.022 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:49:17.037 [info] > git config --get commit.template [5ms]
2025-06-07 15:49:17.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:17.048 [info] > git status -z -uall [5ms]
2025-06-07 15:49:17.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:49:22.061 [info] > git config --get commit.template [4ms]
2025-06-07 15:49:22.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:49:22.079 [info] > git status -z -uall [10ms]
2025-06-07 15:49:22.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:49:27.102 [info] > git config --get commit.template [6ms]
2025-06-07 15:49:27.103 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:27.115 [info] > git status -z -uall [6ms]
2025-06-07 15:49:27.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:49:32.131 [info] > git config --get commit.template [6ms]
2025-06-07 15:49:32.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:32.143 [info] > git status -z -uall [5ms]
2025-06-07 15:49:32.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:49:37.161 [info] > git config --get commit.template [5ms]
2025-06-07 15:49:37.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 15:49:37.175 [info] > git status -z -uall [5ms]
2025-06-07 15:49:37.176 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:49:42.195 [info] > git config --get commit.template [9ms]
2025-06-07 15:49:42.196 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:42.212 [info] > git status -z -uall [9ms]
2025-06-07 15:49:42.213 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:49:47.234 [info] > git config --get commit.template [9ms]
2025-06-07 15:49:47.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:47.253 [info] > git status -z -uall [8ms]
2025-06-07 15:49:47.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:49:52.271 [info] > git config --get commit.template [7ms]
2025-06-07 15:49:52.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:49:52.289 [info] > git status -z -uall [7ms]
2025-06-07 15:49:52.291 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:49:57.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:49:57.315 [info] > git config --get commit.template [11ms]
2025-06-07 15:49:57.330 [info] > git status -z -uall [9ms]
2025-06-07 15:49:57.330 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-07 15:50:02.349 [info] > git config --get commit.template [8ms]
2025-06-07 15:50:02.350 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:02.363 [info] > git status -z -uall [8ms]
2025-06-07 15:50:02.364 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:50:07.380 [info] > git config --get commit.template [2ms]
2025-06-07 15:50:07.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:07.398 [info] > git status -z -uall [6ms]
2025-06-07 15:50:07.400 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:50:12.417 [info] > git config --get commit.template [9ms]
2025-06-07 15:50:12.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:12.432 [info] > git status -z -uall [7ms]
2025-06-07 15:50:12.433 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:50:17.450 [info] > git config --get commit.template [7ms]
2025-06-07 15:50:17.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:17.468 [info] > git status -z -uall [6ms]
2025-06-07 15:50:17.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:50:22.479 [info] > git config --get commit.template [0ms]
2025-06-07 15:50:22.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:22.509 [info] > git status -z -uall [20ms]
2025-06-07 15:50:22.509 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 15:50:27.524 [info] > git config --get commit.template [5ms]
2025-06-07 15:50:27.525 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:27.534 [info] > git status -z -uall [5ms]
2025-06-07 15:50:27.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:50:32.546 [info] > git config --get commit.template [5ms]
2025-06-07 15:50:32.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:32.555 [info] > git status -z -uall [4ms]
2025-06-07 15:50:32.556 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:50:37.625 [info] > git config --get commit.template [56ms]
2025-06-07 15:50:37.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:37.653 [info] > git status -z -uall [12ms]
2025-06-07 15:50:37.653 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:50:42.671 [info] > git config --get commit.template [8ms]
2025-06-07 15:50:42.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:42.689 [info] > git status -z -uall [10ms]
2025-06-07 15:50:42.693 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 15:50:47.709 [info] > git config --get commit.template [6ms]
2025-06-07 15:50:47.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:50:47.726 [info] > git status -z -uall [10ms]
2025-06-07 15:50:47.728 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:50:52.749 [info] > git config --get commit.template [7ms]
2025-06-07 15:50:52.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:50:52.779 [info] > git status -z -uall [10ms]
2025-06-07 15:50:52.780 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:50:57.797 [info] > git config --get commit.template [1ms]
2025-06-07 15:50:57.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:50:57.855 [info] > git status -z -uall [29ms]
2025-06-07 15:50:57.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:51:02.885 [info] > git config --get commit.template [8ms]
2025-06-07 15:51:02.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:02.904 [info] > git status -z -uall [10ms]
2025-06-07 15:51:02.904 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:51:07.927 [info] > git config --get commit.template [10ms]
2025-06-07 15:51:07.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:51:07.954 [info] > git status -z -uall [13ms]
2025-06-07 15:51:07.957 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:51:12.973 [info] > git config --get commit.template [6ms]
2025-06-07 15:51:12.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:12.982 [info] > git status -z -uall [4ms]
2025-06-07 15:51:12.983 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:51:18.001 [info] > git config --get commit.template [8ms]
2025-06-07 15:51:18.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:18.018 [info] > git status -z -uall [8ms]
2025-06-07 15:51:18.019 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:51:23.034 [info] > git config --get commit.template [7ms]
2025-06-07 15:51:23.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:51:23.049 [info] > git status -z -uall [8ms]
2025-06-07 15:51:23.051 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:51:28.070 [info] > git config --get commit.template [8ms]
2025-06-07 15:51:28.071 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:51:28.088 [info] > git status -z -uall [8ms]
2025-06-07 15:51:28.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:51:33.105 [info] > git config --get commit.template [5ms]
2025-06-07 15:51:33.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:33.121 [info] > git status -z -uall [8ms]
2025-06-07 15:51:33.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:51:38.138 [info] > git config --get commit.template [5ms]
2025-06-07 15:51:38.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 15:51:38.147 [info] > git status -z -uall [4ms]
2025-06-07 15:51:38.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:51:43.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:43.166 [info] > git config --get commit.template [8ms]
2025-06-07 15:51:43.182 [info] > git status -z -uall [7ms]
2025-06-07 15:51:43.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:51:48.198 [info] > git config --get commit.template [5ms]
2025-06-07 15:51:48.198 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:48.213 [info] > git status -z -uall [8ms]
2025-06-07 15:51:48.214 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:51:53.233 [info] > git config --get commit.template [9ms]
2025-06-07 15:51:53.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:51:53.250 [info] > git status -z -uall [9ms]
2025-06-07 15:51:53.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:51:58.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 15:51:58.279 [info] > git config --get commit.template [12ms]
2025-06-07 15:51:58.307 [info] > git status -z -uall [15ms]
2025-06-07 15:51:58.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:52:03.330 [info] > git config --get commit.template [8ms]
2025-06-07 15:52:03.330 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:52:03.347 [info] > git status -z -uall [8ms]
2025-06-07 15:52:03.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 15:52:08.371 [info] > git config --get commit.template [6ms]
2025-06-07 15:52:08.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:52:08.387 [info] > git status -z -uall [9ms]
2025-06-07 15:52:08.388 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:13.413 [info] > git config --get commit.template [10ms]
2025-06-07 15:52:13.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:52:13.439 [info] > git status -z -uall [12ms]
2025-06-07 15:52:13.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:52:18.458 [info] > git config --get commit.template [6ms]
2025-06-07 15:52:18.460 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:52:18.470 [info] > git status -z -uall [5ms]
2025-06-07 15:52:18.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:23.483 [info] > git config --get commit.template [1ms]
2025-06-07 15:52:23.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:52:23.512 [info] > git status -z -uall [12ms]
2025-06-07 15:52:23.514 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:28.531 [info] > git config --get commit.template [5ms]
2025-06-07 15:52:28.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:52:28.547 [info] > git status -z -uall [8ms]
2025-06-07 15:52:28.550 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:52:33.576 [info] > git config --get commit.template [9ms]
2025-06-07 15:52:33.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:52:33.612 [info] > git status -z -uall [12ms]
2025-06-07 15:52:33.613 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:38.630 [info] > git config --get commit.template [6ms]
2025-06-07 15:52:38.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:52:38.648 [info] > git status -z -uall [7ms]
2025-06-07 15:52:38.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:43.668 [info] > git config --get commit.template [9ms]
2025-06-07 15:52:43.668 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:52:43.678 [info] > git status -z -uall [5ms]
2025-06-07 15:52:43.679 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:52:48.702 [info] > git config --get commit.template [10ms]
2025-06-07 15:52:48.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:52:48.716 [info] > git status -z -uall [6ms]
2025-06-07 15:52:48.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:53.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 15:52:53.751 [info] > git config --get commit.template [18ms]
2025-06-07 15:52:53.764 [info] > git status -z -uall [5ms]
2025-06-07 15:52:53.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:52:58.787 [info] > git config --get commit.template [8ms]
2025-06-07 15:52:58.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:52:58.801 [info] > git status -z -uall [6ms]
2025-06-07 15:52:58.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:03.825 [info] > git config --get commit.template [8ms]
2025-06-07 15:53:03.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:03.841 [info] > git status -z -uall [8ms]
2025-06-07 15:53:03.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 15:53:08.873 [info] > git config --get commit.template [12ms]
2025-06-07 15:53:08.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 15:53:08.901 [info] > git status -z -uall [10ms]
2025-06-07 15:53:08.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:13.940 [info] > git config --get commit.template [17ms]
2025-06-07 15:53:13.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:13.970 [info] > git status -z -uall [11ms]
2025-06-07 15:53:13.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:18.995 [info] > git config --get commit.template [11ms]
2025-06-07 15:53:18.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:19.016 [info] > git status -z -uall [10ms]
2025-06-07 15:53:19.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:24.037 [info] > git config --get commit.template [0ms]
2025-06-07 15:53:24.048 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:53:24.067 [info] > git status -z -uall [9ms]
2025-06-07 15:53:24.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:29.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:53:29.095 [info] > git config --get commit.template [14ms]
2025-06-07 15:53:29.117 [info] > git status -z -uall [12ms]
2025-06-07 15:53:29.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:34.140 [info] > git config --get commit.template [9ms]
2025-06-07 15:53:34.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:34.157 [info] > git status -z -uall [6ms]
2025-06-07 15:53:34.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:39.180 [info] > git config --get commit.template [9ms]
2025-06-07 15:53:39.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:39.192 [info] > git status -z -uall [5ms]
2025-06-07 15:53:39.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:53:44.211 [info] > git config --get commit.template [7ms]
2025-06-07 15:53:44.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:44.223 [info] > git status -z -uall [5ms]
2025-06-07 15:53:44.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:53:49.240 [info] > git config --get commit.template [6ms]
2025-06-07 15:53:49.241 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:53:49.261 [info] > git status -z -uall [7ms]
2025-06-07 15:53:49.262 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:53:54.282 [info] > git config --get commit.template [10ms]
2025-06-07 15:53:54.284 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:53:54.306 [info] > git status -z -uall [7ms]
2025-06-07 15:53:54.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:53:59.328 [info] > git config --get commit.template [8ms]
2025-06-07 15:53:59.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:53:59.346 [info] > git status -z -uall [9ms]
2025-06-07 15:53:59.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:54:04.375 [info] > git config --get commit.template [9ms]
2025-06-07 15:54:04.383 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 15:54:04.408 [info] > git status -z -uall [11ms]
2025-06-07 15:54:04.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 15:54:09.433 [info] > git config --get commit.template [9ms]
2025-06-07 15:54:09.434 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:54:09.449 [info] > git status -z -uall [9ms]
2025-06-07 15:54:09.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:54:14.470 [info] > git config --get commit.template [9ms]
2025-06-07 15:54:14.472 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:54:14.493 [info] > git status -z -uall [11ms]
2025-06-07 15:54:14.494 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:54:19.516 [info] > git config --get commit.template [9ms]
2025-06-07 15:54:19.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:54:19.537 [info] > git status -z -uall [9ms]
2025-06-07 15:54:19.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:54:24.574 [info] > git config --get commit.template [11ms]
2025-06-07 15:54:24.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:54:24.610 [info] > git status -z -uall [18ms]
2025-06-07 15:54:24.616 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-07 15:54:29.642 [info] > git config --get commit.template [12ms]
2025-06-07 15:54:29.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:54:29.661 [info] > git status -z -uall [9ms]
2025-06-07 15:54:29.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:54:42.261 [info] > git config --get commit.template [9ms]
2025-06-07 15:54:42.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:54:42.281 [info] > git status -z -uall [8ms]
2025-06-07 15:54:42.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:54:47.298 [info] > git config --get commit.template [5ms]
2025-06-07 15:54:47.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:54:47.307 [info] > git status -z -uall [4ms]
2025-06-07 15:54:47.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:54:52.327 [info] > git config --get commit.template [9ms]
2025-06-07 15:54:52.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:54:52.345 [info] > git status -z -uall [9ms]
2025-06-07 15:54:52.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:54:57.365 [info] > git config --get commit.template [7ms]
2025-06-07 15:54:57.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:54:57.385 [info] > git status -z -uall [10ms]
2025-06-07 15:54:57.385 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:55:02.400 [info] > git config --get commit.template [5ms]
2025-06-07 15:55:02.401 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:55:02.418 [info] > git status -z -uall [9ms]
2025-06-07 15:55:02.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:55:07.438 [info] > git config --get commit.template [8ms]
2025-06-07 15:55:07.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:55:07.457 [info] > git status -z -uall [12ms]
2025-06-07 15:55:07.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:55:12.474 [info] > git config --get commit.template [7ms]
2025-06-07 15:55:12.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:55:12.491 [info] > git status -z -uall [9ms]
2025-06-07 15:55:12.493 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:55:17.512 [info] > git config --get commit.template [8ms]
2025-06-07 15:55:17.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:55:17.525 [info] > git status -z -uall [7ms]
2025-06-07 15:55:17.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:55:22.543 [info] > git config --get commit.template [2ms]
2025-06-07 15:55:22.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:55:22.576 [info] > git status -z -uall [9ms]
2025-06-07 15:55:22.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:55:27.597 [info] > git config --get commit.template [7ms]
2025-06-07 15:55:27.598 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:55:27.606 [info] > git status -z -uall [4ms]
2025-06-07 15:55:27.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:55:32.623 [info] > git config --get commit.template [6ms]
2025-06-07 15:55:32.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:55:32.645 [info] > git status -z -uall [10ms]
2025-06-07 15:55:32.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:55:37.665 [info] > git config --get commit.template [6ms]
2025-06-07 15:55:37.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:55:37.682 [info] > git status -z -uall [7ms]
2025-06-07 15:55:37.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:55:42.705 [info] > git config --get commit.template [10ms]
2025-06-07 15:55:42.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 15:55:42.723 [info] > git status -z -uall [8ms]
2025-06-07 15:55:42.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:55:47.745 [info] > git config --get commit.template [7ms]
2025-06-07 15:55:47.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:55:47.761 [info] > git status -z -uall [7ms]
2025-06-07 15:55:47.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:58:05.951 [info] > git config --get commit.template [6ms]
2025-06-07 15:58:05.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:58:05.962 [info] > git status -z -uall [4ms]
2025-06-07 15:58:05.963 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:58:10.982 [info] > git config --get commit.template [8ms]
2025-06-07 15:58:10.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:58:10.997 [info] > git status -z -uall [7ms]
2025-06-07 15:58:10.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 15:58:16.009 [info] > git config --get commit.template [3ms]
2025-06-07 15:58:16.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:58:16.019 [info] > git status -z -uall [5ms]
2025-06-07 15:58:16.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:58:21.037 [info] > git config --get commit.template [7ms]
2025-06-07 15:58:21.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:58:21.052 [info] > git status -z -uall [7ms]
2025-06-07 15:58:21.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:59:01.126 [info] > git config --get commit.template [1ms]
2025-06-07 15:59:01.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:59:01.148 [info] > git status -z -uall [10ms]
2025-06-07 15:59:01.149 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 15:59:06.166 [info] > git config --get commit.template [4ms]
2025-06-07 15:59:06.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:59:06.197 [info] > git status -z -uall [8ms]
2025-06-07 15:59:06.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:59:11.215 [info] > git config --get commit.template [7ms]
2025-06-07 15:59:11.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 15:59:11.233 [info] > git status -z -uall [9ms]
2025-06-07 15:59:11.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 15:59:16.246 [info] > git config --get commit.template [4ms]
2025-06-07 15:59:16.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 15:59:16.255 [info] > git status -z -uall [4ms]
2025-06-07 15:59:16.256 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:00:35.013 [info] > git config --get commit.template [7ms]
2025-06-07 16:00:35.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:00:35.029 [info] > git status -z -uall [8ms]
2025-06-07 16:00:35.030 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:00:35.040 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-07 16:00:35.048 [info] > git diff --name-status -z --diff-filter=ADMR afcc68bbfc7a9840fe8bc9258ce0aab95444c453...refs/remotes/origin/main [1ms]
2025-06-07 16:00:40.053 [info] > git config --get commit.template [7ms]
2025-06-07 16:00:40.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:00:40.069 [info] > git status -z -uall [9ms]
2025-06-07 16:00:40.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:00:45.087 [info] > git config --get commit.template [7ms]
2025-06-07 16:00:45.088 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:00:45.107 [info] > git status -z -uall [7ms]
2025-06-07 16:00:45.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:00:50.124 [info] > git config --get commit.template [7ms]
2025-06-07 16:00:50.126 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:00:50.138 [info] > git status -z -uall [4ms]
2025-06-07 16:00:50.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:00:55.151 [info] > git config --get commit.template [5ms]
2025-06-07 16:00:55.152 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:00:55.167 [info] > git status -z -uall [10ms]
2025-06-07 16:00:55.168 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:01:12.181 [info] > git config --get commit.template [5ms]
2025-06-07 16:01:12.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:12.201 [info] > git status -z -uall [10ms]
2025-06-07 16:01:12.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:01:17.222 [info] > git config --get commit.template [8ms]
2025-06-07 16:01:17.223 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:17.241 [info] > git status -z -uall [9ms]
2025-06-07 16:01:17.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:01:22.259 [info] > git config --get commit.template [6ms]
2025-06-07 16:01:22.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:22.277 [info] > git status -z -uall [8ms]
2025-06-07 16:01:22.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:01:27.293 [info] > git config --get commit.template [5ms]
2025-06-07 16:01:27.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:27.303 [info] > git status -z -uall [5ms]
2025-06-07 16:01:27.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:01:32.325 [info] > git config --get commit.template [7ms]
2025-06-07 16:01:32.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:32.342 [info] > git status -z -uall [9ms]
2025-06-07 16:01:32.342 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 16:01:37.364 [info] > git config --get commit.template [9ms]
2025-06-07 16:01:37.365 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:37.380 [info] > git status -z -uall [7ms]
2025-06-07 16:01:37.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:01:46.366 [info] > git config --get commit.template [9ms]
2025-06-07 16:01:46.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:46.380 [info] > git status -z -uall [6ms]
2025-06-07 16:01:46.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:01:51.404 [info] > git config --get commit.template [1ms]
2025-06-07 16:01:51.421 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:01:51.442 [info] > git status -z -uall [11ms]
2025-06-07 16:01:51.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:01:56.462 [info] > git config --get commit.template [8ms]
2025-06-07 16:01:56.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:01:56.472 [info] > git status -z -uall [4ms]
2025-06-07 16:01:56.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:02:01.498 [info] > git config --get commit.template [11ms]
2025-06-07 16:02:01.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 16:02:01.516 [info] > git status -z -uall [10ms]
2025-06-07 16:02:01.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 16:02:09.221 [info] > git config --get commit.template [2ms]
2025-06-07 16:02:09.230 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 16:02:09.244 [info] > git status -z -uall [8ms]
2025-06-07 16:02:09.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 16:02:14.270 [info] > git config --get commit.template [1ms]
2025-06-07 16:02:14.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 16:02:14.290 [info] > git status -z -uall [6ms]
2025-06-07 16:02:14.292 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
