2025-06-07 15:38:38.473 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 15:38:38.473 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-07 15:38:38.490 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-07 15:38:39.454 [info] 'AugmentExtension' Retrieving model config
2025-06-07 15:38:39.760 [info] 'AugmentExtension' Retrieved model config
2025-06-07 15:38:39.761 [info] 'AugmentExtension' Returning model config
2025-06-07 15:38:39.984 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-07 15:38:39.984 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 15:38:39.984 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-07 15:38:39.984 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-07 15:38:39.984 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-07 15:38:39.984 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-07 15:38:39.985 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-07 15:38:40.005 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 15:38:40.006 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 15:38:40.006 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-07 15:38:40.006 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-07 15:38:40.029 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 15:38:40.030 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 15:38:40.401 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-07 15:38:40.402 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-07 15:38:40.403 [info] 'OpenFileManager' Opened source folder 100
2025-06-07 15:38:40.403 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 15:38:40.418 [info] 'MtimeCache[workspace]' read 2080 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 15:38:40.714 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 15:38:40.716 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 15:38:40.775 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 15:38:40.775 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 15:38:40.775 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 15:38:40.775 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 15:38:41.048 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-07 15:38:41.048 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-07 15:38:41.258 [info] 'TaskManager' Setting current root task UUID to 1367e5ab-50f8-466f-a7cf-cbf171c70833
2025-06-07 15:38:42.547 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/0526bb66-e090-4e1f-b11b-2166ef5a9f81/document-_home_runner_workspace_server_schema.ts-1749309404892-53c8c554-da68-46af-9167-b812b9537ea8.json
2025-06-07 15:38:42.547 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/0526bb66-e090-4e1f-b11b-2166ef5a9f81/document-_home_runner_workspace_server_schema.ts-1749309404892-53c8c554-da68-46af-9167-b812b9537ea8.json
2025-06-07 15:38:42.844 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:38:42.844 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:38:43.981 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:38:44.453 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:38:49.614 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-07 15:38:49.614 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 723
  - files emitted: 2562
  - other paths emitted: 4
  - total paths emitted: 3289
  - timing stats:
    - readDir: 10 ms
    - filter: 99 ms
    - yield: 18 ms
    - total: 142 ms
2025-06-07 15:38:49.614 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2108
  - paths not accessible: 0
  - not plain files: 0
  - large files: 40
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 2065
  - mtime cache misses: 43
  - probe batches: 4
  - blob names probed: 2130
  - files read: 494
  - blobs uploaded: 22
  - timing stats:
    - ingestPath: 7 ms
    - probe: 1684 ms
    - stat: 33 ms
    - read: 3477 ms
    - upload: 801 ms
2025-06-07 15:38:49.614 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 3 ms
  - read MtimeCache: 15 ms
  - pre-populate PathMap: 172 ms
  - create PathFilter: 212 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 145 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 8662 ms
  - enable persist: 3 ms
  - total: 9214 ms
2025-06-07 15:38:49.614 [info] 'WorkspaceManager' Workspace startup complete in 9644 ms
2025-06-07 15:39:08.153 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:39:08.694 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:39:17.969 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:39:18.152 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46887 bytes)
2025-06-07 15:39:24.052 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 15:39:35.148 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:39:35.149 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (46887 bytes)
2025-06-07 15:39:35.502 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:39:36.421 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:39:36.901 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:39:36.902 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (47415 bytes)
2025-06-07 15:40:15.696 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:15.697 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (47415 bytes)
2025-06-07 15:40:16.007 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:40:16.880 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:40:17.301 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:17.302 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (47440 bytes)
2025-06-07 15:40:30.493 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:30.493 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (47440 bytes)
2025-06-07 15:40:30.685 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:40:31.626 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:40:32.002 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:32.003 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48263 bytes)
2025-06-07 15:40:37.578 [error] 'AugmentExtension' API request 23b7938f-1f1b-4ced-b307-07d593995306 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 15:40:37.888 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 15:40:38.203 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 15:40:44.661 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:44.661 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48263 bytes)
2025-06-07 15:40:44.921 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:40:45.817 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:40:46.574 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:46.574 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48622 bytes)
2025-06-07 15:40:57.441 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:40:57.441 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48622 bytes)
2025-06-07 15:41:03.088 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 15:41:09.842 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 15:41:18.882 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:41:18.882 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48622 bytes)
2025-06-07 15:41:19.071 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:41:20.016 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:41:20.416 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:41:20.417 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48819 bytes)
2025-06-07 15:42:27.138 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:42:27.138 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48819 bytes)
2025-06-07 15:42:27.431 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:42:28.406 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:42:28.776 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:42:28.777 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (49259 bytes)
2025-06-07 15:42:37.711 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:42:37.901 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (49259 bytes)
2025-06-07 15:42:43.747 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 15:42:54.997 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:42:54.998 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (49259 bytes)
2025-06-07 15:42:55.403 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:42:56.263 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:42:56.854 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-07 15:42:56.854 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (48819 bytes)
2025-06-07 15:44:14.211 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:44:14.397 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:44:14.898 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:54:09.756 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:55:08.240 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:55:08.786 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:55:27.018 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:56:22.599 [error] 'AugmentExtension' API request 70c4700a-dc78-4701-b792-f058689c96ed to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 15:56:22.837 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 15:56:23.141 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 15:56:25.600 [error] 'AugmentExtension' API request 4ffe96b9-f580-482e-8412-0bb87265a4dc to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 15:56:25.800 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 15:56:26.072 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 15:59:02.705 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 15:59:02.814 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 15:59:03.128 [info] 'TaskManager' Setting current root task UUID to 3307715c-011b-4fa4-9fac-10ec4fc87746
2025-06-07 15:59:03.128 [info] 'TaskManager' Setting current root task UUID to 3307715c-011b-4fa4-9fac-10ec4fc87746
2025-06-07 15:59:08.958 [info] 'TaskManager' Setting current root task UUID to 1367e5ab-50f8-466f-a7cf-cbf171c70833
2025-06-07 15:59:08.958 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 15:59:11.025 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-06-07 16:00:19.611 [error] 'AugmentExtension' API request 8a02345f-98e4-4959-b529-417f5a9815f9 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 16:00:19.839 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 16:00:20.149 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-07 16:02:10.622 [error] 'AugmentExtension' API request 507ad82f-c432-476f-ade1-ff3bb8541dad to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-07 16:02:10.905 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-07 16:02:11.219 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
