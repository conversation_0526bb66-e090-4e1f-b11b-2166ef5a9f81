import { pgTable, text, serial, integer, boolean, timestamp, json, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User role enum
export const userRoleEnum = pgEnum('user_role', ['client', 'doctor', 'admin']);

// Type of invitation code
export const invitationTypeEnum = pgEnum('invitation_type', ['doctor', 'client']);

// User table for all user types
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  name: text("name").notNull(),
  userRole: userRoleEnum("user_role").notNull().default('client'),
  professionalRole: text("professional_role"),  // For doctors (e.g., "Psychologist", "Therapist")
  email: text("email").notNull().unique(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
});

// Client table
export const clients = pgTable("clients", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  status: text("status").notNull().default("stable"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertClientSchema = createInsertSchema(clients).omit({
  id: true,
  createdAt: true,
});

// Session notes table
export const sessionNotes = pgTable("session_notes", {
  id: serial("id").primaryKey(),
  clientId: integer("client_id").notNull().references(() => clients.id),
  createdBy: integer("created_by").notNull().references(() => users.id),
  content: text("content").notNull(),
  summary: text("summary"),
  recommendations: text("recommendations").array(),
  analyzed: boolean("analyzed").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  // Additional fields for client compatibility
  sessionDate: timestamp("session_date").defaultNow().notNull(),
  duration: integer("duration").default(50).notNull(), // Duration in minutes
  aiProcessed: boolean("ai_processed").default(false).notNull(),
  rawNotes: text("raw_notes"), // Alias for content for client compatibility
});

export const insertSessionNoteSchema = createInsertSchema(sessionNotes).omit({
  id: true,
  summary: true,
  recommendations: true,
  analyzed: true,
  createdAt: true,
  aiProcessed: true,
});

// Client themes table
export const clientThemes = pgTable("client_themes", {
  id: serial("id").primaryKey(),
  clientId: integer("client_id").notNull().references(() => clients.id),
  name: text("name").notNull(),
  occurrences: integer("occurrences").notNull().default(1),
  trend: integer("trend").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertClientThemeSchema = createInsertSchema(clientThemes).omit({
  id: true,
  createdAt: true,
});

// Theme interface
export interface Theme {
  name: string;
  occurrences: number;
  trend: number; // positive, negative, or zero
}

// Type definitions
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Client = typeof clients.$inferSelect;
export type InsertClient = z.infer<typeof insertClientSchema>;

export type SessionNote = typeof sessionNotes.$inferSelect;
export type InsertSessionNote = z.infer<typeof insertSessionNoteSchema>;

export type ClientTheme = typeof clientThemes.$inferSelect;
export type InsertClientTheme = z.infer<typeof insertClientThemeSchema>;

// AI Therapy Conversations table
export const aiTherapyConversations = pgTable("ai_therapy_conversations", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  clientId: integer("client_id").notNull().references(() => clients.id),
  // Using created_at as the start time instead of a separate started_at field
  endedAt: timestamp("ended_at"),
  title: text("title").notNull().default("New Conversation"),
  summary: text("summary"),
  active: boolean("active").default(true).notNull(),
  aiProcessed: boolean("ai_processed").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertAiTherapyConversationSchema = createInsertSchema(aiTherapyConversations).omit({
  id: true,
  endedAt: true,
  summary: true,
  aiProcessed: true,
  createdAt: true,
});

// AI Therapy Messages table
export const aiTherapyMessages = pgTable("ai_therapy_messages", {
  id: serial("id").primaryKey(),
  conversationId: integer("conversation_id").notNull().references(() => aiTherapyConversations.id),
  role: text("role").notNull(), // 'user' or 'assistant'
  content: text("content").notNull(),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
  audioUrl: text("audio_url"), // URL to stored audio file for voice messages
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertAiTherapyMessageSchema = createInsertSchema(aiTherapyMessages).omit({
  id: true,
  createdAt: true,
});

// AI Therapy Settings for Admin configuration
export const aiTherapySettings = pgTable("ai_therapy_settings", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  systemPrompt: text("system_prompt").notNull(),
  model: text("model").notNull().default("gpt-4o"),
  temperature: integer("temperature").notNull().default(70), // 0-100 representing 0-1.0
  active: boolean("active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertAiTherapySettingsSchema = createInsertSchema(aiTherapySettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Doctor-Client relationships
export const doctorClients = pgTable("doctor_clients", {
  id: serial("id").primaryKey(),
  doctorId: integer("doctor_id").notNull().references(() => users.id),
  clientId: integer("client_id").notNull().references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertDoctorClientSchema = createInsertSchema(doctorClients).omit({
  id: true,
  createdAt: true,
});

// Dashboard stats - This is a helper type, not a table
export interface DashboardStats {
  totalClients: number;
  weeklyNotes: number;
  insights: number;
  improving: number;
  improvingPercentage: number;
}

// Type definitions for new tables
export type AiTherapyConversation = typeof aiTherapyConversations.$inferSelect;
export type InsertAiTherapyConversation = z.infer<typeof insertAiTherapyConversationSchema>;

export type AiTherapyMessage = typeof aiTherapyMessages.$inferSelect;
export type InsertAiTherapyMessage = z.infer<typeof insertAiTherapyMessageSchema>;

export type AiTherapySettings = typeof aiTherapySettings.$inferSelect;
export type InsertAiTherapySettings = z.infer<typeof insertAiTherapySettingsSchema>;

export type DoctorClient = typeof doctorClients.$inferSelect;
export type InsertDoctorClient = z.infer<typeof insertDoctorClientSchema>;

// Invitation codes table
export const invitationCodes = pgTable("invitation_codes", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  type: invitationTypeEnum("type").notNull(),
  createdBy: integer("created_by_id").notNull().references(() => users.id),
  used: boolean("used").default(false).notNull(),
  usedBy: integer("used_by_id").references(() => users.id),
  usedAt: timestamp("used_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  expiresAt: timestamp("expires_at"),
});

export const insertInvitationCodeSchema = createInsertSchema(invitationCodes).omit({
  id: true,
  used: true,
  usedBy: true,
  usedAt: true,
  createdAt: true,
});

export type InvitationCode = typeof invitationCodes.$inferSelect;
export type InsertInvitationCode = z.infer<typeof insertInvitationCodeSchema>;

export interface VoiceConfig {
  voice: string;
  speed: number;
  pitch: number;
  emphasis: string;
  prosody: {
    emotionalRange: number;
    questionInflection: number;
    pauseDuration: number;
  };
}

export interface ConversationConfig {
  temperature: number;
  maxTokens: number;
  presencePenalty: number;
  frequencyPenalty: number;
  turnTaking: {
    backchannelFrequency: number;
    minSilenceDuration: number;
    maxInterruptionGap: number;
  };
  responseStyle: {
    minResponseLength: number;
    maxResponseLength: number;
    temperature: number;
    presencePenalty: number;
    frequencyPenalty: number;
  };
}

export interface Metrics {
  latency: number;
  responseTime: number;
  audioQuality: number;
  conversationFlow: number;
}

export type WebSocketMessage = {
  type: 'error' | 'audio' | 'voice_config' | 'voice_config_updated' | 
        'conversation_config' | 'conversation_config_updated' | 'metrics_update' | 
        'reset' | 'reset_complete' | 'transcript' | 'assistant_response' | 'summary' |
        'start' | 'ready' | 'status' | 'transcription' | 'processing' | 'processing_audio' |
        'chunk' | 'complete' | 'complete_with_audio';
  message?: string;
  config?: VoiceConfig | ConversationConfig;
  audioData?: string;
  userId?: string | number;
  clientId?: string | number;
  conversationId?: string | number;
  metrics?: {
    latency: number;
    responseTime: number;
    audioQuality?: number;
    conversationFlow?: number;
  };
  text?: string;
  audioUrl?: string;
  content?: string;
  summary?: {
    duration: number;
    totalTurns: number;
    averageResponseTime: number;
    keyThemes: string[];
    recommendations: string[];
  };
};
