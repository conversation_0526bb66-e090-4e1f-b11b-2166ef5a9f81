import express, { Express, Request, Response, NextFunction } from 'express';
import { Server as HttpServer, createServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import { 
  analyzeTherapyNotes, 
  transcribeAudio, 
  streamAITherapistResponse,
  summarizeTherapyConversation
} from './openai';
import { storage } from './storage';
import { 
  insertSessionNoteSchema, 
  insertClientThemeSchema, 
  insertAiTherapySettingsSchema,
  insertInvitationCodeSchema
} from '@shared/schema';
import { getDashboardStatsSQL, getRecentSessionNotesSQL } from './custom-queries';
import { pool } from './db';
import { setupAuth } from './auth';
import { directLoginHandler, createAdminUser } from './temp-login-fix';
import {
  Client,
  ConversationMessage,
  AITherapySettings,
  TranscriptionResult,
  AIResponse
} from './types';
// WebSocket handler is imported in index.ts

// Set up multer for file upload handling
const uploadsDir = path.join(process.cwd(), 'uploads');

// Create uploads directory if it doesn't exist
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const upload = multer({
  storage: multer.diskStorage({
    destination: (_req, _file, cb) => {
      cb(null, uploadsDir);
    },
    filename: (_req, file, cb) => {
      // Create unique filename with timestamp and original extension
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, uniqueSuffix + path.extname(file.originalname));
    }
  }),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50 MB max file size
  }
});

// Temporary in-memory store for voice therapy sessions
// Use this when database connection fails
const tempConversations = new Map<number, {
  id: number;
  userId: number;
  clientId: number;
  title: string;
  active: boolean;
  createdAt: Date;
  summary?: string;
}>();

const tempMessages = new Map<number, {
  id: number;
  conversationId: number;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  audioUrl?: string | null;
}>();

let tempConversationIdCounter = 1;
let tempMessageIdCounter = 1;

declare global {
  namespace Express {
    interface User {
      id: number;
      username: string;
      [key: string]: any;
    }
  }
}

function randomInvitationCode(length = 8): string {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

export async function registerRoutes(app: Express): Promise<HttpServer> {
  // Create admin user if needed
  try {
    await createAdminUser();
    console.log('[STARTUP] Admin user check complete');
  } catch (error) {
    console.error('[STARTUP] Error checking/creating admin user:', error);
  }

  // SECURITY: Add security headers
  app.use((req, res, next) => {
    // Prevent clickjacking
    res.setHeader('X-Frame-Options', 'DENY');
    // Prevent MIME type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');
    // Enable XSS protection
    res.setHeader('X-XSS-Protection', '1; mode=block');
    // Referrer policy
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    next();
  });

  // Set up standard auth routes (/api/register, /api/login, /api/logout, /api/user)
  setupAuth(app);

  // Add direct login route that bypasses Passport.js
  app.post('/api/direct-login', directLoginHandler);
  console.log('[STARTUP] Direct login route registered at /api/direct-login');
  
  const isAuthenticated = (req: Request, res: Response, next: Function) => {
    if (req.isAuthenticated()) {
      return next();
    }
    res.status(401).json({ message: 'Not authenticated' });
  };
  
  const isAdmin = (req: Request, res: Response, next: Function) => {
    if (req.isAuthenticated() && req.user && req.user.userRole === 'admin') {
      return next();
    }
    res.status(403).json({ message: 'Admin access required' });
  };
  
  const handleError = (res: Response, error: unknown) => {
    console.error('API Error:', error);
    res.status(500).json({ message: error instanceof Error ? error.message : 'Server error' });
  };
  
  // ------------------------
  // CLIENTS ROUTES
  // ------------------------
  
  // Get all clients for a doctor
  app.get('/api/clients', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      
      if (!userId || isNaN(userId)) {
        return res.status(401).json({ message: 'User ID is required' });
      }
      
      const user = await storage.getUser(Number(userId));
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      let clients: Client[] = [];

      if (user.userRole === 'admin') {
        // Admin can see all clients
        clients = await storage.getAllClients();
      } else if (user.userRole === 'doctor') {
        // Doctor can only see their assigned clients
        clients = await storage.getClientsByDoctor(Number(userId));
      } else if (user.userRole === 'client') {
        // Client can only see themselves
        const client = await storage.getClientByUserId(Number(userId));
        if (client) {
          clients = [client];
        }
      }
      
      res.json(clients);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Create a client
  app.post('/api/clients', isAuthenticated, async (req, res) => {
    try {
      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {
        return res.status(403).json({ message: 'Not authorized to create clients' });
      }
      
      const clientData = req.body;
      const client = await storage.createClient(clientData);
      
      // Assign doctor-client relationship
      if (req.user?.userRole === 'doctor') {
        await storage.createDoctorClient({
          doctorId: req.user.id,
          clientId: client.userId
        });
      }
      
      res.status(201).json(client);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Update client status
  app.patch('/api/clients/:id/status', isAuthenticated, async (req, res) => {
    try {
      const clientId = parseInt(req.params.id);
      const { status } = req.body;
      
      if (!status) {
        return res.status(400).json({ message: 'Status is required' });
      }
      
      const client = await storage.updateClientStatus(Number(clientId), status);
      
      if (!client) {
        return res.status(404).json({ message: 'Client not found' });
      }
      
      res.json(client);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // SESSION NOTES ROUTES
  // ------------------------
  
  // Get all session notes for logged in user (doctor or client)
  app.get('/api/notes', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      
      if (!userId || isNaN(userId)) {
        return res.status(401).json({ message: 'User ID is required' });
      }
      
      // Pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const clientId = req.query.clientId ? parseInt(req.query.clientId as string) : undefined;
      
      // Get notes based on user role
      let notes = [];
      
      if (clientId) {
        // If a specific client ID is requested
        notes = await storage.getSessionNotesByClientId(Number(clientId));
      } else {
        // Get recent notes for the user using SQL directly
        notes = await getRecentSessionNotesSQL(pool, Number(userId), limit);
      }
      
      res.json(notes);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Get a specific session note
  app.get('/api/notes/:id', isAuthenticated, async (req, res) => {
    try {
      const noteId = parseInt(req.params.id);
      const note = await storage.getSessionNote(Number(noteId));
      
      if (!note) {
        return res.status(404).json({ message: 'Note not found' });
      }
      
      // Check if user has access to this note
      const userId = req.user?.id;
      const userRole = req.user?.userRole;
      
      if (userRole === 'admin') {
        // Admin can access any note
      } else if (userRole === 'doctor') {
        // Doctor can access notes for their clients
        const clients = await storage.getClientsByUserId(Number(userId));
        const clientIds = clients.map(c => c.id);
        
        if (!clientIds.includes(note.clientId)) {
          return res.status(403).json({ message: 'Not authorized to access this note' });
        }
      } else if (userRole === 'client') {
        // Client can only access their own notes
        const client = await storage.getClientByUserId(Number(userId));
        
        if (!client || note.clientId !== client.id) {
          return res.status(403).json({ message: 'Not authorized to access this note' });
        }
      }
      
      res.json(note);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Create a new session note
  app.post('/api/notes', isAuthenticated, async (req, res) => {
    try {
      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {
        return res.status(403).json({ message: 'Not authorized to create notes' });
      }
      
      const noteData = insertSessionNoteSchema.parse({
        ...req.body,
        createdBy: req.user.id
      });
      
      const note = await storage.createSessionNote(noteData);
      
      // Process with AI in the background
      analyzeTherapyNotes(note.content)
        .then(async (analysis) => {
          // Update the note with AI analysis
          await storage.updateSessionNoteAnalysis(
            note.id,
            analysis.summary,
            analysis.themes,
            analysis.recommendations
          );
          
          // Update client themes based on analysis
          if (analysis.themes) {
            for (const theme of analysis.themes) {
              await storage.updateOrCreateClientTheme(
                note.clientId,
                theme.name,
                theme.trend
              );
            }
          }
        })
        .catch(err => {
          console.error('Error analyzing notes:', err);
        });
      
      res.status(201).json(note);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // THEME ROUTES
  // ------------------------
  
  // Get themes for a client
  app.get('/api/clients/:id/themes', isAuthenticated, async (req, res) => {
    try {
      const clientId = parseInt(req.params.id);

      // SECURITY: Check access permissions
      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      if (userRole === 'admin') {
        // Admin can access all client themes
      } else if (userRole === 'doctor') {
        // Doctor can only access themes for their assigned clients
        const doctorClients = await storage.getClientsByDoctor(userId);
        const hasAccess = doctorClients.some(c => c.id === clientId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied: Not your assigned client' });
        }
      } else if (userRole === 'client') {
        // Client can only access their own themes
        const client = await storage.getClientByUserId(userId);
        if (!client || client.id !== clientId) {
          return res.status(403).json({ message: 'Access denied: Not your themes' });
        }
      } else {
        return res.status(403).json({ message: 'Access denied: Invalid role' });
      }

      const themes = await storage.getClientThemes(Number(clientId));
      res.json(themes);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Create or update a theme for a client
  app.post('/api/clients/:id/themes', isAuthenticated, async (req, res) => {
    try {
      if (req.user?.userRole !== 'doctor' && req.user?.userRole !== 'admin') {
        return res.status(403).json({ message: 'Not authorized to manage themes' });
      }
      
      const clientId = parseInt(req.params.id);
      if (!clientId || isNaN(clientId)) {
        return res.status(400).json({ message: 'Client ID is required' });
      }
      
      const { name, trend } = insertClientThemeSchema.parse(req.body);
      const trendNumber = Number(trend);
      
      if (isNaN(trendNumber)) {
        return res.status(400).json({ message: 'Trend must be a number' });
      }
      
      const theme = await storage.updateOrCreateClientTheme(clientId, name, trendNumber);
      res.status(201).json(theme);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // AI THERAPY ROUTES
  // ------------------------
  
  // Get all AI therapy conversations for a user
  app.get('/api/therapy/conversations', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      if (!userId || isNaN(userId)) {
        return res.status(401).json({ message: 'User ID is required' });
      }
      
      const clientIdParam = req.query.clientId ? parseInt(req.query.clientId as string) : undefined;
      const clientId = clientIdParam && !isNaN(clientIdParam) ? Number(clientIdParam) : undefined;
      
      const conversations = await storage.getAiTherapyConversations(Number(userId), clientId);
      res.json(conversations);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Get a specific AI therapy conversation
  app.get('/api/therapy/conversations/:id', isAuthenticated, async (req, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const conversation = await storage.getAiTherapyConversation(Number(conversationId));

      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      // SECURITY: Check access permissions
      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      if (userRole === 'admin') {
        // Admin can access all conversations
      } else if (userRole === 'doctor') {
        // Doctor can only access conversations of their assigned clients
        const client = await storage.getClient(conversation.clientId);
        if (!client) {
          return res.status(404).json({ message: 'Client not found' });
        }

        const doctorClients = await storage.getClientsByDoctor(userId);
        const hasAccess = doctorClients.some(c => c.id === conversation.clientId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied: Not your assigned client' });
        }
      } else {
        // Client can only access their own conversations
        if (conversation.clientId !== userId) {
          return res.status(403).json({ message: 'Access denied: Not your conversation' });
        }
      }

      // Get messages for this conversation
      const messages = await storage.getAiTherapyMessages(Number(conversationId));

      res.json({
        conversation,
        messages
      });
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Create a new AI therapy conversation
  app.post('/api/therapy/conversations', isAuthenticated, async (req, res) => {
    try {
      const conversationData = {
        ...req.body,
        userId: req.user?.id
      };
      
      const conversation = await storage.createAiTherapyConversation(conversationData);
      res.status(201).json(conversation);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // End an AI therapy conversation
  app.post('/api/therapy/conversations/:id/end', isAuthenticated, async (req, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const { summary } = req.body;

      if (!summary) {
        return res.status(400).json({ message: 'Summary is required' });
      }

      // SECURITY: Check access permissions before ending conversation
      const existingConversation = await storage.getAiTherapyConversation(Number(conversationId));
      if (!existingConversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      if (userRole === 'admin') {
        // Admin can end any conversation
      } else if (userRole === 'doctor') {
        // Doctor can only end conversations of their assigned clients
        const doctorClients = await storage.getClientsByDoctor(userId);
        const hasAccess = doctorClients.some(c => c.id === existingConversation.clientId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied: Not your assigned client' });
        }
      } else {
        // Client can only end their own conversations
        if (existingConversation.clientId !== userId) {
          return res.status(403).json({ message: 'Access denied: Not your conversation' });
        }
      }

      const conversation = await storage.endAiTherapyConversation(Number(conversationId), summary);

      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      res.json(conversation);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Send a message in an AI therapy conversation
  app.post('/api/therapy/conversations/:id/messages', isAuthenticated, async (req, res) => {
    try {
      const conversationId = parseInt(req.params.id);
      const { content, role } = req.body;

      if (!content) {
        return res.status(400).json({ message: 'Content is required' });
      }

      // SECURITY: Check access permissions before sending message
      const conversation = await storage.getAiTherapyConversation(Number(conversationId));
      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }

      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      if (userRole === 'admin') {
        // Admin can send messages in any conversation
      } else if (userRole === 'doctor') {
        // Doctor can only send messages in conversations of their assigned clients
        const doctorClients = await storage.getClientsByDoctor(userId);
        const hasAccess = doctorClients.some(c => c.id === conversation.clientId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied: Not your assigned client' });
        }
      } else {
        // Client can only send messages in their own conversations
        if (conversation.clientId !== userId) {
          return res.status(403).json({ message: 'Access denied: Not your conversation' });
        }
      }
      
      // Create user message
      const message = await storage.createAiTherapyMessage({
        conversationId,
        content,
        role: role || 'user',
        timestamp: new Date()
      });
      
      // Get AI response
      const rawMessages = await storage.getAiTherapyMessages(conversationId);
      const conversationHistory: ConversationMessage[] = rawMessages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        timestamp: msg.timestamp
      }));
      
      const settings = await storage.getActiveAiTherapySettings();
      
      // Create a temporary WebSocket for streaming
      const ws = new WebSocket('ws://localhost:3000/ws');
      
      // Wait for connection
      await new Promise((resolve) => ws.on('open', resolve));
      
      // Get AI response
      const aiResponse = await streamAITherapistResponse(
        ws,
        content,
        conversationHistory,
        true,
        settings?.systemPrompt
      );
      
      // Save AI response
      const aiMessage = await storage.createAiTherapyMessage({
        conversationId,
        content: aiResponse.message,
        role: 'assistant',
        timestamp: new Date()
      });
      
      res.status(201).json({
        userMessage: message,
        aiMessage
      });
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // AI SETTINGS ROUTES
  // ------------------------
  
  // Get all AI therapy settings
  app.get('/api/therapy/settings', isAuthenticated, async (req, res) => {
    try {
      if (req.user?.userRole !== 'admin') {
        return res.status(403).json({ message: 'Admin access required' });
      }
      
      const settings = await storage.getAiTherapySettings();
      res.json(settings);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Get active AI therapy settings
  app.get('/api/therapy/settings/active', isAuthenticated, async (req, res) => {
    try {
      const settings = await storage.getActiveAiTherapySettings();
      
      if (!settings) {
        return res.status(404).json({ message: 'No active settings found' });
      }
      
      res.json(settings);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Create new AI therapy settings
  app.post('/api/therapy/settings', isAdmin, async (req, res) => {
    try {
      const settingsData = insertAiTherapySettingsSchema.parse(req.body);
      const settings = await storage.createAiTherapySettings(settingsData);
      res.status(201).json(settings);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Update AI therapy settings
  app.patch('/api/therapy/settings/:id', isAdmin, async (req, res) => {
    try {
      const settingsId = parseInt(req.params.id);
      const updatedData = req.body;
      
      const settings = await storage.updateAiTherapySettings(Number(settingsId), updatedData);
      
      if (!settings) {
        return res.status(404).json({ message: 'Settings not found' });
      }
      
      res.json(settings);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // AUDIO ROUTES
  // ------------------------
  
  // Transcribe audio file
  app.post('/api/audio/transcribe', isAuthenticated, upload.single('audio'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No audio file provided' });
      }
      
      const audioFilePath = req.file.path;
      const audioBuffer = fs.readFileSync(audioFilePath);
      
      const result = await transcribeAudio(audioBuffer, req.file.originalname);
      
      // Clean up the temp file
      fs.unlinkSync(audioFilePath);
      
      res.json(result);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // USER ROUTES
  // ------------------------
  
  // Get all users (admin only)
  app.get('/api/users', isAdmin, async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      handleError(res, error);
    }
  });

  // ------------------------
  // ADMIN ROUTES
  // ------------------------

  // Admin - Get all users
  app.get('/api/admin/users', isAdmin, async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin - Create new user
  app.post('/api/admin/users', isAdmin, async (req, res) => {
    try {
      const userData = req.body;
      const newUser = await storage.createUser(userData);

      // If user is a client, create a client record
      if (userData.userRole === 'client') {
        await storage.createClient({
          userId: newUser.id,
          name: userData.name,
          status: 'active'
        });
      }

      res.status(201).json(newUser);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin - Update user
  app.patch('/api/admin/users/:id', isAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const userData = req.body;

      const updatedUser = await storage.updateUser(userId, userData);

      if (!updatedUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      res.json(updatedUser);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin - Get doctor-client assignments
  app.get('/api/admin/assignments', isAdmin, async (req, res) => {
    try {
      const assignments = await storage.getAllDoctorClientRelationships();

      // Enrich with user names
      const enrichedAssignments = await Promise.all(
        assignments.map(async (assignment) => {
          const doctor = await storage.getUser(assignment.doctorId);
          const client = await storage.getClient(assignment.clientId);

          return {
            ...assignment,
            doctorName: doctor?.name,
            clientName: client?.name,
          };
        })
      );

      res.json(enrichedAssignments);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin - Create doctor-client assignment
  app.post('/api/admin/assignments', isAdmin, async (req, res) => {
    try {
      const { doctorId, clientId } = req.body;

      // Validate doctor and client existence
      const doctor = await storage.getUser(doctorId);
      const client = await storage.getClient(clientId);

      if (!doctor || doctor.userRole !== 'doctor') {
        return res.status(400).json({ message: 'Invalid doctor ID' });
      }

      if (!client) {
        return res.status(400).json({ message: 'Invalid client ID' });
      }

      const assignment = await storage.createDoctorClient({ doctorId, clientId });
      res.status(201).json(assignment);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin - Get invitation codes
  app.get('/api/admin/invitation-codes', isAdmin, async (req, res) => {
    try {
      const codes = await storage.getAllInvitationCodes();

      // Enrich with user names
      const enrichedCodes = await Promise.all(
        codes.map(async (code) => {
          const creator = await storage.getUser(code.createdBy);
          const usedBy = code.usedBy ? await storage.getUser(code.usedBy) : null;

          return {
            ...code,
            creatorName: creator?.name,
            usedByName: usedBy?.name,
          };
        })
      );

      res.json(enrichedCodes);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin - Create invitation code
  app.post('/api/admin/invitation-codes', isAdmin, async (req, res) => {
    try {
      const { type } = req.body;

      // Generate a unique code
      const generateCode = () => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      };

      let code = generateCode();
      let attempts = 0;

      // Ensure code is unique
      while (attempts < 10) {
        const existing = await storage.getInvitationCodeByCode(code);
        if (!existing) break;
        code = generateCode();
        attempts++;
      }

      if (attempts >= 10) {
        return res.status(500).json({ message: 'Failed to generate unique invitation code' });
      }

      const codeData = {
        code,
        type,
        createdBy: req.user!.id,
      };

      const invitationCode = await storage.createInvitationCode(codeData);
      res.status(201).json(invitationCode);
    } catch (error) {
      handleError(res, error);
    }
  });

  // ------------------------
  // ANALYTICS ROUTES
  // ------------------------

  // Get analytics data
  app.get('/api/analytics', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      // Get date range from query params (default to last 30 days)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      let analytics;

      if (userRole === 'admin') {
        // Admin sees system-wide analytics
        analytics = await getSystemAnalytics(startDate, endDate);
      } else if (userRole === 'doctor') {
        // Doctor sees their client analytics
        analytics = await getDoctorAnalytics(userId, startDate, endDate);
      } else {
        // Client sees their own analytics
        analytics = await getClientAnalytics(userId, startDate, endDate);
      }

      res.json(analytics);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Get weekly activity data
  app.get('/api/analytics/weekly', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      const weeklyData = await getWeeklyActivity(userId, userRole);
      res.json(weeklyData);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Get monthly trends
  app.get('/api/analytics/monthly', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const userRole = req.user!.userRole;

      const monthlyData = await getMonthlyTrends(userId, userRole);
      res.json(monthlyData);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Update user
  app.patch('/api/users/:id', isAuthenticated, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // Only allow users to update themselves or admins to update anyone
      if (req.user?.id !== userId && req.user?.userRole !== 'admin') {
        return res.status(403).json({ message: 'Not authorized to update this user' });
      }
      
      const userData = req.body;
      
      // Prevent role escalation by non-admins
      if (userData.userRole && req.user?.userRole !== 'admin') {
        delete userData.userRole;
      }
      
      const user = await storage.updateUser(Number(userId), userData);
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      res.json(user);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // DASHBOARD ROUTES
  // ------------------------
  
  // Get dashboard stats
  app.get('/api/dashboard/stats', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      
      if (!userId || isNaN(userId)) {
        return res.status(401).json({ message: 'User ID is required' });
      }
      
      // Use custom SQL query instead of ORM method
      const stats = await getDashboardStatsSQL(pool, Number(userId));
      res.json(stats);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // ------------------------
  // INVITE CODE ROUTES
  // ------------------------
  
  // Create a new invitation code
  app.post('/api/invites', isAuthenticated, async (req, res) => {
    try {
      // Validate user permissions to create invitation codes
      if (req.user?.userRole === 'client') {
        return res.status(403).json({ message: 'Clients cannot create invitation codes' });
      }
      
      if (req.user?.userRole === 'doctor' && req.body.type !== 'client') {
        return res.status(403).json({ message: 'Doctors can only create client invitation codes' });
      }
      
      // Generate a random code if not provided
      const code = req.body.code || randomInvitationCode();
      
      const inviteData = insertInvitationCodeSchema.parse({
        ...req.body,
        code,
        createdBy: req.user?.id
      });
      
      const invite = await storage.createInvitationCode(inviteData);
      res.status(201).json(invite);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Get invitation codes created by the user
  app.get('/api/invites', isAuthenticated, async (req, res) => {
    try {
      const userId = req.user?.id;
      if (!userId || isNaN(userId)) {
        return res.status(401).json({ message: 'User ID is required' });
      }
      
      const type = req.query.type as 'doctor' | 'client' | undefined;
      
      const codes = await storage.getInvitationCodesByCreator(Number(userId), type);
      res.json(codes);
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Check if an invitation code is valid
  app.get('/api/invites/:code', async (req, res) => {
    try {
      const code = req.params.code;
      const inviteCode = await storage.getInvitationCodeByCode(code);
      
      if (!inviteCode) {
        return res.status(404).json({ message: 'Invitation code not found' });
      }
      
      if (inviteCode.used) {
        return res.status(400).json({ message: 'Invitation code has already been used' });
      }
      
      if (inviteCode.expiresAt && new Date(inviteCode.expiresAt) < new Date()) {
        return res.status(400).json({ message: 'Invitation code has expired' });
      }
      
      res.json({
        valid: true,
        type: inviteCode.type
      });
    } catch (error) {
      handleError(res, error);
    }
  });
  
  // Create HTTP server
  const httpServer = createServer(app);
  
  // Test voice endpoint
  app.get('/api/test-voice', async (req, res) => {
    try {
      // Extract parameters from query
      const voice = req.query.voice?.toString() || 'shimmer';
      const speed = parseFloat(req.query.speed?.toString() || '1.0');
      
      // Test phrases for each voice
      const testPhrases: Record<string, string> = {
        'alloy': 'This is a test of the Alloy voice. Can you hear this clearly?',
        'echo': 'Testing the Echo voice. Please confirm if the audio is working properly.',
        'fable': 'The Fable voice is being tested now. Is the sound quality good?',
        'onyx': 'This is the Onyx voice test. Can you hear this message?',
        'nova': 'Nova voice test in progress. Please verify the audio output.',
        'shimmer': 'Testing the Shimmer voice output. Is this coming through clearly?'
      };
      
      const text = testPhrases[voice] || 'This is a test of the voice output system. Can you hear this clearly?';
      
      console.log(`Processing test audio request: ${text}`);
      
      // Ensure uploads directory exists
      const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }
      
      // Generate unique filename
      const filename = `speech-${uuidv4()}.mp3`;
      const filePath = path.join(uploadsDir, filename);
      const fileUrl = `/uploads/audio/${filename}`;
      
      // Create an instance of the OpenAI client
      const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
      
      // Generate speech using OpenAI API
      const mp3Response = await openai.audio.speech.create({
        model: "tts-1",
        voice: voice as any,
        input: text,
        speed: speed
      });
      
      // Convert to buffer and save to file
      const buffer = Buffer.from(await mp3Response.arrayBuffer());
      fs.writeFileSync(filePath, buffer);
      
      console.log(`Sent test audio response with URL: ${fileUrl}`);
      
      // Return URL to audio file
      res.json({ url: fileUrl });
      
    } catch (error) {
      console.error('Error generating test voice:', error);
      res.status(500).json({ 
        error: 'Failed to generate test voice', 
        details: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // WebSocket server is set up in index.ts

  return httpServer;
}

// ------------------------
// ANALYTICS HELPER FUNCTIONS
// ------------------------

async function getSystemAnalytics(startDate: Date, endDate: Date) {
  // Get total conversations, users, and other system metrics
  const conversations = await storage.getAllConversations();
  const users = await storage.getAllUsers();
  const clients = await storage.getAllClients();

  // Filter conversations by date range
  const recentConversations = conversations.filter(conv => {
    const convDate = new Date(conv.createdAt);
    return convDate >= startDate && convDate <= endDate;
  });

  // Calculate metrics
  const totalSessions = recentConversations.length;
  const totalUsers = users.length;
  const totalClients = clients.length;
  const activeDoctors = users.filter(u => u.userRole === 'doctor').length;

  // Calculate average session duration (if available)
  const avgSessionDuration = recentConversations.length > 0
    ? recentConversations.reduce((sum, conv) => {
        if (conv.endedAt && conv.createdAt) {
          const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();
          return sum + duration;
        }
        return sum;
      }, 0) / recentConversations.length / 1000 / 60 // Convert to minutes
    : 0;

  return {
    totalSessions,
    totalUsers,
    totalClients,
    activeDoctors,
    avgSessionDuration: Math.round(avgSessionDuration),
    sessionGrowth: calculateGrowthRate(conversations, startDate, endDate),
    userGrowth: calculateUserGrowthRate(users, startDate, endDate)
  };
}

async function getDoctorAnalytics(doctorId: number, startDate: Date, endDate: Date) {
  // Get doctor's clients and their conversations
  const doctorClients = await storage.getClientsByDoctor(doctorId);
  const clientIds = doctorClients.map(c => c.id);

  // Get conversations for doctor's clients
  const allConversations = await storage.getAllConversations();
  const doctorConversations = allConversations.filter(conv =>
    clientIds.includes(conv.clientId) &&
    new Date(conv.createdAt) >= startDate &&
    new Date(conv.createdAt) <= endDate
  );

  const totalSessions = doctorConversations.length;
  const activeClients = new Set(doctorConversations.map(c => c.clientId)).size;
  const totalClients = doctorClients.length;

  // Calculate average session duration
  const avgSessionDuration = doctorConversations.length > 0
    ? doctorConversations.reduce((sum, conv) => {
        if (conv.endedAt && conv.createdAt) {
          const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();
          return sum + duration;
        }
        return sum;
      }, 0) / doctorConversations.length / 1000 / 60
    : 0;

  return {
    totalSessions,
    activeClients,
    totalClients,
    avgSessionDuration: Math.round(avgSessionDuration),
    sessionGrowth: calculateGrowthRate(doctorConversations, startDate, endDate)
  };
}

async function getClientAnalytics(clientId: number, startDate: Date, endDate: Date) {
  // Get client's conversations
  const allConversations = await storage.getAllConversations();
  const clientConversations = allConversations.filter(conv =>
    conv.clientId === clientId &&
    new Date(conv.createdAt) >= startDate &&
    new Date(conv.createdAt) <= endDate
  );

  const totalSessions = clientConversations.length;

  // Calculate total session time
  const totalSessionTime = clientConversations.reduce((sum, conv) => {
    if (conv.endedAt && conv.createdAt) {
      const duration = new Date(conv.endedAt).getTime() - new Date(conv.createdAt).getTime();
      return sum + duration;
    }
    return sum;
  }, 0) / 1000 / 60; // Convert to minutes

  const avgSessionDuration = totalSessions > 0 ? totalSessionTime / totalSessions : 0;

  // Get recent session dates for streak calculation
  const sessionDates = clientConversations
    .map(conv => new Date(conv.createdAt).toDateString())
    .filter((date, index, arr) => arr.indexOf(date) === index)
    .sort();

  return {
    totalSessions,
    totalSessionTime: Math.round(totalSessionTime),
    avgSessionDuration: Math.round(avgSessionDuration),
    sessionStreak: calculateSessionStreak(sessionDates),
    recentActivity: sessionDates.slice(-7) // Last 7 unique session dates
  };
}

async function getWeeklyActivity(userId: number, userRole: string) {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 49); // 7 weeks

  const allConversations = await storage.getAllConversations();
  let relevantConversations;

  if (userRole === 'admin') {
    relevantConversations = allConversations;
  } else if (userRole === 'doctor') {
    const doctorClients = await storage.getClientsByDoctor(userId);
    const clientIds = doctorClients.map(c => c.id);
    relevantConversations = allConversations.filter(conv => clientIds.includes(conv.clientId));
  } else {
    relevantConversations = allConversations.filter(conv => conv.clientId === userId);
  }

  // Group by week
  const weeklyData = [];
  for (let i = 6; i >= 0; i--) {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - (i * 7));
    weekStart.setHours(0, 0, 0, 0);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    const weekConversations = relevantConversations.filter(conv => {
      const convDate = new Date(conv.createdAt);
      return convDate >= weekStart && convDate <= weekEnd;
    });

    weeklyData.push({
      week: `Week ${7 - i}`,
      sessions: weekConversations.length,
      date: weekStart.toISOString().split('T')[0]
    });
  }

  return weeklyData;
}

async function getMonthlyTrends(userId: number, userRole: string) {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - 11); // 12 months

  const allConversations = await storage.getAllConversations();
  let relevantConversations;

  if (userRole === 'admin') {
    relevantConversations = allConversations;
  } else if (userRole === 'doctor') {
    const doctorClients = await storage.getClientsByDoctor(userId);
    const clientIds = doctorClients.map(c => c.id);
    relevantConversations = allConversations.filter(conv => clientIds.includes(conv.clientId));
  } else {
    relevantConversations = allConversations.filter(conv => conv.clientId === userId);
  }

  // Group by month
  const monthlyData = [];
  for (let i = 11; i >= 0; i--) {
    const monthStart = new Date();
    monthStart.setMonth(monthStart.getMonth() - i);
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);

    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);
    monthEnd.setDate(0);
    monthEnd.setHours(23, 59, 59, 999);

    const monthConversations = relevantConversations.filter(conv => {
      const convDate = new Date(conv.createdAt);
      return convDate >= monthStart && convDate <= monthEnd;
    });

    monthlyData.push({
      month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      sessions: monthConversations.length,
      date: monthStart.toISOString().split('T')[0]
    });
  }

  return monthlyData;
}

function calculateGrowthRate(conversations: any[], startDate: Date, endDate: Date) {
  const midDate = new Date((startDate.getTime() + endDate.getTime()) / 2);

  const firstHalf = conversations.filter(conv => {
    const convDate = new Date(conv.createdAt);
    return convDate >= startDate && convDate < midDate;
  }).length;

  const secondHalf = conversations.filter(conv => {
    const convDate = new Date(conv.createdAt);
    return convDate >= midDate && convDate <= endDate;
  }).length;

  if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;
  return Math.round(((secondHalf - firstHalf) / firstHalf) * 100);
}

function calculateUserGrowthRate(users: any[], startDate: Date, endDate: Date) {
  const midDate = new Date((startDate.getTime() + endDate.getTime()) / 2);

  const firstHalf = users.filter(user => {
    const userDate = new Date(user.createdAt);
    return userDate >= startDate && userDate < midDate;
  }).length;

  const secondHalf = users.filter(user => {
    const userDate = new Date(user.createdAt);
    return userDate >= midDate && userDate <= endDate;
  }).length;

  if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;
  return Math.round(((secondHalf - firstHalf) / firstHalf) * 100);
}

function calculateSessionStreak(sessionDates: string[]) {
  if (sessionDates.length === 0) return 0;

  let streak = 1;
  let maxStreak = 1;

  for (let i = 1; i < sessionDates.length; i++) {
    const prevDate = new Date(sessionDates[i - 1]);
    const currDate = new Date(sessionDates[i]);
    const dayDiff = (currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24);

    if (dayDiff <= 7) { // Within a week
      streak++;
      maxStreak = Math.max(maxStreak, streak);
    } else {
      streak = 1;
    }
  }

  return maxStreak;
}