// This file is not used in the current implementation
import { type Theme } from '@shared/schema';

// Voice configuration schema
export interface VoiceConfig {
  id: number;
  name: string;
  voice: string;
  speed: number;
  pitch: number;
  emphasis: 'strong' | 'moderate' | 'subtle';
  prosody: {
    questionInflection: number; // 0-1 scale
    emotionalRange: number; // 0-1 scale
    pauseDuration: number; // milliseconds
  };
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Conversation flow configuration
export interface ConversationConfig {
  id: number;
  name: string;
  turnTaking: {
    minSilenceDuration: number; // milliseconds
    maxInterruptionGap: number; // milliseconds
    backchannelFrequency: number; // 0-1 scale
  };
  responseStyle: {
    maxResponseLength: number; // words
    minResponseLength: number; // words
    temperature: number; // 0-1 scale
    presencePenalty: number; // 0-1 scale
    frequencyPenalty: number; // 0-1 scale
  };
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Create tables for configurations
export async function createConfigTables() {
  await sql`
    CREATE TABLE IF NOT EXISTS voice_configs (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      voice VARCHAR(50) NOT NULL,
      speed FLOAT NOT NULL,
      pitch FLOAT NOT NULL,
      emphasis VARCHAR(20) NOT NULL,
      prosody JSONB NOT NULL,
      is_default BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS conversation_configs (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      turn_taking JSONB NOT NULL,
      response_style JSONB NOT NULL,
      is_default BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
  `;
}

// Default configurations
export const DEFAULT_VOICE_CONFIG: VoiceConfig = {
  id: 1,
  name: "Default Voice",
  voice: "shimmer",
  speed: 1.05,
  pitch: 1.0,
  emphasis: "moderate",
  prosody: {
    questionInflection: 0.8,
    emotionalRange: 0.7,
    pauseDuration: 300
  },
  isDefault: true,
  createdAt: new Date(),
  updatedAt: new Date()
};

export const DEFAULT_CONVERSATION_CONFIG: ConversationConfig = {
  id: 1,
  name: "Default Conversation",
  turnTaking: {
    minSilenceDuration: 300,
    maxInterruptionGap: 500,
    backchannelFrequency: 0.3
  },
  responseStyle: {
    maxResponseLength: 150,
    minResponseLength: 50,
    temperature: 0.8,
    presencePenalty: 0.1,
    frequencyPenalty: 0.1
  },
  isDefault: true,
  createdAt: new Date(),
  updatedAt: new Date()
};

export interface ClientTheme {
  id: number;
  clientId: number;
  name: string;
  trend: number;
  occurrences: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AiTherapyConversation {
  id: number;
  clientId: number;
  userId: number;
  title: string;
  active: boolean;
  createdAt: Date;
  startedAt: Date;
  endedAt: Date | null;
  summary: string | null;
  aiProcessed: boolean | null;
}

export interface AiTherapyMessage {
  id: number;
  conversationId: number;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  createdAt: Date;
  audioUrl: string | null;
}

export interface AiTherapySettings {
  id: number;
  name: string;
  active: boolean;
  systemPrompt: string;
  model: string;
  temperature: number;
  createdAt: Date;
  updatedAt: Date;
} 