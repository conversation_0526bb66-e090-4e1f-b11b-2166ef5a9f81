#!/usr/bin/env node

/**
 * COMPREHENSIVE BROWSER-BASED VOICE CONVERSATION TEST
 * Tests the actual user experience in a real browser
 */

import puppeteer from 'puppeteer';

async function runBrowserTest() {
  console.log('🌐 STARTING COMPREHENSIVE BROWSER TEST');
  console.log('Testing actual user experience with voice conversation');
  
  let browser;
  let page;
  
  try {
    // Launch browser with audio permissions
    browser = await puppeteer.launch({
      headless: false, // Show browser for debugging
      args: [
        '--use-fake-ui-for-media-stream', // Auto-grant microphone permission
        '--use-fake-device-for-media-stream', // Use fake microphone
        '--allow-running-insecure-content',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--autoplay-policy=no-user-gesture-required' // Allow audio autoplay
      ]
    });
    
    page = await browser.newPage();
    
    // Grant microphone permissions
    const context = browser.defaultBrowserContext();
    await context.overridePermissions('http://localhost', ['microphone']);
    
    console.log('🔊 Browser launched with audio permissions');
    
    // Navigate to the app
    console.log('📱 Navigating to app...');
    await page.goto('http://localhost', { waitUntil: 'networkidle0' });
    
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    console.log('🔍 Looking for Admin AI Test page...');
    
    // Try to find and click the Admin AI Test link
    try {
      await page.waitForSelector('a[href*="admin-ai-test"]', { timeout: 5000 });
      await page.click('a[href*="admin-ai-test"]');
      console.log('✅ Navigated to Admin AI Test page');
    } catch (e) {
      console.log('⚠️ Admin AI Test link not found, trying direct navigation...');
      await page.goto('http://localhost/admin-ai-test', { waitUntil: 'networkidle0' });
    }
    
    await page.waitForTimeout(2000);
    
    // Set up console logging to capture frontend logs
    const frontendLogs = [];
    page.on('console', msg => {
      const text = msg.text();
      frontendLogs.push(text);
      if (text.includes('🔊') || text.includes('📡') || text.includes('AUDIO') || text.includes('ERROR')) {
        console.log(`[FRONTEND] ${text}`);
      }
    });
    
    // Set up error logging
    page.on('pageerror', error => {
      console.error(`[PAGE ERROR] ${error.message}`);
    });
    
    console.log('🎤 Looking for Start Interactive Session button...');
    
    // Find and click the Start Interactive Session button
    await page.waitForSelector('button', { timeout: 10000 });
    
    // Look for the start button by text content
    const startButton = await page.evaluateHandle(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      return buttons.find(btn => 
        btn.textContent.includes('Start Interactive Session') ||
        btn.textContent.includes('Start Session') ||
        btn.textContent.includes('Interactive')
      );
    });
    
    if (!startButton.asElement()) {
      throw new Error('Start Interactive Session button not found');
    }
    
    console.log('🚀 Starting interactive session...');
    await startButton.asElement().click();
    
    // Wait for session to initialize
    await page.waitForTimeout(3000);
    
    console.log('🔍 Monitoring session for 10 seconds...');
    
    // Monitor the page for audio-related activity
    let audioChunksReceived = 0;
    let audioPlaybackStarted = false;
    let speechDetectionTriggered = false;
    let feedbackLoopDetected = false;
    
    const startTime = Date.now();
    const monitorDuration = 10000; // 10 seconds
    
    while (Date.now() - startTime < monitorDuration) {
      // Check for audio chunks in the logs
      const recentLogs = frontendLogs.slice(-10);
      
      for (const log of recentLogs) {
        if (log.includes('🔊 AI AUDIO CHUNK')) {
          audioChunksReceived++;
        }
        if (log.includes('▶️ Playing') || log.includes('AI SPEAKING')) {
          audioPlaybackStarted = true;
        }
        if (log.includes('🎤 SPEECH DETECTED') || log.includes('SPEECH STARTED')) {
          speechDetectionTriggered = true;
          // Check if this happens during AI playback (feedback loop)
          if (audioPlaybackStarted) {
            feedbackLoopDetected = true;
          }
        }
      }
      
      await page.waitForTimeout(500);
    }
    
    console.log('\n============================================================');
    console.log('🔍 COMPREHENSIVE BROWSER TEST RESULTS');
    console.log('============================================================\n');
    
    console.log('📊 AUDIO SYSTEM STATUS:');
    console.log(`Audio Chunks Received:    ${audioChunksReceived > 0 ? '✅' : '❌'} (${audioChunksReceived})`);
    console.log(`Audio Playback Started:   ${audioPlaybackStarted ? '✅' : '❌'}`);
    console.log(`Speech Detection:         ${speechDetectionTriggered ? '✅' : '❌'}`);
    console.log(`Feedback Loop Detected:   ${feedbackLoopDetected ? '❌ PROBLEM' : '✅ OK'}`);
    
    console.log('\n🔧 DIAGNOSIS:');
    if (audioChunksReceived === 0) {
      console.log('❌ No audio chunks received - frontend not processing response.audio.delta');
    }
    if (!audioPlaybackStarted) {
      console.log('❌ Audio playback not starting - check AudioContext initialization');
    }
    if (feedbackLoopDetected) {
      console.log('❌ Feedback loop detected - AI audio triggering speech detection');
    }
    if (audioChunksReceived > 0 && audioPlaybackStarted && !feedbackLoopDetected) {
      console.log('✅ All systems appear to be working correctly!');
    }
    
    console.log('\n📋 RECENT FRONTEND LOGS:');
    frontendLogs.slice(-20).forEach(log => {
      if (log.includes('🔊') || log.includes('📡') || log.includes('AUDIO') || log.includes('ERROR')) {
        console.log(`  ${log}`);
      }
    });
    
    // Keep browser open for manual inspection
    console.log('\n🔍 Browser will remain open for 30 seconds for manual inspection...');
    await page.waitForTimeout(30000);
    
  } catch (error) {
    console.error('❌ Browser test failed:', error.message);
    console.error(error.stack);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
try {
  require('puppeteer');
  runBrowserTest().catch(console.error);
} catch (e) {
  console.log('⚠️ Puppeteer not available, installing...');
  console.log('Run: npm install puppeteer');
  console.log('Then run this test again');
}
