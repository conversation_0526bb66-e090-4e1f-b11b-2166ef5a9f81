/**
 * Direct PostgreSQL connection module for authentication
 * This provides a more reliable connection for auth operations
 */

import pg from 'pg';
import crypto from 'crypto';
import util from 'util';

const scryptAsync = util.promisify(crypto.scrypt);

// Create a standard pg pool
export const pgPool = new pg.Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false // Allow self-signed certs for development
  },
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000
});

// Test pool and log connection status
pgPool.on('connect', client => {
  console.log('PostgreSQL direct connection established');
});

pgPool.on('error', err => {
  console.error('PostgreSQL direct connection error:', err);
});

// Hash password in the same format as auth.ts
export async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(16).toString('hex');
  const derivedKey = await scryptAsync(password, salt, 64);
  return `${(derivedKey as Buffer).toString('hex')}.${salt}`;
}

// Verify a password against stored hash
export async function verifyPassword(password: string, storedHash: string): Promise<boolean> {
  console.log(`[AUTH] Verifying password against stored hash: ${storedHash.substring(0, 10)}...`);
  
  const [hash, salt] = storedHash.split('.');
  if (!hash || !salt) {
    console.log(`[AUTH] Invalid hash format, missing hash or salt`);
    return false;
  }
  
  try {
    // Using scrypt directly to match exactly how we hash passwords
    return new Promise((resolve, reject) => {
      crypto.scrypt(password, salt, 64, (err, derivedKey) => {
        if (err) {
          console.error(`[AUTH] Error during password verification:`, err);
          return reject(err);
        }
        
        const computedHash = derivedKey.toString('hex');
        const match = computedHash === hash;
        
        console.log(`[AUTH] Password verification result: ${match ? 'match' : 'no match'}`);
        if (!match) {
          console.log(`[AUTH] Expected hash length: ${hash.length}, Computed hash length: ${computedHash.length}`);
        }
        
        resolve(match);
      });
    });
  } catch (error) {
    console.error(`[AUTH] Unexpected error during password verification:`, error);
    return false;
  }
}

// Get user by username for authentication
export async function getUserByUsername(username: string) {
  try {
    const result = await pgPool.query(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );
    
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error fetching user by username:', error);
    return null;
  }
}

// Direct authentication function
export async function authenticateUser(username: string, password: string) {
  try {
    // 1. Get user
    const user = await getUserByUsername(username);
    
    if (!user) {
      console.log(`[AUTH-DIRECT] User not found: ${username}`);
      return null;
    }
    
    // 2. Verify password
    const isValid = await verifyPassword(password, user.password);
    
    if (!isValid) {
      console.log(`[AUTH-DIRECT] Password invalid for user: ${username}`);
      return null;
    }
    
    console.log(`[AUTH-DIRECT] Authentication successful for user: ${username}`);
    
    // 3. Return user without password
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    console.error('[AUTH-DIRECT] Authentication error:', error);
    return null;
  }
}