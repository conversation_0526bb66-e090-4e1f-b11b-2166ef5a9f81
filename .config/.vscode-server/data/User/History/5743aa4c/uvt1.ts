// Global debug configuration for the application
interface DebugConfig {
  enabled: boolean;
  realTimeConversation: boolean;
  audioProcessing: boolean;
  websocketEvents: boolean;
  sessionManagement: boolean;
}

class DebugManager {
  private config: DebugConfig = {
    enabled: false, // Set to false by default
    realTimeConversation: false,
    audioProcessing: false,
    websocketEvents: false,
    sessionManagement: false,
  };

  private originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info
  };

  constructor() {
    // Load debug state from localStorage
    const savedDebugState = localStorage.getItem('debug-enabled');
    if (savedDebugState === 'true') {
      this.setEnabled(true);
    }
  }

  // Enable/disable all debug logging
  setEnabled(enabled: boolean) {
    this.config.enabled = enabled;
    this.config.realTimeConversation = enabled;
    this.config.audioProcessing = enabled;
    this.config.websocketEvents = enabled;
    this.config.sessionManagement = enabled;

    // Store in localStorage for persistence
    localStorage.setItem('debug-enabled', enabled.toString());

    // Override console methods when debug is disabled
    if (!enabled) {
      this.suppressConsoleLogging();
    } else {
      this.restoreConsoleLogging();
    }
  }

  // Enable/disable specific debug categories
  setCategory(category: keyof Omit<DebugConfig, 'enabled'>, enabled: boolean) {
    this.config[category] = enabled;
  }

  // Check if debug is enabled globally
  get isEnabled(): boolean {
    return this.config.enabled;
  }

  // Check specific debug categories
  get realTimeConversation(): boolean {
    return this.config.enabled && this.config.realTimeConversation;
  }

  get audioProcessing(): boolean {
    return this.config.enabled && this.config.audioProcessing;
  }

  get websocketEvents(): boolean {
    return this.config.enabled && this.config.websocketEvents;
  }

  get sessionManagement(): boolean {
    return this.config.enabled && this.config.sessionManagement;
  }

  // Debug logging methods
  log(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {
    if (this.config.enabled && this.config[category]) {
      console.log(`[DEBUG:${category.toUpperCase()}]`, message, ...args);
    }
  }

  error(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {
    if (this.config.enabled && this.config[category]) {
      console.error(`[DEBUG:${category.toUpperCase()}]`, message, ...args);
    }
  }

  warn(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {
    if (this.config.enabled && this.config[category]) {
      console.warn(`[DEBUG:${category.toUpperCase()}]`, message, ...args);
    }
  }

  // Get current config for UI display
  getConfig(): DebugConfig {
    return { ...this.config };
  }

  // Suppress console logging when debug is disabled
  private suppressConsoleLogging() {
    if (process.env.NODE_ENV === 'production') {
      console.log = () => {};
      console.info = () => {};
      console.warn = () => {};
      // Keep console.error for critical errors
    }
  }

  // Restore original console logging
  private restoreConsoleLogging() {
    console.log = this.originalConsole.log;
    console.warn = this.originalConsole.warn;
    console.error = this.originalConsole.error;
    console.info = this.originalConsole.info;
  }

  // Clear all debug logs
  clearLogs() {
    // This can be used to clear any stored debug logs
    console.clear();
  }
}

// Export singleton instance
export const debugManager = new DebugManager();

// Convenience function for quick debug logging
export const debugLog = (category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) => {
  debugManager.log(category, message, ...args);
};

export default debugManager;
