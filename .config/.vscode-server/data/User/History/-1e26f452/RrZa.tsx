import React, { useState, useEffect } from 'react';
import { VoiceConfig, ConversationConfig } from '@shared/schema';

interface ConfigManagementProps {
  userId: string;
  clientId: string;
  onConfigChange: (config: { voice: VoiceConfig; conversation: ConversationConfig }) => void;
}

export const ConfigManagement: React.FC<ConfigManagementProps> = ({ userId, clientId, onConfigChange }) => {
  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({
    voice: "shimmer",
    speed: 1.05,
    pitch: 1.0,
    emphasis: "moderate",
    prosody: {
      emotionalRange: 0.7,
      questionInflection: 0.8,
      pauseDuration: 300
    }
  });

  const [conversationConfig, setConversationConfig] = useState<ConversationConfig>({
    temperature: 0.7,
    maxTokens: 1000,
    presencePenalty: 0.1,
    frequencyPenalty: 0.1,
    turnTaking: {
      backchannelFrequency: 0.3,
      minSilenceDuration: 300,
      maxInterruptionGap: 500
    },
    responseStyle: {
      minResponseLength: 50,
      maxResponseLength: 150,
      temperature: 0.8,
      presencePenalty: 0.1,
      frequencyPenalty: 0.1
    }
  });

  useEffect(() => {
    // Load configurations for this client
    const loadConfigurations = async () => {
      try {
        const response = await fetch('/api/configs', {
          headers: {
            'x-user-id': userId,
            'x-client-id': clientId
          }
        });
        if (!response.ok) throw new Error('Failed to load configurations');
        const { voice, conversation } = await response.json();
        setVoiceConfig(voice);
        setConversationConfig(conversation);
      } catch (error) {
        console.error('Error loading configurations:', error);
      }
    };

    loadConfigurations();
  }, [userId, clientId]);

  const handleVoiceConfigChange = (key: keyof VoiceConfig, value: any) => {
    const newConfig = { ...voiceConfig, [key]: value };
    setVoiceConfig(newConfig);
    onConfigChange({ voice: newConfig, conversation: conversationConfig });
  };

  const handleConversationConfigChange = (key: keyof ConversationConfig, value: any) => {
    const newConfig = { ...conversationConfig, [key]: value };
    setConversationConfig(newConfig);
    onConfigChange({ voice: voiceConfig, conversation: newConfig });
  };

  return (
    <div className="space-y-6">
      {/* Voice Configuration */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Voice Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Voice</label>
            <select
              value={voiceConfig.voice}
              onChange={(e) => handleVoiceConfigChange('voice', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="shimmer">Shimmer</option>
              <option value="alloy">Alloy</option>
              <option value="echo">Echo</option>
              <option value="fable">Fable</option>
              <option value="onyx">Onyx</option>
              <option value="nova">Nova</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Speed</label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.05"
              value={voiceConfig.speed}
              onChange={(e) => handleVoiceConfigChange('speed', parseFloat(e.target.value))}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{voiceConfig.speed.toFixed(2)}x</span>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Emphasis</label>
            <select
              value={voiceConfig.emphasis}
              onChange={(e) => handleVoiceConfigChange('emphasis', e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="strong">Strong</option>
              <option value="moderate">Moderate</option>
              <option value="subtle">Subtle</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Emotional Range</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={voiceConfig.prosody.emotionalRange}
              onChange={(e) => handleVoiceConfigChange('prosody', {
                ...voiceConfig.prosody,
                emotionalRange: parseFloat(e.target.value)
              })}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{(voiceConfig.prosody.emotionalRange * 100).toFixed(0)}%</span>
          </div>
        </div>
      </div>

      {/* Conversation Configuration */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">Conversation Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Backchannel Frequency</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={conversationConfig.turnTaking.backchannelFrequency}
              onChange={(e) => handleConversationConfigChange('turnTaking', {
                ...conversationConfig.turnTaking,
                backchannelFrequency: parseFloat(e.target.value)
              })}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{(conversationConfig.turnTaking.backchannelFrequency * 100).toFixed(0)}%</span>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Min Silence Duration</label>
            <input
              type="range"
              min="100"
              max="1000"
              step="50"
              value={conversationConfig.turnTaking.minSilenceDuration}
              onChange={(e) => handleConversationConfigChange('turnTaking', {
                ...conversationConfig.turnTaking,
                minSilenceDuration: parseInt(e.target.value)
              })}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{conversationConfig.turnTaking.minSilenceDuration}ms</span>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Response Temperature</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={conversationConfig.responseStyle.temperature}
              onChange={(e) => handleConversationConfigChange('responseStyle', {
                ...conversationConfig.responseStyle,
                temperature: parseFloat(e.target.value)
              })}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{conversationConfig.responseStyle.temperature.toFixed(1)}</span>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Max Response Length</label>
            <input
              type="range"
              min="50"
              max="300"
              step="10"
              value={conversationConfig.responseStyle.maxResponseLength}
              onChange={(e) => handleConversationConfigChange('responseStyle', {
                ...conversationConfig.responseStyle,
                maxResponseLength: parseInt(e.target.value)
              })}
              className="w-full"
            />
            <span className="text-sm text-gray-500">{conversationConfig.responseStyle.maxResponseLength} words</span>
          </div>
        </div>
      </div>
    </div>
  );
}; 