import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { User } from "@shared/schema";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import logoImage from "../assets/lamaMind-logo.png";

interface SidebarProps {
  user?: User;
  className?: string;
  onLinkClick?: () => void;
}

export default function Sidebar({ user, className, onLinkClick }: SidebarProps) {
  const [location, setLocation] = useLocation();
  const { logoutMutation } = useAuth();
  const { toast } = useToast();

  const isLinkActive = (path: string) => {
    if (path === "/" && location === "/") return true;
    if (path !== "/" && location.startsWith(path)) return true;
    return false;
  };

  // Determine which navigation items to show based on user role
  const baseNavItems = [
    { path: "/dashboard", icon: "dashboard", label: "Dashboard" },
    { path: "/settings", icon: "settings", label: "Settings" },
  ];
  
  // Add role-specific navigation items
  let navItems = [];
  
  if (user?.userRole === 'client') {
    navItems = [
      ...baseNavItems,
      { path: "/voice-therapy", icon: "mic", label: "Voice Therapy" },
    ];
  } else if (user?.userRole === 'admin') {
    navItems = [
      ...baseNavItems,
      { path: "/clients", icon: "people", label: "Clients" },
      { path: "/notes", icon: "description", label: "Session Notes" },
      { path: "/analytics", icon: "insights", label: "Analytics" },
      { path: "/admin-dashboard", icon: "admin_panel_settings", label: "Admin Dashboard" },
      { path: "/admin-ai-test", icon: "psychology", label: "AI Test Center" },
    ];
  } else {
    // Default for doctor role
    navItems = [
      ...baseNavItems,
      { path: "/clients", icon: "people", label: "Clients" },
      { path: "/notes", icon: "description", label: "Session Notes" },
      { path: "/analytics", icon: "insights", label: "Analytics" },
    ];
  }

  const handleClick = () => {
    if (onLinkClick) onLinkClick();
  };
  
  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        toast({
          title: "Logged out successfully",
          description: "You have been logged out of your account.",
        });
        setLocation("/auth");
      },
      onError: (error) => {
        toast({
          title: "Logout failed",
          description: error.message,
          variant: "destructive",
        });
      }
    });
  };

  console.log('Sidebar rendering with user:', user);
  console.log('Navigation items:', navItems);

  return (
    <aside className={cn("flex flex-col w-64 bg-white border-r border-gray-200", className)} style={{ backgroundColor: 'white', borderRight: '1px solid #e5e7eb' }}>
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-gray-50" style={{ backgroundColor: '#f9fafb', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>
        <div className="flex items-center">
          <img src={logoImage} alt="LamaMind Logo" className="h-10 w-10 mr-2" style={{ height: '40px', width: '40px', marginRight: '8px' }} />
          <span className="text-lg font-semibold text-gray-900" style={{ fontSize: '18px', fontWeight: '600', color: '#111827' }}>LamaMind</span>
        </div>
      </div>
      <nav className="flex-1 px-3 py-4 space-y-1">
        {navItems.map((item) => (
          <div key={item.path} style={{ marginBottom: '4px' }}>
            <Link href={item.path} onClick={handleClick}>
              <a
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '12px 16px',
                  fontSize: '14px',
                  fontWeight: '500',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  backgroundColor: isLinkActive(item.path) ? '#2563eb' : 'transparent',
                  color: isLinkActive(item.path) ? 'white' : '#374151',
                  transition: 'all 0.15s ease',
                  border: 'none',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  if (!isLinkActive(item.path)) {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isLinkActive(item.path)) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <span
                  className="material-icons"
                  style={{
                    marginRight: '12px',
                    fontSize: '18px',
                    color: isLinkActive(item.path) ? 'white' : '#6b7280'
                  }}
                >
                  {item.icon}
                </span>
                {item.label}
              </a>
            </Link>
          </div>
        ))}
      </nav>

      {user && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
                <span className="text-sm font-medium">
                  {user.name.split(" ").map(n => n[0]).join("").toUpperCase()}
                </span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">{user.name}</p>
              <p className="text-xs text-gray-500 capitalize">{user.userRole}</p>
            </div>
            <div className="ml-auto flex">
              <Link href="/profile">
                <a
                  className="text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1 block"
                  title="Profile"
                >
                  <span className="material-icons text-sm">person</span>
                </a>
              </Link>
              <button
                onClick={handleLogout}
                className="text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1"
                title="Logout"
              >
                <span className="material-icons text-sm">logout</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </aside>
  );
}
