import OpenAI from "openai";
import { type Theme } from "@shared/schema";
import fs from "fs";
import path from "path";
import { randomUUID } from "crypto";
import { WebSocket } from "ws";
import { AIResponse, ConversationMessage } from './types';

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const CHAT_MODEL = "gpt-4o";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || ""
});

// Debug OpenAI configuration
console.log(`OpenAI SDK Version: ${openai.constructor.name}`);
console.log(`Using model: ${CHAT_MODEL} for regular calls`);
console.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);

// Ensure audio uploads directory exists
const UPLOADS_DIR = path.join(process.cwd(), "uploads");
const AUDIO_DIR = path.join(UPLOADS_DIR, "audio");

if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}
if (!fs.existsSync(AUDIO_DIR)) {
  fs.mkdirSync(AUDIO_DIR, { recursive: true });
}

interface NoteAnalysisResult {
  summary: string;
  themes: Theme[];
  recommendations: string[];
}

interface TextToSpeechResult {
  audioUrl: string;
}

interface TranscriptionResult {
  text: string;
}

interface AIConversationResponse {
  message: string;
  audioUrl?: string;
}

interface VoiceConfig {
  voice: string;
  speed: number;
  pitch: number;
  emphasis: 'strong' | 'moderate' | 'subtle';
  prosody: {
    emotionalRange: number;
    questionInflection: number;
    pauseDuration: number;
  };
}

interface ConversationConfig {
  turnTaking: {
    backchannelFrequency: number;
    minSilenceDuration: number;
    maxInterruptionGap: number;
  };
  responseStyle: {
    minResponseLength: number;
    maxResponseLength: number;
    temperature: number;
    presencePenalty: number;
    frequencyPenalty: number;
  };
}

// Default voice configuration
const DEFAULT_VOICE_CONFIG: VoiceConfig = {
  voice: "shimmer",
  speed: 1.05,
  pitch: 1.0,
  emphasis: 'moderate',
  prosody: {
    emotionalRange: 0.6,
    questionInflection: 0.7,
    pauseDuration: 300
  }
};

export async function analyzeTherapyNotes(
  notes: string,
  previousThemes: string[] = []
): Promise<NoteAnalysisResult> {
  try {
    // Check if API key is available
    if (!openai.apiKey) {
      console.error("OpenAI API key is missing");
      throw new Error("OpenAI API key is missing");
    }

    // Create a prompt that includes previousThemes for trend detection
    const prompt = `
    You are a professional AI assistant for mental health professionals.
    
    Analyze the following therapy session notes and provide:
    1. A concise summary of the key points (max 150 words)
    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:
       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement
       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression
       - Provide 0 if the theme is new or stable
       - Mark as "New" if the theme wasn't in previous sessions
    3. 3-5 actionable recommendations or insights for the therapist
    
    Previous session themes (for reference): ${previousThemes.join(", ")}
    
    Session notes:
    ${notes}
    
    Respond with JSON in this exact format:
    {
      "summary": "concise summary here",
      "themes": [
        {"name": "theme1", "occurrences": number, "trend": number},
        {"name": "theme2", "occurrences": number, "trend": number}
      ],
      "recommendations": ["recommendation1", "recommendation2", "recommendation3"]
    }
    `;

    const response = await openai.chat.completions.create({
      model: CHAT_MODEL, // newest model as of May 2024
      messages: [
        {
          role: "system",
          content: "You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" }
    });

    const content = response.choices[0].message.content;
    
    if (!content) {
      throw new Error("No content in OpenAI response");
    }

    const result: NoteAnalysisResult = JSON.parse(content);
    
    // Ensure the result has the correct structure
    if (!result.summary || !result.themes || !result.recommendations) {
      throw new Error("Incomplete response from OpenAI");
    }

    return result;
  } catch (error) {
    console.error("Error analyzing therapy notes:", error);
    // Return a fallback result if API call fails
    return {
      summary: "Error analyzing notes. Please try again.",
      themes: [],
      recommendations: ["Unable to generate recommendations due to an error."]
    };
  }
}

// Function to transcribe audio files with enhanced features for Vale-like interaction
export async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {
  try {
    if (!openai.apiKey) {
      throw new Error("OpenAI API key is missing");
    }
    
    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);
    
    // Check if the buffer is empty or too small
    if (audioBuffer.length < 100) {
      console.error('Audio buffer is too small or empty:', audioBuffer.length);
      throw new Error('Audio buffer is too small or empty');
    }

    // Save the audio buffer to a temporary file
    const tempFilename = filename || `${randomUUID()}.webm`;
    const filePath = path.join(AUDIO_DIR, tempFilename);
    
    // Ensure the uploads directory exists
    if (!fs.existsSync(AUDIO_DIR)) {
      fs.mkdirSync(AUDIO_DIR, { recursive: true });
      console.log(`Created directory: ${AUDIO_DIR}`);
    }
    
    // Save the buffer with proper binary encoding
    fs.writeFileSync(filePath, audioBuffer);
    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);
    
    // Create a readable stream from the file
    const audioFile = fs.createReadStream(filePath);
    
    // Transcribe the audio using Whisper model for reliability
    console.log("Starting transcription with whisper-1 model");
    
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      console.error("OPENAI_API_KEY is not configured");
      throw new Error("OpenAI API key is not configured");
    }
    
    const transcription = await openai.audio.transcriptions.create({
      file: audioFile,
      model: "whisper-1", // Using reliable Whisper model for transcription
      language: "en", // Specify language to improve accuracy
      prompt: "This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.",
      response_format: "text", // Plaintext output for simplicity
      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context
    });
    
    console.log("Transcription raw result:", transcription);
    
    // Clean up the temporary file
    fs.unlinkSync(filePath);
    
    // Get the text content properly
    const transcriptionText = typeof transcription === 'string' 
      ? transcription 
      : (transcription as any).text || '';
    
    // Post-process the transcribed text
    const processedText = postProcessTranscription(transcriptionText);
    console.log("Processed transcription:", processedText);
    
    return { text: processedText };
  } catch (error) {
    console.error("Error transcribing audio:", error);
    // Keep the file for debugging if it exists
    const tempFilename = filename || `${randomUUID()}.webm`;
    const filePath = path.join(AUDIO_DIR, tempFilename);
    if (fs.existsSync(filePath)) {
      console.log(`Audio file preserved at ${filePath} for debugging`);
    }
    return { text: "Error transcribing audio. Please try again." };
  }
}

// Helper function to post-process transcription for therapy-specific terminology
function postProcessTranscription(text: string): string {
  const corrections: Record<string, string> = {
    // Common therapy term corrections that might be misheard
    "see bt": "CBT", // Cognitive Behavioral Therapy
    "mindful ness": "mindfulness",
    "coping mechanisms": "coping mechanisms",
    "cognitive distortions": "cognitive distortions",
    "anxiety attacks": "anxiety attacks",
    "panic attacks": "panic attacks"
    // Add more domain-specific corrections as needed
  };
  
  let processedText = text;
  
  // Apply corrections
  for (const [incorrect, correct] of Object.entries(corrections)) {
    const regex = new RegExp(`\\b${incorrect}\\b`, 'gi');
    processedText = processedText.replace(regex, correct);
  }
  
  return processedText;
}

// Enhanced prosody control
function enhanceTextForValeVoice(text: string, config: VoiceConfig): string {
  const { prosody } = config;
  
  // Add prosody markers based on configuration
  const enhancedText = text
    // Add emotional emphasis based on config
    .replace(/\b(feel|felt|feeling|emotion|emotional)\b/gi, (match) => {
      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';
      return `${match}${emphasis}`;
    })
    
    // Add question inflection
    .replace(/\?/g, (match) => {
      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';
      return `${match}${inflection}`;
    })
    
    // Add pauses based on sentence structure
    .replace(/\.\s+([A-Z])/g, (match) => {
      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';
      return `${pause} $1`;
    })
    
    // Add emphasis to important words
    .replace(/\b(important|crucial|significant|key)\b/gi, (match) => {
      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';
      return `${match}${emphasis}`;
    })
    
    // Clean up redundant markers
    .replace(/↗↗/g, '↗')
    .replace(/↘↘/g, '↘')
    .replace(/!!/g, '!')
    .replace(/\.\./g, '.')
    .replace(/,,/g, ',');
    
  return enhancedText;
}

// Streaming transcription service optimized for real-time
export class StreamingTranscriptionService {
  private buffer: Buffer[] = [];
  private lastProcessedTime = 0;
  private transcriptionContext = '';
  private isProcessing = false;
  private readonly CHUNK_SIZE = 500; // ms of audio per chunk
  private readonly MIN_CHUNKS = 5; // Minimum chunks to process

  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {
    this.buffer.push(chunk);
    
    // Process if we have enough audio
    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {
      this.isProcessing = true;
      try {
        const audioBuffer = Buffer.concat(this.buffer);
        
        // Create a temporary file for transcription
        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);
        fs.writeFileSync(tempFile, audioBuffer);
        
        // Transcribe with context using Whisper for reliability
        const transcription = await openai.audio.transcriptions.create({
          file: fs.createReadStream(tempFile),
          model: "whisper-1", // Using the reliable Whisper model
          language: "en",
          prompt: `Previous context: ${this.transcriptionContext}\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,
          response_format: "text", 
          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context
        });
        
        // Clean up temp file
        fs.unlinkSync(tempFile);
        
        // Update context with new transcription
        this.transcriptionContext = transcription;
        
        // Clear processed chunks
        this.buffer = [];
        
        return { text: transcription };
      } finally {
        this.isProcessing = false;
      }
    }
    
    return { text: '' };
  }

  reset() {
    this.buffer = [];
    this.transcriptionContext = '';
    this.lastProcessedTime = 0;
  }
}

// Real-time streaming response optimized for gpt-4o-mini-realtime-preview
export async function streamAITherapistResponse(
  ws: WebSocket,
  userMessage: string,
  conversationHistory: ConversationMessage[],
  shouldStream: boolean,
  systemPrompt?: string,
  voiceConfig?: VoiceConfig,
  conversationConfig?: ConversationConfig
): Promise<AIResponse> {
  try {
    if (!openai.apiKey) {
      throw new Error("OpenAI API key not configured");
    }

    if (ws.readyState !== WebSocket.OPEN) {
      throw new Error("WebSocket is not open");
    }

    // Use a more compact history for real-time interactions
    // This is critical for fast, responsive AI in therapy contexts
    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context
    
    const messages = [
      {
        role: "system",
        content: systemPrompt || createSystemPrompt(conversationConfig)
      },
      ...optimizedHistory,
      {
        role: "user",
        content: userMessage
      }
    ];

    // Convert messages to proper format for OpenAI API
    const formattedMessages = messages.map(msg => ({
      role: msg.role as "system" | "user" | "assistant", 
      content: msg.content
    })) as any; // Type assertion needed to match OpenAI's types
    
    // Use the most appropriate model for our real-time therapy application
    console.log(`Using ${CHAT_MODEL} for therapeutic conversation`);
    const stream = await openai.chat.completions.create({
      model: CHAT_MODEL, // the newest OpenAI model is "gpt-4o" which was released May 13, 2024
      messages: formattedMessages,
      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,
      temperature: conversationConfig?.responseStyle.temperature || 0.7,
      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,
      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,
      top_p: 0.9,
      stream: true // Always stream for better real-time experience
    });

    let fullResponse = "";
    let currentSentence = "";
    let isFirstChunk = true;

    // Process stream with optimized sentence detection for chat completions
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content || "";
      if (content) {
        fullResponse += content;
        currentSentence += content;

        // Detect sentence boundaries for natural response
        if (currentSentence.match(/[.!?]\s*$/) || 
            currentSentence.length > 50) { // Also break on long phrases
          // Send complete sentence
          ws.send(JSON.stringify({
            type: "text",
            content: currentSentence
          }));

          // Generate audio for the sentence
          if (shouldStream) {
            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);
          }

          currentSentence = "";
        }
      }
    }

    // Handle any remaining text
    if (currentSentence) {
      ws.send(JSON.stringify({
        type: "text",
        content: currentSentence
      }));
      if (shouldStream) {
        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);
      }
    }

    return {
      message: fullResponse,
      audioUrl: undefined
    };
  } catch (error) {
    console.error("Error in streamAITherapistResponse:", error);
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ 
        type: "error",
        message: "Error processing request"
      }));
    }
    throw error;
  }
}

// Enhanced real-time audio streaming
async function streamAudioResponse(
  ws: WebSocket,
  text: string,
  voiceConfig: VoiceConfig
): Promise<void> {
  try {
    if (!openai.apiKey) {
      throw new Error("OpenAI API key not configured");
    }

    // Validate inputs before sending to OpenAI
    const sanitizedText = text?.trim() || "I'm sorry, I couldn't process that properly.";
    
    // Ensure voice is one of the valid OpenAI voices
    const validVoice = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"].includes(voiceConfig.voice)
      ? voiceConfig.voice
      : "shimmer"; // Default to shimmer if invalid
    
    // Ensure speed is within valid range
    const validSpeed = typeof voiceConfig.speed === 'number' && 
                      voiceConfig.speed >= 0.5 && 
                      voiceConfig.speed <= 2.0
      ? voiceConfig.speed
      : 1.1; // Default to 1.1 if invalid
    
    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);
    
    // Add SSML-like enhancements if needed through text preprocessing
    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);

    // Generate speech with TTS-1 (reliable production model)
    const audioResponse = await openai.audio.speech.create({
      model: "tts-1", // Using the stable production TTS model
      voice: validVoice,
      input: enhancedText,
      speed: validSpeed,
      response_format: "mp3"
    });

    // Stream audio in optimized chunks with better error handling
    const buffer = Buffer.from(await audioResponse.arrayBuffer());
    
    // Log successful audio generation
    console.log(`Generated ${buffer.length} bytes of audio for "${sanitizedText.substring(0, 50)}..."`);
    
    const chunkSize = 4096; // Balanced chunk size for streaming
    
    for (let i = 0; i < buffer.length; i += chunkSize) {
      const chunk = buffer.slice(i, i + chunkSize);
      ws.send(JSON.stringify({
        type: "audio_chunk",
        audioData: chunk.toString('base64')
      }));
    }
  } catch (error) {
    console.error("Error streaming audio:", error);
    throw error;
  }
}

// Helper function to create system prompt based on conversation config
function createSystemPrompt(config?: ConversationConfig): string {
  const defaultPrompt = 
    "You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. " +
    "Your therapeutic approach combines warmth with curiosity-driven exploration. " +
    "Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. " +
    "Ask thoughtful questions with a curious, exploratory tone. " +
    "Use conversational language with occasional upward inflections at the end of questions to show interest. " +
    "Keep responses concise (2-3 sentences max) as this is a voice conversation. " +
    "Never diagnose conditions or prescribe treatments. " +
    "Respond authentically, as if having a real conversation rather than giving scripted responses.";

  if (!config) return defaultPrompt;

  // Enhance prompt based on conversation config
  return defaultPrompt + "\n" +
    `Response Style Guidelines:
    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words
    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses
    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns
    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;
}

// Function to summarize a completed AI therapy conversation with Vale-like insightful tone
export async function summarizeTherapyConversation(
  conversationHistory: Array<{role: "user" | "assistant" | "system", content: string}>
): Promise<string> {
  try {
    if (!openai.apiKey) {
      throw new Error("OpenAI API key is missing");
    }
    
    // Create a readable format of the conversation for the AI to analyze
    const conversationText = conversationHistory
      .filter(msg => msg.role !== "system")
      .map(msg => `${msg.role === "user" ? "Client" : "AI Therapist"}: ${msg.content}`)
      .join("\n\n");
    
    const response = await openai.chat.completions.create({
      model: CHAT_MODEL,
      messages: [
        {
          role: "system",
          content: "You are LamaMind's summary module with a bright, inquisitive analytical approach. " +
            "When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. " +
            "Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: " +
            "1) Key themes and patterns that emerged during the conversation " +
            "2) The client's emotional states and shifts throughout the session " +
            "3) Important insights or breakthroughs that occurred " +
            "4) Specific areas that might benefit from deeper exploration in future sessions " +
            "5) Any notable therapeutic techniques that were particularly effective " +
            "Balance clinical precision with warmth and understanding in your analysis."
        },
        {
          role: "user",
          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\n\n${conversationText}`
        }
      ],
      max_tokens: 500,
      temperature: 0.7, // Balanced between consistency and creativity
      top_p: 0.9, // Focus on highest probability tokens while allowing some variety
    });
    
    return response.choices[0].message.content || 
      "Unable to generate a summary of the conversation.";
  } catch (error) {
    console.error("Error summarizing therapy conversation:", error);
    return "Error generating conversation summary. Please review the conversation manually.";
  }
}
