import React, { useState, useRef, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, <PERSON>cO<PERSON>, Bo<PERSON>, Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';

interface RealTimeVoiceChatProps {
  userId: number;
  clientId?: number;
  onConversationCreated?: (conversationId: number) => void;
  onConversationEnded?: (summary: string, conversationId: number) => void;
}

const RealTimeVoiceChat: React.FC<RealTimeVoiceChatProps> = ({ 
  userId, 
  clientId = 1,
  onConversationCreated,
  onConversationEnded 
}) => {
  const { toast } = useToast();
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [messages, setMessages] = useState<{
    role: 'user' | 'assistant';
    content: string;
    isStreaming?: boolean;
  }[]>([]);
  const [conversationId, setConversationId] = useState<number | null>(null);
  const [wsStatus, setWsStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  
  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const micStreamRef = useRef<MediaStream | null>(null);
  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);
  const audioQueueRef = useRef<Float32Array[]>([]);
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Initialize WebSocket connection
  useEffect(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    setWsStatus('connecting');
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;
    
    ws.onopen = () => {
      setWsStatus('connected');
      // Start the conversation
      ws.send(JSON.stringify({
        type: 'start',
        userId,
        clientId
      }));
    };
    
    ws.onclose = () => {
      setWsStatus('disconnected');
    };
    
    ws.onerror = () => {
      setWsStatus('error');
      toast({
        title: "Connection Error",
        description: "Failed to connect to therapy server",
        variant: "destructive"
      });
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'ready') {
          setConversationId(data.conversationId);
          if (onConversationCreated) {
            onConversationCreated(data.conversationId);
          }
          
          // Add welcome message
          setMessages([
            {
              role: 'assistant',
              content: "Hello, I'm your AI therapist. How can I help you today?",
              isStreaming: false
            }
          ]);
        }
        else if (data.type === 'stream') {
          // Update the last assistant message with streaming content
          setMessages(prev => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            if (lastMessage.role === 'assistant') {
              lastMessage.content = data.content;
              lastMessage.isStreaming = true;
            } else {
              newMessages.push({
                role: 'assistant',
                content: data.content,
                isStreaming: true
              });
            }
            return newMessages;
          });
        }
        else if (data.type === 'complete') {
          // Mark the last assistant message as complete
          setMessages(prev => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            if (lastMessage.role === 'assistant') {
              lastMessage.isStreaming = false;
            }
            return newMessages;
          });
        }
        else if (data.type === 'summary') {
          // Handle session summary
          if (onConversationEnded && conversationId) {
            onConversationEnded(data.content, conversationId);
          }
        }
        else if (data.type === 'error') {
          toast({
            title: "Error",
            description: data.message,
            variant: "destructive"
          });
          setIsProcessing(false);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
    
    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [userId, clientId, toast, onConversationCreated]);
  
  // Start real-time voice processing
  const startRecording = async () => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      micStreamRef.current = stream;
      
      // Create audio context
      const audioContext = new AudioContext();
      audioContextRef.current = audioContext;
      
      // Create audio source from microphone
      const source = audioContext.createMediaStreamSource(stream);
      
      // Create script processor for real-time audio processing
      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      audioProcessorRef.current = processor;
      
      // Process audio in real-time
      processor.onaudioprocess = (e) => {
        const inputData = e.inputBuffer.getChannelData(0);
        audioQueueRef.current.push(new Float32Array(inputData));
        
        // Process audio queue every 100ms
        if (!processingTimeoutRef.current) {
          processingTimeoutRef.current = setTimeout(() => {
            if (audioQueueRef.current.length > 0 && wsRef.current?.readyState === WebSocket.OPEN) {
              // Convert audio data to base64 and send
              const audioData = audioQueueRef.current;
              audioQueueRef.current = [];
              
              // Send audio data to server for real-time processing
              wsRef.current.send(JSON.stringify({
                type: 'audio',
                data: audioData
              }));
            }
            processingTimeoutRef.current = null;
          }, 100);
        }
      };
      
      // Connect processor to audio context
      source.connect(processor);
      processor.connect(audioContext.destination);
      
      setIsRecording(true);
      toast({
        title: "Recording started",
        description: "Speak naturally - the AI will respond in real-time"
      });
    } catch (error) {
      console.error('Error accessing microphone:', error);
      toast({
        title: "Microphone Error",
        description: "Could not access your microphone. Please check permissions.",
        variant: "destructive"
      });
    }
  };
  
  // Stop recording
  const stopRecording = () => {
    if (audioProcessorRef.current) {
      audioProcessorRef.current.disconnect();
      audioProcessorRef.current = null;
    }
    
    if (micStreamRef.current) {
      micStreamRef.current.getTracks().forEach(track => track.stop());
      micStreamRef.current = null;
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }
    
    setIsRecording(false);
    toast({
      title: "Recording stopped",
      description: "The conversation will continue in real-time"
    });
  };
  
  return (
    <Card className="w-full max-w-3xl mx-auto">
      {/* Chat messages */}
      <div className="h-[500px] overflow-y-auto p-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-4 ${
              message.role === "user" ? "text-right" : "text-left"
            }`}
          >
            <div
              className={`inline-block p-3 rounded-lg ${
                message.role === "user"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {message.content}
              {message.isStreaming && (
                <span className="inline-block w-1 h-4 ml-1 bg-current animate-pulse"></span>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* Controls */}
      <div className="border-t p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span
              className={`h-2 w-2 rounded-full ${
                wsStatus === 'connected'
                  ? 'bg-green-500'
                  : wsStatus === 'connecting'
                  ? 'bg-yellow-500'
                  : 'bg-red-500'
              }`}
            ></span>
            <span className="text-sm text-gray-500">
              {wsStatus === 'connected' ? 'Connected' : wsStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}
            </span>
          </div>
          
          <Button
            onClick={isRecording ? stopRecording : startRecording}
            className={`${
              isRecording
                ? "bg-red-500 hover:bg-red-600"
                : "bg-blue-500 hover:bg-blue-600"
            }`}
            disabled={wsStatus !== 'connected'}
          >
            {isRecording ? (
              <>
                <MicOff className="h-4 w-4 mr-2" />
                Stop Recording
              </>
            ) : (
              <>
                <Mic className="h-4 w-4 mr-2" />
                Start Recording
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default RealTimeVoiceChat; 