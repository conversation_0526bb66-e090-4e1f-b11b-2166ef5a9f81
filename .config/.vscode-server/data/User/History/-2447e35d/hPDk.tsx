import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface NewNoteFormProps {
  onSuccess?: () => void;
}

const formSchema = z.object({
  clientId: z.string().min(1, "Please select a client"),
  sessionDate: z.string().min(1, "Please select a date"),
  duration: z.number().min(1, "Duration must be at least 1 minute"),
  content: z.string().min(1, "Please enter session notes")
});

type FormValues = z.infer<typeof formSchema>;

export default function NewNoteForm({ onSuccess }: NewNoteFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch clients for the select dropdown
  const { data: clients, isLoading: isLoadingClients } = useQuery({
    queryKey: ["/api/clients"],
  });

  // Get today's date in YYYY-MM-DD format for the date input
  const today = new Date().toISOString().split("T")[0];

  const { register, handleSubmit, formState: { errors }, reset } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sessionDate: today,
      duration: 50,
      content: ""
    }
  });

  const createNoteMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      const res = await apiRequest("POST", "/api/notes", {
        clientId: parseInt(data.clientId),
        sessionDate: data.sessionDate,
        duration: data.duration,
        content: data.content
      });
      return await res.json();
    },
    onSuccess: () => {
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/notes"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard/stats"] });
      
      // Show success message
      toast({
        title: "Note created successfully",
        description: "Your session note has been submitted for AI analysis",
      });
      
      // Reset form
      reset();
      
      // Call onSuccess callback if provided
      if (onSuccess) onSuccess();
      
      setIsSubmitting(false);
    },
    onError: (error) => {
      console.error("Error creating note:", error);
      toast({
        title: "Error creating note",
        description: "There was a problem submitting your session note. Please try again.",
        variant: "destructive"
      });
      setIsSubmitting(false);
    }
  });

  const onSubmit = (data: FormValues) => {
    setIsSubmitting(true);
    createNoteMutation.mutate(data);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <h3 className="text-lg font-medium text-neutral-dark mb-4">Add New Session Note</h3>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Label htmlFor="client-select">Select Client</Label>
            <Select disabled={isLoadingClients} {...register("clientId")}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a client" />
              </SelectTrigger>
              <SelectContent>
                {clients?.map((client) => (
                  <SelectItem key={client.id} value={client.id.toString()}>
                    {client.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.clientId && (
              <p className="text-sm text-red-500 mt-1">{errors.clientId.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="session-date">Session Date</Label>
            <Input 
              type="date" 
              id="session-date"
              max={today}
              {...register("sessionDate")} 
            />
            {errors.sessionDate && (
              <p className="text-sm text-red-500 mt-1">{errors.sessionDate.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="session-duration">Session Duration (minutes)</Label>
            <Input 
              type="number" 
              id="session-duration" 
              min="1" 
              step="1"
              {...register("duration")} 
            />
            {errors.duration && (
              <p className="text-sm text-red-500 mt-1">{errors.duration.message}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="session-notes">Session Notes</Label>
            <Textarea 
              id="session-notes" 
              rows={6} 
              placeholder="Enter your detailed session notes here..."
              {...register("content")}
            />
            <p className="mt-1 text-sm text-gray-500">Your notes will be securely processed and analyzed by our HIPAA-compliant AI.</p>
            {errors.content && (
              <p className="text-sm text-red-500 mt-1">{errors.content.message}</p>
            )}
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button 
              type="button" 
              variant="outline"
              onClick={() => reset()}
              disabled={isSubmitting}
            >
              Clear
            </Button>
            <Button 
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="material-icons text-sm mr-2 animate-spin">progress_activity</span>
                  Processing...
                </>
              ) : (
                <>
                  <span className="material-icons text-sm mr-2">psychology</span>
                  Analyze Session
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
