import { useQuery } from "@tanstack/react-query";
import { cn } from "@/lib/utils";

interface InsightsSidebarProps {
  className?: string;
}

export default function InsightsSidebar({ className }: InsightsSidebarProps) {
  // Fetch dashboard stats for insights
  const { data: stats } = useQuery({
    queryKey: ["/api/dashboard/stats"],
  });

  // Fetch clients to get all themes
  const { data: clients } = useQuery({
    queryKey: ["/api/clients"],
  });

  // Create aggregated data for themes across all clients
  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];
  const themeCounts: Record<string, number> = {};
  
  allThemes.forEach((theme: any) => {
    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;
  });
  
  // Sort theme entries by occurrence count
  const sortedThemes = Object.entries(themeCounts)
    .sort(([, countA], [, countB]) => countB - countA)
    .slice(0, 5);
  
  // Weekly activity data (mocked for now)
  const weeklyActivityData = [
    { day: "M", percentage: 40 },
    { day: "T", percentage: 60 },
    { day: "W", percentage: 80 },
    { day: "T", percentage: 30 },
    { day: "F", percentage: 70 },
    { day: "S", percentage: 10 },
    { day: "S", percentage: 5 }
  ];

  return (
    <aside className={cn("h-full w-64 bg-white border-l border-gray-200 overflow-y-auto", className)}>
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg font-medium text-neutral-dark mb-4">Insights Overview</h3>
        
        {/* Weekly Activity Chart */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-neutral-dark mb-2">Weekly Activity</h4>
          <div className="bg-neutral-light p-2 rounded-md">
            <div className="flex items-end justify-between h-32">
              {weeklyActivityData.map((item) => (
                <div key={item.day} className="flex flex-col items-center w-8">
                  <div 
                    className={`${item.day === "S" || item.day === "S" ? "bg-primary-light opacity-50" : "bg-primary"} w-4 rounded-t`} 
                    style={{ height: `${item.percentage}%` }}
                  ></div>
                  <span className="text-xs mt-1">{item.day}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Common Themes */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-neutral-dark mb-2">Common Themes</h4>
          <div className="space-y-2">
            {sortedThemes.map(([name, count], index) => {
              const maxCount = sortedThemes[0][1];
              const percentage = Math.round((count / maxCount) * 100);
              
              return (
                <div key={name} className="flex items-center justify-between">
                  <span className="text-sm text-neutral-dark">{name}</span>
                  <div className="w-1/2 bg-neutral-light rounded-full h-2">
                    <div 
                      className="bg-primary rounded-full h-2" 
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Client Status Distribution */}
        <div>
          <h4 className="text-sm font-medium text-neutral-dark mb-2">Client Status</h4>
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="bg-success-light bg-opacity-20 p-2 rounded">
              <div className="text-lg font-semibold text-success">
                {(stats as any)?.improvingPercentage || 0}%
              </div>
              <div className="text-xs text-neutral-dark">Improving</div>
            </div>
            <div className="bg-warning-light bg-opacity-20 p-2 rounded">
              <div className="text-lg font-semibold text-warning-dark">
                {(stats as any) ? Math.round((((stats as any).totalClients - (stats as any).improving - 3) / (stats as any).totalClients) * 100) : 0}%
              </div>
              <div className="text-xs text-neutral-dark">Stable</div>
            </div>
            <div className="bg-error-light bg-opacity-20 p-2 rounded">
              <div className="text-lg font-semibold text-error">
                {(stats as any) ? Math.round((3 / (stats as any).totalClients) * 100) : 0}%
              </div>
              <div className="text-xs text-neutral-dark">Needs Attention</div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}
