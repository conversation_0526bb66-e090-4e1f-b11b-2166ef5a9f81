import { useQuery } from "@tanstack/react-query";
import { cn } from "@/lib/utils";

interface InsightsSidebarProps {
  className?: string;
}

export default function InsightsSidebar({ className }: InsightsSidebarProps) {
  // Fetch dashboard stats for insights
  const { data: stats } = useQuery({
    queryKey: ["/api/dashboard/stats"],
  });

  // Fetch clients to get all themes
  const { data: clients } = useQuery({
    queryKey: ["/api/clients"],
  });

  // Create aggregated data for themes across all clients
  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];
  const themeCounts: Record<string, number> = {};
  
  allThemes.forEach((theme: any) => {
    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;
  });
  
  // Sort theme entries by occurrence count
  const sortedThemes = Object.entries(themeCounts)
    .sort(([, countA], [, countB]) => countB - countA)
    .slice(0, 5);
  
  // Weekly activity data (mocked for now)
  const weeklyActivityData = [
    { day: "M", percentage: 40 },
    { day: "T", percentage: 60 },
    { day: "W", percentage: 80 },
    { day: "T", percentage: 30 },
    { day: "F", percentage: 70 },
    { day: "S", percentage: 10 },
    { day: "S", percentage: 5 }
  ];

  return (
    <aside className={cn("h-full w-64 bg-white border-l border-gray-200 overflow-y-auto", className)}>
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Insights Overview</h3>
        
        {/* Weekly Activity Chart */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Weekly Activity</h4>
          <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
            <div className="flex items-end justify-between h-20 space-x-2">
              {weeklyActivityData.map((item, index) => (
                <div key={`${item.day}-${index}`} className="flex flex-col items-center flex-1">
                  <div className="w-full flex flex-col justify-end h-16">
                    <div
                      className={`${index >= 5 ? "bg-blue-300" : "bg-blue-500"} w-full rounded-t transition-all duration-300 min-h-[4px]`}
                      style={{ height: `${Math.max(item.percentage, 10)}%` }}
                    ></div>
                  </div>
                  <span className="text-xs mt-2 text-gray-600 font-medium">{item.day}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Common Themes */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Common Themes</h4>
          <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
            {sortedThemes.length > 0 ? (
              <div className="space-y-3">
                {sortedThemes.map(([name, count], index) => {
                  const maxCount = sortedThemes[0][1];
                  const percentage = Math.round((count / maxCount) * 100);

                  return (
                    <div key={name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-800 font-medium">{name}</span>
                        <span className="text-xs text-gray-600 bg-gray-200 px-2 py-1 rounded">{count}</span>
                      </div>
                      <div className="w-full bg-gray-300 rounded-full h-2">
                        <div
                          className="bg-blue-500 rounded-full h-2 transition-all duration-300"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-sm text-gray-500 text-center">No themes available</p>
            )}
          </div>
        </div>
        
        {/* Client Status Distribution */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Client Status</h4>
          <div className="space-y-2">
            <div className="bg-green-50 border border-green-200 p-3 rounded-lg flex items-center justify-between">
              <div>
                <div className="text-lg font-bold text-green-600">
                  {(stats as any)?.improvingPercentage || 0}%
                </div>
                <div className="text-sm text-green-700">Improving</div>
              </div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg flex items-center justify-between">
              <div>
                <div className="text-lg font-bold text-yellow-600">
                  {(stats as any) ? Math.round((((stats as any).totalClients - (stats as any).improving - 3) / (stats as any).totalClients) * 100) : 0}%
                </div>
                <div className="text-sm text-yellow-700">Stable</div>
              </div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            </div>
            <div className="bg-red-50 border border-red-200 p-3 rounded-lg flex items-center justify-between">
              <div>
                <div className="text-lg font-bold text-red-600">
                  {(stats as any) ? Math.round((3 / (stats as any).totalClients) * 100) : 0}%
                </div>
                <div className="text-sm text-red-700">Needs Attention</div>
              </div>
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}
