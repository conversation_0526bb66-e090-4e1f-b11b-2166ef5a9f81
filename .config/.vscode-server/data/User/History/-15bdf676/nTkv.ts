import { 
  users, type User, type InsertUser,
  clients, type Client, type InsertClient,
  sessionNotes, type SessionNote, type InsertSessionNote,
  clientThemes, type ClientTheme, type InsertClientTheme,
  aiTherapyConversations, type AiTherapyConversation, type InsertAiTherapyConversation,
  aiTherapyMessages, type AiTherapyMessage, type InsertAiTherapyMessage,
  aiTherapySettings, type AiTherapySettings, type InsertAiTherapySettings,
  doctorClients, type DoctorClient, type InsertDoctorClient,
  invitationCodes, type InvitationCode, type InsertInvitationCode,
  type Theme, type DashboardStats
} from "@shared/schema";
import session from "express-session";
import createMemoryStore from "memorystore";
import { db } from "./db";
import { eq, desc, and, inArray, not } from "drizzle-orm";
import PgSession from "connect-pg-simple";
import { pool } from "./db";

// DatabaseStorage class that implements IStorage for PostgreSQL
export class DatabaseStorage implements IStorage {
  sessionStore: session.Store;

  constructor() {
    // Initialize session store with PostgreSQL
    const PgStore = PgSession(session);
    this.sessionStore = new PgStore({
      pool: pool,
      tableName: 'session',
      createTableIfMissing: true
    });
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id));
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username));
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const result = await db.insert(users).values(user).returning();
    return result[0];
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const result = await db.update(users).set(userData).where(eq(users.id, id)).returning();
    return result[0];
  }

  async getAllUsers(): Promise<User[]> {
    return await db.select().from(users);
  }

  async getAllClients(): Promise<Client[]> {
    return await db.select().from(clients);
  }

  async getClientsByDoctor(doctorId: number): Promise<Client[]> {
    const assignments = await db.select()
      .from(doctorClients)
      .where(eq(doctorClients.doctorId, doctorId));

    const clientIds = assignments.map(a => a.clientId);
    if (clientIds.length === 0) return [];

    // Fix: Use inArray to get ALL clients for this doctor
    return await db.select()
      .from(clients)
      .where(inArray(clients.id, clientIds));
  }

  async getAllConversations(): Promise<AiTherapyConversation[]> {
    return await db.select()
      .from(aiTherapyConversations)
      .orderBy(desc(aiTherapyConversations.createdAt));
  }
  
  // Doctor-Client assignment operations
  async createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient> {
    const result = await db.insert(doctorClients).values(assignment).returning();
    return result[0];
  }

  async getAllDoctorClientRelationships(): Promise<DoctorClient[]> {
    return await db.select().from(doctorClients);
  }
  
  // Client operations
  async getClient(id: number): Promise<Client | undefined> {
    const result = await db.select().from(clients).where(eq(clients.id, id));
    return result[0];
  }

  async getClientsByUserId(userId: number): Promise<Client[]> {
    return await db.select().from(clients).where(eq(clients.userId, userId));
  }

  async getClientByUserId(userId: number): Promise<Client | undefined> {
    const result = await db.select().from(clients).where(eq(clients.userId, userId));
    return result[0];
  }

  async createClient(client: InsertClient): Promise<Client> {
    const result = await db.insert(clients).values(client).returning();
    return result[0];
  }

  async updateClientStatus(id: number, status: string): Promise<Client | undefined> {
    const result = await db.update(clients)
      .set({ status })
      .where(eq(clients.id, id))
      .returning();
    return result[0];
  }
  
  // Session notes operations
  async getSessionNote(id: number): Promise<SessionNote | undefined> {
    const result = await db.select().from(sessionNotes).where(eq(sessionNotes.id, id));
    return result[0];
  }

  async getSessionNotesByClientId(clientId: number): Promise<SessionNote[]> {
    return await db.select()
      .from(sessionNotes)
      .where(eq(sessionNotes.clientId, clientId))
      .orderBy(desc(sessionNotes.createdAt));
  }

  async getRecentSessionNotes(userId: number, limit: number = 10): Promise<SessionNote[]> {
    // This is a simplified implementation that just returns recent notes
    // A more complete implementation would filter based on user access
    return await db.select()
      .from(sessionNotes)
      .orderBy(desc(sessionNotes.createdAt))
      .limit(limit);
  }

  async createSessionNote(note: InsertSessionNote): Promise<SessionNote> {
    const result = await db.insert(sessionNotes).values(note).returning();
    return result[0];
  }

  async updateSessionNoteAnalysis(
    id: number,
    summary: string,
    themes: Theme[],
    recommendations: string[]
  ): Promise<SessionNote | undefined> {
    const result = await db.update(sessionNotes)
      .set({
        summary,
        recommendations,
        analyzed: true
      })
      .where(eq(sessionNotes.id, id))
      .returning();
      
    if (result[0] && themes && themes.length > 0) {
      for (const theme of themes) {
        await this.updateOrCreateClientTheme(result[0].clientId, theme.name, theme.trend);
      }
    }
    
    return result[0];
  }
  
  // Client themes operations
  async getClientThemes(clientId: number): Promise<ClientTheme[]> {
    return await db.select()
      .from(clientThemes)
      .where(eq(clientThemes.clientId, clientId))
      .orderBy(desc(clientThemes.occurrences));
  }

  async updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme> {
    // First try to find existing theme
    const existingTheme = await db.select()
      .from(clientThemes)
      .where(and(
        eq(clientThemes.clientId, clientId),
        eq(clientThemes.name, name)
      ));
    
    if (existingTheme.length > 0) {
      // Update existing theme
      const result = await db.update(clientThemes)
        .set({
          occurrences: existingTheme[0].occurrences + 1,
          trend
        })
        .where(eq(clientThemes.id, existingTheme[0].id))
        .returning();
      return result[0];
    } else {
      // Create new theme
      const result = await db.insert(clientThemes)
        .values({
          clientId,
          name,
          occurrences: 1,
          trend
        })
        .returning();
      return result[0];
    }
  }
  
  // AI Therapy Conversation operations
  async getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined> {
    const result = await db.select()
      .from(aiTherapyConversations)
      .where(eq(aiTherapyConversations.id, id));
    return result[0];
  }

  async getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]> {
    if (clientId) {
      return await db.select()
        .from(aiTherapyConversations)
        .where(eq(aiTherapyConversations.clientId, clientId))
        .orderBy(desc(aiTherapyConversations.createdAt));
    } else {
      return await db.select()
        .from(aiTherapyConversations)
        .where(eq(aiTherapyConversations.userId, userId))
        .orderBy(desc(aiTherapyConversations.createdAt));
    }
  }

  async createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation> {
    const result = await db.insert(aiTherapyConversations)
      .values({
        ...conversation,
        title: conversation.title || "New Conversation",
        active: true,
        aiProcessed: false
      })
      .returning();
    return result[0];
  }

  async endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined> {
    const result = await db.update(aiTherapyConversations)
      .set({
        summary,
        active: false,
        aiProcessed: true,
        endedAt: new Date()
      })
      .where(eq(aiTherapyConversations.id, id))
      .returning();
    return result[0];
  }
  
  // AI Therapy Message operations
  async getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]> {
    return await db.select()
      .from(aiTherapyMessages)
      .where(eq(aiTherapyMessages.conversationId, conversationId))
      .orderBy(aiTherapyMessages.timestamp);
  }

  async createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage> {
    const result = await db.insert(aiTherapyMessages)
      .values(message)
      .returning();
    return result[0];
  }
  
  // AI Therapy Settings operations
  async getAiTherapySettings(): Promise<AiTherapySettings[]> {
    return await db.select().from(aiTherapySettings);
  }

  async getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined> {
    const result = await db.select()
      .from(aiTherapySettings)
      .where(eq(aiTherapySettings.active, true));
    return result[0];
  }

  async createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings> {
    // If this is going to be the active settings, deactivate all others
    if (settings.active) {
      await db.update(aiTherapySettings)
        .set({ active: false })
        .where(eq(aiTherapySettings.active, true));
    }
    
    const result = await db.insert(aiTherapySettings)
      .values(settings)
      .returning();
    return result[0];
  }

  async updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined> {
    // If this update is to activate settings, deactivate all others
    if (settings.active) {
      await db.update(aiTherapySettings)
        .set({ active: false })
        .where(and(
          eq(aiTherapySettings.active, true),
          not(eq(aiTherapySettings.id, id))
        ));
    }
    
    const result = await db.update(aiTherapySettings)
      .set({
        ...settings,
        updatedAt: new Date()
      })
      .where(eq(aiTherapySettings.id, id))
      .returning();
    return result[0];
  }
  
  // Dashboard stats operations
  async getDashboardStats(userId: number): Promise<DashboardStats> {
    // Get all clients for this user
    const clientsResult = await db.select().from(clients).where(eq(clients.userId, userId));
    const totalClients = clientsResult.length;
    
    // Calculate how many clients are improving
    const improving = clientsResult.filter(client => client.status === 'improving').length;
    const improvingPercentage = totalClients > 0 ? Math.round((improving / totalClients) * 100) : 0;
    
    // Get client IDs for this user
    const clientIds = clientsResult.map(client => client.id);
    
    // Calculate weekly notes (for the last 7 days)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    let weeklyNotes = 0;
    let insights = 0;
    
    if (clientIds.length > 0) {
      // Count weekly notes
      const notesResult = await db.execute(
        `SELECT COUNT(*) FROM session_notes 
         WHERE client_id = ANY($1) AND created_at >= $2`,
        [clientIds, oneWeekAgo]
      );
      weeklyNotes = parseInt(notesResult.rows[0].count || "0");
      
      // Count analyzed notes (insights)
      const insightsResult = await db.execute(
        `SELECT COUNT(*) FROM session_notes 
         WHERE client_id = ANY($1) AND analyzed = true`,
        [clientIds]
      );
      insights = parseInt(insightsResult.rows[0].count || "0");
    }
    
    return {
      totalClients,
      weeklyNotes,
      insights,
      improving,
      improvingPercentage
    };
  }
  
  // Invitation code operations
  async createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode> {
    const result = await db.insert(invitationCodes)
      .values(code)
      .returning();
    return result[0];
  }

  async getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined> {
    const result = await db.select()
      .from(invitationCodes)
      .where(eq(invitationCodes.code, code));
    return result[0];
  }

  async getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]> {
    if (type) {
      return await db.select()
        .from(invitationCodes)
        .where(and(
          eq(invitationCodes.createdBy, userId),
          eq(invitationCodes.type, type)
        ));
    } else {
      return await db.select()
        .from(invitationCodes)
        .where(eq(invitationCodes.createdBy, userId));
    }
  }

  async markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined> {
    const result = await db.update(invitationCodes)
      .set({
        used: true,
        usedBy: userId,
        usedAt: new Date()
      })
      .where(eq(invitationCodes.code, code))
      .returning();
    return result[0];
  }

  async getAllInvitationCodes(): Promise<InvitationCode[]> {
    return await db.select()
      .from(invitationCodes)
      .orderBy(desc(invitationCodes.createdAt));
  }
}

export interface IStorage {
  // Session store for authentication
  sessionStore: session.Store;
  
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>;
  getAllClients(): Promise<Client[]>;
  getClientsByDoctor(doctorId: number): Promise<Client[]>;
  getAllConversations(): Promise<AiTherapyConversation[]>;
  
  // Doctor-Client assignment operations
  createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient>;
  getAllDoctorClientRelationships(): Promise<DoctorClient[]>;
  
  // Client operations
  getClient(id: number): Promise<Client | undefined>;
  getClientsByUserId(userId: number): Promise<Client[]>;
  getClientByUserId(userId: number): Promise<Client | undefined>;
  createClient(client: InsertClient): Promise<Client>;
  updateClientStatus(id: number, status: string): Promise<Client | undefined>;
  
  // Session notes operations
  getSessionNote(id: number): Promise<SessionNote | undefined>;
  getSessionNotesByClientId(clientId: number): Promise<SessionNote[]>;
  getRecentSessionNotes(userId: number, limit?: number): Promise<SessionNote[]>;
  createSessionNote(note: InsertSessionNote): Promise<SessionNote>;
  updateSessionNoteAnalysis(
    id: number, 
    summary: string, 
    themes: Theme[], 
    recommendations: string[]
  ): Promise<SessionNote | undefined>;
  
  // Client themes operations
  getClientThemes(clientId: number): Promise<ClientTheme[]>;
  updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme>;
  
  // AI Therapy Conversation operations
  getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined>;
  getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]>;
  createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation>;
  endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined>;
  
  // AI Therapy Message operations
  getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]>;
  createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage>;
  
  // AI Therapy Settings operations
  getAiTherapySettings(): Promise<AiTherapySettings[]>;
  getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined>;
  createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings>;
  updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined>;
  
  // Dashboard stats operations
  getDashboardStats(userId: number): Promise<DashboardStats>;
  
  // Invitation code operations
  createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode>;
  getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined>;
  getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]>;
  getAllInvitationCodes(): Promise<InvitationCode[]>;
  markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined>;
}

export class MemStorage implements IStorage {
  sessionStore: session.Store;
  
  private users: Map<number, User>;
  private clients: Map<number, Client>;
  private sessionNotes: Map<number, SessionNote>;
  private clientThemes: Map<number, ClientTheme>;
  private aiConversations: Map<number, AiTherapyConversation>;
  private aiMessages: Map<number, AiTherapyMessage>;
  private aiSettings: Map<number, AiTherapySettings>;
  private doctorClients: Map<number, DoctorClient>;
  
  private userId: number;
  private clientId: number;
  private noteId: number;
  private themeId: number;
  private conversationId: number;
  private messageId: number;
  private settingId: number;
  private doctorClientId: number;
  private invitationCodes: Map<number, InvitationCode>;
  private invitationCodeId: number;

  constructor() {
    // Initialize session store
    const MemoryStore = createMemoryStore(session);
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // Prune expired entries every 24h
    });
    
    // Initialize data collections
    this.users = new Map();
    this.clients = new Map();
    this.sessionNotes = new Map();
    this.clientThemes = new Map();
    this.aiConversations = new Map();
    this.aiMessages = new Map();
    this.aiSettings = new Map();
    this.doctorClients = new Map();
    this.invitationCodes = new Map();
    
    // Initialize counters for IDs
    this.userId = 1;
    this.clientId = 1;
    this.noteId = 1;
    this.themeId = 1;
    this.conversationId = 1;
    this.messageId = 1;
    this.settingId = 1;
    this.doctorClientId = 1;
    this.invitationCodeId = 1;
    
    // Create a default therapist user
    this.createUser({
      username: "drroberts",
      password: "password123", // In a real app, this would be hashed
      name: "Dr. Roberts",
      userRole: "doctor",
      professionalRole: "Psychologist",
      email: "<EMAIL>"
    });
    
    // Create some default clients
    const userId = 1;
    this.createClient({
      userId,
      name: "Jane Doe",
      status: "improving"
    });
    
    this.createClient({
      userId,
      name: "Michael Smith",
      status: "stable"
    });
    
    this.createClient({
      userId,
      name: "Alex Lee",
      status: "needs_attention"
    });
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userId++;
    const user: User = { 
      ...insertUser, 
      id, 
      userRole: insertUser.userRole || "client",
      professionalRole: insertUser.professionalRole || null,
      createdAt: new Date() 
    };
    this.users.set(id, user);
    return user;
  }
  
  // Client operations
  async getClient(id: number): Promise<Client | undefined> {
    return this.clients.get(id);
  }
  
  async getClientsByUserId(userId: number): Promise<Client[]> {
    return Array.from(this.clients.values()).filter(
      (client) => client.userId === userId
    );
  }
  
  async createClient(insertClient: InsertClient): Promise<Client> {
    const id = this.clientId++;
    const client: Client = {
      ...insertClient,
      id,
      status: insertClient.status || "stable",
      createdAt: new Date()
    };
    this.clients.set(id, client);
    return client;
  }
  
  async updateClientStatus(id: number, status: string): Promise<Client | undefined> {
    const client = await this.getClient(id);
    if (client) {
      const updatedClient = { ...client, status };
      this.clients.set(id, updatedClient);
      return updatedClient;
    }
    return undefined;
  }
  
  // Session notes operations
  async getSessionNote(id: number): Promise<SessionNote | undefined> {
    return this.sessionNotes.get(id);
  }
  
  async getSessionNotesByClientId(clientId: number): Promise<SessionNote[]> {
    return Array.from(this.sessionNotes.values())
      .filter((note) => note.clientId === clientId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  async getRecentSessionNotes(userId: number, limit: number = 10): Promise<SessionNote[]> {
    const user = await this.getUser(userId);
    if (!user) return [];
    
    const notes = Array.from(this.sessionNotes.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
    
    return notes;
  }
  
  async createSessionNote(insertNote: InsertSessionNote): Promise<SessionNote> {
    const id = this.noteId++;
    const note: SessionNote = {
      ...insertNote,
      id,
      summary: null,
      recommendations: null,
      analyzed: false,
      createdAt: new Date()
    };
    this.sessionNotes.set(id, note);
    return note;
  }
  
  async updateSessionNoteAnalysis(
    id: number, 
    summary: string, 
    themes: Theme[], 
    recommendations: string[]
  ): Promise<SessionNote | undefined> {
    const note = await this.getSessionNote(id);
    if (note) {
      const updatedNote: SessionNote = {
        ...note,
        summary,
        recommendations,
        analyzed: true
      };
      this.sessionNotes.set(id, updatedNote);
      
      // Update client themes based on analysis
      if (themes && themes.length > 0) {
        for (const theme of themes) {
          await this.updateOrCreateClientTheme(note.clientId, theme.name, theme.trend);
        }
      }
      
      return updatedNote;
    }
    return undefined;
  }
  
  // Client themes operations
  async getClientThemes(clientId: number): Promise<ClientTheme[]> {
    return Array.from(this.clientThemes.values())
      .filter((theme) => theme.clientId === clientId)
      .sort((a, b) => b.occurrences - a.occurrences);
  }
  
  async updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme> {
    const existingTheme = Array.from(this.clientThemes.values()).find(
      (theme) => theme.clientId === clientId && theme.name === name
    );
    
    if (existingTheme) {
      const updatedTheme = {
        ...existingTheme,
        occurrences: existingTheme.occurrences + 1,
        trend: trend
      };
      this.clientThemes.set(existingTheme.id, updatedTheme);
      return updatedTheme;
    } else {
      const id = this.themeId++;
      const newTheme: ClientTheme = {
        id,
        clientId,
        name,
        occurrences: 1,
        trend,
        createdAt: new Date()
      };
      this.clientThemes.set(id, newTheme);
      return newTheme;
    }
  }
  
  // Client operations - added for AI therapy
  async getClientByUserId(userId: number): Promise<Client | undefined> {
    return Array.from(this.clients.values()).find(
      (client) => client.userId === userId
    );
  }
  
  // AI Therapy Conversation operations
  async getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined> {
    return this.aiConversations.get(id);
  }
  
  async getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]> {
    let conversations = Array.from(this.aiConversations.values());
    
    if (clientId) {
      conversations = conversations.filter(conv => conv.clientId === clientId);
    } else {
      // If no specific client ID, first we need to get all client IDs belonging to this user
      const clients = await this.getClientsByUserId(userId);
      const clientIds = clients.map(client => client.id);
      conversations = conversations.filter(conv => clientIds.includes(conv.clientId));
    }
    
    return conversations.sort((a, b) => {
      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  }
  
  async createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation> {
    const id = this.conversationId++;
    const newConversation: AiTherapyConversation = {
      ...conversation,
      id,
      summary: null,
      active: true,
      aiProcessed: false,
      createdAt: new Date(),
      endedAt: null
    };
    
    this.aiConversations.set(id, newConversation);
    return newConversation;
  }
  
  async endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined> {
    const conversation = await this.getAiTherapyConversation(id);
    if (!conversation) return undefined;
    
    const updatedConversation: AiTherapyConversation = {
      ...conversation,
      summary,
      active: false,
      aiProcessed: true,
      endedAt: new Date()
    };
    
    this.aiConversations.set(id, updatedConversation);
    return updatedConversation;
  }
  
  // AI Therapy Message operations
  async getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]> {
    return Array.from(this.aiMessages.values())
      .filter(message => message.conversationId === conversationId)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }
  
  async createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage> {
    const id = this.messageId++;
    const newMessage: AiTherapyMessage = {
      ...message,
      id,
      timestamp: message.timestamp || new Date(),
      createdAt: new Date(),
      audioUrl: message.audioUrl || null
    };
    
    this.aiMessages.set(id, newMessage);
    return newMessage;
  }
  
  // AI Therapy Settings operations
  async getAiTherapySettings(): Promise<AiTherapySettings[]> {
    return Array.from(this.aiSettings.values());
  }
  
  async getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined> {
    return Array.from(this.aiSettings.values()).find(settings => settings.active);
  }
  
  async createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings> {
    // If this is going to be the active settings, deactivate all others
    if (settings.active) {
      for (const [id, existingSettings] of this.aiSettings.entries()) {
        if (existingSettings.active) {
          this.aiSettings.set(id, { ...existingSettings, active: false });
        }
      }
    }
    
    const id = this.settingId++;
    const newSettings: AiTherapySettings = {
      ...settings,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
      model: settings.model || "gpt-4o",
      temperature: settings.temperature || 70,
      active: settings.active === undefined ? true : settings.active
    };
    
    this.aiSettings.set(id, newSettings);
    return newSettings;
  }
  
  async updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined> {
    const existingSettings = this.aiSettings.get(id);
    if (!existingSettings) return undefined;
    
    // If this update is to activate settings, deactivate all others
    if (settings.active) {
      for (const [settingsId, currentSettings] of this.aiSettings.entries()) {
        if (settingsId !== id && currentSettings.active) {
          this.aiSettings.set(settingsId, { ...currentSettings, active: false });
        }
      }
    }
    
    const updatedSettings: AiTherapySettings = {
      ...existingSettings,
      ...settings,
      updatedAt: new Date()
    };
    
    this.aiSettings.set(id, updatedSettings);
    return updatedSettings;
  }
  
  // Dashboard stats operations
  async getDashboardStats(userId: number): Promise<DashboardStats> {
    const clients = await this.getClientsByUserId(userId);
    const totalClients = clients.length;
    
    // Calculate how many clients are improving
    const improving = clients.filter(client => client.status === 'improving').length;
    const improvingPercentage = totalClients > 0 ? Math.round((improving / totalClients) * 100) : 0;
    
    // Calculate weekly notes (for the last 7 days)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    const clientIds = clients.map(client => client.id);
    const allNotes = Array.from(this.sessionNotes.values());
    
    const weeklyNotes = allNotes.filter(note => 
      clientIds.includes(note.clientId) && 
      note.createdAt >= oneWeekAgo
    ).length;
    
    // Calculate total insights (analyzed notes)
    const insights = allNotes.filter(note => 
      clientIds.includes(note.clientId) && 
      note.analyzed
    ).length;
    
    return {
      totalClients,
      weeklyNotes,
      insights,
      improving,
      improvingPercentage
    };
  }
  
  // User update operations
  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const user = await this.getUser(id);
    if (!user) return undefined;
    
    const updatedUser = { ...user, ...userData };
    this.users.set(id, updatedUser);
    return updatedUser;
  }
  
  // Get all users
  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  async getAllClients(): Promise<Client[]> {
    return Array.from(this.clients.values());
  }

  async getClientsByDoctor(doctorId: number): Promise<Client[]> {
    const assignments = Array.from(this.doctorClients.values())
      .filter(dc => dc.doctorId === doctorId);

    const clientIds = assignments.map(a => a.clientId);
    return Array.from(this.clients.values())
      .filter(client => clientIds.includes(client.id));
  }

  async getAllConversations(): Promise<AiTherapyConversation[]> {
    return Array.from(this.aiConversations.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  // Doctor-Client operations
  async createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient> {
    const id = this.doctorClientId++;
    const doctorClient: DoctorClient = {
      ...assignment,
      id,
      createdAt: new Date()
    };
    this.doctorClients.set(id, doctorClient);
    return doctorClient;
  }
  
  async getAllDoctorClientRelationships(): Promise<DoctorClient[]> {
    return Array.from(this.doctorClients.values());
  }
  
  // Invitation code operations
  async createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode> {
    const id = this.invitationCodeId++;
    const invitationCode: InvitationCode = {
      ...code,
      id,
      used: false,
      usedBy: null,
      usedAt: null,
      createdAt: new Date()
    };
    this.invitationCodes.set(id, invitationCode);
    return invitationCode;
  }
  
  async getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined> {
    return Array.from(this.invitationCodes.values()).find(
      invitation => invitation.code === code
    );
  }
  
  async getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]> {
    let invitations = Array.from(this.invitationCodes.values())
      .filter(invitation => invitation.createdBy === userId);
    
    if (type) {
      invitations = invitations.filter(invitation => invitation.type === type);
    }
    
    return invitations;
  }
  
  async markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined> {
    const invitation = await this.getInvitationCodeByCode(code);
    if (!invitation || invitation.used) return undefined;

    const updatedInvitation: InvitationCode = {
      ...invitation,
      used: true,
      usedBy: userId,
      usedAt: new Date()
    };

    this.invitationCodes.set(invitation.id, updatedInvitation);
    return updatedInvitation;
  }

  async getAllInvitationCodes(): Promise<InvitationCode[]> {
    return Array.from(this.invitationCodes.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
}

// Use the memory storage implementation
// Use the DatabaseStorage instead of MemStorage for production
export const storage = new DatabaseStorage();