import { useQuery } from "@tanstack/react-query";
import { ClientTheme, DashboardStats } from "@shared/schema";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { getThemeColor } from "@/lib/utils";
import { 
  BarChart, Bar, 
  LineChart, Line, 
  XAxis, YAxis, 
  Tooltip, Legend, 
  ResponsiveContainer, 
  PieChart, Pie, Cell
} from "recharts";

export default function Analytics() {
  // Fetch dashboard stats
  const { data: stats, isLoading: isLoadingStats } = useQuery<DashboardStats>({
    queryKey: ["/api/dashboard/stats"],
  });

  // Fetch real analytics data
  const { data: analytics, isLoading: isLoadingAnalytics } = useQuery({
    queryKey: ["/api/analytics"],
  });

  // Fetch weekly activity data
  const { data: weeklyActivity = [], isLoading: isLoadingWeekly } = useQuery({
    queryKey: ["/api/analytics/weekly"],
  });

  // Fetch monthly trends data
  const { data: monthlyTrends = [], isLoading: isLoadingMonthly } = useQuery({
    queryKey: ["/api/analytics/monthly"],
  });

  // Fetch clients to get all themes
  const { data: clients, isLoading: isLoadingClients } = useQuery({
    queryKey: ["/api/clients"],
  });

  // Create aggregated data for themes across all clients
  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];
  const themeCounts: Record<string, number> = {};

  allThemes.forEach(theme => {
    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;
  });

  const themeData = Object.keys(themeCounts).map((name, index) => ({
    name,
    value: themeCounts[name],
    color: getThemeColor(index).split(" ")[0]
  }));

  // Client status distribution based on real data
  const statusData = [
    { name: "Improving", value: stats?.improving || 0, color: "bg-success-light" },
    { name: "Stable", value: (stats?.totalClients || 0) - (stats?.improving || 0) - Math.max(0, Math.floor((stats?.totalClients || 0) * 0.1)), color: "bg-warning-light" },
    { name: "Needs Attention", value: Math.max(0, Math.floor((stats?.totalClients || 0) * 0.1)), color: "bg-error-light" }
  ];

  const isLoading = isLoadingStats || isLoadingAnalytics || isLoadingWeekly || isLoadingMonthly;

  return (
    <div>
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-neutral-dark sm:text-3xl">
            Analytics
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Visualize insights and trends across your practice
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Weekly Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Activity</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={weeklyActivity}>
                  <XAxis dataKey="week" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="sessions" fill="#4A6FA5" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        {/* Client Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Client Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingStats ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 mb-6">
        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Trends</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={monthlyTrends}>
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="sessions" stroke="#4A6FA5" name="Sessions" />
                </LineChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Common Themes */}
        <Card>
          <CardHeader>
            <CardTitle>Common Themes</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingClients ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <div className="space-y-4">
                {themeData.slice(0, 5).map((theme, index) => (
                  <div key={theme.name} className="flex items-center justify-between">
                    <span className="text-sm text-neutral-dark">{theme.name}</span>
                    <div className="w-2/3">
                      <div className="bg-neutral-light rounded-full h-2">
                        <div 
                          className="bg-primary rounded-full h-2" 
                          style={{ width: `${(theme.value / Math.max(...themeData.map(t => t.value))) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Theme Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Theme Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingClients ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={themeData}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name }) => name}
                  >
                    {themeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
