import { Skeleton } from "@/components/ui/skeleton";
import { Client, ClientTheme } from "@shared/schema";
import { getInitials, getStatusColor, formatDate, getThemeColor } from "@/lib/utils";

interface ClientsListProps {
  clients?: (Client & { 
    themes?: ClientTheme[];
    lastSession?: Date | null;
  })[];
  isLoading: boolean;
  onClientClick?: (clientId: number) => void;
}

export default function ClientsList({ clients, isLoading, onClientClick }: ClientsListProps) {
  if (isLoading) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {[1, 2, 3].map((i) => (
            <li key={i} className="px-4 py-4 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="ml-4">
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-3 w-40" />
                  </div>
                </div>
                <Skeleton className="h-6 w-24" />
              </div>
              <div className="mt-2">
                <Skeleton className="h-3 w-20 mb-2" />
                <div className="flex flex-wrap gap-1">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                  <Skeleton className="h-5 w-14" />
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    );
  }

  if (!clients || clients.length === 0) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
        <p className="text-gray-900">No clients found</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <ul className="divide-y divide-gray-200">
        {clients.map((client) => {
          const status = getStatusColor(client.status);
          
          return (
            <li key={client.id}>
              <a 
                href="#" 
                className="block hover:bg-neutral-light"
                onClick={(e) => {
                  e.preventDefault();
                  if (onClientClick) onClientClick(client.id);
                }}
              >
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                        {getInitials(client.name)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{client.name}</div>
                        <div className="text-sm text-gray-500">
                          Last Session: {client.lastSession ? formatDate(client.lastSession) : "No sessions"}
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bg} ${status.text}`}>
                        {status.label}
                      </div>
                    </div>
                  </div>
                  <div className="mt-2">
                    <div className="text-sm text-gray-500 mb-1">Top themes:</div>
                    <div className="flex flex-wrap gap-1">
                      {client.themes && client.themes.length > 0 ? (
                        client.themes.map((theme, index) => (
                          <span 
                            key={index}
                            className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getThemeColor(index)}`}
                          >
                            {theme.name}
                          </span>
                        ))
                      ) : (
                        <span className="text-xs text-gray-500">No themes available</span>
                      )}
                    </div>
                  </div>
                </div>
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
