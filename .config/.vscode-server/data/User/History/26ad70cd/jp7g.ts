import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage, pool } from "./storage";
import { User as SelectUser } from "@shared/schema";
import connectPgSimple from "connect-pg-simple";

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

const scryptAsync = promisify(scrypt);

export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

export function setupAuth(app: Express) {
  // Create PostgreSQL session store for production
  const PgSession = connectPgSimple(session);

  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "therapy-notes-ai-secret",
    resave: false,
    saveUninitialized: false, // Better for production
    store: new PgSession({
      pool: pool as any,
      tableName: 'session',
      createTableIfMissing: true
    }),
    cookie: {
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      secure: process.env.NODE_ENV === 'production' && (process.env.HTTPS === 'true' || process.env.NODE_ENV === 'production'),
      httpOnly: true,
      sameSite: 'strict' // CSRF protection
    },
    name: 'therapy-ai-sid'
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  passport.use(
    new LocalStrategy(async (username, password, done) => {
      try {
        console.log(`[AUTH] Login attempt: ${username}`);
        const user = await storage.getUserByUsername(username);
        
        if (!user) {
          console.log(`[AUTH] User not found: ${username}`);
          return done(null, false, { message: 'User not found' });
        }
        
        console.log(`[AUTH] User found: ${user.username} (ID: ${user.id})`);
        const passwordMatch = await comparePasswords(password, user.password);
        
        if (!passwordMatch) {
          console.log(`[AUTH] Password mismatch for user: ${username}`);
          return done(null, false, { message: 'Invalid password' });
        }
        
        console.log(`[AUTH] Login successful: ${username} (ID: ${user.id})`);
        return done(null, user);
      } catch (err) {
        console.error(`[AUTH] Error during login:`, err);
        return done(err);
      }
    }),
  );

  passport.serializeUser((user, done) => done(null, user.id));
  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (err) {
      done(err);
    }
  });

  app.post("/api/register", async (req, res, next) => {
    try {
      const { invitationCode, ...userData } = req.body;
      
      // Prevent admin self-registration
      if (userData.userRole === 'admin') {
        return res.status(403).json({ message: "Admin accounts can only be created by existing administrators" });
      }
      
      // Check if username already exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }
      
      // Validate invitation code
      if (!invitationCode) {
        return res.status(400).json({ message: "Invitation code is required" });
      }
      
      const code = await storage.getInvitationCodeByCode(invitationCode);
      if (!code) {
        return res.status(400).json({ message: "Invalid invitation code" });
      }
      
      if (code.used) {
        return res.status(400).json({ message: "Invitation code has already been used" });
      }
      
      // Check if user role matches invitation code type
      if (code.type !== userData.userRole) {
        return res.status(400).json({ 
          message: `This invitation code is for ${code.type} accounts only` 
        });
      }
      
      // Create the user
      const user = await storage.createUser({
        ...userData,
        password: await hashPassword(userData.password),
      });
      
      // Mark invitation code as used
      await storage.markInvitationCodeAsUsed(invitationCode, user.id);
      
      // If user type is client, create a client record
      if (userData.userRole === "client") {
        await storage.createClient({
          userId: user.id,
          name: userData.name,
          status: "stable"
        });
      }

      req.login(user, (err) => {
        if (err) return next(err);
        res.status(201).json(user);
      });
    } catch (err) {
      next(err);
    }
  });

  app.post("/api/login", (req, res, next) => {
    console.log(`[AUTH] Login attempt at ${new Date().toISOString()}`);
    console.log(`[AUTH] Login request body:`, req.body);
    
    passport.authenticate("local", (err: Error | null, user: any, info: any) => {
      if (err) {
        console.error(`[AUTH] Login error:`, err);
        return next(err);
      }
      
      if (!user) {
        console.log(`[AUTH] Authentication failed:`, info?.message || 'Invalid credentials');
        return res.status(401).json({ 
          message: info?.message || "Invalid credentials",
          time: new Date().toISOString()
        });
      }
      
      console.log(`[AUTH] User authenticated successfully:`, {
        id: user.id,
        username: user.username,
        role: user.userRole
      });
      
      req.login(user, (err: Error | null) => {
        if (err) {
          console.error(`[AUTH] Session creation error:`, err);
          return next(err);
        }
        
        console.log(`[AUTH] Login successful, session created`);
        console.log(`[AUTH] Session ID:`, req.sessionID);
        
        // Return user info without password
        const { password, ...safeUser } = user;
        res.status(200).json(safeUser);
      });
    })(req, res, next);
  });

  app.post("/api/logout", (req, res, next) => {
    req.logout((err: Error | null) => {
      if (err) return next(err);
      res.sendStatus(200);
    });
  });

  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    res.json(req.user);
  });
}