// This file is not used in the current implementation
import { saveVoiceConfig, saveConversationConfig, loadVoiceConfig, loadConversationConfig } from './db';
import { VoiceConfig, ConversationConfig } from '@shared/schema';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST' && req.url === '/api/save-config') {
    try {
      const { voice, conversation } = req.body;
      const userId = req.headers['x-user-id'] as string;
      
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      await saveVoiceConfig(userId, voice);
      await saveConversationConfig(userId, conversation);

      res.status(200).json({ success: true });
    } catch (error) {
      console.error('Error saving configuration:', error);
      res.status(500).json({ error: 'Failed to save configuration' });
    }
  } else if (req.method === 'GET' && req.url === '/api/configs') {
    try {
      const userId = req.headers['x-user-id'] as string;
      
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      const voiceConfig = await loadVoiceConfig(userId);
      const conversationConfig = await loadConversationConfig(userId);

      res.status(200).json({
        voice: voiceConfig,
        conversation: conversationConfig
      });
    } catch (error) {
      console.error('Error loading configurations:', error);
      res.status(500).json({ error: 'Failed to load configurations' });
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
} 