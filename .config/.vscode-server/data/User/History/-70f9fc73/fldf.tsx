import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { DashboardStats } from "@shared/schema";
import { Link } from "wouter";

interface StatsCardsProps {
  stats?: DashboardStats;
  isLoading: boolean;
}

export default function StatsCards({ stats, isLoading }: StatsCardsProps) {
  const statsCards = [
    {
      title: "Total Clients",
      value: stats?.totalClients,
      icon: "people",
      iconBg: "bg-blue-500",
      link: "/clients",
      linkText: "View all clients"
    },
    {
      title: "Notes This Week",
      value: stats?.weeklyNotes,
      icon: "description",
      iconBg: "bg-purple-500",
      link: "/notes",
      linkText: "View all notes"
    },
    {
      title: "Insights Generated",
      value: stats?.insights,
      icon: "lightbulb",
      iconBg: "bg-yellow-500",
      link: "/analytics",
      linkText: "View insights"
    },
    {
      title: "Clients Improving",
      value: stats?.improving,
      icon: "trending_up",
      iconBg: "bg-green-500",
      secondaryValue: stats ? `${stats.improvingPercentage}%` : null,
      link: "/analytics",
      linkText: "View trends"
    }
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="overflow-hidden">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <Skeleton className="h-10 w-10 rounded-md" />
                <div className="ml-5 w-0 flex-1">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-7 w-10" />
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-4 py-2">
              <Skeleton className="h-4 w-24" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      {statsCards.map((card, index) => (
        <Card key={index} className="overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className={`flex-shrink-0 ${card.iconBg} rounded-md p-3`}>
                <span className="material-icons text-white">{card.icon}</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">{card.title}</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {card.value !== undefined ? card.value : "-"}
                    </div>
                    {card.secondaryValue && (
                      <p className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                        <span>{card.secondaryValue}</span>
                      </p>
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-neutral-light px-4 py-2">
            <div className="text-sm">
              <Link to={card.link} className="font-medium text-primary hover:text-primary-dark">
                {card.linkText}
              </Link>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
