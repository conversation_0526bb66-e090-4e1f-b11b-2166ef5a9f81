import { Skeleton } from "@/components/ui/skeleton";
import { SessionNote, Theme } from "@shared/schema";
import { formatDate, getThemeColor, formatTrendIndicator } from "@/lib/utils";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "wouter";

interface SessionNotesListProps {
  notes?: (SessionNote & { clientName?: string })[];
  isLoading: boolean;
  clientId?: number;
  showFullDetails?: boolean;
}

export default function SessionNotesList({ 
  notes, 
  isLoading, 
  clientId,
  showFullDetails = false 
}: SessionNotesListProps) {
  if (isLoading) {
    return (
      <Card className="shadow overflow-hidden rounded-lg divide-y divide-gray-200">
        {[1, 2].map((i) => (
          <div key={i} className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <Skeleton className="h-4 w-40 mb-2" />
                <Skeleton className="h-3 w-32" />
              </div>
              <Skeleton className="h-8 w-24" />
            </div>
            
            <div className="mb-4">
              <Skeleton className="h-3 w-24 mb-2" />
              <Skeleton className="h-20 w-full" />
            </div>
            
            <div className="mb-4">
              <Skeleton className="h-3 w-24 mb-2" />
              <div className="flex flex-wrap gap-1">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
            
            <div>
              <Skeleton className="h-3 w-40 mb-2" />
              <ul className="space-y-1">
                <li className="flex items-start">
                  <Skeleton className="h-5 w-5 mr-2" />
                  <Skeleton className="h-4 w-full" />
                </li>
                <li className="flex items-start">
                  <Skeleton className="h-5 w-5 mr-2" />
                  <Skeleton className="h-4 w-full" />
                </li>
              </ul>
            </div>
          </div>
        ))}
      </Card>
    );
  }

  if (!notes || notes.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-neutral-dark mb-4">No session notes found</p>
        <Link to="/notes">
          <Button>Create New Note</Button>
        </Link>
      </Card>
    );
  }

  return (
    <Card className="shadow overflow-hidden rounded-lg divide-y divide-gray-200">
      {notes.map((note) => {
        // Filter notes by client if clientId is provided
        if (clientId && note.clientId !== clientId) {
          return null;
        }
        
        return (
          <div key={note.id} className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-sm font-medium text-neutral-dark">
                  Client: <span>{note.clientName}</span>
                </h4>
                <p className="text-xs text-gray-500">
                  {formatDate(note.sessionDate || note.createdAt)} • {note.duration} min session
                </p>
              </div>
              <div>
                <button className="text-sm font-medium text-primary hover:text-primary-dark">
                  View full note
                </button>
              </div>
            </div>
            
            {note.aiProcessed || note.analyzed ? (
              <>
                <div className="mb-4">
                  <h5 className="text-xs uppercase font-medium text-gray-500 mb-3">AI Summary</h5>
                  <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                    <div className="text-sm text-gray-800 leading-relaxed">
                      {note.summary ? (
                        <div className="space-y-2">
                          {note.summary.split('\n').filter(line => line.trim()).map((paragraph, index) => (
                            <p key={index} className="mb-2 last:mb-0">
                              {paragraph.trim()}
                            </p>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">No summary available</p>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Themes are now stored separately in clientThemes table */}
                
                {note.recommendations && note.recommendations.length > 0 && (
                  <div>
                    <h5 className="text-xs uppercase font-medium text-gray-500 mb-2">
                      AI Recommendations
                    </h5>
                    <ul className="text-sm text-neutral-dark space-y-1">
                      {note.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start">
                          <span className="material-icons text-accent mr-2 text-sm">lightbulb</span>
                          <span>{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </>
            ) : (
              <div className="py-4 text-center">
                <p className="text-sm text-neutral-dark mb-2">
                  Note is currently being processed by AI
                </p>
                <div className="flex justify-center items-center text-primary">
                  <span className="material-icons animate-spin mr-2">progress_activity</span>
                  <span className="text-sm">Processing...</span>
                </div>
              </div>
            )}
            
            {showFullDetails && (
              <Accordion type="single" collapsible className="mt-4 w-full">
                <AccordionItem value="raw-notes">
                  <AccordionTrigger className="text-xs uppercase font-medium text-gray-500">
                    Original Notes
                  </AccordionTrigger>
                  <AccordionContent>
                    <p className="text-sm text-neutral-dark whitespace-pre-line">
                      {note.content}
                    </p>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </div>
        );
      })}
    </Card>
  );
}
