{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/api.ts"}, "originalCode": "import { NextApiRequest, NextApiResponse } from 'next';\nimport { saveVoiceConfig, saveConversationConfig, loadVoiceConfig, loadConversationConfig } from './db';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  if (req.method === 'POST' && req.url === '/api/save-config') {\n    try {\n      const { voice, conversation } = req.body;\n      const userId = req.headers['x-user-id'] as string;\n      \n      if (!userId) {\n        return res.status(401).json({ error: 'Unauthorized' });\n      }\n\n      await saveVoiceConfig(userId, voice);\n      await saveConversationConfig(userId, conversation);\n\n      res.status(200).json({ success: true });\n    } catch (error) {\n      console.error('Error saving configuration:', error);\n      res.status(500).json({ error: 'Failed to save configuration' });\n    }\n  } else if (req.method === 'GET' && req.url === '/api/configs') {\n    try {\n      const userId = req.headers['x-user-id'] as string;\n      \n      if (!userId) {\n        return res.status(401).json({ error: 'Unauthorized' });\n      }\n\n      const voiceConfig = await loadVoiceConfig(userId);\n      const conversationConfig = await loadConversationConfig(userId);\n\n      res.status(200).json({\n        voice: voiceConfig,\n        conversation: conversationConfig\n      });\n    } catch (error) {\n      console.error('Error loading configurations:', error);\n      res.status(500).json({ error: 'Failed to load configurations' });\n    }\n  } else {\n    res.status(405).json({ error: 'Method not allowed' });\n  }\n} ", "modifiedCode": "// This file is not used in the current implementation\nimport { saveVoiceConfig, saveConversationConfig, loadVoiceConfig, loadConversationConfig } from './db';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  if (req.method === 'POST' && req.url === '/api/save-config') {\n    try {\n      const { voice, conversation } = req.body;\n      const userId = req.headers['x-user-id'] as string;\n      \n      if (!userId) {\n        return res.status(401).json({ error: 'Unauthorized' });\n      }\n\n      await saveVoiceConfig(userId, voice);\n      await saveConversationConfig(userId, conversation);\n\n      res.status(200).json({ success: true });\n    } catch (error) {\n      console.error('Error saving configuration:', error);\n      res.status(500).json({ error: 'Failed to save configuration' });\n    }\n  } else if (req.method === 'GET' && req.url === '/api/configs') {\n    try {\n      const userId = req.headers['x-user-id'] as string;\n      \n      if (!userId) {\n        return res.status(401).json({ error: 'Unauthorized' });\n      }\n\n      const voiceConfig = await loadVoiceConfig(userId);\n      const conversationConfig = await loadConversationConfig(userId);\n\n      res.status(200).json({\n        voice: voiceConfig,\n        conversation: conversationConfig\n      });\n    } catch (error) {\n      console.error('Error loading configurations:', error);\n      res.status(500).json({ error: 'Failed to load configurations' });\n    }\n  } else {\n    res.status(405).json({ error: 'Method not allowed' });\n  }\n} "}