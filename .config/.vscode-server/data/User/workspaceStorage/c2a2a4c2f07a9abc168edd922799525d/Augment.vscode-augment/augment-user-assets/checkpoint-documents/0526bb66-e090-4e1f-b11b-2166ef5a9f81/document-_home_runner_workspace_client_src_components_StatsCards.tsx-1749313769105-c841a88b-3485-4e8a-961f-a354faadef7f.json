{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/StatsCards.tsx"}, "originalCode": "import { Card } from \"@/components/ui/card\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { DashboardStats } from \"@shared/schema\";\nimport { Link } from \"wouter\";\n\ninterface StatsCardsProps {\n  stats?: DashboardStats;\n  isLoading: boolean;\n}\n\nexport default function StatsCards({ stats, isLoading }: StatsCardsProps) {\n  const statsCards = [\n    {\n      title: \"Total Clients\",\n      value: stats?.totalClients,\n      icon: \"people\",\n      iconBg: \"bg-primary-light\",\n      link: \"/clients\",\n      linkText: \"View all clients\"\n    },\n    {\n      title: \"Notes This Week\",\n      value: stats?.weeklyNotes,\n      icon: \"description\",\n      iconBg: \"bg-secondary-light\",\n      link: \"/notes\",\n      linkText: \"View all notes\"\n    },\n    {\n      title: \"Insights Generated\",\n      value: stats?.insights,\n      icon: \"lightbulb\",\n      iconBg: \"bg-accent-light\",\n      link: \"/analytics\",\n      linkText: \"View insights\"\n    },\n    {\n      title: \"Clients Improving\",\n      value: stats?.improving,\n      icon: \"trending_up\",\n      iconBg: \"bg-success-light\",\n      secondaryValue: stats ? `${stats.improvingPercentage}%` : null,\n      link: \"/analytics\",\n      linkText: \"View trends\"\n    }\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n        {[1, 2, 3, 4].map((i) => (\n          <Card key={i} className=\"overflow-hidden\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <div className=\"flex items-center\">\n                <Skeleton className=\"h-10 w-10 rounded-md\" />\n                <div className=\"ml-5 w-0 flex-1\">\n                  <Skeleton className=\"h-4 w-20 mb-2\" />\n                  <Skeleton className=\"h-7 w-10\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-neutral-light px-4 py-2\">\n              <Skeleton className=\"h-4 w-24\" />\n            </div>\n          </Card>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n      {statsCards.map((card, index) => (\n        <Card key={index} className=\"overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center\">\n              <div className={`flex-shrink-0 ${card.iconBg} rounded-md p-3`}>\n                <span className=\"material-icons text-white\">{card.icon}</span>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">{card.title}</dt>\n                  <dd className=\"flex items-baseline\">\n                    <div className=\"text-2xl font-semibold text-neutral-dark\">\n                      {card.value !== undefined ? card.value : \"-\"}\n                    </div>\n                    {card.secondaryValue && (\n                      <p className=\"ml-2 flex items-baseline text-sm font-semibold text-success\">\n                        <span>{card.secondaryValue}</span>\n                      </p>\n                    )}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-neutral-light px-4 py-2\">\n            <div className=\"text-sm\">\n              <Link to={card.link} className=\"font-medium text-primary hover:text-primary-dark\">\n                {card.linkText}\n              </Link>\n            </div>\n          </div>\n        </Card>\n      ))}\n    </div>\n  );\n}\n", "modifiedCode": "import { Card } from \"@/components/ui/card\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { DashboardStats } from \"@shared/schema\";\nimport { Link } from \"wouter\";\n\ninterface StatsCardsProps {\n  stats?: DashboardStats;\n  isLoading: boolean;\n}\n\nexport default function StatsCards({ stats, isLoading }: StatsCardsProps) {\n  const statsCards = [\n    {\n      title: \"Total Clients\",\n      value: stats?.totalClients,\n      icon: \"people\",\n      iconBg: \"bg-blue-500\",\n      link: \"/clients\",\n      linkText: \"View all clients\"\n    },\n    {\n      title: \"Notes This Week\",\n      value: stats?.weeklyNotes,\n      icon: \"description\",\n      iconBg: \"bg-purple-500\",\n      link: \"/notes\",\n      linkText: \"View all notes\"\n    },\n    {\n      title: \"Insights Generated\",\n      value: stats?.insights,\n      icon: \"lightbulb\",\n      iconBg: \"bg-yellow-500\",\n      link: \"/analytics\",\n      linkText: \"View insights\"\n    },\n    {\n      title: \"Clients Improving\",\n      value: stats?.improving,\n      icon: \"trending_up\",\n      iconBg: \"bg-green-500\",\n      secondaryValue: stats ? `${stats.improvingPercentage}%` : null,\n      link: \"/analytics\",\n      linkText: \"View trends\"\n    }\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n        {[1, 2, 3, 4].map((i) => (\n          <Card key={i} className=\"overflow-hidden\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <div className=\"flex items-center\">\n                <Skeleton className=\"h-10 w-10 rounded-md\" />\n                <div className=\"ml-5 w-0 flex-1\">\n                  <Skeleton className=\"h-4 w-20 mb-2\" />\n                  <Skeleton className=\"h-7 w-10\" />\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-neutral-light px-4 py-2\">\n              <Skeleton className=\"h-4 w-24\" />\n            </div>\n          </Card>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n      {statsCards.map((card, index) => (\n        <Card key={index} className=\"overflow-hidden\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center\">\n              <div className={`flex-shrink-0 ${card.iconBg} rounded-md p-3`}>\n                <span className=\"material-icons text-white\">{card.icon}</span>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">{card.title}</dt>\n                  <dd className=\"flex items-baseline\">\n                    <div className=\"text-2xl font-semibold text-neutral-dark\">\n                      {card.value !== undefined ? card.value : \"-\"}\n                    </div>\n                    {card.secondaryValue && (\n                      <p className=\"ml-2 flex items-baseline text-sm font-semibold text-success\">\n                        <span>{card.secondaryValue}</span>\n                      </p>\n                    )}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-neutral-light px-4 py-2\">\n            <div className=\"text-sm\">\n              <Link to={card.link} className=\"font-medium text-primary hover:text-primary-dark\">\n                {card.linkText}\n              </Link>\n            </div>\n          </div>\n        </Card>\n      ))}\n    </div>\n  );\n}\n"}