{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/VoiceTherapy.tsx"}, "originalCode": "import { useState } from 'react';\nimport { useAuth } from '@/hooks/use-auth';\nimport Layout from '@/components/Layout';\nimport VoiceChat from '@/components/VoiceChat';\nimport { useToast } from '@/hooks/use-toast';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Loader2 } from 'lucide-react';\nimport { useQuery } from '@tanstack/react-query';\nimport { apiRequest } from '@/lib/queryClient';\nimport { Client } from '@shared/schema';\n\nexport default function VoiceTherapy() {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  const [activeSession, setActiveSession] = useState(false);\n  const [sessionSummary, setSessionSummary] = useState<string | null>(null);\n  \n  // Get the client profile if user is a client\n  const { data: clientProfile, isLoading: isLoadingClient } = useQuery<Client>({\n    queryKey: ['/api/clients/me'],\n    queryFn: async () => {\n      try {\n        const res = await apiRequest('GET', '/api/clients/me');\n        return await res.json();\n      } catch (error) {\n        console.error('Error fetching client profile:', error);\n        return null;\n      }\n    },\n    enabled: !!user && user.userRole === 'client',\n  });\n  \n  const handleConversationCreated = (conversationId: number) => {\n    console.log('New conversation created:', conversationId);\n    setActiveSession(true);\n    \n    toast({\n      title: 'Session Started',\n      description: 'Your voice therapy session has begun',\n      variant: 'default',\n    });\n  };\n  \n  const handleConversationEnded = (summary: string, conversationId: number) => {\n    console.log('Conversation ended:', conversationId);\n    setActiveSession(false);\n    setSessionSummary(summary);\n    \n    toast({\n      title: 'Session Ended',\n      description: 'Your voice therapy session has been summarized',\n      variant: 'default',\n    });\n  };\n  \n  const startNewSession = () => {\n    setSessionSummary(null);\n    setActiveSession(true);\n  };\n  \n  if (isLoadingClient) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center h-[600px]\">\n          <Loader2 className=\"w-8 h-8 animate-spin text-primary\" />\n        </div>\n      </Layout>\n    );\n  }\n  \n  // Ensure user is logged in and is a client or admin\n  if (!user || (user.userRole !== 'client' && user.userRole !== 'admin') || (!clientProfile && user.userRole === 'client')) {\n    return (\n      <Layout>\n        <div className=\"max-w-3xl mx-auto py-10 px-4\">\n          <h1 className=\"text-3xl font-bold text-center mb-8\">Voice Therapy</h1>\n          <Card>\n            <CardContent className=\"py-10 text-center\">\n              <p className=\"mb-4\">\n                You need to be logged in as a client to access voice therapy sessions.\n                {user?.userRole === 'admin' ? \" As an admin, you can proceed with testing this feature.\" : \"\"}\n              </p>\n              {!user ? (\n                <Button variant=\"default\" onClick={() => window.location.href = '/auth'}>\n                  Go to Login\n                </Button>\n              ) : null}\n            </CardContent>\n          </Card>\n        </div>\n      </Layout>\n    );\n  }\n  \n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-10 px-4\">\n        <h1 className=\"text-3xl font-bold text-center mb-2\">\n          <span className=\"bg-gradient-to-r from-blue-600 via-cyan-500 to-purple-500 text-transparent bg-clip-text\">\n            Voice Therapy Session\n          </span>\n        </h1>\n        <p className=\"text-center text-muted-foreground mb-8\">\n          Ancient Wisdom Meets Modern Therapy • AI-Powered Voice Interactions\n        </p>\n        \n        {sessionSummary ? (\n          <div className=\"mb-8\">\n            <Card>\n              <CardContent className=\"py-6\">\n                <h2 className=\"text-xl font-semibold mb-4\">Session Summary</h2>\n                <div className=\"prose max-w-none\">\n                  <p>{sessionSummary}</p>\n                </div>\n                <div className=\"mt-6 text-center\">\n                  <Button onClick={startNewSession}>Start New Session</Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        ) : activeSession ? (\n          <VoiceChat \n            userId={user.id}\n            clientId={user.userRole === 'admin' ? 1 : clientProfile?.id || 1} // Use ID 1 for admin testing\n            onConversationCreated={handleConversationCreated}\n            onConversationEnded={handleConversationEnded}\n          />\n        ) : (\n          <div className=\"text-center py-10\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Ready for a therapy session?</h2>\n            <p className=\"text-muted-foreground mb-6\">\n              Experience the fusion of ancient wisdom and modern AI technology with our \n              voice assistant. \n            </p>\n            <Button \n              size=\"lg\" \n              onClick={startNewSession} \n              className=\"bg-gradient-to-r from-blue-600 via-cyan-500 to-purple-500 hover:from-blue-700 hover:via-cyan-600 hover:to-purple-600\"\n            >\n              Begin Voice Therapy Session\n            </Button>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n}", "modifiedCode": "import { useState } from 'react';\nimport { useAuth } from '@/hooks/use-auth';\nimport Layout from '@/components/Layout';\nimport VoiceChat from '@/components/VoiceChat';\nimport { useToast } from '@/hooks/use-toast';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Loader2 } from 'lucide-react';\nimport { useQuery } from '@tanstack/react-query';\nimport { apiRequest } from '@/lib/queryClient';\nimport { Client } from '@shared/schema';\n\nexport default function VoiceTherapy() {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  const [activeSession, setActiveSession] = useState(false);\n  const [sessionSummary, setSessionSummary] = useState<string | null>(null);\n  \n  // Get the client profile if user is a client\n  const { data: clientProfile, isLoading: isLoadingClient } = useQuery<Client>({\n    queryKey: ['/api/clients/me'],\n    queryFn: async () => {\n      try {\n        const res = await apiRequest('GET', '/api/clients/me');\n        return await res.json();\n      } catch (error) {\n        console.error('Error fetching client profile:', error);\n        return null;\n      }\n    },\n    enabled: !!user && user.userRole === 'client',\n  });\n  \n  const handleConversationCreated = (conversationId: number) => {\n    console.log('New conversation created:', conversationId);\n    setActiveSession(true);\n    \n    toast({\n      title: 'Session Started',\n      description: 'Your voice therapy session has begun',\n      variant: 'default',\n    });\n  };\n  \n  const handleConversationEnded = (summary: string, conversationId: number) => {\n    console.log('Conversation ended:', conversationId);\n    setActiveSession(false);\n    setSessionSummary(summary);\n    \n    toast({\n      title: 'Session Ended',\n      description: 'Your voice therapy session has been summarized',\n      variant: 'default',\n    });\n  };\n  \n  const startNewSession = () => {\n    setSessionSummary(null);\n    setActiveSession(true);\n  };\n  \n  if (isLoadingClient) {\n    return (\n      <Layout>\n        <div className=\"flex items-center justify-center h-[600px]\">\n          <Loader2 className=\"w-8 h-8 animate-spin text-primary\" />\n        </div>\n      </Layout>\n    );\n  }\n  \n  // Ensure user is logged in and is a client or admin\n  if (!user || (user.userRole !== 'client' && user.userRole !== 'admin') || (!clientProfile && user.userRole === 'client')) {\n    return (\n      <Layout>\n        <div className=\"max-w-3xl mx-auto py-10 px-4\">\n          <h1 className=\"text-3xl font-bold text-center mb-8\">Voice Therapy</h1>\n          <Card>\n            <CardContent className=\"py-10 text-center\">\n              <p className=\"mb-4\">\n                You need to be logged in as a client to access voice therapy sessions.\n                {user?.userRole === 'admin' ? \" As an admin, you can proceed with testing this feature.\" : \"\"}\n              </p>\n              {!user ? (\n                <Button variant=\"default\" onClick={() => window.location.href = '/auth'}>\n                  Go to Login\n                </Button>\n              ) : null}\n            </CardContent>\n          </Card>\n        </div>\n      </Layout>\n    );\n  }\n  \n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto py-10 px-4\">\n        <h1 className=\"text-3xl font-bold text-center mb-2\">\n          <span className=\"bg-gradient-to-r from-blue-600 via-cyan-500 to-purple-500 text-transparent bg-clip-text\">\n            Voice Therapy Session\n          </span>\n        </h1>\n        <p className=\"text-center text-muted-foreground mb-8\">\n          Ancient Wisdom Meets Modern Therapy • AI-Powered Voice Interactions\n        </p>\n        \n        {sessionSummary ? (\n          <div className=\"mb-8\">\n            <Card>\n              <CardContent className=\"py-6\">\n                <h2 className=\"text-xl font-semibold mb-4\">Session Summary</h2>\n                <div className=\"prose max-w-none\">\n                  <p>{sessionSummary}</p>\n                </div>\n                <div className=\"mt-6 text-center\">\n                  <Button onClick={startNewSession}>Start New Session</Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        ) : activeSession ? (\n          <VoiceChat \n            userId={user.id}\n            clientId={user.userRole === 'admin' ? 1 : clientProfile?.id || 1} // Use ID 1 for admin testing\n            onConversationCreated={handleConversationCreated}\n            onConversationEnded={handleConversationEnded}\n          />\n        ) : (\n          <div className=\"text-center py-10\">\n            <h2 className=\"text-2xl font-semibold mb-4\">Ready for a therapy session?</h2>\n            <p className=\"text-muted-foreground mb-6\">\n              Experience the fusion of ancient wisdom and modern AI technology with our \n              voice assistant. \n            </p>\n            <Button \n              size=\"lg\" \n              onClick={startNewSession} \n              className=\"bg-gradient-to-r from-blue-600 via-cyan-500 to-purple-500 hover:from-blue-700 hover:via-cyan-600 hover:to-purple-600\"\n            >\n              Begin Voice Therapy Session\n            </Button>\n          </div>\n        )}\n      </div>\n    </Layout>\n  );\n}"}