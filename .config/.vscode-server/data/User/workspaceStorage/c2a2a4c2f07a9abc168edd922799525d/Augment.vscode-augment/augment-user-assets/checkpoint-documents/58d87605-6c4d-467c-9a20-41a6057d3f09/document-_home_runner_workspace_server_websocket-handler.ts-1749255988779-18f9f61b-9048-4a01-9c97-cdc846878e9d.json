{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/websocket-handler.ts"}, "originalCode": "import { WebSocket } from 'ws';\nimport { OpenAIRealtimeProxy } from './openai-realtime-proxy';\n\n// Simple interface for WebSocket messages\ninterface WebSocketMessage {\n  type: string;\n  [key: string]: any;\n}\n\n// Store for Realtime API proxies and session data\nconst realtimeProxies = new Map<WebSocket, OpenAIRealtimeProxy>();\nconst sessionTranscripts = new Map<WebSocket, {\n  userId: string;\n  clientId: string;\n  messages: Array<{\n    speaker: 'user' | 'ai';\n    text: string;\n    timestamp: Date;\n  }>;\n  startTime: Date;\n}>();\n\nexport const handleWebSocketConnection = (ws: WebSocket) => {\n  console.log('New WebSocket connection');\n  \n  ws.onmessage = async (event) => {\n    try {\n      const message = JSON.parse(event.data.toString()) as WebSocketMessage;\n      const messageType = message.type as string;\n      \n      // Get existing proxy if available\n      const realtimeProxy = realtimeProxies.get(ws);\n      \n      switch (messageType) {\n        case 'start':\n          await handleSessionStart(ws, message);\n          break;\n          \n        case 'conversationEnded':\n          await handleSessionEnd(ws);\n          break;\n          \n        case 'summary_request':\n          await handleSummaryRequest(ws);\n          break;\n          \n        case 'test_audio':\n          await handleTestAudio(ws, message);\n          break;\n          \n        case 'ping':\n          ws.send(JSON.stringify({\n            type: 'pong',\n            timestamp: new Date().toISOString()\n          }));\n          break;\n          \n        default:\n          // Route all other messages to Realtime proxy if exists\n          if (realtimeProxy) {\n            realtimeProxy.handleClientMessage(message);\n          } else {\n            console.log(`Received message but no active session: ${messageType}`);\n          }\n          break;\n      }\n    } catch (error) {\n      console.error('Error handling WebSocket message:', error);\n      ws.send(JSON.stringify({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      }));\n    }\n  };\n  \n  ws.onclose = () => {\n    console.log('WebSocket connection closed');\n    cleanupSession(ws);\n  };\n  \n  ws.onerror = (error) => {\n    console.error('WebSocket error:', error);\n    cleanupSession(ws);\n  };\n};\n\nasync function handleSessionStart(ws: WebSocket, message: any) {\n  console.log('Starting OpenAI Realtime API session...');\n  \n  try {\n    const openaiApiKey = process.env.OPENAI_API_KEY;\n    if (!openaiApiKey) {\n      throw new Error('OPENAI_API_KEY environment variable is required');\n    }\n\n    // Extract user info\n    const userId = message.userId?.toString() || '1';\n    const clientId = message.clientId?.toString() || '1';\n\n    // Initialize session transcript tracking\n    sessionTranscripts.set(ws, {\n      userId,\n      clientId,\n      messages: [],\n      startTime: new Date()\n    });\n\n    // Create Realtime API configuration\n    const realtimeConfig = {\n      apiKey: openaiApiKey,\n      model: message.behavior?.model || 'gpt-4o-realtime-preview',\n      voice: message.behavior?.voice?.voice || 'shimmer',\n      instructions: message.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n      temperature: message.behavior?.temperature || 0.7\n    };\n\n    // Create and connect proxy with transcript tracking\n    const realtimeProxy = new OpenAIRealtimeProxy(realtimeConfig);\n    realtimeProxies.set(ws, realtimeProxy);\n    \n    // Hook into proxy to capture transcript\n    realtimeProxy.onTranscriptUpdate = (speaker: 'user' | 'ai', text: string) => {\n      console.log(`📝 Capturing transcript: ${speaker} said \"${text}\"`);\n      const session = sessionTranscripts.get(ws);\n      if (session) {\n        session.messages.push({\n          speaker,\n          text,\n          timestamp: new Date()\n        });\n        console.log(`📝 Session now has ${session.messages.length} messages`);\n      }\n    };\n    \n    await realtimeProxy.connect(ws);\n    console.log('OpenAI Realtime API session initialized');\n    \n  } catch (error) {\n    console.error('Failed to initialize OpenAI Realtime API:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Failed to initialize real-time session: ' + (error instanceof Error ? error.message : 'Unknown error')\n    }));\n  }\n}\n\nasync function handleSessionEnd(ws: WebSocket) {\n  console.log('Ending session...');\n  \n  const realtimeProxy = realtimeProxies.get(ws);\n  if (realtimeProxy) {\n    realtimeProxy.disconnect();\n    realtimeProxies.delete(ws);\n  }\n  \n  // Send acknowledgment\n  ws.send(JSON.stringify({\n    type: 'end_ack'\n  }));\n}\n\nasync function handleSummaryRequest(ws: WebSocket) {\n  console.log('Generating session summary...');\n  \n  const session = sessionTranscripts.get(ws);\n  console.log(`📋 Session transcript has ${session?.messages.length || 0} messages`);\n  \n  if (!session || session.messages.length === 0) {\n    console.log('📋 No transcript to summarize - sending empty response');\n    ws.send(JSON.stringify({\n      type: 'summary',\n      content: 'No conversation to summarize.'\n    }));\n    return;\n  }\n\n  try {\n    // Use traditional OpenAI API to generate summary\n    const { summarizeTherapySession } = await import('./summary-service');\n    const summary = await summarizeTherapySession(session.messages, {\n      userId: session.userId,\n      clientId: session.clientId,\n      sessionDuration: Date.now() - session.startTime.getTime()\n    });\n    \n    ws.send(JSON.stringify({\n      type: 'summary',\n      content: summary\n    }));\n    \n    // Clean up session data after summary\n    sessionTranscripts.delete(ws);\n    \n  } catch (error) {\n    console.error('Error generating summary:', error);\n    ws.send(JSON.stringify({\n      type: 'summary',\n      content: 'Failed to generate summary: ' + (error instanceof Error ? error.message : 'Unknown error')\n    }));\n  }\n}\n\nasync function handleTestAudio(ws: WebSocket, message: any) {\n  try {\n    const { generateSpeechFile } = await import('./improved-voice-api');\n    \n    const testText = message.text || \"This is a test of the audio output. Can you hear this clearly?\";\n    const voice = message.voice || \"shimmer\";\n    const speed = message.speed || 1.0;\n    \n    const audioUrl = await generateSpeechFile(testText, voice, speed);\n    \n    ws.send(JSON.stringify({\n      type: 'test_audio_response',\n      audioUrl,\n      text: testText,\n      voice\n    }));\n    \n    console.log('Sent test audio response');\n  } catch (error) {\n    console.error('Error generating test audio:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Failed to generate test audio',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }));\n  }\n}\n\nfunction cleanupSession(ws: WebSocket) {\n  const realtimeProxy = realtimeProxies.get(ws);\n  if (realtimeProxy) {\n    realtimeProxy.disconnect();\n    realtimeProxies.delete(ws);\n  }\n  \n  sessionTranscripts.delete(ws);\n} ", "modifiedCode": "import { WebSocket } from 'ws';\nimport { OpenAIRealtimeProxy } from './openai-realtime-proxy';\n\n// Simple interface for WebSocket messages\ninterface WebSocketMessage {\n  type: string;\n  [key: string]: any;\n}\n\n// Store for Realtime API proxies and session data\nconst realtimeProxies = new Map<WebSocket, OpenAIRealtimeProxy>();\nconst sessionTranscripts = new Map<WebSocket, {\n  userId: string;\n  clientId: string;\n  messages: Array<{\n    speaker: 'user' | 'ai';\n    text: string;\n    timestamp: Date;\n  }>;\n  startTime: Date;\n}>();\n\nexport const handleWebSocketConnection = (ws: WebSocket) => {\n  console.log('New WebSocket connection');\n  \n  ws.onmessage = async (event) => {\n    try {\n      const message = JSON.parse(event.data.toString()) as WebSocketMessage;\n      const messageType = message.type as string;\n      \n      // Get existing proxy if available\n      const realtimeProxy = realtimeProxies.get(ws);\n      \n      switch (messageType) {\n        case 'start':\n          await handleSessionStart(ws, message);\n          break;\n          \n        case 'conversationEnded':\n          await handleSessionEnd(ws);\n          break;\n          \n        case 'summary_request':\n          await handleSummaryRequest(ws);\n          break;\n          \n        case 'test_audio':\n          await handleTestAudio(ws, message);\n          break;\n          \n        case 'ping':\n          ws.send(JSON.stringify({\n            type: 'pong',\n            timestamp: new Date().toISOString()\n          }));\n          break;\n          \n        default:\n          // Route all other messages to Realtime proxy if exists\n          if (realtimeProxy) {\n            realtimeProxy.handleClientMessage(message);\n          } else {\n            console.log(`Received message but no active session: ${messageType}`);\n          }\n          break;\n      }\n    } catch (error) {\n      console.error('Error handling WebSocket message:', error);\n      ws.send(JSON.stringify({\n        type: 'error',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      }));\n    }\n  };\n  \n  ws.onclose = () => {\n    console.log('WebSocket connection closed');\n    cleanupSession(ws);\n  };\n  \n  ws.onerror = (error) => {\n    console.error('WebSocket error:', error);\n    cleanupSession(ws);\n  };\n};\n\nasync function handleSessionStart(ws: WebSocket, message: any) {\n  console.log('Starting OpenAI Realtime API session...');\n  \n  try {\n    const openaiApiKey = process.env.OPENAI_API_KEY;\n    if (!openaiApiKey) {\n      throw new Error('OPENAI_API_KEY environment variable is required');\n    }\n\n    // Extract user info\n    const userId = message.userId?.toString() || '1';\n    const clientId = message.clientId?.toString() || '1';\n\n    // Initialize session transcript tracking\n    sessionTranscripts.set(ws, {\n      userId,\n      clientId,\n      messages: [],\n      startTime: new Date()\n    });\n\n    // Create Realtime API configuration\n    const realtimeConfig = {\n      apiKey: openaiApiKey,\n      model: message.behavior?.model || 'gpt-4o-realtime-preview',\n      voice: message.behavior?.voice?.voice || 'shimmer',\n      instructions: message.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n      temperature: message.behavior?.temperature || 0.7\n    };\n\n    // Create and connect proxy with transcript tracking\n    const realtimeProxy = new OpenAIRealtimeProxy(realtimeConfig);\n    realtimeProxies.set(ws, realtimeProxy);\n    \n    // Hook into proxy to capture transcript\n    realtimeProxy.onTranscriptUpdate = (speaker: 'user' | 'ai', text: string) => {\n      console.log(`📝 Capturing transcript: ${speaker} said \"${text}\"`);\n      const session = sessionTranscripts.get(ws);\n      if (session) {\n        session.messages.push({\n          speaker,\n          text,\n          timestamp: new Date()\n        });\n        console.log(`📝 Session now has ${session.messages.length} messages`);\n      }\n    };\n    \n    await realtimeProxy.connect(ws);\n    console.log('OpenAI Realtime API session initialized');\n    \n  } catch (error) {\n    console.error('Failed to initialize OpenAI Realtime API:', error);\n\n    // Enhanced error reporting for debugging\n    let errorMessage = 'Failed to initialize real-time session: ';\n    if (error instanceof Error) {\n      errorMessage += error.message;\n      console.error('Error details:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack\n      });\n    } else {\n      errorMessage += 'Unknown error';\n    }\n\n    // Check if this is an API access issue\n    if (error instanceof Error && (\n      error.message.includes('401') ||\n      error.message.includes('403') ||\n      error.message.includes('Unauthorized') ||\n      error.message.includes('realtime')\n    )) {\n      errorMessage += '\\n\\nNOTE: The OpenAI Realtime API requires special beta access. Your API key may not have Realtime API permissions.';\n    }\n\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: errorMessage\n    }));\n  }\n}\n\nasync function handleSessionEnd(ws: WebSocket) {\n  console.log('Ending session...');\n  \n  const realtimeProxy = realtimeProxies.get(ws);\n  if (realtimeProxy) {\n    realtimeProxy.disconnect();\n    realtimeProxies.delete(ws);\n  }\n  \n  // Send acknowledgment\n  ws.send(JSON.stringify({\n    type: 'end_ack'\n  }));\n}\n\nasync function handleSummaryRequest(ws: WebSocket) {\n  console.log('Generating session summary...');\n  \n  const session = sessionTranscripts.get(ws);\n  console.log(`📋 Session transcript has ${session?.messages.length || 0} messages`);\n  \n  if (!session || session.messages.length === 0) {\n    console.log('📋 No transcript to summarize - sending empty response');\n    ws.send(JSON.stringify({\n      type: 'summary',\n      content: 'No conversation to summarize.'\n    }));\n    return;\n  }\n\n  try {\n    // Use traditional OpenAI API to generate summary\n    const { summarizeTherapySession } = await import('./summary-service');\n    const summary = await summarizeTherapySession(session.messages, {\n      userId: session.userId,\n      clientId: session.clientId,\n      sessionDuration: Date.now() - session.startTime.getTime()\n    });\n    \n    ws.send(JSON.stringify({\n      type: 'summary',\n      content: summary\n    }));\n    \n    // Clean up session data after summary\n    sessionTranscripts.delete(ws);\n    \n  } catch (error) {\n    console.error('Error generating summary:', error);\n    ws.send(JSON.stringify({\n      type: 'summary',\n      content: 'Failed to generate summary: ' + (error instanceof Error ? error.message : 'Unknown error')\n    }));\n  }\n}\n\nasync function handleTestAudio(ws: WebSocket, message: any) {\n  try {\n    const { generateSpeechFile } = await import('./improved-voice-api');\n    \n    const testText = message.text || \"This is a test of the audio output. Can you hear this clearly?\";\n    const voice = message.voice || \"shimmer\";\n    const speed = message.speed || 1.0;\n    \n    const audioUrl = await generateSpeechFile(testText, voice, speed);\n    \n    ws.send(JSON.stringify({\n      type: 'test_audio_response',\n      audioUrl,\n      text: testText,\n      voice\n    }));\n    \n    console.log('Sent test audio response');\n  } catch (error) {\n    console.error('Error generating test audio:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Failed to generate test audio',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }));\n  }\n}\n\nfunction cleanupSession(ws: WebSocket) {\n  const realtimeProxy = realtimeProxies.get(ws);\n  if (realtimeProxy) {\n    realtimeProxy.disconnect();\n    realtimeProxies.delete(ws);\n  }\n  \n  sessionTranscripts.delete(ws);\n} "}