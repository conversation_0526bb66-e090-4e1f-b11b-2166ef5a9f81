{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Dashboard.tsx"}, "originalCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { DashboardStats, Client } from \"@shared/schema\";\nimport StatsCards from \"@/components/StatsCards\";\nimport ClientsList from \"@/components/ClientsList\";\nimport SessionNotesList from \"@/components/SessionNotesList\";\nimport InsightsSidebar from \"@/components/InsightsSidebar\";\nimport NewNoteForm from \"@/components/NewNoteForm\";\nimport { useLocation } from \"wouter\";\n\nexport default function Dashboard() {\n  const [, setLocation] = useLocation();\n\n  // Fetch dashboard stats\n  const { data: stats, isLoading: isLoadingStats } = useQuery<DashboardStats>({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch clients\n  const { data: clients, isLoading: isLoadingClients } = useQuery<(Client & { themes: any[]; lastSession: Date | null })[]>({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Fetch recent notes\n  const { data: notes, isLoading: isLoadingNotes } = useQuery({\n    queryKey: [\"/api/notes\"],\n  });\n\n  const handleClientClick = (clientId: number) => {\n    setLocation(`/clients/${clientId}`);\n  };\n\n  return (\n    <div>\n      <div className=\"md:flex md:items-center md:justify-between mb-6\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-neutral-dark sm:text-3xl\">\n            Dashboard\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Your client insights and recent activities\n          </p>\n        </div>\n        <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n          <button \n            type=\"button\" \n            className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            onClick={() => setLocation(\"/notes\")}\n          >\n            <span className=\"material-icons text-sm mr-2\">add</span>\n            New Session\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <StatsCards stats={stats} isLoading={isLoadingStats} />\n\n      {/* Recent Clients */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Recent Clients</h3>\n        <ClientsList \n          clients={clients} \n          isLoading={isLoadingClients} \n          onClientClick={handleClientClick}\n        />\n        <div className=\"mt-4 text-right\">\n          <a href=\"/clients\" className=\"text-sm font-medium text-primary hover:text-primary-dark\">\n            View all clients →\n          </a>\n        </div>\n      </div>\n\n      {/* Recent Session Notes */}\n      <div>\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Recent Session Notes</h3>\n        <SessionNotesList notes={notes as any} isLoading={isLoadingNotes} />\n\n        {/* New Note Entry Form */}\n        <div className=\"mt-6\">\n          <NewNoteForm />\n        </div>\n      </div>\n    </div>\n  );\n}\n", "modifiedCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { DashboardStats, Client } from \"@shared/schema\";\nimport StatsCards from \"@/components/StatsCards\";\nimport ClientsList from \"@/components/ClientsList\";\nimport SessionNotesList from \"@/components/SessionNotesList\";\nimport InsightsSidebar from \"@/components/InsightsSidebar\";\nimport NewNoteForm from \"@/components/NewNoteForm\";\nimport { useLocation } from \"wouter\";\n\nexport default function Dashboard() {\n  const [, setLocation] = useLocation();\n\n  // Fetch dashboard stats\n  const { data: stats, isLoading: isLoadingStats } = useQuery<DashboardStats>({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch clients\n  const { data: clients, isLoading: isLoadingClients } = useQuery<(Client & { themes: any[]; lastSession: Date | null })[]>({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Fetch recent notes\n  const { data: notes, isLoading: isLoadingNotes } = useQuery({\n    queryKey: [\"/api/notes\"],\n  });\n\n  const handleClientClick = (clientId: number) => {\n    setLocation(`/clients/${clientId}`);\n  };\n\n  return (\n    <div className=\"flex h-full\">\n      {/* Main Content */}\n      <div className=\"flex-1 overflow-auto\">\n        <div className=\"p-6\">\n          <div className=\"md:flex md:items-center md:justify-between mb-6\">\n            <div className=\"flex-1 min-w-0\">\n              <h2 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl\">\n                Dashboard\n              </h2>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Your client insights and recent activities\n              </p>\n            </div>\n            <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n              <button\n                type=\"button\"\n                className=\"ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                onClick={() => setLocation(\"/notes\")}\n              >\n                <span className=\"material-icons text-sm mr-2\">add</span>\n                New Session\n              </button>\n            </div>\n          </div>\n\n      {/* Stats Cards */}\n      <StatsCards stats={stats} isLoading={isLoadingStats} />\n\n      {/* Recent Clients */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Recent Clients</h3>\n        <ClientsList \n          clients={clients} \n          isLoading={isLoadingClients} \n          onClientClick={handleClientClick}\n        />\n        <div className=\"mt-4 text-right\">\n          <a href=\"/clients\" className=\"text-sm font-medium text-primary hover:text-primary-dark\">\n            View all clients →\n          </a>\n        </div>\n      </div>\n\n      {/* Recent Session Notes */}\n      <div>\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Recent Session Notes</h3>\n        <SessionNotesList notes={notes as any} isLoading={isLoadingNotes} />\n\n        {/* New Note Entry Form */}\n        <div className=\"mt-6\">\n          <NewNoteForm />\n        </div>\n      </div>\n    </div>\n  );\n}\n"}