{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-voice-fixes.js"}, "modifiedCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconsole.log('🔧 TESTING VOICE CONVERSATION FIXES');\nconsole.log('=' * 50);\n\nconst ws = new WebSocket('ws://localhost:5000/ws');\n\nlet messageCount = 0;\nlet sessionReady = false;\nlet greetingReceived = false;\nlet speechDetected = false;\nlet transcriptionReceived = false;\nlet responseReceived = false;\nconst receivedMessages = [];\n\nfunction logEvent(event, details = '', important = false) {\n  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];\n  const logLine = `[${timestamp}] ${event}${details ? ': ' + details : ''}`;\n  console.log(important ? `🔥 ${logLine}` : logLine);\n}\n\n// Generate realistic test audio data\nfunction generateTestAudioChunk(duration = 0.1) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * duration);\n  const buffer = new ArrayBuffer(samples * 2);\n  const view = new DataView(buffer);\n  \n  for (let i = 0; i < samples; i++) {\n    const t = i / sampleRate;\n    const fundamental = 150;\n    const harmonics = Math.sin(2 * Math.PI * fundamental * t) * 0.3 +\n                     Math.sin(2 * Math.PI * fundamental * 2 * t) * 0.2 +\n                     Math.sin(2 * Math.PI * fundamental * 3 * t) * 0.1;\n    \n    const noise = (Math.random() - 0.5) * 0.1;\n    const envelope = Math.sin(t * 10) * 0.5 + 0.5;\n    \n    const sample = (harmonics + noise) * envelope * 0.4;\n    const int16Sample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));\n    view.setInt16(i * 2, int16Sample, true);\n  }\n  \n  const uint8Array = new Uint8Array(buffer);\n  return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n}\n\nws.on('open', () => {\n  logEvent('WebSocket connected', '', true);\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-fixes\",\n    clientId: \"test-fixes\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview-2024-10-01\",\n      temperature: 0.7,\n      voice: { voice: \"shimmer\", speed: 1.0 }\n    },\n    instructions: \"You are Vale, an empathetic AI therapeutic assistant. Greet the user warmly and ask how they're feeling today.\"\n  };\n  \n  logEvent('Sending start message', '', true);\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    receivedMessages.push(message.type);\n    \n    switch (message.type) {\n      case 'ready':\n        sessionReady = true;\n        logEvent('Session ready', 'Starting audio test in 2 seconds', true);\n        setTimeout(startAudioTest, 2000);\n        break;\n        \n      case 'session.created':\n        logEvent('OpenAI session created', `ID: ${message.session?.id}`, true);\n        break;\n        \n      case 'session.updated':\n        const vadType = message.session?.turn_detection?.type || 'none';\n        logEvent('Session configured', `VAD: ${vadType}`, true);\n        break;\n        \n      case 'input_audio_buffer.speech_started':\n        speechDetected = true;\n        logEvent('SPEECH DETECTED', 'OpenAI VAD working', true);\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        transcriptionReceived = true;\n        logEvent('USER TRANSCRIBED', `\"${message.transcript}\"`, true);\n        break;\n        \n      case 'response.audio_transcript.done':\n        const transcript = message.transcript || '';\n        if (transcript && !greetingReceived) {\n          greetingReceived = true;\n          logEvent('AI GREETING', `\"${transcript}\"`, true);\n        } else if (transcript) {\n          responseReceived = true;\n          logEvent('AI RESPONSE', `\"${transcript}\"`, true);\n        }\n        break;\n        \n      case 'response.audio.delta':\n        logEvent('AI AUDIO', `${message.delta?.length || 0} chars`);\n        break;\n        \n      case 'error':\n        logEvent('ERROR', message.message, true);\n        break;\n    }\n  } catch (error) {\n    logEvent('Parse error', error.message, true);\n  }\n});\n\nfunction startAudioTest() {\n  logEvent('Starting audio test', 'Sending 8 chunks of realistic audio', true);\n  \n  let chunkCount = 0;\n  const sendChunk = () => {\n    if (chunkCount >= 8) {\n      logEvent('Audio test complete', 'Waiting for response', true);\n      setTimeout(generateReport, 5000);\n      return;\n    }\n    \n    const audioData = generateTestAudioChunk(0.1);\n    ws.send(JSON.stringify({\n      type: 'input_audio_buffer.append',\n      audio: audioData\n    }));\n    \n    chunkCount++;\n    logEvent(`Audio chunk ${chunkCount}/8`, `${audioData.length} chars`);\n    \n    setTimeout(sendChunk, 100);\n  };\n  \n  sendChunk();\n}\n\nfunction generateReport() {\n  console.log('\\n' + '='.repeat(50));\n  console.log('🔧 VOICE CONVERSATION FIX TEST RESULTS');\n  console.log('='.repeat(50));\n  \n  console.log('\\n📊 TEST RESULTS:');\n  console.log(`Session Ready:        ${formatResult(sessionReady)}`);\n  console.log(`AI Greeting:          ${formatResult(greetingReceived)}`);\n  console.log(`Speech Detection:     ${formatResult(speechDetected)}`);\n  console.log(`Transcription:        ${formatResult(transcriptionReceived)}`);\n  console.log(`AI Response:          ${formatResult(responseReceived)}`);\n  \n  console.log('\\n📋 MESSAGE FLOW:');\n  console.log(`Total Messages:       ${messageCount}`);\n  console.log(`Message Types:        ${[...new Set(receivedMessages)].join(', ')}`);\n  \n  console.log('\\n🔧 DIAGNOSIS:');\n  if (sessionReady && greetingReceived && speechDetected && transcriptionReceived) {\n    console.log('✅ VOICE CONVERSATION SYSTEM IS WORKING!');\n    console.log('✅ All core components are functioning correctly');\n    console.log('✅ The fixes have resolved the voice interaction issues');\n  } else {\n    console.log('❌ Some issues remain:');\n    if (!sessionReady) console.log('  - Session initialization failed');\n    if (!greetingReceived) console.log('  - AI greeting not received');\n    if (!speechDetected) console.log('  - Speech detection not working');\n    if (!transcriptionReceived) console.log('  - Audio transcription failed');\n  }\n  \n  console.log('\\n💡 NEXT STEPS:');\n  if (sessionReady && greetingReceived && speechDetected) {\n    console.log('1. Test the Admin AI Test Center in the browser');\n    console.log('2. Verify microphone permissions are granted');\n    console.log('3. Check that audio levels are visible in the UI');\n    console.log('4. Speak clearly and check for speech detection events');\n  } else {\n    console.log('1. Check server logs for any errors');\n    console.log('2. Verify OpenAI API key has Realtime API access');\n    console.log('3. Test with different audio settings');\n  }\n  \n  ws.close();\n}\n\nfunction formatResult(result) {\n  return result ? '✅ PASS' : '❌ FAIL';\n}\n\nws.on('close', () => {\n  logEvent('WebSocket closed', '', true);\n});\n\nws.on('error', (error) => {\n  logEvent('WebSocket error', error.message, true);\n});\n\n// Timeout\nsetTimeout(() => {\n  logEvent('TEST TIMEOUT', 'Ending test', true);\n  generateReport();\n}, 25000);\n"}