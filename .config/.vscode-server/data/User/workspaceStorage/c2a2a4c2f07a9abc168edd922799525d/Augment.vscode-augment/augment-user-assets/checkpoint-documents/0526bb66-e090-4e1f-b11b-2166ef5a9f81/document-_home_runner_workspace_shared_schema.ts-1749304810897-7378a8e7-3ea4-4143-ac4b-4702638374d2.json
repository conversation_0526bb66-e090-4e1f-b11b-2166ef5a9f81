{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}, "originalCode": "import { pgTable, text, serial, integer, boolean, timestamp, json, pgEnum } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// User role enum\nexport const userRoleEnum = pgEnum('user_role', ['client', 'doctor', 'admin']);\n\n// Type of invitation code\nexport const invitationTypeEnum = pgEnum('invitation_type', ['doctor', 'client']);\n\n// User table for all user types\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  name: text(\"name\").notNull(),\n  userRole: userRoleEnum(\"user_role\").notNull().default('client'),\n  professionalRole: text(\"professional_role\"),  // For doctors (e.g., \"Psychologist\", \"Therapist\")\n  email: text(\"email\").notNull().unique(),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Client table\nexport const clients = pgTable(\"clients\", {\n  id: serial(\"id\").primaryKey(),\n  userId: integer(\"user_id\").notNull().references(() => users.id),\n  name: text(\"name\").notNull(),\n  status: text(\"status\").notNull().default(\"stable\"),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertClientSchema = createInsertSchema(clients).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Session notes table\nexport const sessionNotes = pgTable(\"session_notes\", {\n  id: serial(\"id\").primaryKey(),\n  clientId: integer(\"client_id\").notNull().references(() => clients.id),\n  createdBy: integer(\"created_by\").notNull().references(() => users.id),\n  content: text(\"content\").notNull(),\n  summary: text(\"summary\"),\n  recommendations: text(\"recommendations\").array(),\n  analyzed: boolean(\"analyzed\").default(false).notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  // Additional fields for client compatibility\n  sessionDate: timestamp(\"session_date\").defaultNow().notNull(),\n  duration: integer(\"duration\").default(50).notNull(), // Duration in minutes\n  aiProcessed: boolean(\"ai_processed\").default(false).notNull(),\n  rawNotes: text(\"raw_notes\"), // Alias for content for client compatibility\n});\n\nexport const insertSessionNoteSchema = createInsertSchema(sessionNotes).omit({\n  id: true,\n  summary: true,\n  recommendations: true,\n  analyzed: true,\n  createdAt: true,\n  aiProcessed: true,\n});\n\n// Client themes table\nexport const clientThemes = pgTable(\"client_themes\", {\n  id: serial(\"id\").primaryKey(),\n  clientId: integer(\"client_id\").notNull().references(() => clients.id),\n  name: text(\"name\").notNull(),\n  occurrences: integer(\"occurrences\").notNull().default(1),\n  trend: integer(\"trend\").default(0),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertClientThemeSchema = createInsertSchema(clientThemes).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Theme interface\nexport interface Theme {\n  name: string;\n  occurrences: number;\n  trend: number; // positive, negative, or zero\n}\n\n// Type definitions\nexport type User = typeof users.$inferSelect;\nexport type InsertUser = z.infer<typeof insertUserSchema>;\n\nexport type Client = typeof clients.$inferSelect;\nexport type InsertClient = z.infer<typeof insertClientSchema>;\n\nexport type SessionNote = typeof sessionNotes.$inferSelect;\nexport type InsertSessionNote = z.infer<typeof insertSessionNoteSchema>;\n\nexport type ClientTheme = typeof clientThemes.$inferSelect;\nexport type InsertClientTheme = z.infer<typeof insertClientThemeSchema>;\n\n// AI Therapy Conversations table\nexport const aiTherapyConversations = pgTable(\"ai_therapy_conversations\", {\n  id: serial(\"id\").primaryKey(),\n  userId: integer(\"user_id\").notNull().references(() => users.id),\n  clientId: integer(\"client_id\").notNull().references(() => clients.id),\n  // Using created_at as the start time instead of a separate started_at field\n  endedAt: timestamp(\"ended_at\"),\n  title: text(\"title\").notNull().default(\"New Conversation\"),\n  summary: text(\"summary\"),\n  active: boolean(\"active\").default(true).notNull(),\n  aiProcessed: boolean(\"ai_processed\").default(false),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertAiTherapyConversationSchema = createInsertSchema(aiTherapyConversations).omit({\n  id: true,\n  endedAt: true,\n  summary: true,\n  aiProcessed: true,\n  createdAt: true,\n});\n\n// AI Therapy Messages table\nexport const aiTherapyMessages = pgTable(\"ai_therapy_messages\", {\n  id: serial(\"id\").primaryKey(),\n  conversationId: integer(\"conversation_id\").notNull().references(() => aiTherapyConversations.id),\n  role: text(\"role\").notNull(), // 'user' or 'assistant'\n  content: text(\"content\").notNull(),\n  timestamp: timestamp(\"timestamp\").defaultNow().notNull(),\n  audioUrl: text(\"audio_url\"), // URL to stored audio file for voice messages\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertAiTherapyMessageSchema = createInsertSchema(aiTherapyMessages).omit({\n  id: true,\n  createdAt: true,\n});\n\n// AI Therapy Settings for Admin configuration\nexport const aiTherapySettings = pgTable(\"ai_therapy_settings\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull().unique(),\n  systemPrompt: text(\"system_prompt\").notNull(),\n  model: text(\"model\").notNull().default(\"gpt-4o\"),\n  temperature: integer(\"temperature\").notNull().default(70), // 0-100 representing 0-1.0\n  active: boolean(\"active\").notNull().default(true),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  updatedAt: timestamp(\"updated_at\").defaultNow().notNull(),\n});\n\nexport const insertAiTherapySettingsSchema = createInsertSchema(aiTherapySettings).omit({\n  id: true,\n  createdAt: true,\n  updatedAt: true,\n});\n\n// Doctor-Client relationships\nexport const doctorClients = pgTable(\"doctor_clients\", {\n  id: serial(\"id\").primaryKey(),\n  doctorId: integer(\"doctor_id\").notNull().references(() => users.id),\n  clientId: integer(\"client_id\").notNull().references(() => users.id),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertDoctorClientSchema = createInsertSchema(doctorClients).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Dashboard stats - This is a helper type, not a table\nexport interface DashboardStats {\n  totalClients: number;\n  weeklyNotes: number;\n  insights: number;\n  improving: number;\n  improvingPercentage: number;\n}\n\n// Type definitions for new tables\nexport type AiTherapyConversation = typeof aiTherapyConversations.$inferSelect;\nexport type InsertAiTherapyConversation = z.infer<typeof insertAiTherapyConversationSchema>;\n\nexport type AiTherapyMessage = typeof aiTherapyMessages.$inferSelect;\nexport type InsertAiTherapyMessage = z.infer<typeof insertAiTherapyMessageSchema>;\n\nexport type AiTherapySettings = typeof aiTherapySettings.$inferSelect;\nexport type InsertAiTherapySettings = z.infer<typeof insertAiTherapySettingsSchema>;\n\nexport type DoctorClient = typeof doctorClients.$inferSelect;\nexport type InsertDoctorClient = z.infer<typeof insertDoctorClientSchema>;\n\n// Invitation codes table\nexport const invitationCodes = pgTable(\"invitation_codes\", {\n  id: serial(\"id\").primaryKey(),\n  code: text(\"code\").notNull().unique(),\n  type: invitationTypeEnum(\"type\").notNull(),\n  createdBy: integer(\"created_by_id\").notNull().references(() => users.id),\n  used: boolean(\"used\").default(false).notNull(),\n  usedBy: integer(\"used_by_id\").references(() => users.id),\n  usedAt: timestamp(\"used_at\"),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  expiresAt: timestamp(\"expires_at\"),\n});\n\nexport const insertInvitationCodeSchema = createInsertSchema(invitationCodes).omit({\n  id: true,\n  used: true,\n  usedBy: true,\n  usedAt: true,\n  createdAt: true,\n});\n\nexport type InvitationCode = typeof invitationCodes.$inferSelect;\nexport type InsertInvitationCode = z.infer<typeof insertInvitationCodeSchema>;\n\nexport interface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: string;\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\nexport interface ConversationConfig {\n  temperature: number;\n  maxTokens: number;\n  presencePenalty: number;\n  frequencyPenalty: number;\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n  };\n}\n\nexport interface Metrics {\n  latency: number;\n  responseTime: number;\n  audioQuality: number;\n  conversationFlow: number;\n}\n\nexport type WebSocketMessage = {\n  type: 'error' | 'audio' | 'voice_config' | 'voice_config_updated' | \n        'conversation_config' | 'conversation_config_updated' | 'metrics_update' | \n        'reset' | 'reset_complete' | 'transcript' | 'assistant_response' | 'summary' |\n        'start' | 'ready' | 'status' | 'transcription' | 'processing' | 'processing_audio' |\n        'chunk' | 'complete' | 'complete_with_audio';\n  message?: string;\n  config?: VoiceConfig | ConversationConfig;\n  audioData?: string;\n  userId?: string | number;\n  clientId?: string | number;\n  conversationId?: string | number;\n  metrics?: {\n    latency: number;\n    responseTime: number;\n    audioQuality?: number;\n    conversationFlow?: number;\n  };\n  text?: string;\n  audioUrl?: string;\n  content?: string;\n  summary?: {\n    duration: number;\n    totalTurns: number;\n    averageResponseTime: number;\n    keyThemes: string[];\n    recommendations: string[];\n  };\n};\n", "modifiedCode": "import { pgTable, text, serial, integer, boolean, timestamp, json, pgEnum } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// User role enum\nexport const userRoleEnum = pgEnum('user_role', ['client', 'doctor', 'admin']);\n\n// Type of invitation code\nexport const invitationTypeEnum = pgEnum('invitation_type', ['doctor', 'client']);\n\n// User table for all user types\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  name: text(\"name\").notNull(),\n  userRole: userRoleEnum(\"user_role\").notNull().default('client'),\n  professionalRole: text(\"professional_role\"),  // For doctors (e.g., \"Psychologist\", \"Therapist\")\n  email: text(\"email\").notNull().unique(),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Client table\nexport const clients = pgTable(\"clients\", {\n  id: serial(\"id\").primaryKey(),\n  userId: integer(\"user_id\").notNull().references(() => users.id),\n  name: text(\"name\").notNull(),\n  status: text(\"status\").notNull().default(\"stable\"),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertClientSchema = createInsertSchema(clients).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Session notes table\nexport const sessionNotes = pgTable(\"session_notes\", {\n  id: serial(\"id\").primaryKey(),\n  clientId: integer(\"client_id\").notNull().references(() => clients.id),\n  createdBy: integer(\"created_by\").notNull().references(() => users.id),\n  content: text(\"content\").notNull(),\n  summary: text(\"summary\"),\n  recommendations: text(\"recommendations\").array(),\n  analyzed: boolean(\"analyzed\").default(false).notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  // Additional fields for client compatibility\n  sessionDate: timestamp(\"session_date\").defaultNow().notNull(),\n  duration: integer(\"duration\").default(50).notNull(), // Duration in minutes\n  aiProcessed: boolean(\"ai_processed\").default(false).notNull(),\n  rawNotes: text(\"raw_notes\"), // Alias for content for client compatibility\n});\n\nexport const insertSessionNoteSchema = createInsertSchema(sessionNotes).omit({\n  id: true,\n  summary: true,\n  recommendations: true,\n  analyzed: true,\n  createdAt: true,\n  aiProcessed: true,\n});\n\n// Client themes table\nexport const clientThemes = pgTable(\"client_themes\", {\n  id: serial(\"id\").primaryKey(),\n  clientId: integer(\"client_id\").notNull().references(() => clients.id),\n  name: text(\"name\").notNull(),\n  occurrences: integer(\"occurrences\").notNull().default(1),\n  trend: integer(\"trend\").default(0),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertClientThemeSchema = createInsertSchema(clientThemes).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Theme interface\nexport interface Theme {\n  name: string;\n  occurrences: number;\n  trend: number; // positive, negative, or zero\n}\n\n// Type definitions\nexport type User = typeof users.$inferSelect;\nexport type InsertUser = z.infer<typeof insertUserSchema>;\n\nexport type Client = typeof clients.$inferSelect;\nexport type InsertClient = z.infer<typeof insertClientSchema>;\n\nexport type SessionNote = typeof sessionNotes.$inferSelect;\nexport type InsertSessionNote = z.infer<typeof insertSessionNoteSchema>;\n\nexport type ClientTheme = typeof clientThemes.$inferSelect;\nexport type InsertClientTheme = z.infer<typeof insertClientThemeSchema>;\n\n// AI Therapy Conversations table\nexport const aiTherapyConversations = pgTable(\"ai_therapy_conversations\", {\n  id: serial(\"id\").primaryKey(),\n  userId: integer(\"user_id\").notNull().references(() => users.id),\n  clientId: integer(\"client_id\").notNull().references(() => clients.id),\n  // Using created_at as the start time instead of a separate started_at field\n  endedAt: timestamp(\"ended_at\"),\n  title: text(\"title\").notNull().default(\"New Conversation\"),\n  summary: text(\"summary\"),\n  active: boolean(\"active\").default(true).notNull(),\n  aiProcessed: boolean(\"ai_processed\").default(false),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertAiTherapyConversationSchema = createInsertSchema(aiTherapyConversations).omit({\n  id: true,\n  endedAt: true,\n  summary: true,\n  aiProcessed: true,\n  createdAt: true,\n});\n\n// AI Therapy Messages table\nexport const aiTherapyMessages = pgTable(\"ai_therapy_messages\", {\n  id: serial(\"id\").primaryKey(),\n  conversationId: integer(\"conversation_id\").notNull().references(() => aiTherapyConversations.id),\n  role: text(\"role\").notNull(), // 'user' or 'assistant'\n  content: text(\"content\").notNull(),\n  timestamp: timestamp(\"timestamp\").defaultNow().notNull(),\n  audioUrl: text(\"audio_url\"), // URL to stored audio file for voice messages\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertAiTherapyMessageSchema = createInsertSchema(aiTherapyMessages).omit({\n  id: true,\n  createdAt: true,\n});\n\n// AI Therapy Settings for Admin configuration\nexport const aiTherapySettings = pgTable(\"ai_therapy_settings\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull().unique(),\n  systemPrompt: text(\"system_prompt\").notNull(),\n  model: text(\"model\").notNull().default(\"gpt-4o\"),\n  temperature: integer(\"temperature\").notNull().default(70), // 0-100 representing 0-1.0\n  active: boolean(\"active\").notNull().default(true),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  updatedAt: timestamp(\"updated_at\").defaultNow().notNull(),\n});\n\nexport const insertAiTherapySettingsSchema = createInsertSchema(aiTherapySettings).omit({\n  id: true,\n  createdAt: true,\n  updatedAt: true,\n});\n\n// Doctor-Client relationships\nexport const doctorClients = pgTable(\"doctor_clients\", {\n  id: serial(\"id\").primaryKey(),\n  doctorId: integer(\"doctor_id\").notNull().references(() => users.id),\n  clientId: integer(\"client_id\").notNull().references(() => users.id),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const insertDoctorClientSchema = createInsertSchema(doctorClients).omit({\n  id: true,\n  createdAt: true,\n});\n\n// Dashboard stats - This is a helper type, not a table\nexport interface DashboardStats {\n  totalClients: number;\n  weeklyNotes: number;\n  insights: number;\n  improving: number;\n  improvingPercentage: number;\n}\n\n// Type definitions for new tables\nexport type AiTherapyConversation = typeof aiTherapyConversations.$inferSelect;\nexport type InsertAiTherapyConversation = z.infer<typeof insertAiTherapyConversationSchema>;\n\nexport type AiTherapyMessage = typeof aiTherapyMessages.$inferSelect;\nexport type InsertAiTherapyMessage = z.infer<typeof insertAiTherapyMessageSchema>;\n\nexport type AiTherapySettings = typeof aiTherapySettings.$inferSelect;\nexport type InsertAiTherapySettings = z.infer<typeof insertAiTherapySettingsSchema>;\n\nexport type DoctorClient = typeof doctorClients.$inferSelect;\nexport type InsertDoctorClient = z.infer<typeof insertDoctorClientSchema>;\n\n// Invitation codes table\nexport const invitationCodes = pgTable(\"invitation_codes\", {\n  id: serial(\"id\").primaryKey(),\n  code: text(\"code\").notNull().unique(),\n  type: invitationTypeEnum(\"type\").notNull(),\n  createdBy: integer(\"created_by_id\").notNull().references(() => users.id),\n  used: boolean(\"used\").default(false).notNull(),\n  usedBy: integer(\"used_by_id\").references(() => users.id),\n  usedAt: timestamp(\"used_at\"),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  expiresAt: timestamp(\"expires_at\"),\n});\n\nexport const insertInvitationCodeSchema = createInsertSchema(invitationCodes).omit({\n  id: true,\n  used: true,\n  usedBy: true,\n  usedAt: true,\n  createdAt: true,\n});\n\nexport type InvitationCode = typeof invitationCodes.$inferSelect;\nexport type InsertInvitationCode = z.infer<typeof insertInvitationCodeSchema>;\n\nexport interface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: string;\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\nexport interface ConversationConfig {\n  temperature: number;\n  maxTokens: number;\n  presencePenalty: number;\n  frequencyPenalty: number;\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\nexport interface Metrics {\n  latency: number;\n  responseTime: number;\n  audioQuality: number;\n  conversationFlow: number;\n}\n\nexport type WebSocketMessage = {\n  type: 'error' | 'audio' | 'voice_config' | 'voice_config_updated' | \n        'conversation_config' | 'conversation_config_updated' | 'metrics_update' | \n        'reset' | 'reset_complete' | 'transcript' | 'assistant_response' | 'summary' |\n        'start' | 'ready' | 'status' | 'transcription' | 'processing' | 'processing_audio' |\n        'chunk' | 'complete' | 'complete_with_audio';\n  message?: string;\n  config?: VoiceConfig | ConversationConfig;\n  audioData?: string;\n  userId?: string | number;\n  clientId?: string | number;\n  conversationId?: string | number;\n  metrics?: {\n    latency: number;\n    responseTime: number;\n    audioQuality?: number;\n    conversationFlow?: number;\n  };\n  text?: string;\n  audioUrl?: string;\n  content?: string;\n  summary?: {\n    duration: number;\n    totalTurns: number;\n    averageResponseTime: number;\n    keyThemes: string[];\n    recommendations: string[];\n  };\n};\n"}