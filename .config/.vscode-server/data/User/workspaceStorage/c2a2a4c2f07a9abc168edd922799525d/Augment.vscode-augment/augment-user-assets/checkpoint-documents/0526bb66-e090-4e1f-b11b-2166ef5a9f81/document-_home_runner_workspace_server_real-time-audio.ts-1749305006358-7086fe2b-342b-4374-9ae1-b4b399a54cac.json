{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/real-time-audio.ts"}, "originalCode": "import { WebSocket } from 'ws';\nimport { streamAITherapistResponse, transcribeAudio } from './openai';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\nimport { loadVoiceConfig, loadConversationConfig } from './db';\nimport { WebSocketMessage, ConversationMessage, ClientState, ClientMetrics } from './types';\nimport fs from 'fs';\nimport path from 'path';\nimport { v4 as randomUUID } from 'uuid';\n\n// Ensure uploads directory exists\nconst AUDIO_DIR = path.join(process.cwd(), 'uploads');\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\n// Interface for real-time audio state\ninterface RealTimeAudioState {\n  audioBuffer: Buffer[];\n  isProcessing: boolean;\n  lastProcessedTime: number;\n  conversationHistory: ConversationMessage[];\n  voiceConfig: VoiceConfig;\n  conversationConfig: ConversationConfig;\n}\n\n// Map to store real-time audio states\nconst audioStates = new Map<WebSocket, RealTimeAudioState>();\n\n// Process audio data in real-time\nexport async function processRealTimeAudio(\n  ws: WebSocket,\n  audioData: Buffer,\n  state: RealTimeAudioState\n): Promise<void> {\n  // Add new audio data to buffer\n  state.audioBuffer.push(audioData);\n  \n  // Calculate total buffer size\n  const totalBufferSize = state.audioBuffer.reduce((size, buf) => size + buf.length, 0);\n  \n  // Process audio if enough data has accumulated (about 0.5 seconds of audio at 16kHz, 16-bit)\n  // This is approximately 16000 samples/sec * 0.5 sec * 2 bytes/sample = 16000 bytes\n  if (totalBufferSize >= 16000 && !state.isProcessing) {\n    state.isProcessing = true;\n    \n    try {\n      // Concatenate all buffers\n      const audioBuffer = Buffer.concat(state.audioBuffer);\n      \n      // Clear the buffer after concatenation\n      state.audioBuffer = [];\n      \n      // Save audio to a temporary file for debugging\n      const tempFilename = `${randomUUID()}.webm`;\n      const filePath = path.join(AUDIO_DIR, tempFilename);\n      fs.writeFileSync(filePath, audioBuffer);\n      console.log(`Saved real-time audio chunk to ${filePath}, size: ${audioBuffer.length} bytes`);\n      \n      // Transcribe the audio\n      console.log(`Transcribing real-time audio chunk of size: ${audioBuffer.length} bytes`);\n      const transcription = await transcribeAudio(audioBuffer, tempFilename);\n      \n      // Process the transcription if we got text\n      if (transcription.text && transcription.text.trim().length > 0) {\n        console.log(`Real-time transcription: \"${transcription.text}\"`);\n        \n        // Add user message to conversation history\n        state.conversationHistory.push({\n          role: 'user',\n          content: transcription.text,\n          timestamp: new Date()\n        });\n        \n        // Send transcription to client for immediate feedback\n        ws.send(JSON.stringify({\n          type: 'transcription',\n          text: transcription.text\n        }));\n        \n        // Stream AI response\n        await streamAITherapistResponse(\n          ws,\n          transcription.text,\n          state.conversationHistory,\n          true,\n          undefined,\n          state.voiceConfig,\n          state.conversationConfig\n        );\n      } else {\n        console.log('No text transcribed from audio chunk');\n      }\n    } catch (error) {\n      console.error('Error processing real-time audio:', error);\n      ws.send(JSON.stringify({\n        type: 'error',\n        message: 'Failed to process audio: ' + (error instanceof Error ? error.message : String(error))\n      }));\n    } finally {\n      state.isProcessing = false;\n      state.lastProcessedTime = Date.now();\n    }\n  }\n}\n\n// Handle WebSocket message for audio processing\nexport async function handleRealTimeAudioMessage(\n  ws: WebSocket,\n  message: any\n): Promise<void> {\n  // Get or create state for this connection\n  let state = audioStates.get(ws);\n  \n  if (!state) {\n    console.error('No state found for WebSocket connection. Initialize first.');\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Session not initialized. Please start a new session.'\n    }));\n    return;\n  }\n  \n  try {\n    // Process different audio data formats\n    let audioBuffer: Buffer;\n    \n    if (message.audio_data && Array.isArray(message.audio_data)) {\n      console.log(`Received audio data array of length: ${message.audio_data.length}`);\n      \n      // Handle int16 format from client\n      if (message.format === 'int16') {\n        // Create a proper Int16Array and then convert to Buffer\n        const int16Samples = new Int16Array(message.audio_data.length);\n        for (let i = 0; i < message.audio_data.length; i++) {\n          int16Samples[i] = message.audio_data[i];\n        }\n        \n        // Convert to buffer ensuring proper byte layout\n        audioBuffer = Buffer.from(int16Samples.buffer);\n        console.log(`Processed int16 audio format, samples: ${message.audio_data.length}, buffer size: ${audioBuffer.length} bytes`);\n      } else {\n        // Legacy format handling - convert float to int16\n        const floatSamples = message.audio_data;\n        const int16Samples = new Int16Array(floatSamples.length);\n        \n        for (let i = 0; i < floatSamples.length; i++) {\n          const sample = Math.max(-1, Math.min(1, floatSamples[i]));\n          int16Samples[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n        }\n        \n        audioBuffer = Buffer.from(int16Samples.buffer);\n        console.log(`Processed float audio data, converted to int16 format: ${audioBuffer.length} bytes`);\n      }\n    } else if (typeof message.audioData === 'string') {\n      // Convert base64 audioData to Buffer\n      audioBuffer = Buffer.from(message.audioData, 'base64');\n      console.log(`Processed base64 audio data: ${audioBuffer.length} bytes`);\n    } else {\n      console.error('No valid audio data found in message');\n      ws.send(JSON.stringify({\n        type: 'error',\n        message: 'Invalid audio data format'\n      }));\n      return;\n    }\n    \n    // Process the audio data\n    await processRealTimeAudio(ws, audioBuffer, state);\n  } catch (error) {\n    console.error('Error handling real-time audio message:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Error processing audio data: ' + (error instanceof Error ? error.message : String(error))\n    }));\n  }\n}\n\n// Initialize real-time audio state\nexport async function initializeRealTimeAudio(\n  ws: WebSocket,\n  userId: string\n): Promise<void> {\n  try {\n    console.log(`Initializing real-time audio for user: ${userId}`);\n    \n    // Load configurations\n    const voiceConfig = await loadVoiceConfig(userId);\n    const conversationConfig = await loadConversationConfig(userId);\n    \n    if (!voiceConfig || !conversationConfig) {\n      console.warn(`Missing configurations for user ${userId}, using defaults`);\n    }\n    \n    // Use defaults if configurations are missing\n    const defaultVoiceConfig: VoiceConfig = {\n      voice: 'shimmer',\n      speed: 1.0,\n      pitch: 1.0,\n      emphasis: 'moderate',\n      prosody: {\n        emotionalRange: 70,\n        questionInflection: 60,\n        pauseDuration: 50\n      }\n    };\n    \n    const defaultConversationConfig: ConversationConfig = {\n      turnTaking: {\n        backchannelFrequency: 50,\n        minSilenceDuration: 500,\n        maxInterruptionGap: 200\n      },\n      responseStyle: {\n        minResponseLength: 50,\n        maxResponseLength: 150,\n        temperature: 70,\n        presencePenalty: 60,\n        frequencyPenalty: 40\n      }\n    };\n    \n    // Create state with configurations\n    const state: RealTimeAudioState = {\n      audioBuffer: [],\n      isProcessing: false,\n      lastProcessedTime: Date.now(),\n      conversationHistory: [],\n      voiceConfig: voiceConfig || defaultVoiceConfig,\n      conversationConfig: conversationConfig || defaultConversationConfig\n    };\n    \n    // Store state for this connection\n    audioStates.set(ws, state);\n    \n    console.log(`Real-time audio initialized for user ${userId}`);\n    ws.send(JSON.stringify({\n      type: 'ready',\n      message: 'Voice session initialized successfully'\n    }));\n  } catch (error) {\n    console.error('Error initializing real-time audio:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Failed to initialize voice session: ' + (error instanceof Error ? error.message : String(error))\n    }));\n  }\n}\n\n// Clean up real-time audio state when WebSocket closes\nexport function cleanupRealTimeAudio(ws: WebSocket): void {\n  if (audioStates.has(ws)) {\n    console.log('Cleaning up real-time audio state');\n    audioStates.delete(ws);\n  }\n}", "modifiedCode": "import { WebSocket } from 'ws';\nimport { streamAITherapistResponse, transcribeAudio } from './openai';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\nimport { loadVoiceConfig, loadConversationConfig } from './db';\nimport { WebSocketMessage, ConversationMessage, ClientState, ClientMetrics } from './types';\nimport fs from 'fs';\nimport path from 'path';\nimport { v4 as randomUUID } from 'uuid';\n\n// Ensure uploads directory exists\nconst AUDIO_DIR = path.join(process.cwd(), 'uploads');\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\n// Interface for real-time audio state\ninterface RealTimeAudioState {\n  audioBuffer: Buffer[];\n  isProcessing: boolean;\n  lastProcessedTime: number;\n  conversationHistory: ConversationMessage[];\n  voiceConfig: VoiceConfig;\n  conversationConfig: ConversationConfig;\n}\n\n// Map to store real-time audio states\nconst audioStates = new Map<WebSocket, RealTimeAudioState>();\n\n// Process audio data in real-time\nexport async function processRealTimeAudio(\n  ws: WebSocket,\n  audioData: Buffer,\n  state: RealTimeAudioState\n): Promise<void> {\n  // Add new audio data to buffer\n  state.audioBuffer.push(audioData);\n  \n  // Calculate total buffer size\n  const totalBufferSize = state.audioBuffer.reduce((size, buf) => size + buf.length, 0);\n  \n  // Process audio if enough data has accumulated (about 0.5 seconds of audio at 16kHz, 16-bit)\n  // This is approximately 16000 samples/sec * 0.5 sec * 2 bytes/sample = 16000 bytes\n  if (totalBufferSize >= 16000 && !state.isProcessing) {\n    state.isProcessing = true;\n    \n    try {\n      // Concatenate all buffers\n      const audioBuffer = Buffer.concat(state.audioBuffer);\n      \n      // Clear the buffer after concatenation\n      state.audioBuffer = [];\n      \n      // Save audio to a temporary file for debugging\n      const tempFilename = `${randomUUID()}.webm`;\n      const filePath = path.join(AUDIO_DIR, tempFilename);\n      fs.writeFileSync(filePath, audioBuffer);\n      console.log(`Saved real-time audio chunk to ${filePath}, size: ${audioBuffer.length} bytes`);\n      \n      // Transcribe the audio\n      console.log(`Transcribing real-time audio chunk of size: ${audioBuffer.length} bytes`);\n      const transcription = await transcribeAudio(audioBuffer, tempFilename);\n      \n      // Process the transcription if we got text\n      if (transcription.text && transcription.text.trim().length > 0) {\n        console.log(`Real-time transcription: \"${transcription.text}\"`);\n        \n        // Add user message to conversation history\n        state.conversationHistory.push({\n          role: 'user',\n          content: transcription.text,\n          timestamp: new Date()\n        });\n        \n        // Send transcription to client for immediate feedback\n        ws.send(JSON.stringify({\n          type: 'transcription',\n          text: transcription.text\n        }));\n        \n        // Stream AI response\n        await streamAITherapistResponse(\n          ws,\n          transcription.text,\n          state.conversationHistory,\n          true,\n          undefined,\n          state.voiceConfig,\n          state.conversationConfig\n        );\n      } else {\n        console.log('No text transcribed from audio chunk');\n      }\n    } catch (error) {\n      console.error('Error processing real-time audio:', error);\n      ws.send(JSON.stringify({\n        type: 'error',\n        message: 'Failed to process audio: ' + (error instanceof Error ? error.message : String(error))\n      }));\n    } finally {\n      state.isProcessing = false;\n      state.lastProcessedTime = Date.now();\n    }\n  }\n}\n\n// Handle WebSocket message for audio processing\nexport async function handleRealTimeAudioMessage(\n  ws: WebSocket,\n  message: any\n): Promise<void> {\n  // Get or create state for this connection\n  let state = audioStates.get(ws);\n  \n  if (!state) {\n    console.error('No state found for WebSocket connection. Initialize first.');\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Session not initialized. Please start a new session.'\n    }));\n    return;\n  }\n  \n  try {\n    // Process different audio data formats\n    let audioBuffer: Buffer;\n    \n    if (message.audio_data && Array.isArray(message.audio_data)) {\n      console.log(`Received audio data array of length: ${message.audio_data.length}`);\n      \n      // Handle int16 format from client\n      if (message.format === 'int16') {\n        // Create a proper Int16Array and then convert to Buffer\n        const int16Samples = new Int16Array(message.audio_data.length);\n        for (let i = 0; i < message.audio_data.length; i++) {\n          int16Samples[i] = message.audio_data[i];\n        }\n        \n        // Convert to buffer ensuring proper byte layout\n        audioBuffer = Buffer.from(int16Samples.buffer);\n        console.log(`Processed int16 audio format, samples: ${message.audio_data.length}, buffer size: ${audioBuffer.length} bytes`);\n      } else {\n        // Legacy format handling - convert float to int16\n        const floatSamples = message.audio_data;\n        const int16Samples = new Int16Array(floatSamples.length);\n        \n        for (let i = 0; i < floatSamples.length; i++) {\n          const sample = Math.max(-1, Math.min(1, floatSamples[i]));\n          int16Samples[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n        }\n        \n        audioBuffer = Buffer.from(int16Samples.buffer);\n        console.log(`Processed float audio data, converted to int16 format: ${audioBuffer.length} bytes`);\n      }\n    } else if (typeof message.audioData === 'string') {\n      // Convert base64 audioData to Buffer\n      audioBuffer = Buffer.from(message.audioData, 'base64');\n      console.log(`Processed base64 audio data: ${audioBuffer.length} bytes`);\n    } else {\n      console.error('No valid audio data found in message');\n      ws.send(JSON.stringify({\n        type: 'error',\n        message: 'Invalid audio data format'\n      }));\n      return;\n    }\n    \n    // Process the audio data\n    await processRealTimeAudio(ws, audioBuffer, state);\n  } catch (error) {\n    console.error('Error handling real-time audio message:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Error processing audio data: ' + (error instanceof Error ? error.message : String(error))\n    }));\n  }\n}\n\n// Initialize real-time audio state\nexport async function initializeRealTimeAudio(\n  ws: WebSocket,\n  userId: string\n): Promise<void> {\n  try {\n    console.log(`Initializing real-time audio for user: ${userId}`);\n    \n    // Load configurations\n    const voiceConfig = await loadVoiceConfig(userId);\n    const conversationConfig = await loadConversationConfig(userId);\n    \n    if (!voiceConfig || !conversationConfig) {\n      console.warn(`Missing configurations for user ${userId}, using defaults`);\n    }\n    \n    // Use defaults if configurations are missing\n    const defaultVoiceConfig: VoiceConfig = {\n      voice: 'shimmer',\n      speed: 1.0,\n      pitch: 1.0,\n      emphasis: 'moderate',\n      prosody: {\n        emotionalRange: 70,\n        questionInflection: 60,\n        pauseDuration: 50\n      }\n    };\n    \n    const defaultConversationConfig: ConversationConfig = {\n      temperature: 0.7,\n      maxTokens: 1000,\n      presencePenalty: 0.6,\n      frequencyPenalty: 0.4,\n      turnTaking: {\n        backchannelFrequency: 50,\n        minSilenceDuration: 500,\n        maxInterruptionGap: 200\n      },\n      responseStyle: {\n        minResponseLength: 50,\n        maxResponseLength: 150,\n        temperature: 0.7,\n        presencePenalty: 0.6,\n        frequencyPenalty: 0.4\n      }\n    };\n    \n    // Create state with configurations\n    const state: RealTimeAudioState = {\n      audioBuffer: [],\n      isProcessing: false,\n      lastProcessedTime: Date.now(),\n      conversationHistory: [],\n      voiceConfig: voiceConfig || defaultVoiceConfig,\n      conversationConfig: conversationConfig || defaultConversationConfig\n    };\n    \n    // Store state for this connection\n    audioStates.set(ws, state);\n    \n    console.log(`Real-time audio initialized for user ${userId}`);\n    ws.send(JSON.stringify({\n      type: 'ready',\n      message: 'Voice session initialized successfully'\n    }));\n  } catch (error) {\n    console.error('Error initializing real-time audio:', error);\n    ws.send(JSON.stringify({\n      type: 'error',\n      message: 'Failed to initialize voice session: ' + (error instanceof Error ? error.message : String(error))\n    }));\n  }\n}\n\n// Clean up real-time audio state when WebSocket closes\nexport function cleanupRealTimeAudio(ws: WebSocket): void {\n  if (audioStates.has(ws)) {\n    console.log('Cleaning up real-time audio state');\n    audioStates.delete(ws);\n  }\n}"}