{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/temp-login-fix.ts"}, "originalCode": "/**\n * Temporary login fix route\n * This provides a direct authentication route to bypass Passport.js issues\n */\n\nimport { Request, Response } from 'express';\nimport { authenticateUser } from './pg-direct';\n\n// Direct login route handler\nexport async function directLogin<PERSON>andler(req: Request, res: Response) {\n  try {\n    const { username, password } = req.body;\n    \n    console.log(`[DIRECT-LOGIN] Login attempt with username: ${username}`);\n    \n    if (!username || !password) {\n      return res.status(400).json({ \n        success: false, \n        message: 'Username and password required'\n      });\n    }\n    \n    const user = await authenticateUser(username, password);\n    \n    if (!user) {\n      console.log(`[DIRECT-LOGIN] Authentication failed for user: ${username}`);\n      return res.status(401).json({ \n        success: false, \n        message: 'Invalid credentials'\n      });\n    }\n    \n    // Manual session handling\n    if (req.session) {\n      // Create authenticated session\n      req.session.user = user;\n      req.session.authenticated = true;\n      \n      console.log(`[DIRECT-LOGIN] Session created with ID: ${req.sessionID}`);\n      console.log(`[DIRECT-LOGIN] Session user:`, user);\n    } else {\n      console.error('[DIRECT-LOGIN] No session object available');\n    }\n    \n    // Return success with user data\n    return res.status(200).json({\n      success: true,\n      user\n    });\n  } catch (error) {\n    console.error('[DIRECT-LOGIN] Error during authentication:', error);\n    return res.status(500).json({ \n      success: false, \n      message: 'Authentication error' \n    });\n  }\n}\n\n// Create admin user for testing\nexport async function createAdminUser() {\n  const { pgPool, hashPassword } = await import('./pg-direct');\n  \n  try {\n    // Check if admin exists\n    const checkResult = await pgPool.query(\n      \"SELECT id FROM users WHERE username = 'admin'\"\n    );\n    \n    if (checkResult.rows.length > 0) {\n      console.log('Admin user already exists');\n      return;\n    }\n    \n    // Create admin user\n    const hashedPassword = await hashPassword('admin123');\n    \n    await pgPool.query(\n      `INSERT INTO users (username, password, name, email, user_role, professional_role) \n       VALUES ($1, $2, $3, $4, $5, $6)`,\n      [\n        'admin',\n        hashedPassword,\n        'Administrator',\n        '<EMAIL>',\n        'admin',\n        'System Administrator'\n      ]\n    );\n    \n    console.log('Admin user created successfully');\n  } catch (error) {\n    console.error('Error creating admin user:', error);\n  }\n}", "modifiedCode": "/**\n * Temporary login fix route\n * This provides a direct authentication route to bypass Passport.js issues\n */\n\nimport { Request, Response } from 'express';\nimport { authenticateUser } from './pg-direct';\n\n// Direct login route handler\nexport async function directLogin<PERSON>andler(req: Request, res: Response) {\n  try {\n    const { username, password } = req.body;\n    \n    console.log(`[DIRECT-LOGIN] Login attempt with username: ${username}`);\n    \n    if (!username || !password) {\n      return res.status(400).json({ \n        success: false, \n        message: 'Username and password required'\n      });\n    }\n    \n    const user = await authenticateUser(username, password);\n    \n    if (!user) {\n      console.log(`[DIRECT-LOGIN] Authentication failed for user: ${username}`);\n      return res.status(401).json({ \n        success: false, \n        message: 'Invalid credentials'\n      });\n    }\n    \n    // Use Passport's login method to properly set up the session\n    req.login(user, (err) => {\n      if (err) {\n        console.error('[DIRECT-LOGIN] Error setting up session:', err);\n        return res.status(500).json({\n          success: false,\n          message: 'Session setup error'\n        });\n      }\n\n      console.log(`[DIRECT-LOGIN] Session created with ID: ${req.sessionID}`);\n      console.log(`[DIRECT-LOGIN] User authenticated:`, user.username);\n\n      // Return success with user data\n      return res.status(200).json({\n        success: true,\n        user\n      });\n    });\n  } catch (error) {\n    console.error('[DIRECT-LOGIN] Error during authentication:', error);\n    return res.status(500).json({ \n      success: false, \n      message: 'Authentication error' \n    });\n  }\n}\n\n// Create admin user for testing\nexport async function createAdminUser() {\n  const { pgPool, hashPassword } = await import('./pg-direct');\n  \n  try {\n    // Check if admin exists\n    const checkResult = await pgPool.query(\n      \"SELECT id FROM users WHERE username = 'admin'\"\n    );\n    \n    if (checkResult.rows.length > 0) {\n      console.log('Admin user already exists');\n      return;\n    }\n    \n    // Create admin user\n    const hashedPassword = await hashPassword('admin123');\n    \n    await pgPool.query(\n      `INSERT INTO users (username, password, name, email, user_role, professional_role) \n       VALUES ($1, $2, $3, $4, $5, $6)`,\n      [\n        'admin',\n        hashedPassword,\n        'Administrator',\n        '<EMAIL>',\n        'admin',\n        'System Administrator'\n      ]\n    );\n    \n    console.log('Admin user created successfully');\n  } catch (error) {\n    console.error('Error creating admin user:', error);\n  }\n}"}