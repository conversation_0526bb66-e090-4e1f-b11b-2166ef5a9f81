{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/openai.ts"}, "originalCode": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst CHAT_MODEL = \"gpt-4o\";\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\nconsole.log(`Using model: ${CHAT_MODEL} for regular calls`);\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n      model: CHAT_MODEL, // newest model as of May 2024\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(\"Using gpt-4o for therapeutic conversation\");\n    const stream = await openai.chat.completions.create({\n      model: \"gpt-4o\", // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n", "modifiedCode": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst CHAT_MODEL = \"gpt-4o\";\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\nconsole.log(`Using model: ${CHAT_MODEL} for regular calls`);\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n      model: CHAT_MODEL, // newest model as of May 2024\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(`Using ${CHAT_MODEL} for therapeutic conversation`);\n    const stream = await openai.chat.completions.create({\n      model: CHAT_MODEL, // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n"}