{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ConfigManagement.tsx"}, "originalCode": "import React, { useState, useEffect } from 'react';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\n\ninterface ConfigManagementProps {\n  userId: string;\n  clientId: string;\n  onConfigChange: (config: { voice: VoiceConfig; conversation: ConversationConfig }) => void;\n}\n\nexport const ConfigManagement: React.FC<ConfigManagementProps> = ({ userId, clientId, onConfigChange }) => {\n  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({\n    voice: \"shimmer\",\n    speed: 1.05,\n    pitch: 1.0,\n    emphasis: \"moderate\",\n    prosody: {\n      emotionalRange: 0.7,\n      questionInflection: 0.8,\n      pauseDuration: 300\n    }\n  });\n\n  const [conversationConfig, setConversationConfig] = useState<ConversationConfig>({\n    turnTaking: {\n      backchannelFrequency: 0.3,\n      minSilenceDuration: 300,\n      maxInterruptionGap: 500\n    },\n    responseStyle: {\n      minResponseLength: 50,\n      maxResponseLength: 150,\n      temperature: 0.8,\n      presencePenalty: 0.1,\n      frequencyPenalty: 0.1\n    }\n  });\n\n  useEffect(() => {\n    // Load configurations for this client\n    const loadConfigurations = async () => {\n      try {\n        const response = await fetch('/api/configs', {\n          headers: {\n            'x-user-id': userId,\n            'x-client-id': clientId\n          }\n        });\n        if (!response.ok) throw new Error('Failed to load configurations');\n        const { voice, conversation } = await response.json();\n        setVoiceConfig(voice);\n        setConversationConfig(conversation);\n      } catch (error) {\n        console.error('Error loading configurations:', error);\n      }\n    };\n\n    loadConfigurations();\n  }, [userId, clientId]);\n\n  const handleVoiceConfigChange = (key: keyof VoiceConfig, value: any) => {\n    const newConfig = { ...voiceConfig, [key]: value };\n    setVoiceConfig(newConfig);\n    onConfigChange({ voice: newConfig, conversation: conversationConfig });\n  };\n\n  const handleConversationConfigChange = (key: keyof ConversationConfig, value: any) => {\n    const newConfig = { ...conversationConfig, [key]: value };\n    setConversationConfig(newConfig);\n    onConfigChange({ voice: voiceConfig, conversation: newConfig });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Voice Configuration */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-lg font-semibold mb-4\">Voice Configuration</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Voice</label>\n            <select\n              value={voiceConfig.voice}\n              onChange={(e) => handleVoiceConfigChange('voice', e.target.value)}\n              className=\"w-full p-2 border rounded\"\n            >\n              <option value=\"shimmer\">Shimmer</option>\n              <option value=\"alloy\">Alloy</option>\n              <option value=\"echo\">Echo</option>\n              <option value=\"fable\">Fable</option>\n              <option value=\"onyx\">Onyx</option>\n              <option value=\"nova\">Nova</option>\n            </select>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Speed</label>\n            <input\n              type=\"range\"\n              min=\"0.5\"\n              max=\"2\"\n              step=\"0.05\"\n              value={voiceConfig.speed}\n              onChange={(e) => handleVoiceConfigChange('speed', parseFloat(e.target.value))}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{voiceConfig.speed.toFixed(2)}x</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Emphasis</label>\n            <select\n              value={voiceConfig.emphasis}\n              onChange={(e) => handleVoiceConfigChange('emphasis', e.target.value)}\n              className=\"w-full p-2 border rounded\"\n            >\n              <option value=\"strong\">Strong</option>\n              <option value=\"moderate\">Moderate</option>\n              <option value=\"subtle\">Subtle</option>\n            </select>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Emotional Range</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={voiceConfig.prosody.emotionalRange}\n              onChange={(e) => handleVoiceConfigChange('prosody', {\n                ...voiceConfig.prosody,\n                emotionalRange: parseFloat(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{(voiceConfig.prosody.emotionalRange * 100).toFixed(0)}%</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Conversation Configuration */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-lg font-semibold mb-4\">Conversation Configuration</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Backchannel Frequency</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={conversationConfig.turnTaking.backchannelFrequency}\n              onChange={(e) => handleConversationConfigChange('turnTaking', {\n                ...conversationConfig.turnTaking,\n                backchannelFrequency: parseFloat(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{(conversationConfig.turnTaking.backchannelFrequency * 100).toFixed(0)}%</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Min Silence Duration</label>\n            <input\n              type=\"range\"\n              min=\"100\"\n              max=\"1000\"\n              step=\"50\"\n              value={conversationConfig.turnTaking.minSilenceDuration}\n              onChange={(e) => handleConversationConfigChange('turnTaking', {\n                ...conversationConfig.turnTaking,\n                minSilenceDuration: parseInt(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{conversationConfig.turnTaking.minSilenceDuration}ms</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Response Temperature</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={conversationConfig.responseStyle.temperature}\n              onChange={(e) => handleConversationConfigChange('responseStyle', {\n                ...conversationConfig.responseStyle,\n                temperature: parseFloat(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{conversationConfig.responseStyle.temperature.toFixed(1)}</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Max Response Length</label>\n            <input\n              type=\"range\"\n              min=\"50\"\n              max=\"300\"\n              step=\"10\"\n              value={conversationConfig.responseStyle.maxResponseLength}\n              onChange={(e) => handleConversationConfigChange('responseStyle', {\n                ...conversationConfig.responseStyle,\n                maxResponseLength: parseInt(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{conversationConfig.responseStyle.maxResponseLength} words</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}; ", "modifiedCode": "import React, { useState, useEffect } from 'react';\nimport { VoiceConfig, ConversationConfig } from '@shared/schema';\n\ninterface ConfigManagementProps {\n  userId: string;\n  clientId: string;\n  onConfigChange: (config: { voice: VoiceConfig; conversation: ConversationConfig }) => void;\n}\n\nexport const ConfigManagement: React.FC<ConfigManagementProps> = ({ userId, clientId, onConfigChange }) => {\n  const [voiceConfig, setVoiceConfig] = useState<VoiceConfig>({\n    voice: \"shimmer\",\n    speed: 1.05,\n    pitch: 1.0,\n    emphasis: \"moderate\",\n    prosody: {\n      emotionalRange: 0.7,\n      questionInflection: 0.8,\n      pauseDuration: 300\n    }\n  });\n\n  const [conversationConfig, setConversationConfig] = useState<ConversationConfig>({\n    temperature: 0.7,\n    maxTokens: 1000,\n    presencePenalty: 0.1,\n    frequencyPenalty: 0.1,\n    turnTaking: {\n      backchannelFrequency: 0.3,\n      minSilenceDuration: 300,\n      maxInterruptionGap: 500\n    },\n    responseStyle: {\n      minResponseLength: 50,\n      maxResponseLength: 150,\n      temperature: 0.8,\n      presencePenalty: 0.1,\n      frequencyPenalty: 0.1\n    }\n  });\n\n  useEffect(() => {\n    // Load configurations for this client\n    const loadConfigurations = async () => {\n      try {\n        const response = await fetch('/api/configs', {\n          headers: {\n            'x-user-id': userId,\n            'x-client-id': clientId\n          }\n        });\n        if (!response.ok) throw new Error('Failed to load configurations');\n        const { voice, conversation } = await response.json();\n        setVoiceConfig(voice);\n        setConversationConfig(conversation);\n      } catch (error) {\n        console.error('Error loading configurations:', error);\n      }\n    };\n\n    loadConfigurations();\n  }, [userId, clientId]);\n\n  const handleVoiceConfigChange = (key: keyof VoiceConfig, value: any) => {\n    const newConfig = { ...voiceConfig, [key]: value };\n    setVoiceConfig(newConfig);\n    onConfigChange({ voice: newConfig, conversation: conversationConfig });\n  };\n\n  const handleConversationConfigChange = (key: keyof ConversationConfig, value: any) => {\n    const newConfig = { ...conversationConfig, [key]: value };\n    setConversationConfig(newConfig);\n    onConfigChange({ voice: voiceConfig, conversation: newConfig });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Voice Configuration */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-lg font-semibold mb-4\">Voice Configuration</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Voice</label>\n            <select\n              value={voiceConfig.voice}\n              onChange={(e) => handleVoiceConfigChange('voice', e.target.value)}\n              className=\"w-full p-2 border rounded\"\n            >\n              <option value=\"shimmer\">Shimmer</option>\n              <option value=\"alloy\">Alloy</option>\n              <option value=\"echo\">Echo</option>\n              <option value=\"fable\">Fable</option>\n              <option value=\"onyx\">Onyx</option>\n              <option value=\"nova\">Nova</option>\n            </select>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Speed</label>\n            <input\n              type=\"range\"\n              min=\"0.5\"\n              max=\"2\"\n              step=\"0.05\"\n              value={voiceConfig.speed}\n              onChange={(e) => handleVoiceConfigChange('speed', parseFloat(e.target.value))}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{voiceConfig.speed.toFixed(2)}x</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Emphasis</label>\n            <select\n              value={voiceConfig.emphasis}\n              onChange={(e) => handleVoiceConfigChange('emphasis', e.target.value)}\n              className=\"w-full p-2 border rounded\"\n            >\n              <option value=\"strong\">Strong</option>\n              <option value=\"moderate\">Moderate</option>\n              <option value=\"subtle\">Subtle</option>\n            </select>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Emotional Range</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={voiceConfig.prosody.emotionalRange}\n              onChange={(e) => handleVoiceConfigChange('prosody', {\n                ...voiceConfig.prosody,\n                emotionalRange: parseFloat(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{(voiceConfig.prosody.emotionalRange * 100).toFixed(0)}%</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Conversation Configuration */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h3 className=\"text-lg font-semibold mb-4\">Conversation Configuration</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Backchannel Frequency</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={conversationConfig.turnTaking.backchannelFrequency}\n              onChange={(e) => handleConversationConfigChange('turnTaking', {\n                ...conversationConfig.turnTaking,\n                backchannelFrequency: parseFloat(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{(conversationConfig.turnTaking.backchannelFrequency * 100).toFixed(0)}%</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Min Silence Duration</label>\n            <input\n              type=\"range\"\n              min=\"100\"\n              max=\"1000\"\n              step=\"50\"\n              value={conversationConfig.turnTaking.minSilenceDuration}\n              onChange={(e) => handleConversationConfigChange('turnTaking', {\n                ...conversationConfig.turnTaking,\n                minSilenceDuration: parseInt(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{conversationConfig.turnTaking.minSilenceDuration}ms</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Response Temperature</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"1\"\n              step=\"0.1\"\n              value={conversationConfig.responseStyle.temperature}\n              onChange={(e) => handleConversationConfigChange('responseStyle', {\n                ...conversationConfig.responseStyle,\n                temperature: parseFloat(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{conversationConfig.responseStyle.temperature.toFixed(1)}</span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-gray-700\">Max Response Length</label>\n            <input\n              type=\"range\"\n              min=\"50\"\n              max=\"300\"\n              step=\"10\"\n              value={conversationConfig.responseStyle.maxResponseLength}\n              onChange={(e) => handleConversationConfigChange('responseStyle', {\n                ...conversationConfig.responseStyle,\n                maxResponseLength: parseInt(e.target.value)\n              })}\n              className=\"w-full\"\n            />\n            <span className=\"text-sm text-gray-500\">{conversationConfig.responseStyle.maxResponseLength} words</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}; "}