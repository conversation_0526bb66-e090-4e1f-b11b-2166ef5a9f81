{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AdminAITest.tsx"}, "originalCode": "import React, { useState, useRef, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from \"@/components/ui/card\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"@/components/ui/tabs\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Mic, MicOff, Download, RefreshCw, Zap, Volume2, Settings } from \"lucide-react\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Slider } from \"@/components/ui/slider\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { debugManager } from \"@/lib/debug-config\";\n\n// WebSocket URL using the same protocol and host as the current page\nfunction getWebSocketUrl() {\n  const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n  return `${protocol}//${window.location.host}/ws`;\n}\n\nconst AdminAITest = () => {\n  const { toast } = useToast();\n  const [sessionActive, setSessionActive] = useState(false);\n  const [transcript, setTranscript] = useState<Array<{ speaker: string; text: string; timestamp: Date }>>([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState(\"disconnected\");\n  const [responseLog, setResponseLog] = useState<string[]>([]);\n  const [selectedVoice, setSelectedVoice] = useState(\"shimmer\");\n  const [voiceSpeed, setVoiceSpeed] = useState(1.05);\n  const [temperature, setTemperature] = useState(0.7);\n  const [loadingStatus, setLoadingStatus] = useState(\"\");\n  const [summary, setSummary] = useState<string | null>(null);\n  const [audioInputLevel, setAudioInputLevel] = useState(0);\n  const [audioOutputPlaying, setAudioOutputPlaying] = useState(false);\n  const [micDevices, setMicDevices] = useState<MediaDeviceInfo[]>([]);\n  const [selectedMicDevice, setSelectedMicDevice] = useState<string>(\"\");\n  const [debugEnabled, setDebugEnabled] = useState(debugManager.isEnabled);\n  \n  // Audio diagnostics state\n  const [audioSupported, setAudioSupported] = useState(false);\n  const [micPermissionGranted, setMicPermissionGranted] = useState(false);\n  const [audioContextState, setAudioContextState] = useState<string>(\"suspended\");\n  const [audioStreamActive, setAudioStreamActive] = useState(false);\n  \n  // References\n  const wsRef = useRef<WebSocket | null>(null);\n  const transcriptEndRef = useRef<HTMLDivElement>(null);\n  const testAudioRef = useRef<HTMLAudioElement | null>(null);\n  \n  // Audio context for microphone (16kHz for input)\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const audioStreamRef = useRef<MediaStream | null>(null);\n  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);\n  const analyzerRef = useRef<AnalyserNode | null>(null);\n  const levelCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Client-side speech detection and manual VAD\n  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const [isUserSpeaking, setIsUserSpeaking] = useState(false);\n  const [speechDetectionEnabled, setSpeechDetectionEnabled] = useState(true);\n  const speechBufferRef = useRef<string[]>([]);\n  const speechStartTimeRef = useRef<number | null>(null);\n  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Audio playback context (24kHz for OpenAI output)\n  const playbackContextRef = useRef<AudioContext | null>(null);\n  const audioQueueRef = useRef<AudioBuffer[]>([]);\n  const isPlayingRef = useRef(false);\n  const nextPlayTimeRef = useRef(0);\n  \n  // Transcript accumulation\n  const transcriptAccumulator = useRef<{[speaker: string]: string}>({});\n  const transcriptTimeoutRef = useRef<{[speaker: string]: NodeJS.Timeout}>({});\n\n  // Initialize WebSocket and start session\n  const startInteractiveSession = async () => {\n    setLoadingStatus(\"Connecting to server...\");\n    \n    // Close existing connection if any\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      wsRef.current.close();\n    }\n    \n    try {\n      // Initialize audio first to make sure we have microphone access\n      const audioInitialized = await initializeAudioRecording();\n      \n      if (!audioInitialized) {\n        setLoadingStatus(\"\");\n        toast({\n          title: \"Microphone access required\",\n          description: \"Please allow microphone access to start the voice therapy session.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n      \n      // Initialize playback audio context (required for hearing AI responses)\n      const playbackReady = await initializePlaybackContext();\n      if (!playbackReady) {\n        addToLog('⚠️ Audio playback not ready - you may not hear AI responses');\n        toast({\n          title: \"Audio Warning\",\n          description: \"Audio playback may not work. Try 'Test Audio Output' first if you can't hear responses.\",\n          variant: \"destructive\",\n        });\n      }\n      \n      // Create WebSocket connection\n      const ws = new WebSocket(getWebSocketUrl());\n      wsRef.current = ws;\n\n      ws.onopen = () => {\n        console.log(\"WebSocket connection established\");\n        setConnectionStatus(\"connected\");\n        addToLog(\"WebSocket connection established\");\n        \n        // Send initial configuration message with Realtime API enabled\n        const startMessage = {\n          type: \"start\",\n          userId: \"1\",\n          clientId: \"1\",\n          useRealtimeAPI: true, // Enable OpenAI Realtime API\n          mode: \"realtime\",\n          behavior: {\n            model: \"gpt-4o-realtime-preview-2025-06-03\",\n            temperature: temperature,\n            voice: {\n              voice: selectedVoice,\n              speed: voiceSpeed\n            }\n          },\n          instructions: \"You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts. Keep responses concise and natural for voice conversation.\"\n        };\n        \n        ws.send(JSON.stringify(startMessage));\n        addToLog(\"🚀 STARTING SESSION: Real-time conversation with server-side VAD\");\n        addToLog(`🎯 CONFIG: Model=${startMessage.behavior.model}, Voice=${startMessage.behavior.voice.voice}, Temp=${startMessage.behavior.temperature}`);\n        setLoadingStatus(\"Initializing session...\");\n      };\n\n              ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          debugManager.log('websocketEvents', \"WebSocket message received:\", data);\n\n          // Enhanced debug logging for real-time conversation events\n          const logRealtimeEvent = (eventType: string, details?: string, isImportant = false) => {\n            const prefix = isImportant ? \"🔥\" : \"📡\";\n            const message = details ? `${prefix} ${eventType}: ${details}` : `${prefix} ${eventType}`;\n            debugManager.log('realTimeConversation', message);\n            if (debugManager.isEnabled) {\n              addToLog(message);\n            }\n          };\n\n          // Log all received messages if debug is enabled\n          if (debugManager.websocketEvents) {\n            addToLog(`📡 RECEIVED: ${data.type} ${data.message ? '- ' + data.message : ''}`);\n          }\n\n          // Log all important OpenAI Realtime API events\n          switch (data.type) {\n            case \"session.created\":\n              logRealtimeEvent(\"SESSION CREATED\", `ID: ${data.session?.id || 'unknown'}`, true);\n              break;\n            case \"session.updated\":\n              const vadConfig = data.session?.turn_detection;\n              const vadType = vadConfig?.type || 'none';\n              logRealtimeEvent(\"SESSION CONFIGURED\", `VAD: ${vadType}, Model: ${data.session?.model || 'unknown'}`, true);\n              break;\n            case \"ready\":\n              logRealtimeEvent(\"SESSION READY\", \"Real-time conversation active\", true);\n              break;\n            case \"input_audio_buffer.speech_started\":\n              logRealtimeEvent(\"🎤 SPEECH STARTED\", \"OpenAI detected you speaking\", true);\n              toast({\n                title: \"🎤 Speech Detected\",\n                description: \"Keep talking, I'm listening...\",\n              });\n              break;\n            case \"input_audio_buffer.speech_stopped\":\n              logRealtimeEvent(\"🔇 SPEECH STOPPED\", \"OpenAI detected silence\", true);\n              toast({\n                title: \"🔇 Processing Speech\",\n                description: \"Transcribing and generating response...\",\n              });\n              break;\n            case \"input_audio_buffer.committed\":\n              logRealtimeEvent(\"AUDIO COMMITTED\", \"Audio buffer sent for processing\");\n              break;\n            case \"conversation.item.created\":\n              const itemType = data.item?.type || 'unknown';\n              logRealtimeEvent(\"CONVERSATION ITEM\", `Created: ${itemType}`);\n              break;\n            case \"conversation.item.input_audio_transcription.completed\":\n              const transcript = data.transcript || '';\n              logRealtimeEvent(\"🎯 USER TRANSCRIBED\", `\"${transcript}\"`, true);\n              break;\n            case \"response.created\":\n              logRealtimeEvent(\"🤖 AI RESPONSE STARTED\", \"Generating response\", true);\n              break;\n            case \"response.output_item.added\":\n              logRealtimeEvent(\"RESPONSE ITEM\", `Added: ${data.item?.type || 'unknown'}`);\n              break;\n            case \"response.content_part.added\":\n              logRealtimeEvent(\"CONTENT PART\", `Type: ${data.part?.type || 'unknown'}`);\n              break;\n            case \"response.audio.delta\":\n              logRealtimeEvent(\"🔊 AUDIO CHUNK\", `${data.delta?.length || 0} chars`);\n              // CRITICAL: Process audio for playback\n              addToLog(`🔊 AUDIO DELTA: ${data.delta ? data.delta.length + ' chars' : 'NO DELTA'}`);\n              console.log('🔊 PROCESSING AUDIO DELTA:', data.delta ? data.delta.length : 'NO DELTA');\n              if (data.delta) {\n                console.log('🔊 CALLING queueAudioChunk with', data.delta.length, 'chars');\n                queueAudioChunk(data.delta);\n              } else {\n                addToLog(`❌ AUDIO DELTA: No delta data in response.audio.delta message`);\n                console.error('❌ NO DELTA DATA IN AUDIO MESSAGE:', data);\n              }\n              break;\n            case \"response.audio.done\":\n              logRealtimeEvent(\"🔊 AUDIO COMPLETE\", \"AI audio generation finished\");\n              break;\n            case \"response.audio_transcript.delta\":\n              // Don't log deltas to avoid spam\n              break;\n            case \"response.audio_transcript.done\":\n              const aiTranscript = data.transcript || '';\n              logRealtimeEvent(\"🤖 AI TRANSCRIBED\", `\"${aiTranscript}\"`, true);\n              break;\n            case \"response.done\":\n              logRealtimeEvent(\"✅ RESPONSE COMPLETE\", \"AI finished responding\", true);\n              break;\n            case \"error\":\n              logRealtimeEvent(\"❌ ERROR\", data.message || 'Unknown error', true);\n              break;\n            case \"rate_limits.updated\":\n              logRealtimeEvent(\"RATE LIMITS\", `Remaining: ${JSON.stringify(data.rate_limits || {})}`);\n              break;\n            default:\n              // Log other events with less emphasis\n              logRealtimeEvent(`OTHER: ${data.type}`, data.message || '');\n              break;\n          }\n          \n          if (data.type === \"ready\") {\n            setSessionActive(true);\n            setLoadingStatus(\"\");\n\n            addToLog(\"✅ SESSION ACTIVE: Microphone streaming enabled, speak naturally\");\n            addToLog(\"💡 INSTRUCTIONS: Just speak - OpenAI will detect when you start/stop talking\");\n            setIsRecording(true);\n          }\n\n          // CRITICAL FIX: Also activate session on session.created or response.audio.delta\n          if (data.type === \"session.created\" || data.type === \"response.audio.delta\") {\n            if (!sessionActive) {\n              addToLog(\"🔧 FORCE ACTIVATING SESSION: Detected working OpenAI connection\");\n              setSessionActive(true);\n              setIsRecording(true);\n              setLoadingStatus(\"\");\n            }\n            \n            toast({\n              title: \"Session started\",\n              description: \"Voice recognition is active. Start speaking.\",\n            });\n          } else if (data.type === \"transcription\") {\n            // Add user speech to transcript\n            if (data.text) {\n              addToTranscript(\"You\", data.text);\n            }\n          } else if (data.type === \"conversation.item.input_audio_transcription.completed\") {\n            // OpenAI Realtime API - user speech transcription\n            const text = data.transcript || \"\";\n            if (text) {\n              addToTranscript(\"You\", text);\n            }\n          } else if (data.type === \"input_audio_buffer.speech_started\") {\n            // OpenAI Realtime API - speech detection started\n            addToLog(\"🎤 User speech started\");\n            toast({\n              title: \"Speech Detected\",\n              description: \"Listening to your voice...\",\n            });\n          } else if (data.type === \"input_audio_buffer.speech_stopped\") {\n            // OpenAI Realtime API - speech detection stopped\n            addToLog(\"🔇 User speech stopped\");\n            toast({\n              title: \"Processing Speech\",\n              description: \"Transcribing and generating response...\",\n            });\n          } else if (data.type === \"response.audio_transcript.delta\") {\n            // Ignore fragments - we'll use the complete transcript from done event\n            // This prevents fragmented display\n          } else if (data.type === \"response.audio_transcript.done\") {\n            // Use only the complete transcript from OpenAI\n            if (data.transcript && data.transcript.trim()) {\n              addToTranscript(\"AI\", data.transcript.trim());\n            }\n          } else if (data.type === \"response.done\") {\n            // OpenAI Realtime API - response completed\n            addToLog(\"Response completed\");\n          } else if (data.type === \"assistant_response\" || data.type === \"chunk\" || data.type === \"text\") {\n            // Legacy API response handling\n            const content = data.content || data.text || \"\";\n            if (content) {\n              addToTranscript(\"AI\", content);\n            }\n          } else if (data.type === \"audio_chunk\") {\n            // Handle audio playback (legacy)\n            if (data.audioData) {\n              queueAudioChunk(data.audioData);\n            }\n          } else if (data.type === \"test_audio_response\") {\n            // Handle test audio response\n            if (data.audioUrl) {\n              playTestAudio(data.audioUrl);\n            }\n            setAudioOutputPlaying(false);\n          } else if (data.type === \"error\") {\n            toast({\n              title: \"Error\",\n              description: data.message || \"Something went wrong\",\n              variant: \"destructive\",\n            });\n          } else if (data.type === \"summary\") {\n            // Display conversation summary\n            setSummary(data.content || \"No summary available\");\n          }\n        } catch (error) {\n          console.error(\"Error parsing WebSocket message:\", error);\n        }\n      };\n\n      ws.onclose = () => {\n        console.log(\"WebSocket connection closed\");\n        setConnectionStatus(\"disconnected\");\n        setSessionActive(false);\n        setIsRecording(false);\n        stopAudioRecording();\n        addToLog(\"WebSocket connection closed\");\n      };\n\n      ws.onerror = (error) => {\n        console.error(\"WebSocket error:\", error);\n        setConnectionStatus(\"error\");\n        toast({\n          title: \"Connection error\",\n          description: \"WebSocket connection failed\",\n          variant: \"destructive\",\n        });\n      };\n    } catch (error) {\n      console.error(\"Error starting session:\", error);\n      setLoadingStatus(\"\");\n      toast({\n        title: \"Session error\",\n        description: error instanceof Error ? error.message : \"Failed to start session\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Auto-scroll to bottom when transcript updates\n  useEffect(() => {\n    if (transcriptEndRef.current) {\n      transcriptEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n    }\n  }, [transcript]);\n\n  // Start microphone level monitoring on mount\n  useEffect(() => {\n    startMicrophoneLevelMonitoring();\n    return () => {\n      // Cleanup on unmount\n      if (levelCheckIntervalRef.current) {\n        clearInterval(levelCheckIntervalRef.current);\n      }\n    };\n  }, []);\n\n  // Initialize playback audio context for 24kHz OpenAI audio\n  const initializePlaybackContext = async () => {\n    try {\n      console.log('🔊 Initializing playback context...');\n      addToLog('🔊 Initializing audio playback context');\n      \n      if (!playbackContextRef.current) {\n        // Create AudioContext with proper error handling\n        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;\n        if (!AudioContextClass) {\n          throw new Error('AudioContext not supported in this browser');\n        }\n        \n        playbackContextRef.current = new AudioContextClass({\n          sampleRate: 24000 // OpenAI Realtime API uses 24kHz\n        });\n        \n        console.log('🔊 AudioContext created, state:', playbackContextRef.current.state);\n        addToLog(`🔊 AudioContext created (${playbackContextRef.current.sampleRate}Hz, state: ${playbackContextRef.current.state})`);\n      }\n      \n      // Resume if needed (required for user interaction)\n      if (playbackContextRef.current.state === 'suspended') {\n        console.log('🔊 Resuming suspended AudioContext...');\n        addToLog('🔊 Resuming suspended AudioContext');\n        await playbackContextRef.current.resume();\n        console.log('🔊 AudioContext resumed, new state:', playbackContextRef.current.state);\n        addToLog(`🔊 AudioContext resumed (state: ${playbackContextRef.current.state})`);\n      }\n      \n      if (playbackContextRef.current.state === 'running') {\n        console.log('✅ Playback context ready!');\n        addToLog('✅ Playback context ready');\n        return true;\n      } else {\n        console.warn('⚠️ AudioContext state:', playbackContextRef.current.state);\n        addToLog(`⚠️ AudioContext state: ${playbackContextRef.current.state}`);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error initializing playback context:', error);\n      addToLog(`❌ Playback context failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      toast({\n        title: \"Audio Playback Error\",\n        description: \"Failed to initialize audio playback. Try clicking 'Test Audio Output' first.\",\n        variant: \"destructive\",\n      });\n      \n      return false;\n    }\n  };\n\n  // Add text to transcript display\n  const addToTranscript = (speaker: string, text: string) => {\n    setTranscript(prev => [...prev, {\n      speaker,\n      text,\n      timestamp: new Date()\n    }]);\n  };\n\n  // Accumulate transcript deltas and flush complete phrases\n  const accumulateTranscript = (speaker: string, delta: string) => {\n    // Clear any existing timeout for this speaker\n    if (transcriptTimeoutRef.current[speaker]) {\n      clearTimeout(transcriptTimeoutRef.current[speaker]);\n    }\n\n    // Accumulate the delta\n    if (!transcriptAccumulator.current[speaker]) {\n      transcriptAccumulator.current[speaker] = '';\n    }\n    transcriptAccumulator.current[speaker] += delta;\n\n    // Set a longer timeout to ensure complete sentences are accumulated\n    transcriptTimeoutRef.current[speaker] = setTimeout(() => {\n      const accumulatedText = transcriptAccumulator.current[speaker];\n      if (accumulatedText && accumulatedText.trim()) {\n        addToTranscript(speaker, accumulatedText.trim());\n        transcriptAccumulator.current[speaker] = '';\n      }\n    }, 1000); // 1 second delay to accumulate complete sentences\n  };\n\n  // Add message to response log\n  const addToLog = (message: string) => {\n    if (debugManager.isEnabled) {\n      setResponseLog(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`].slice(-20));\n    }\n  };\n  \n  // Queue audio chunk for proper sequential playback\n  const queueAudioChunk = async (audioData: string) => {\n    try {\n      console.log('🔊 QUEUE AUDIO CHUNK CALLED:', audioData.length, 'chars');\n      addToLog(`🔊 AI AUDIO CHUNK: ${audioData.length} chars (${(audioData.length * 0.75 / 1024).toFixed(1)}KB)`);\n      console.log('🔊 queueAudioChunk function executing...');\n\n      if (!playbackContextRef.current) {\n        console.log('🔊 Initializing playback context...');\n        await initializePlaybackContext();\n      }\n\n      if (!playbackContextRef.current) {\n        console.error('❌ No playback context available');\n        addToLog('❌ PLAYBACK ERROR: No audio context - cannot play AI voice');\n        return;\n      }\n\n      // Convert base64 to ArrayBuffer (PCM16 format)\n      const binaryString = atob(audioData);\n      const bytes = new Uint8Array(binaryString.length);\n      for (let i = 0; i < binaryString.length; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n      }\n      \n      console.log('🔊 Decoded audio data:', bytes.length, 'bytes');\n      addToLog(`🔊 Decoded ${bytes.length} bytes of audio data`);\n      \n      // Create audio buffer for 24kHz PCM16\n      const arrayBuffer = bytes.buffer.slice(bytes.byteOffset, bytes.byteOffset + bytes.byteLength);\n      const audioBuffer = playbackContextRef.current.createBuffer(1, arrayBuffer.byteLength / 2, 24000);\n      const channelData = audioBuffer.getChannelData(0);\n      \n      // Convert Int16 to Float32\n      const int16Array = new Int16Array(arrayBuffer);\n      for (let i = 0; i < int16Array.length; i++) {\n        channelData[i] = int16Array[i] / 32768.0; // Convert to -1.0 to 1.0 range\n      }\n      \n      console.log('🔊 Audio buffer created:', audioBuffer.length, 'samples,', audioBuffer.duration.toFixed(3), 'seconds');\n      addToLog(`🔊 Buffer: ${audioBuffer.length} samples, ${audioBuffer.duration.toFixed(3)}s`);\n      \n      // Add to queue\n      audioQueueRef.current.push(audioBuffer);\n      console.log('🔊 Queue size:', audioQueueRef.current.length);\n      addToLog(`📋 Audio queue size: ${audioQueueRef.current.length}`);\n      \n      // Start playing if not already playing\n      if (!isPlayingRef.current) {\n        console.log('🔊 Starting audio playback...');\n        addToLog('▶️ AI SPEAKING: Starting voice playback');\n        setAudioOutputPlaying(true);\n        playNextInQueue();\n      }\n    } catch (error) {\n      console.error('❌ Error queueing audio chunk:', error);\n      addToLog(`❌ Audio error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  };\n\n  // Play the next audio buffer in the queue\n  const playNextInQueue = () => {\n    if (!playbackContextRef.current || audioQueueRef.current.length === 0) {\n      isPlayingRef.current = false;\n      setAudioOutputPlaying(false);\n      console.log('🔊 Audio playback complete - queue empty');\n      addToLog('⏹️ AI FINISHED SPEAKING: Voice playback complete');\n      return;\n    }\n\n    isPlayingRef.current = true;\n    const audioBuffer = audioQueueRef.current.shift()!;\n    \n    console.log('🔊 Playing audio buffer:', audioBuffer.duration.toFixed(3), 'seconds');\n    addToLog(`▶️ Playing ${audioBuffer.duration.toFixed(3)}s of audio`);\n    \n    const source = playbackContextRef.current.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(playbackContextRef.current.destination);\n    \n    // Calculate when this should start relative to the audio timeline\n    const currentTime = playbackContextRef.current.currentTime;\n    const startTime = Math.max(currentTime, nextPlayTimeRef.current);\n    \n    source.start(startTime);\n    \n    // Calculate when the next audio should start\n    nextPlayTimeRef.current = startTime + audioBuffer.duration;\n    \n    // When this source ends, play the next one\n    source.onended = () => {\n      console.log('🔊 Audio chunk finished, playing next...');\n      playNextInQueue();\n    };\n  };\n  \n  // Play test audio from URL\n  const playTestAudio = (audioUrl: string) => {\n    try {\n      if (!testAudioRef.current) {\n        testAudioRef.current = new Audio();\n      }\n      \n      testAudioRef.current.src = audioUrl;\n      testAudioRef.current.play().catch(console.error);\n      \n      toast({\n        title: \"Test Audio Playing\",\n        description: \"If you can hear this, your audio output is working correctly.\",\n      });\n    } catch (error) {\n      console.error('Error playing test audio:', error);\n      toast({\n        title: \"Audio Playback Error\",\n        description: \"Failed to play test audio.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Fetch available microphone devices\n  const fetchMicrophoneDevices = async () => {\n    try {\n      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {\n        throw new Error(\"Media devices API not supported in this browser\");\n      }\n      \n      // Get permission first (required to see device labels)\n      await navigator.mediaDevices.getUserMedia({ audio: true });\n      \n      // List available devices\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const audioInputDevices = devices.filter(device => device.kind === 'audioinput');\n      \n      console.log(\"Available audio input devices:\", audioInputDevices);\n      setMicDevices(audioInputDevices);\n      \n      // Select the default device if available\n      if (audioInputDevices.length > 0) {\n        setSelectedMicDevice(audioInputDevices[0].deviceId);\n      }\n      \n      return audioInputDevices.length > 0;\n    } catch (error) {\n      console.error(\"Error fetching microphone devices:\", error);\n      toast({\n        title: \"Microphone Error\",\n        description: \"Failed to detect microphone devices. Please ensure microphone permissions are granted.\",\n        variant: \"destructive\",\n      });\n      return false;\n    }\n  };\n  \n  // Test audio recording setup\n  const testAudioRecording = async () => {\n    addToLog(\"Testing audio recording setup...\");\n    \n    try {\n      // 1. Check API support\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\"getUserMedia API not supported\");\n      }\n      addToLog(\"✓ getUserMedia API supported\");\n      setAudioSupported(true);\n      \n      // 2. Request microphone permission\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 24000\n        } \n      });\n      addToLog(\"✓ Microphone permission granted\");\n      setMicPermissionGranted(true);\n      setAudioStreamActive(true);\n      \n      // 3. Test audio context\n      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({\n        sampleRate: 24000\n      });\n      addToLog(`✓ Audio context created (sample rate: ${audioContext.sampleRate}Hz)`);\n      setAudioContextState(audioContext.state);\n      \n      // 4. Test audio processing\n      const source = audioContext.createMediaStreamSource(stream);\n      const analyzer = audioContext.createAnalyser();\n      analyzer.fftSize = 256;\n      source.connect(analyzer);\n      addToLog(\"✓ Audio analyzer connected\");\n      \n      // 5. Test level detection for 3 seconds\n      let maxLevel = 0;\n      const bufferLength = analyzer.frequencyBinCount;\n      const dataArray = new Uint8Array(bufferLength);\n      \n      const testDuration = 3000; // 3 seconds\n      const startTime = Date.now();\n      \n      const levelTest = () => {\n        if (Date.now() - startTime < testDuration) {\n          analyzer.getByteFrequencyData(dataArray);\n          let sum = 0;\n          for (let i = 0; i < bufferLength; i++) {\n            sum += dataArray[i];\n          }\n          const level = sum / bufferLength / 255;\n          maxLevel = Math.max(maxLevel, level);\n          setAudioInputLevel(level);\n          \n          requestAnimationFrame(levelTest);\n        } else {\n          // Test complete\n          setAudioInputLevel(0);\n          stream.getTracks().forEach(track => track.stop());\n          audioContext.close();\n          setAudioStreamActive(false);\n          \n          addToLog(`✓ Audio test complete (max level: ${(maxLevel * 100).toFixed(1)}%)`);\n          \n          if (maxLevel > 0.01) {\n            addToLog(\"✓ Audio input is working correctly\");\n            toast({\n              title: \"Audio Test Passed\",\n              description: `Microphone is working. Max level detected: ${(maxLevel * 100).toFixed(1)}%`,\n            });\n          } else {\n            addToLog(\"⚠ Low audio input detected - check microphone\");\n            toast({\n              title: \"Audio Test Warning\",\n              description: \"Low audio input detected. Please check your microphone volume.\",\n              variant: \"destructive\",\n            });\n          }\n        }\n      };\n      \n      addToLog(\"Testing audio input for 3 seconds - please speak...\");\n      toast({\n        title: \"Audio Test\",\n        description: \"Testing microphone for 3 seconds. Please speak into your microphone.\",\n      });\n      \n      // Resume audio context if needed\n      if (audioContext.state === 'suspended') {\n        await audioContext.resume();\n      }\n      \n      levelTest();\n      \n    } catch (error) {\n      console.error(\"Audio test failed:\", error);\n      addToLog(`✗ Audio test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      toast({\n        title: \"Audio Test Failed\",\n        description: error instanceof Error ? error.message : \"Failed to test audio recording\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Test WebSocket connection\n  const testWebSocketConnection = () => {\n    addToLog(\"Testing WebSocket connection...\");\n    \n    const testWs = new WebSocket(getWebSocketUrl());\n    \n    testWs.onopen = () => {\n      addToLog(\"✓ WebSocket connection successful\");\n      testWs.send(JSON.stringify({ type: \"ping\" }));\n    };\n    \n    testWs.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        addToLog(`✓ WebSocket message received: ${data.type}`);\n      } catch (error) {\n        addToLog(`✓ WebSocket raw message: ${event.data}`);\n      }\n      testWs.close();\n    };\n    \n    testWs.onclose = () => {\n      addToLog(\"✓ WebSocket test complete\");\n      toast({\n        title: \"WebSocket Test\",\n        description: \"WebSocket connection test completed successfully\",\n      });\n    };\n    \n    testWs.onerror = (error) => {\n      addToLog(\"✗ WebSocket connection failed\");\n      toast({\n        title: \"WebSocket Test Failed\",\n        description: \"Failed to connect to WebSocket server\",\n        variant: \"destructive\",\n      });\n    };\n  };\n\n  // Generate test audio from OpenAI\n  const generateTestAudio = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      addToLog(\"🎤 Requesting test audio generation...\");\n      \n      wsRef.current.send(JSON.stringify({\n        type: \"generate_test_audio\",\n        text: \"Hello, this is a test of the voice system. I am speaking clearly so you can hear me and test the audio processing.\"\n      }));\n      \n      toast({\n        title: \"Generating Test Audio\",\n        description: \"OpenAI is generating test speech audio...\",\n      });\n    } else {\n      toast({\n        title: \"Not Connected\",\n        description: \"Please start a session first\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Test complete round-trip audio flow\n  const testRoundTripAudio = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      addToLog(\"🔄 Starting round-trip audio test...\");\n      \n      wsRef.current.send(JSON.stringify({\n        type: \"test_round_trip\"\n      }));\n      \n      toast({\n        title: \"Round-Trip Test\",\n        description: \"Testing complete audio generation and processing flow...\",\n      });\n    } else {\n      toast({\n        title: \"Not Connected\",\n        description: \"Please start a session first\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Enhanced microphone level monitoring\n  const startMicrophoneLevelMonitoring = (forceStart: boolean = false) => {\n    addToLog(`Attempting to start microphone monitoring: recording=${isRecording}, forceStart=${forceStart}, analyzer=${!!analyzerRef.current}`);\n    \n    if (!analyzerRef.current) {\n      addToLog(\"⚠ No analyzer available for level monitoring\");\n      return;\n    }\n    \n    if (!isRecording && !forceStart) {\n      addToLog(\"⚠ Not recording, skipping level monitoring\");\n      return;\n    }\n    \n    addToLog(\"✓ Starting microphone level monitoring\");\n    const analyzer = analyzerRef.current;\n    const bufferLength = analyzer.frequencyBinCount;\n    const dataArray = new Uint8Array(bufferLength);\n    \n    const updateLevel = () => {\n      // Check current states\n      const currentlyRecording = isRecording;\n      const hasAnalyzer = !!analyzerRef.current;\n      const sessionIsActive = sessionActive;\n      \n      if (!currentlyRecording || !hasAnalyzer || !sessionIsActive) {\n        setAudioInputLevel(0);\n        addToLog(`Level monitoring stopped: recording=${currentlyRecording}, analyzer=${hasAnalyzer}, session=${sessionIsActive}`);\n        return;\n      }\n      \n      try {\n        analyzer.getByteFrequencyData(dataArray);\n        \n        // Calculate average frequency amplitude for visualization\n        let sum = 0;\n        for (let i = 0; i < bufferLength; i++) {\n          sum += dataArray[i];\n        }\n        const average = sum / bufferLength / 255;\n        \n        // Apply some amplification for better visibility\n        const amplified = Math.min(1, average * 3);\n        setAudioInputLevel(amplified);\n        \n        requestAnimationFrame(updateLevel);\n      } catch (error) {\n        console.error(\"Error updating audio level:\", error);\n        addToLog(`Error in level monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        setAudioInputLevel(0);\n      }\n    };\n    \n    updateLevel();\n  };\n\n  // Enhanced audio recording initialization with comprehensive error handling\n  const initializeAudioRecording = async () => {\n    try {\n      addToLog(\"🎤 Initializing audio recording...\");\n\n      // Check browser support\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\"Media devices API not supported in this browser\");\n      }\n\n      if (!window.AudioContext && !(window as any).webkitAudioContext) {\n        throw new Error(\"Web Audio API not supported in this browser\");\n      }\n\n      // Clean up any existing audio context\n      if (audioContextRef.current) {\n        try {\n          await audioContextRef.current.close();\n        } catch (e) {\n          console.warn(\"Error closing existing audio context:\", e);\n        }\n        audioContextRef.current = null;\n      }\n\n      // Request microphone access with optimal settings for speech recognition\n      const constraints: MediaStreamConstraints = {\n        audio: selectedMicDevice\n          ? {\n              deviceId: { exact: selectedMicDevice },\n              echoCancellation: true,\n              noiseSuppression: false,  // Disable to preserve speech clarity for OpenAI\n              autoGainControl: true,\n              sampleRate: { ideal: 24000, min: 16000, max: 48000 },\n              channelCount: 1,  // Mono audio for efficiency\n            }\n          : {\n              echoCancellation: true,\n              noiseSuppression: false,  // Disable to preserve speech clarity for OpenAI\n              autoGainControl: true,\n              sampleRate: { ideal: 24000, min: 16000, max: 48000 },\n              channelCount: 1,  // Mono audio for efficiency\n            }\n      };\n\n      console.log(\"🎤 Using audio constraints:\", constraints);\n      addToLog(`🎤 Requesting microphone access${selectedMicDevice ? ` (device: ${selectedMicDevice.substr(0, 8)}...)` : ''}`);\n\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      audioStreamRef.current = stream;\n      setAudioStreamActive(true);\n      setMicPermissionGranted(true);\n      addToLog(\"✅ Microphone access granted\");\n\n      // Log actual stream settings\n      const audioTrack = stream.getAudioTracks()[0];\n      if (audioTrack) {\n        const settings = audioTrack.getSettings();\n        addToLog(`🎤 Actual audio settings: ${settings.sampleRate}Hz, ${settings.channelCount} channel(s)`);\n        console.log(\"🎤 Audio track settings:\", settings);\n      }\n\n      // Initialize audio context with 24kHz for OpenAI Realtime API\n      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;\n      audioContextRef.current = new AudioContextClass({\n        sampleRate: 24000, // Match OpenAI Realtime API sample rate exactly\n      });\n\n      const audioContext = audioContextRef.current;\n      addToLog(`🎤 Audio context created: ${audioContext.sampleRate}Hz, state: ${audioContext.state}`);\n\n      // Resume if suspended (required for user interaction)\n      if (audioContext.state === 'suspended') {\n        addToLog(\"🎤 Resuming suspended audio context...\");\n        await audioContext.resume();\n      }\n      setAudioContextState(audioContext.state);\n      addToLog(`✅ Audio context ready: ${audioContext.state}`);\n\n      const source = audioContext.createMediaStreamSource(stream);\n\n      // Create analyzer for audio visualization\n      const analyzer = audioContext.createAnalyser();\n      analyzer.fftSize = 256;\n      analyzer.smoothingTimeConstant = 0.8;\n      analyzer.minDecibels = -90;\n      analyzer.maxDecibels = -10;\n      source.connect(analyzer);\n      analyzerRef.current = analyzer;\n      addToLog(\"✅ Audio analyzer connected\");\n\n      // Create processor for audio processing (using deprecated but reliable ScriptProcessorNode)\n      const processor = audioContext.createScriptProcessor(4096, 1, 1);\n      audioProcessorRef.current = processor;\n      \n      // Set up audio processor for real-time audio streaming\n      let audioFrameCount = 0;\n      addToLog(\"✓ Setting up audio processor callback\");\n      \n      processor.onaudioprocess = (e) => {\n        try {\n          audioFrameCount++;\n\n          // Debug: Log that processor is working\n          if (audioFrameCount <= 5) {\n            console.log(`🔄 Audio processor callback #${audioFrameCount}`);\n            addToLog(`🔄 Audio processor active: frame ${audioFrameCount}`);\n          }\n\n          // CRITICAL DEBUG: Log every 50 frames to confirm processor is running\n          if (audioFrameCount % 50 === 0) {\n            addToLog(`🔄 Audio processor still active: frame ${audioFrameCount}`);\n          }\n\n          // Check if we should be recording and sending audio\n          const wsConnected = wsRef.current !== null && wsRef.current.readyState === WebSocket.OPEN;\n          // FORCE RECORDING: If WebSocket is connected, always record (bypass broken state management)\n          const currentlyRecording = wsConnected;\n\n          // Log the first few callbacks to confirm it's working\n          if (audioFrameCount <= 3) {\n            addToLog(`🎤 Audio callback ${audioFrameCount}: recording=${currentlyRecording}, session=${sessionActive}, ws=${wsConnected}`);\n          }\n\n          // CRITICAL DEBUG: Log recording state every 50 frames\n          if (audioFrameCount % 50 === 0) {\n            addToLog(`🎤 Recording check frame ${audioFrameCount}: recording=${currentlyRecording}, session=${sessionActive}, ws=${wsConnected}, isRecording=${isRecording}`);\n          }\n\n          // Always update audio level for visual feedback, even if not recording\n          if (analyzerRef.current) {\n            try {\n              const analyzer = analyzerRef.current;\n              const bufferLength = analyzer.frequencyBinCount;\n              const dataArray = new Uint8Array(bufferLength);\n              analyzer.getByteFrequencyData(dataArray);\n\n              let sum = 0;\n              for (let i = 0; i < bufferLength; i++) {\n                sum += dataArray[i];\n              }\n              const level = (sum / bufferLength / 255) * 3; // Amplify for visibility\n              setAudioInputLevel(Math.min(1, level));\n\n              // Debug: Log audio level for first few frames\n              if (audioFrameCount <= 3) {\n                addToLog(`🔊 Visual level update: ${(level * 100).toFixed(1)}% (analyzer working)`);\n              }\n            } catch (analyzerError) {\n              console.warn(\"Error updating audio level:\", analyzerError);\n              if (audioFrameCount <= 3) {\n                addToLog(`❌ Visual level error: ${analyzerError instanceof Error ? analyzerError.message : 'Unknown'}`);\n              }\n            }\n          } else {\n            // No analyzer available\n            if (audioFrameCount <= 3) {\n              addToLog(`❌ No analyzer available for visual level update`);\n            }\n          }\n\n          if (currentlyRecording) {\n            // Get audio data from the input channel\n            const inputData = e.inputBuffer.getChannelData(0);\n\n            if (!inputData || inputData.length === 0) {\n              console.warn(\"No audio input data received\");\n              return;\n            }\n\n            // Calculate audio energy to detect if there's actual sound\n            let energy = 0;\n            for (let i = 0; i < inputData.length; i++) {\n              energy += inputData[i] * inputData[i];\n            }\n            const rms = Math.sqrt(energy / inputData.length);\n\n            // Convert Float32 audio to Int16 PCM for OpenAI with moderate gain\n            const int16Data = new Int16Array(inputData.length);\n            const gainMultiplier = 2.0; // Moderate amplification for clarity\n\n            for (let i = 0; i < inputData.length; i++) {\n              // Apply gain and convert float [-1,1] to int16 [-32768,32767] with proper clamping\n              let sample = inputData[i] * gainMultiplier;\n              sample = Math.max(-1, Math.min(1, sample)); // Clamp to prevent distortion\n              int16Data[i] = sample < 0 ? Math.round(sample * 32768) : Math.round(sample * 32767);\n            }\n\n            // Convert Int16Array to base64 for OpenAI Realtime API\n            const buffer = new ArrayBuffer(int16Data.length * 2);\n            const view = new DataView(buffer);\n            for (let i = 0; i < int16Data.length; i++) {\n              view.setInt16(i * 2, int16Data[i], true); // little-endian\n            }\n            const uint8Array = new Uint8Array(buffer);\n            const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array) as any));\n          \n            // HYBRID VAD: Use client-side detection to control audio streaming with silence gaps\n            const amplifiedRms = rms * gainMultiplier;\n            const speechThreshold = 0.01;\n            const isSpeechDetected = amplifiedRms > speechThreshold;\n\n            // Always send audio to OpenAI (including silence) for proper VAD processing\n            try {\n              wsRef.current!.send(JSON.stringify({\n                type: 'input_audio_buffer.append',\n                audio: base64Audio\n              }));\n\n              // Log successful audio transmission for first few frames\n              if (audioFrameCount <= 3) {\n                addToLog(`✅ Audio sent to OpenAI: ${base64Audio.length} chars, RMS=${amplifiedRms.toFixed(4)}`);\n              }\n\n              // CRITICAL DEBUG: Log every 100 frames to confirm audio is being sent\n              if (audioFrameCount % 100 === 0) {\n                addToLog(`📤 Audio transmission active: frame ${audioFrameCount}, ${base64Audio.length} chars, RMS=${amplifiedRms.toFixed(4)}`);\n              }\n            } catch (wsError) {\n              console.error(\"Error sending audio to WebSocket:\", wsError);\n              addToLog(`❌ Failed to send audio: ${wsError instanceof Error ? wsError.message : 'Unknown error'}`);\n            }\n\n            // Manage speech state with proper timing for visual feedback\n            if (isSpeechDetected && !isUserSpeaking) {\n              setIsUserSpeaking(true);\n              addToLog(\"🎤 SPEECH DETECTED: Audio streaming to OpenAI VAD\");\n\n              // Clear any existing silence timeout\n              if (silenceTimeoutRef.current) {\n                clearTimeout(silenceTimeoutRef.current);\n                silenceTimeoutRef.current = null;\n              }\n            } else if (!isSpeechDetected && isUserSpeaking) {\n              // Start silence timer for visual feedback only\n              if (!silenceTimeoutRef.current) {\n                silenceTimeoutRef.current = setTimeout(() => {\n                  setIsUserSpeaking(false);\n                  addToLog(\"🔇 SILENCE DETECTED: Waiting for OpenAI VAD to process speech end\");\n                  silenceTimeoutRef.current = null;\n                }, 1000); // 1 second delay for visual feedback\n              }\n            }\n\n            // Log speech detection status periodically with more useful info\n            if (audioFrameCount % 100 === 0) { // Reduced frequency to avoid spam\n              const energyLevel = amplifiedRms > 0.1 ? \"LOUD\" : amplifiedRms > 0.01 ? \"GOOD\" : amplifiedRms > 0.001 ? \"LOW\" : \"SILENT\";\n              const wsStatus = wsRef.current?.readyState === WebSocket.OPEN ? \"CONNECTED\" : \"DISCONNECTED\";\n              addToLog(`📊 MIC STATUS: Energy=${energyLevel} (${amplifiedRms.toFixed(4)}), WS=${wsStatus}, Frames=${audioFrameCount}`);\n            }\n\n            // Log very first few audio frames to confirm processor is working\n            if (audioFrameCount <= 10) {\n              console.log(`🔄 Audio processor frame ${audioFrameCount}: RMS=${amplifiedRms.toFixed(4)}, threshold=${speechThreshold}, detected=${isSpeechDetected}`);\n            }\n          }\n        } catch (processorError) {\n          console.error(\"Error in audio processor:\", processorError);\n          if (audioFrameCount <= 5) {\n            addToLog(`❌ Audio processor error: ${processorError instanceof Error ? processorError.message : 'Unknown error'}`);\n          }\n        }\n      };\n      \n      // Connect the processor properly for audio processing\n      source.connect(processor);\n\n      // CRITICAL FIX: ScriptProcessorNode must be connected to destination to work\n      // We create a gain node with zero volume to avoid feedback but enable processing\n      const silentGain = audioContext.createGain();\n      silentGain.gain.value = 0; // Silent output to prevent feedback\n      processor.connect(silentGain);\n      silentGain.connect(audioContext.destination);\n\n      addToLog(\"✓ Audio processor connected with silent output (enables processing, prevents feedback)\");\n      \n      // Store the analyzer for level monitoring\n      analyzerRef.current = analyzer;\n      addToLog(\"✓ Audio analyzer stored for monitoring\");\n      \n      return true;\n    } catch (error) {\n      console.error(\"Error initializing audio recording:\", error);\n      addToLog(`✗ Audio initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      setAudioStreamActive(false);\n      setMicPermissionGranted(false);\n      \n      toast({\n        title: \"Microphone error\",\n        description: error instanceof Error ? error.message : \"Failed to access microphone\",\n        variant: \"destructive\",\n      });\n      \n      return false;\n    }\n  };\n\n  // Stop audio recording\n  const stopAudioRecording = () => {\n    setIsRecording(false);\n    \n    // Clean up audio processing\n    if (audioProcessorRef.current) {\n      try {\n        audioProcessorRef.current.disconnect();\n      } catch (e) {\n        console.error(\"Error disconnecting audio processor:\", e);\n      }\n      audioProcessorRef.current = null;\n    }\n    \n    if (audioStreamRef.current) {\n      audioStreamRef.current.getTracks().forEach(track => track.stop());\n      audioStreamRef.current = null;\n    }\n  };\n  \n  // Test the voice output\n  const testAudio = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      setAudioOutputPlaying(true);\n      \n      // Send test audio request\n      wsRef.current.send(JSON.stringify({\n        type: \"test_audio\",\n        voice: selectedVoice,\n        speed: voiceSpeed,\n        text: \"This is a test of the voice output. Can you hear this clearly?\"\n      }));\n      \n      addToLog(`Sent test audio request (${selectedVoice}, speed: ${voiceSpeed})`);\n      \n      toast({\n        title: \"Testing Audio Output\",\n        description: `Playing test audio with ${selectedVoice} voice`,\n      });\n    } else {\n      // Create a WebSocket connection for testing if one doesn't exist\n      const ws = new WebSocket(getWebSocketUrl());\n      wsRef.current = ws;\n      \n      ws.onopen = () => {\n        setConnectionStatus(\"connected\");\n        addToLog(\"WebSocket connection established for audio test\");\n        \n        // Send test audio request\n        ws.send(JSON.stringify({\n          type: \"test_audio\",\n          voice: selectedVoice,\n          speed: voiceSpeed,\n          text: \"This is a test of the voice output. Can you hear this clearly?\"\n        }));\n        \n        setAudioOutputPlaying(true);\n        addToLog(`Sent test audio request (${selectedVoice}, speed: ${voiceSpeed})`);\n      };\n      \n      ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log(\"Test audio response:\", data);\n          \n          if (data.type === \"test_audio_response\") {\n            // Play the audio\n            const audioElement = new Audio(data.audioUrl);\n            testAudioRef.current = audioElement;\n            \n            audioElement.onplay = () => {\n              addToLog(`Playing test audio (${selectedVoice})`);\n            };\n            \n            audioElement.onended = () => {\n              addToLog(`Test audio playback completed`);\n              setAudioOutputPlaying(false);\n              ws.close();\n            };\n            \n            audioElement.onerror = (e) => {\n              console.error(\"Audio playback error:\", e);\n              setAudioOutputPlaying(false);\n              addToLog(`Error playing test audio`);\n              ws.close();\n            };\n            \n            // Start playback\n            audioElement.play()\n              .catch(err => {\n                console.error(\"Audio play error:\", err);\n                setAudioOutputPlaying(false);\n                addToLog(`Play error: ${err.message}`);\n              });\n          }\n        } catch (error) {\n          console.error(\"Error parsing WebSocket message:\", error);\n        }\n      };\n      \n      ws.onerror = (error) => {\n        console.error(\"WebSocket error during audio test:\", error);\n        setAudioOutputPlaying(false);\n        toast({\n          title: \"Connection Error\",\n          description: \"Failed to connect to server for audio test\",\n          variant: \"destructive\",\n        });\n      };\n    }\n  };\n\n  // End the session and get a summary\n  const endSession = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      // First end the session\n      wsRef.current.send(JSON.stringify({\n        type: \"conversationEnded\"\n      }));\n\n      debugManager.log('sessionManagement', \"Session ended, requesting summary...\");\n      if (debugManager.isEnabled) {\n        addToLog(\"Ended session, requesting summary...\");\n      }\n\n      // Stop recording\n      stopAudioRecording();\n\n      // Request summary after a brief delay to allow session cleanup\n      setTimeout(() => {\n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify({\n            type: \"summary_request\"\n          }));\n\n          debugManager.log('sessionManagement', \"Summary requested\");\n          if (debugManager.isEnabled) {\n            addToLog(\"Summary requested\");\n          }\n        }\n      }, 500);\n\n      toast({\n        title: \"Session ended\",\n        description: \"Generating session summary...\",\n      });\n    }\n  };\n  \n  // Export transcript as text\n  const exportTranscript = () => {\n    const textContent = transcript.map(t => \n      `[${t.timestamp.toLocaleTimeString()}] ${t.speaker}: ${t.text}`\n    ).join('\\n\\n');\n    \n    // Add summary if available\n    const fullContent = summary \n      ? `${textContent}\\n\\n--------\\nSUMMARY:\\n${summary}` \n      : textContent;\n    \n    // Create download link\n    const blob = new Blob([fullContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `voice-therapy-transcript-${new Date().toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n    \n    toast({\n      title: \"Transcript exported\",\n      description: \"The conversation transcript has been downloaded\",\n    });\n  };\n  \n  // Initialize diagnostics on component mount\n  useEffect(() => {\n    // Check audio support\n    const checkAudioSupport = () => {\n      const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n      const hasAudioContext = !!(window.AudioContext || (window as any).webkitAudioContext);\n      \n      setAudioSupported(hasGetUserMedia && hasAudioContext);\n      addToLog(`Audio API support: getUserMedia=${hasGetUserMedia}, AudioContext=${hasAudioContext}`);\n    };\n    \n    checkAudioSupport();\n    \n    // Fetch available microphone devices when the component mounts\n    fetchMicrophoneDevices();\n    \n    return () => {\n      // Stop recording if active\n      stopAudioRecording();\n      \n      // Close WebSocket connection\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n      \n      // Clear intervals\n      if (levelCheckIntervalRef.current) {\n        clearInterval(levelCheckIntervalRef.current);\n      }\n    };\n  }, []);\n  \n  // Start/stop microphone level monitoring when recording state changes\n  useEffect(() => {\n    addToLog(`Recording state changed: isRecording=${isRecording}, sessionActive=${sessionActive}, analyzer=${!!analyzerRef.current}`);\n    \n    if (isRecording && sessionActive) {\n      addToLog(\"✓ Starting microphone level monitoring due to recording state change\");\n      // Use multiple attempts with increasing delays to ensure state has updated\n      setTimeout(() => {\n        startMicrophoneLevelMonitoring(true); // Force start even if state check fails\n      }, 100);\n    } else {\n      setAudioInputLevel(0);\n      if (!isRecording) {\n        addToLog(\"Recording stopped - clearing audio input level\");\n      }\n    }\n  }, [isRecording, sessionActive]);\n\n  // Test debug speech detection\n  const testSpeechDetection = () => {\n    if (!isRecording || !sessionActive) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before testing speech detection.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    debugManager.log('audioProcessing', \"🔍 SPEECH DETECTION TEST STARTED - Say 'Hello' loudly into your microphone\");\n    if (debugManager.isEnabled) {\n      addToLog(\"🔍 SPEECH DETECTION TEST STARTED - Say 'Hello' loudly into your microphone\");\n    }\n\n    toast({\n      title: \"🔍 Speech Detection Test\",\n      description: \"Say 'Hello' loudly into your microphone. Check the debug log for detection status.\",\n    });\n\n    // Log speech detection status every second for 10 seconds\n    let testCount = 0;\n    const testInterval = setInterval(() => {\n      testCount++;\n      const bufferSize = speechBufferRef.current.length;\n      const speaking = isUserSpeaking;\n      const speechTime = speechStartTimeRef.current ? Date.now() - speechStartTimeRef.current : 0;\n\n      const message = `🔍 Test ${testCount}/10: Speaking=${speaking}, Buffer=${bufferSize} chunks, SpeechTime=${speechTime}ms`;\n      debugManager.log('audioProcessing', message);\n      if (debugManager.isEnabled) {\n        addToLog(message);\n      }\n\n      if (testCount >= 10) {\n        clearInterval(testInterval);\n        debugManager.log('audioProcessing', \"🔍 SPEECH DETECTION TEST COMPLETED\");\n        if (debugManager.isEnabled) {\n          addToLog(\"🔍 SPEECH DETECTION TEST COMPLETED\");\n        }\n        toast({\n          title: \"Test Complete\",\n          description: \"Check the debug log to see if your speech was detected. Look for 'Speaking=true' when you spoke.\",\n        });\n      }\n    }, 1000);\n  };\n\n  // Comprehensive voice system test (mimics the successful backend test)\n  const testVoiceSystemComprehensive = () => {\n    if (!sessionActive) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before running the comprehensive test.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    debugManager.log('audioProcessing', \"🚀 COMPREHENSIVE VOICE SYSTEM TEST STARTED\");\n    if (debugManager.isEnabled) {\n      addToLog(\"🚀 COMPREHENSIVE VOICE SYSTEM TEST STARTED\");\n    }\n\n    toast({\n      title: \"🚀 Comprehensive Test\",\n      description: \"Running full voice system test. Check the debug log for detailed results.\",\n    });\n\n    let testResults = {\n      sessionReady: sessionActive,\n      microphoneActive: isRecording,\n      audioContextReady: audioContextRef.current?.state === 'running',\n      websocketConnected: wsRef.current?.readyState === WebSocket.OPEN,\n      audioProcessorActive: !!audioProcessorRef.current,\n      analyzerActive: !!analyzerRef.current\n    };\n\n    const logSystemStatus = (message: string) => {\n      debugManager.log('audioProcessing', message);\n      if (debugManager.isEnabled) {\n        addToLog(message);\n      }\n    };\n\n    logSystemStatus(\"📊 SYSTEM STATUS CHECK:\");\n    logSystemStatus(`  Session Ready: ${testResults.sessionReady ? '✅' : '❌'}`);\n    logSystemStatus(`  Microphone Active: ${testResults.microphoneActive ? '✅' : '❌'}`);\n    logSystemStatus(`  Audio Context: ${testResults.audioContextReady ? '✅' : '❌'} (${audioContextRef.current?.state || 'none'})`);\n    logSystemStatus(`  WebSocket: ${testResults.websocketConnected ? '✅' : '❌'} (${wsRef.current?.readyState || 'none'})`);\n    logSystemStatus(`  Audio Processor: ${testResults.audioProcessorActive ? '✅' : '❌'}`);\n    logSystemStatus(`  Audio Analyzer: ${testResults.analyzerActive ? '✅' : '❌'}`);\n    logSystemStatus(`  Current Audio Level: ${(audioInputLevel * 100).toFixed(1)}%`);\n    logSystemStatus(`  Audio Stream: ${audioStreamRef.current ? '✅ Active' : '❌ None'}`);\n\n    // Additional debugging for audio pipeline\n    if (audioStreamRef.current) {\n      const tracks = audioStreamRef.current.getAudioTracks();\n      logSystemStatus(`  Audio Tracks: ${tracks.length} (${tracks.map(t => t.label || 'Unknown').join(', ')})`);\n      if (tracks.length > 0) {\n        const settings = tracks[0].getSettings();\n        logSystemStatus(`  Track Settings: ${settings.sampleRate}Hz, ${settings.channelCount}ch`);\n      }\n    }\n\n    // Test audio level detection using the same method as the visual indicator\n    if (analyzerRef.current) {\n      logSystemStatus(\"🎤 Testing audio level detection for 5 seconds...\");\n      let maxLevel = 0;\n      let levelTestCount = 0;\n\n      const levelTestInterval = setInterval(() => {\n        levelTestCount++;\n\n        // Use the same audio analysis method as the visual indicator\n        let currentLevel = 0;\n        try {\n          const analyzer = analyzerRef.current;\n          if (analyzer) {\n            const bufferLength = analyzer.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            analyzer.getByteFrequencyData(dataArray);\n\n            let sum = 0;\n            for (let i = 0; i < bufferLength; i++) {\n              sum += dataArray[i];\n            }\n            currentLevel = (sum / bufferLength / 255) * 3; // Same calculation as visual indicator\n            currentLevel = Math.min(1, currentLevel);\n          }\n        } catch (error) {\n          debugManager.warn('audioProcessing', \"Error reading audio level:\", error);\n          currentLevel = audioInputLevel; // Fallback to state variable\n        }\n\n        maxLevel = Math.max(maxLevel, currentLevel);\n\n        if (levelTestCount <= 5) {\n          logSystemStatus(`  Level test ${levelTestCount}/5: ${(currentLevel * 100).toFixed(1)}% (analyzer=${!!analyzerRef.current})`);\n        }\n\n        if (levelTestCount >= 5) {\n          clearInterval(levelTestInterval);\n          logSystemStatus(`✅ Audio level test complete. Max level: ${(maxLevel * 100).toFixed(1)}%`);\n\n          if (maxLevel > 0.05) {\n            logSystemStatus(\"✅ Audio input is working correctly\");\n          } else {\n            logSystemStatus(\"⚠️ Low audio input detected - check microphone volume or analyzer connection\");\n            logSystemStatus(`🔍 Debug: audioInputLevel=${(audioInputLevel * 100).toFixed(1)}%, analyzer=${!!analyzerRef.current}`);\n          }\n        }\n      }, 1000);\n    } else {\n      logSystemStatus(\"❌ No audio analyzer available - microphone may not be properly initialized\");\n    }\n\n    // Generate summary after 6 seconds\n    setTimeout(() => {\n      const allSystemsGo = Object.values(testResults).every(result => result);\n\n      logSystemStatus(\"🔍 COMPREHENSIVE TEST RESULTS:\");\n      if (allSystemsGo) {\n        logSystemStatus(\"✅ ALL SYSTEMS OPERATIONAL - Voice conversation should work!\");\n        toast({\n          title: \"✅ Test Passed\",\n          description: \"All systems are working correctly. Voice conversation should function properly.\",\n        });\n      } else {\n        logSystemStatus(\"❌ SOME SYSTEMS NOT READY - Check failed components above\");\n        toast({\n          title: \"⚠️ Test Issues\",\n          description: \"Some components are not working correctly. Check the debug log for details.\",\n          variant: \"destructive\",\n        });\n      }\n    }, 6000);\n  };\n\n  // Real-time audio flow debugging\n  const testRealTimeAudioFlow = () => {\n    if (!sessionActive) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before testing audio flow.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    const logRealTimeDebug = (message: string) => {\n      debugManager.log('audioProcessing', message);\n      if (debugManager.isEnabled) {\n        addToLog(message);\n      }\n    };\n\n    logRealTimeDebug(\"🔍 REAL-TIME AUDIO FLOW DEBUG STARTED\");\n    logRealTimeDebug(\"📢 SPEAK NOW - monitoring audio processing in real-time\");\n\n    toast({\n      title: \"🔍 Real-Time Debug\",\n      description: \"SPEAK NOW! Monitoring audio processing for 10 seconds.\",\n    });\n\n    let debugCount = 0;\n    let lastAudioFrameCount = 0;\n    let lastTransmissionCount = 0;\n    let audioTransmissionCount = 0;\n\n    // Monitor WebSocket messages for audio responses\n    const originalOnMessage = wsRef.current?.onmessage;\n    let speechStartReceived = false;\n    let transcriptionReceived = false;\n    let aiResponseReceived = false;\n\n    if (wsRef.current) {\n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n\n          switch (data.type) {\n            case 'input_audio_buffer.speech_started':\n              speechStartReceived = true;\n              logRealTimeDebug(\"🎤 REAL-TIME: OpenAI detected speech start!\");\n              break;\n            case 'input_audio_buffer.speech_stopped':\n              logRealTimeDebug(\"🔇 REAL-TIME: OpenAI detected speech stop!\");\n              break;\n            case 'conversation.item.input_audio_transcription.completed':\n              transcriptionReceived = true;\n              logRealTimeDebug(`📝 REAL-TIME: Transcription received: \"${data.transcript}\"`);\n              break;\n            case 'response.audio_transcript.done':\n              aiResponseReceived = true;\n              logRealTimeDebug(`🤖 REAL-TIME: AI response: \"${data.transcript}\"`);\n              break;\n          }\n        } catch (error) {\n          // Ignore parse errors\n        }\n\n        // Call original handler\n        if (originalOnMessage) {\n          originalOnMessage(event);\n        }\n      };\n    }\n\n    const debugInterval = setInterval(() => {\n      debugCount++;\n\n      // Check if audio processor is running\n      const currentFrameCount = audioFrameCount;\n      const framesProcessed = currentFrameCount - lastAudioFrameCount;\n      lastAudioFrameCount = currentFrameCount;\n\n      // Check current audio level\n      let currentLevel = 0;\n      if (analyzerRef.current) {\n        try {\n          const analyzer = analyzerRef.current;\n          const bufferLength = analyzer.frequencyBinCount;\n          const dataArray = new Uint8Array(bufferLength);\n          analyzer.getByteFrequencyData(dataArray);\n\n          let sum = 0;\n          for (let i = 0; i < bufferLength; i++) {\n            sum += dataArray[i];\n          }\n          currentLevel = (sum / bufferLength / 255) * 100;\n        } catch (error) {\n          // Ignore\n        }\n      }\n\n      addToLog(`🔍 Debug ${debugCount}/10: Frames=${framesProcessed}/sec, Level=${currentLevel.toFixed(1)}%, Speech=${speechStartReceived}, Transcript=${transcriptionReceived}, AI=${aiResponseReceived}`);\n\n      if (debugCount >= 10) {\n        clearInterval(debugInterval);\n\n        // Restore original message handler\n        if (wsRef.current && originalOnMessage) {\n          wsRef.current.onmessage = originalOnMessage;\n        }\n\n        addToLog(\"🔍 REAL-TIME DEBUG COMPLETE\");\n        addToLog(\"📊 RESULTS:\");\n        addToLog(`  Audio Processor: ${lastAudioFrameCount > 0 ? '✅ Running' : '❌ Not running'}`);\n        addToLog(`  Speech Detection: ${speechStartReceived ? '✅ Working' : '❌ Not detected'}`);\n        addToLog(`  Transcription: ${transcriptionReceived ? '✅ Working' : '❌ Not received'}`);\n        addToLog(`  AI Response: ${aiResponseReceived ? '✅ Working' : '❌ Not received'}`);\n\n        if (!speechStartReceived && lastAudioFrameCount > 0) {\n          addToLog(\"⚠️ ISSUE: Audio processor running but OpenAI not detecting speech\");\n          addToLog(\"💡 TRY: Speak louder or check microphone sensitivity\");\n        } else if (!lastAudioFrameCount) {\n          addToLog(\"❌ ISSUE: Audio processor not running - check audio context connection\");\n        }\n\n        toast({\n          title: \"Debug Complete\",\n          description: \"Check the debug log for detailed audio flow analysis.\",\n        });\n      }\n    }, 1000);\n  };\n\n  // Direct audio test - simpler approach\n  const testDirectAudioSend = () => {\n    if (!sessionActive || !wsRef.current) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before testing direct audio.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    addToLog(\"🎯 DIRECT AUDIO TEST - Checking if audio reaches OpenAI\");\n\n    // Generate a simple test audio chunk (like our backend test)\n    const generateTestAudio = () => {\n      const sampleRate = 24000;\n      const duration = 0.1; // 100ms\n      const samples = Math.floor(sampleRate * duration);\n      const buffer = new ArrayBuffer(samples * 2);\n      const view = new DataView(buffer);\n\n      for (let i = 0; i < samples; i++) {\n        const t = i / sampleRate;\n        const frequency = 440; // A4 note\n        const sample = Math.sin(2 * Math.PI * frequency * t) * 0.5;\n        const int16Sample = Math.round(sample * 32767);\n        view.setInt16(i * 2, int16Sample, true);\n      }\n\n      const uint8Array = new Uint8Array(buffer);\n      return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n    };\n\n    // Send 5 test audio chunks\n    let chunkCount = 0;\n    const sendTestChunk = () => {\n      if (chunkCount >= 5) {\n        addToLog(\"🎯 Direct audio test complete - sent 5 chunks\");\n        return;\n      }\n\n      const testAudio = generateTestAudio();\n\n      try {\n        wsRef.current!.send(JSON.stringify({\n          type: 'input_audio_buffer.append',\n          audio: testAudio\n        }));\n\n        chunkCount++;\n        addToLog(`🎯 Sent test audio chunk ${chunkCount}/5: ${testAudio.length} chars`);\n\n        setTimeout(sendTestChunk, 200); // Send every 200ms\n      } catch (error) {\n        addToLog(`❌ Failed to send test audio: ${error instanceof Error ? error.message : 'Unknown'}`);\n      }\n    };\n\n    // Monitor for OpenAI responses\n    const originalOnMessage = wsRef.current.onmessage;\n    let responseCount = 0;\n\n    wsRef.current.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n\n        if (data.type.includes('input_audio_buffer') || data.type.includes('speech') || data.type.includes('response')) {\n          responseCount++;\n          addToLog(`🎯 OpenAI response ${responseCount}: ${data.type}`);\n        }\n      } catch (error) {\n        // Ignore parse errors\n      }\n\n      // Call original handler\n      if (originalOnMessage) {\n        originalOnMessage(event);\n      }\n    };\n\n    // Restore original handler after 10 seconds\n    setTimeout(() => {\n      if (wsRef.current && originalOnMessage) {\n        wsRef.current.onmessage = originalOnMessage;\n      }\n      addToLog(`🎯 Direct test complete. OpenAI responses received: ${responseCount}`);\n\n      if (responseCount === 0) {\n        addToLog(\"❌ ISSUE: No responses from OpenAI - check WebSocket connection or API\");\n      } else {\n        addToLog(\"✅ OpenAI is receiving and processing audio data\");\n      }\n    }, 10000);\n\n    sendTestChunk();\n\n    toast({\n      title: \"🎯 Direct Audio Test\",\n      description: \"Sending test audio directly to OpenAI. Check debug log for results.\",\n    });\n  };\n\n  return (\n    <div className=\"container mx-auto py-6 max-w-5xl\">\n      <h1 className=\"text-3xl font-bold mb-6\">AI Voice Therapy Assistant</h1>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <Card className=\"mb-6\">\n            <CardHeader className=\"flex flex-row items-center justify-between pb-2\">\n              <CardTitle>Real-Time Voice Interaction</CardTitle>\n              <div className=\"flex items-center space-x-2\">\n                <span className={`inline-block w-3 h-3 rounded-full ${\n                  connectionStatus === \"connected\" ? \"bg-green-500\" : \n                  connectionStatus === \"error\" ? \"bg-red-500\" : \"bg-yellow-500\"\n                }`}></span>\n                <span className=\"text-sm text-gray-500\">\n                  {connectionStatus === \"connected\" ? \"Connected\" : \n                   connectionStatus === \"error\" ? \"Error\" : \"Disconnected\"}\n                </span>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {!sessionActive ? (\n                <div className=\"flex flex-col items-center justify-center py-10\">\n                  {loadingStatus ? (\n                    <div className=\"flex flex-col items-center gap-3\">\n                      <RefreshCw className=\"animate-spin h-8 w-8 text-primary\" />\n                      <p>{loadingStatus}</p>\n                    </div>\n                  ) : (\n                    <div className=\"text-center\">\n                      <p className=\"text-lg mb-6\">Start an interactive real-time voice therapy session.</p>\n                                                          <div className=\"text-center\">\n                    <Button onClick={startInteractiveSession} className=\"px-6\" size=\"lg\">\n                      <Zap className=\"mr-2 h-5 w-5\" />\n                      Start Interactive Session\n                    </Button>\n                    <p className=\"text-sm text-gray-500 mt-4\">\n                      Microphone access is required for this feature.\n                    </p>\n                  </div>\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"font-medium\">Live Transcript</h3>\n                    <div className=\"flex items-center space-x-4\">\n                      {isRecording ? (\n                        <div className=\"flex items-center text-green-500\">\n                          <span className=\"inline-block w-2 h-2 rounded-full bg-green-500 mr-2 animate-pulse\"></span>\n                          <span className=\"text-sm\">Recording</span>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center text-red-500\">\n                          <span className=\"inline-block w-2 h-2 rounded-full bg-red-500 mr-2\"></span>\n                          <span className=\"text-sm\">Paused</span>\n                        </div>\n                      )}\n                      {audioOutputPlaying && (\n                        <div className=\"flex items-center text-blue-500\">\n                          <Volume2 className=\"h-4 w-4 mr-1 animate-pulse\" />\n                          <span className=\"text-sm\">AI Speaking</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <ScrollArea className=\"h-80 border rounded-md p-4\">\n                    {transcript.length === 0 ? (\n                      <div className=\"text-center text-gray-500 py-10\">\n                        <p>The conversation transcript will appear here</p>\n                        <p className=\"text-sm mt-2\">Start speaking to begin the session</p>\n                      </div>\n                    ) : (\n                      <div className=\"space-y-4\">\n                        {transcript.map((entry, index) => (\n                          <div key={index} className=\"mb-4\">\n                            <div className=\"flex items-center mb-1\">\n                              <span className=\"font-bold\">{entry.speaker}</span>\n                              <span className=\"text-gray-400 text-xs ml-2\">\n                                {entry.timestamp.toLocaleTimeString()}\n                              </span>\n                            </div>\n                            <div className={`px-4 py-2 rounded-lg ${\n                              entry.speaker === \"You\" \n                                ? \"bg-primary text-primary-foreground ml-0 mr-8\" \n                                : \"bg-secondary text-secondary-foreground ml-8 mr-0\"\n                            }`}>\n                              {entry.text}\n                            </div>\n                          </div>\n                        ))}\n                        <div ref={transcriptEndRef} />\n                      </div>\n                    )}\n                  </ScrollArea>\n                  \n                  {summary && (\n                    <div className=\"mt-4 p-4 border rounded-md bg-muted\">\n                      <h3 className=\"font-bold mb-2\">Session Summary</h3>\n                      <p>{summary}</p>\n                    </div>\n                  )}\n                </>\n              )}\n            </CardContent>\n            {sessionActive && (\n              <CardFooter className=\"flex justify-between\">\n                <div className=\"flex space-x-2\">\n                  <Button\n                    onClick={() => setIsRecording(prev => !prev)}\n                    variant={isRecording ? \"destructive\" : \"outline\"}\n                    className={isRecording ? \"bg-red-500 hover:bg-red-600\" : \"\"}\n                  >\n                    {isRecording ? (\n                      <>\n                        <MicOff className=\"mr-2 h-4 w-4\" /> \n                        Pause Mic\n                      </>\n                    ) : (\n                      <>\n                        <Mic className=\"mr-2 h-4 w-4\" /> \n                        Resume Mic\n                      </>\n                    )}\n                  </Button>\n\n                </div>\n                \n                <div className=\"space-x-2\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={exportTranscript}\n                    disabled={transcript.length === 0}\n                  >\n                    <Download className=\"mr-2 h-4 w-4\" />\n                    Export Transcript\n                  </Button>\n                  \n                  <Button \n                    variant=\"default\" \n                    onClick={endSession}\n                  >\n                    End Session & Summarize\n                  </Button>\n                </div>\n              </CardFooter>\n            )}\n          </Card>\n        </div>\n        \n        <div className=\"lg:col-span-1\">\n          <Tabs defaultValue=\"settings\">\n            <TabsList className=\"w-full\">\n              <TabsTrigger value=\"settings\" className=\"flex-1\">Settings</TabsTrigger>\n              <TabsTrigger value=\"debug\" className=\"flex-1\">Debug</TabsTrigger>\n              <TabsTrigger value=\"logs\" className=\"flex-1\">Logs</TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"settings\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Voice Assistant Settings</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <h3 className=\"text-md font-medium mb-2\">Audio Diagnostics</h3>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Browser Support:</span>\n                      <span className={audioSupported ? \"text-green-500\" : \"text-red-500\"}>\n                        {audioSupported ? \"✓ Supported\" : \"✗ Not Supported\"}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Microphone Permission:</span>\n                      <span className={micPermissionGranted ? \"text-green-500\" : \"text-gray-500\"}>\n                        {micPermissionGranted ? \"✓ Granted\" : \"Not Requested\"}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Audio Context:</span>\n                      <span className={audioContextState === \"running\" ? \"text-green-500\" : \"text-yellow-500\"}>\n                        {audioContextState}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Audio Stream:</span>\n                      <span className={audioStreamActive ? \"text-green-500\" : \"text-gray-500\"}>\n                        {audioStreamActive ? \"✓ Active\" : \"Inactive\"}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <div className=\"space-x-2\">\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={testAudioRecording}\n                        disabled={sessionActive}\n                      >\n                        Test Recording\n                      </Button>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={testWebSocketConnection}\n                        disabled={sessionActive}\n                      >\n                        Test WebSocket\n                      </Button>\n                    </div>\n                    {sessionActive && (\n                      <div className=\"space-x-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={generateTestAudio}\n                        >\n                          Generate Test Audio\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={testRoundTripAudio}\n                        >\n                          Test Round-Trip\n                        </Button>\n                        {debugEnabled && (\n                          <>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testSpeechDetection}\n                              className=\"bg-yellow-50 border-yellow-300 text-yellow-800 hover:bg-yellow-100\"\n                            >\n                              🔍 Test Speech Detection\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testVoiceSystemComprehensive}\n                              className=\"bg-green-50 border-green-300 text-green-800 hover:bg-green-100\"\n                            >\n                              🚀 Comprehensive Test\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testRealTimeAudioFlow}\n                              className=\"bg-purple-50 border-purple-300 text-purple-800 hover:bg-purple-100\"\n                            >\n                              🔍 Real-Time Audio Debug\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testDirectAudioSend}\n                              className=\"bg-red-50 border-red-300 text-red-800 hover:bg-red-100\"\n                            >\n                              🎯 Direct Audio Test\n                            </Button>\n                          </>\n                        )}\n                      </div>\n                    )}\n                  </div>\n\n                  <Separator className=\"my-4\" />\n\n                  <h3 className=\"text-md font-medium mb-2\">Microphone Settings</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"mic-select\">Microphone Device</Label>\n                    <div className=\"flex gap-2 items-center\">\n                      <Select\n                        value={selectedMicDevice}\n                        onValueChange={setSelectedMicDevice}\n                        disabled={sessionActive}\n                      >\n                        <SelectTrigger id=\"mic-select\" className=\"flex-1\">\n                          <SelectValue placeholder=\"Select microphone\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {micDevices.length === 0 ? (\n                            <SelectItem value=\"no-devices\" disabled>No microphones detected</SelectItem>\n                          ) : (\n                            micDevices.map((device) => (\n                              <SelectItem key={device.deviceId || 'unknown'} value={device.deviceId || 'default'}>\n                                {device.label || `Microphone ${(device.deviceId || 'unknown').substr(0, 5)}...`}\n                              </SelectItem>\n                            ))\n                          )}\n                        </SelectContent>\n                      </Select>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={fetchMicrophoneDevices}\n                        disabled={sessionActive}\n                      >\n                        <RefreshCw className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                    <p className=\"text-xs text-gray-500\">\n                      Select your preferred microphone device for audio input.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <Label>Microphone Level</Label>\n                      <span className={`text-xs ${audioInputLevel > 0.1 ? 'text-green-500' : 'text-gray-500'}`}>\n                        {isRecording ? 'Active' : 'Inactive'}\n                      </span>\n                    </div>\n                    <div className=\"h-8 border rounded-md overflow-hidden\">\n                      <div\n                        className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-75`}\n                        style={{ width: `${Math.min(100, audioInputLevel * 100)}%` }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-gray-500\">\n                      Visualizes microphone input level when recording.\n                    </p>\n                  </div>\n\n                  <Separator className=\"my-4\" />\n                  \n                  <h3 className=\"text-md font-medium mb-2\">Voice Output Settings</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"voice-select\">AI Voice</Label>\n                    <Select\n                      value={selectedVoice}\n                      onValueChange={setSelectedVoice}\n                      disabled={sessionActive || audioOutputPlaying}\n                    >\n                      <SelectTrigger id=\"voice-select\">\n                        <SelectValue placeholder=\"Select voice\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"alloy\">Alloy</SelectItem>\n                        <SelectItem value=\"echo\">Echo</SelectItem>\n                        <SelectItem value=\"fable\">Fable</SelectItem>\n                        <SelectItem value=\"onyx\">Onyx</SelectItem>\n                        <SelectItem value=\"nova\">Nova</SelectItem>\n                        <SelectItem value=\"shimmer\">Shimmer</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"voice-speed\">\n                      Voice Speed: {voiceSpeed.toFixed(2)}\n                    </Label>\n                    <Slider\n                      id=\"voice-speed\"\n                      min={0.5}\n                      max={2.0}\n                      step={0.05}\n                      value={[voiceSpeed]}\n                      onValueChange={(values) => setVoiceSpeed(values[0])}\n                      disabled={sessionActive || audioOutputPlaying}\n                    />\n                    <div className=\"flex justify-between text-xs text-gray-500\">\n                      <span>Slower</span>\n                      <span>Faster</span>\n                    </div>\n                  </div>\n                  \n                  <Button \n                    onClick={testAudio}\n                    disabled={audioOutputPlaying || (!wsRef.current && sessionActive)}\n                    className=\"w-full flex gap-2\"\n                  >\n                    {audioOutputPlaying ? (\n                      <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                    ) : (\n                      <Volume2 className=\"h-4 w-4\" />\n                    )}\n                    {audioOutputPlaying ? \"Playing...\" : \"Test Audio Output\"}\n                  </Button>\n                  \n                  <Separator />\n                  \n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"temperature\">\n                      AI Creativity: {temperature.toFixed(2)}\n                    </Label>\n                    <Slider\n                      id=\"temperature\"\n                      min={0.1}\n                      max={1.0}\n                      step={0.05}\n                      value={[temperature]}\n                      onValueChange={(values) => setTemperature(values[0])}\n                      disabled={sessionActive}\n                    />\n                    <div className=\"flex justify-between text-xs text-gray-500\">\n                      <span>Precise</span>\n                      <span>Creative</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"debug\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Settings className=\"h-5 w-5\" />\n                    Debug Controls\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label htmlFor=\"debug-enabled\" className=\"text-sm font-medium\">\n                          Enable Debug Logging\n                        </Label>\n                        <p className=\"text-xs text-gray-500\">\n                          Show detailed debug information and logs\n                        </p>\n                      </div>\n                      <Switch\n                        id=\"debug-enabled\"\n                        checked={debugEnabled}\n                        onCheckedChange={(checked) => {\n                          setDebugEnabled(checked);\n                          debugManager.setEnabled(checked);\n                        }}\n                      />\n                    </div>\n\n                    {debugEnabled && (\n                      <>\n                        <Separator />\n                        <div className=\"space-y-3\">\n                          <h4 className=\"text-sm font-medium\">Debug Categories</h4>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">Real-time Conversation</Label>\n                              <p className=\"text-xs text-gray-500\">OpenAI Realtime API events</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.realTimeConversation}\n                              onCheckedChange={(checked) => debugManager.setCategory('realTimeConversation', checked)}\n                            />\n                          </div>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">Audio Processing</Label>\n                              <p className=\"text-xs text-gray-500\">Audio input/output processing</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.audioProcessing}\n                              onCheckedChange={(checked) => debugManager.setCategory('audioProcessing', checked)}\n                            />\n                          </div>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">WebSocket Events</Label>\n                              <p className=\"text-xs text-gray-500\">WebSocket connection events</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.websocketEvents}\n                              onCheckedChange={(checked) => debugManager.setCategory('websocketEvents', checked)}\n                            />\n                          </div>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">Session Management</Label>\n                              <p className=\"text-xs text-gray-500\">Session lifecycle events</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.sessionManagement}\n                              onCheckedChange={(checked) => debugManager.setCategory('sessionManagement', checked)}\n                            />\n                          </div>\n                        </div>\n\n                        <Separator />\n                        <div className=\"space-y-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => setResponseLog([])}\n                            className=\"w-full\"\n                          >\n                            Clear Debug Logs\n                          </Button>\n                        </div>\n                      </>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"logs\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>System Logs</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {!debugEnabled ? (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      <p>Debug logging is disabled.</p>\n                      <p className=\"text-sm mt-2\">Enable debug mode to see system logs.</p>\n                    </div>\n                  ) : (\n                    <div className=\"bg-black text-green-400 font-mono text-xs p-4 rounded-md h-80 overflow-y-auto\">\n                      {responseLog.length === 0 ? (\n                        <p>No logs yet - start a session to see debug output</p>\n                      ) : (\n                        responseLog.map((log, idx) => (\n                          <div key={idx}>{log}</div>\n                        ))\n                      )}\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminAITest;", "modifiedCode": "import React, { useState, useRef, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from \"@/components/ui/card\";\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"@/components/ui/tabs\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Mic, MicOff, Download, RefreshCw, Zap, Volume2, Settings } from \"lucide-react\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Slider } from \"@/components/ui/slider\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { debugManager } from \"@/lib/debug-config\";\n\n// WebSocket URL using the same protocol and host as the current page\nfunction getWebSocketUrl() {\n  const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n  return `${protocol}//${window.location.host}/ws`;\n}\n\nconst AdminAITest = () => {\n  const { toast } = useToast();\n  const [sessionActive, setSessionActive] = useState(false);\n  const [transcript, setTranscript] = useState<Array<{ speaker: string; text: string; timestamp: Date }>>([]);\n  const [isRecording, setIsRecording] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState(\"disconnected\");\n  const [responseLog, setResponseLog] = useState<string[]>([]);\n  const [selectedVoice, setSelectedVoice] = useState(\"shimmer\");\n  const [voiceSpeed, setVoiceSpeed] = useState(1.05);\n  const [temperature, setTemperature] = useState(0.7);\n  const [loadingStatus, setLoadingStatus] = useState(\"\");\n  const [summary, setSummary] = useState<string | null>(null);\n  const [audioInputLevel, setAudioInputLevel] = useState(0);\n  const [audioOutputPlaying, setAudioOutputPlaying] = useState(false);\n  const [micDevices, setMicDevices] = useState<MediaDeviceInfo[]>([]);\n  const [selectedMicDevice, setSelectedMicDevice] = useState<string>(\"\");\n  const [debugEnabled, setDebugEnabled] = useState(debugManager.isEnabled);\n  \n  // Audio diagnostics state\n  const [audioSupported, setAudioSupported] = useState(false);\n  const [micPermissionGranted, setMicPermissionGranted] = useState(false);\n  const [audioContextState, setAudioContextState] = useState<string>(\"suspended\");\n  const [audioStreamActive, setAudioStreamActive] = useState(false);\n  \n  // References\n  const wsRef = useRef<WebSocket | null>(null);\n  const transcriptEndRef = useRef<HTMLDivElement>(null);\n  const testAudioRef = useRef<HTMLAudioElement | null>(null);\n  \n  // Audio context for microphone (16kHz for input)\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const audioStreamRef = useRef<MediaStream | null>(null);\n  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);\n  const analyzerRef = useRef<AnalyserNode | null>(null);\n  const levelCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Client-side speech detection and manual VAD\n  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const [isUserSpeaking, setIsUserSpeaking] = useState(false);\n  const [speechDetectionEnabled, setSpeechDetectionEnabled] = useState(true);\n  const speechBufferRef = useRef<string[]>([]);\n  const speechStartTimeRef = useRef<number | null>(null);\n  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Audio playback context (24kHz for OpenAI output)\n  const playbackContextRef = useRef<AudioContext | null>(null);\n  const audioQueueRef = useRef<AudioBuffer[]>([]);\n  const isPlayingRef = useRef(false);\n  const nextPlayTimeRef = useRef(0);\n  \n  // Transcript accumulation\n  const transcriptAccumulator = useRef<{[speaker: string]: string}>({});\n  const transcriptTimeoutRef = useRef<{[speaker: string]: NodeJS.Timeout}>({});\n\n  // Initialize WebSocket and start session\n  const startInteractiveSession = async () => {\n    setLoadingStatus(\"Connecting to server...\");\n    \n    // Close existing connection if any\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      wsRef.current.close();\n    }\n    \n    try {\n      // Initialize audio first to make sure we have microphone access\n      const audioInitialized = await initializeAudioRecording();\n      \n      if (!audioInitialized) {\n        setLoadingStatus(\"\");\n        toast({\n          title: \"Microphone access required\",\n          description: \"Please allow microphone access to start the voice therapy session.\",\n          variant: \"destructive\",\n        });\n        return;\n      }\n      \n      // Initialize playback audio context (required for hearing AI responses)\n      const playbackReady = await initializePlaybackContext();\n      if (!playbackReady) {\n        addToLog('⚠️ Audio playback not ready - you may not hear AI responses');\n        toast({\n          title: \"Audio Warning\",\n          description: \"Audio playback may not work. Try 'Test Audio Output' first if you can't hear responses.\",\n          variant: \"destructive\",\n        });\n      }\n      \n      // Create WebSocket connection\n      const ws = new WebSocket(getWebSocketUrl());\n      wsRef.current = ws;\n\n      ws.onopen = () => {\n        console.log(\"WebSocket connection established\");\n        setConnectionStatus(\"connected\");\n        addToLog(\"WebSocket connection established\");\n        \n        // Send initial configuration message with Realtime API enabled\n        const startMessage = {\n          type: \"start\",\n          userId: \"1\",\n          clientId: \"1\",\n          useRealtimeAPI: true, // Enable OpenAI Realtime API\n          mode: \"realtime\",\n          behavior: {\n            model: \"gpt-4o-realtime-preview-2025-06-03\",\n            temperature: temperature,\n            voice: {\n              voice: selectedVoice,\n              speed: voiceSpeed\n            }\n          },\n          instructions: \"You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts. Keep responses concise and natural for voice conversation.\"\n        };\n        \n        ws.send(JSON.stringify(startMessage));\n        addToLog(\"🚀 STARTING SESSION: Real-time conversation with server-side VAD\");\n        addToLog(`🎯 CONFIG: Model=${startMessage.behavior.model}, Voice=${startMessage.behavior.voice.voice}, Temp=${startMessage.behavior.temperature}`);\n        setLoadingStatus(\"Initializing session...\");\n      };\n\n              ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          debugManager.log('websocketEvents', \"WebSocket message received:\", data);\n\n          // Enhanced debug logging for real-time conversation events\n          const logRealtimeEvent = (eventType: string, details?: string, isImportant = false) => {\n            const prefix = isImportant ? \"🔥\" : \"📡\";\n            const message = details ? `${prefix} ${eventType}: ${details}` : `${prefix} ${eventType}`;\n            debugManager.log('realTimeConversation', message);\n            if (debugManager.isEnabled) {\n              addToLog(message);\n            }\n          };\n\n          // Log all received messages if debug is enabled\n          if (debugManager.websocketEvents) {\n            addToLog(`📡 RECEIVED: ${data.type} ${data.message ? '- ' + data.message : ''}`);\n          }\n\n          // Log all important OpenAI Realtime API events\n          switch (data.type) {\n            case \"session.created\":\n              logRealtimeEvent(\"SESSION CREATED\", `ID: ${data.session?.id || 'unknown'}`, true);\n              break;\n            case \"session.updated\":\n              const vadConfig = data.session?.turn_detection;\n              const vadType = vadConfig?.type || 'none';\n              logRealtimeEvent(\"SESSION CONFIGURED\", `VAD: ${vadType}, Model: ${data.session?.model || 'unknown'}`, true);\n              break;\n            case \"ready\":\n              logRealtimeEvent(\"SESSION READY\", \"Real-time conversation active\", true);\n              break;\n            case \"input_audio_buffer.speech_started\":\n              logRealtimeEvent(\"🎤 SPEECH STARTED\", \"OpenAI detected you speaking\", true);\n              toast({\n                title: \"🎤 Speech Detected\",\n                description: \"Keep talking, I'm listening...\",\n              });\n              break;\n            case \"input_audio_buffer.speech_stopped\":\n              logRealtimeEvent(\"🔇 SPEECH STOPPED\", \"OpenAI detected silence\", true);\n              toast({\n                title: \"🔇 Processing Speech\",\n                description: \"Transcribing and generating response...\",\n              });\n              break;\n            case \"input_audio_buffer.committed\":\n              logRealtimeEvent(\"AUDIO COMMITTED\", \"Audio buffer sent for processing\");\n              break;\n            case \"conversation.item.created\":\n              const itemType = data.item?.type || 'unknown';\n              logRealtimeEvent(\"CONVERSATION ITEM\", `Created: ${itemType}`);\n              break;\n            case \"conversation.item.input_audio_transcription.completed\":\n              const transcript = data.transcript || '';\n              logRealtimeEvent(\"🎯 USER TRANSCRIBED\", `\"${transcript}\"`, true);\n              break;\n            case \"response.created\":\n              logRealtimeEvent(\"🤖 AI RESPONSE STARTED\", \"Generating response\", true);\n              break;\n            case \"response.output_item.added\":\n              logRealtimeEvent(\"RESPONSE ITEM\", `Added: ${data.item?.type || 'unknown'}`);\n              break;\n            case \"response.content_part.added\":\n              logRealtimeEvent(\"CONTENT PART\", `Type: ${data.part?.type || 'unknown'}`);\n              break;\n            case \"response.audio.delta\":\n              logRealtimeEvent(\"🔊 AUDIO CHUNK\", `${data.delta?.length || 0} chars`);\n              // CRITICAL: Process audio for playback\n              addToLog(`🔊 AUDIO DELTA: ${data.delta ? data.delta.length + ' chars' : 'NO DELTA'}`);\n              console.log('🔊 PROCESSING AUDIO DELTA:', data.delta ? data.delta.length : 'NO DELTA');\n              if (data.delta) {\n                console.log('🔊 CALLING queueAudioChunk with', data.delta.length, 'chars');\n                queueAudioChunk(data.delta);\n              } else {\n                addToLog(`❌ AUDIO DELTA: No delta data in response.audio.delta message`);\n                console.error('❌ NO DELTA DATA IN AUDIO MESSAGE:', data);\n              }\n              break;\n            case \"response.audio.done\":\n              logRealtimeEvent(\"🔊 AUDIO COMPLETE\", \"AI audio generation finished\");\n              break;\n            case \"response.audio_transcript.delta\":\n              // Don't log deltas to avoid spam\n              break;\n            case \"response.audio_transcript.done\":\n              const aiTranscript = data.transcript || '';\n              logRealtimeEvent(\"🤖 AI TRANSCRIBED\", `\"${aiTranscript}\"`, true);\n              break;\n            case \"response.done\":\n              logRealtimeEvent(\"✅ RESPONSE COMPLETE\", \"AI finished responding\", true);\n              break;\n            case \"error\":\n              logRealtimeEvent(\"❌ ERROR\", data.message || 'Unknown error', true);\n              break;\n            case \"rate_limits.updated\":\n              logRealtimeEvent(\"RATE LIMITS\", `Remaining: ${JSON.stringify(data.rate_limits || {})}`);\n              break;\n            default:\n              // Log other events with less emphasis\n              logRealtimeEvent(`OTHER: ${data.type}`, data.message || '');\n              break;\n          }\n          \n          if (data.type === \"ready\") {\n            setSessionActive(true);\n            setLoadingStatus(\"\");\n\n            addToLog(\"✅ SESSION ACTIVE: Microphone streaming enabled, speak naturally\");\n            addToLog(\"💡 INSTRUCTIONS: Just speak - OpenAI will detect when you start/stop talking\");\n            setIsRecording(true);\n          }\n\n          // CRITICAL FIX: Also activate session on session.created or response.audio.delta\n          if (data.type === \"session.created\" || data.type === \"response.audio.delta\") {\n            if (!sessionActive) {\n              addToLog(\"🔧 FORCE ACTIVATING SESSION: Detected working OpenAI connection\");\n              setSessionActive(true);\n              setIsRecording(true);\n              setLoadingStatus(\"\");\n            }\n            \n            toast({\n              title: \"Session started\",\n              description: \"Voice recognition is active. Start speaking.\",\n            });\n          } else if (data.type === \"transcription\") {\n            // Add user speech to transcript\n            if (data.text) {\n              addToTranscript(\"You\", data.text);\n            }\n          } else if (data.type === \"conversation.item.input_audio_transcription.completed\") {\n            // OpenAI Realtime API - user speech transcription\n            const text = data.transcript || \"\";\n            if (text) {\n              addToTranscript(\"You\", text);\n            }\n          } else if (data.type === \"input_audio_buffer.speech_started\") {\n            // OpenAI Realtime API - speech detection started\n            addToLog(\"🎤 User speech started\");\n            toast({\n              title: \"Speech Detected\",\n              description: \"Listening to your voice...\",\n            });\n          } else if (data.type === \"input_audio_buffer.speech_stopped\") {\n            // OpenAI Realtime API - speech detection stopped\n            addToLog(\"🔇 User speech stopped\");\n            toast({\n              title: \"Processing Speech\",\n              description: \"Transcribing and generating response...\",\n            });\n          } else if (data.type === \"response.audio_transcript.delta\") {\n            // Ignore fragments - we'll use the complete transcript from done event\n            // This prevents fragmented display\n          } else if (data.type === \"response.audio_transcript.done\") {\n            // Use only the complete transcript from OpenAI\n            if (data.transcript && data.transcript.trim()) {\n              addToTranscript(\"AI\", data.transcript.trim());\n            }\n          } else if (data.type === \"response.done\") {\n            // OpenAI Realtime API - response completed\n            addToLog(\"Response completed\");\n          } else if (data.type === \"assistant_response\" || data.type === \"chunk\" || data.type === \"text\") {\n            // Legacy API response handling\n            const content = data.content || data.text || \"\";\n            if (content) {\n              addToTranscript(\"AI\", content);\n            }\n          } else if (data.type === \"audio_chunk\") {\n            // Handle audio playback (legacy)\n            if (data.audioData) {\n              queueAudioChunk(data.audioData);\n            }\n          } else if (data.type === \"test_audio_response\") {\n            // Handle test audio response\n            if (data.audioUrl) {\n              playTestAudio(data.audioUrl);\n            }\n            setAudioOutputPlaying(false);\n          } else if (data.type === \"error\") {\n            toast({\n              title: \"Error\",\n              description: data.message || \"Something went wrong\",\n              variant: \"destructive\",\n            });\n          } else if (data.type === \"summary\") {\n            // Display conversation summary\n            setSummary(data.content || \"No summary available\");\n          }\n        } catch (error) {\n          console.error(\"Error parsing WebSocket message:\", error);\n        }\n      };\n\n      ws.onclose = () => {\n        console.log(\"WebSocket connection closed\");\n        setConnectionStatus(\"disconnected\");\n        setSessionActive(false);\n        setIsRecording(false);\n        stopAudioRecording();\n        addToLog(\"WebSocket connection closed\");\n      };\n\n      ws.onerror = (error) => {\n        console.error(\"WebSocket error:\", error);\n        setConnectionStatus(\"error\");\n        toast({\n          title: \"Connection error\",\n          description: \"WebSocket connection failed\",\n          variant: \"destructive\",\n        });\n      };\n    } catch (error) {\n      console.error(\"Error starting session:\", error);\n      setLoadingStatus(\"\");\n      toast({\n        title: \"Session error\",\n        description: error instanceof Error ? error.message : \"Failed to start session\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Auto-scroll to bottom when transcript updates\n  useEffect(() => {\n    if (transcriptEndRef.current) {\n      transcriptEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n    }\n  }, [transcript]);\n\n  // Start microphone level monitoring on mount\n  useEffect(() => {\n    startMicrophoneLevelMonitoring();\n    return () => {\n      // Cleanup on unmount\n      if (levelCheckIntervalRef.current) {\n        clearInterval(levelCheckIntervalRef.current);\n      }\n    };\n  }, []);\n\n  // Initialize playback audio context for 24kHz OpenAI audio\n  const initializePlaybackContext = async () => {\n    try {\n      console.log('🔊 Initializing playback context...');\n      addToLog('🔊 Initializing audio playback context');\n      \n      if (!playbackContextRef.current) {\n        // Create AudioContext with proper error handling\n        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;\n        if (!AudioContextClass) {\n          throw new Error('AudioContext not supported in this browser');\n        }\n        \n        playbackContextRef.current = new AudioContextClass({\n          sampleRate: 24000 // OpenAI Realtime API uses 24kHz\n        });\n        \n        console.log('🔊 AudioContext created, state:', playbackContextRef.current.state);\n        addToLog(`🔊 AudioContext created (${playbackContextRef.current.sampleRate}Hz, state: ${playbackContextRef.current.state})`);\n      }\n      \n      // Resume if needed (required for user interaction)\n      if (playbackContextRef.current.state === 'suspended') {\n        console.log('🔊 Resuming suspended AudioContext...');\n        addToLog('🔊 Resuming suspended AudioContext');\n        await playbackContextRef.current.resume();\n        console.log('🔊 AudioContext resumed, new state:', playbackContextRef.current.state);\n        addToLog(`🔊 AudioContext resumed (state: ${playbackContextRef.current.state})`);\n      }\n      \n      if (playbackContextRef.current.state === 'running') {\n        console.log('✅ Playback context ready!');\n        addToLog('✅ Playback context ready');\n        return true;\n      } else {\n        console.warn('⚠️ AudioContext state:', playbackContextRef.current.state);\n        addToLog(`⚠️ AudioContext state: ${playbackContextRef.current.state}`);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error initializing playback context:', error);\n      addToLog(`❌ Playback context failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      toast({\n        title: \"Audio Playback Error\",\n        description: \"Failed to initialize audio playback. Try clicking 'Test Audio Output' first.\",\n        variant: \"destructive\",\n      });\n      \n      return false;\n    }\n  };\n\n  // Add text to transcript display\n  const addToTranscript = (speaker: string, text: string) => {\n    setTranscript(prev => [...prev, {\n      speaker,\n      text,\n      timestamp: new Date()\n    }]);\n  };\n\n  // Accumulate transcript deltas and flush complete phrases\n  const accumulateTranscript = (speaker: string, delta: string) => {\n    // Clear any existing timeout for this speaker\n    if (transcriptTimeoutRef.current[speaker]) {\n      clearTimeout(transcriptTimeoutRef.current[speaker]);\n    }\n\n    // Accumulate the delta\n    if (!transcriptAccumulator.current[speaker]) {\n      transcriptAccumulator.current[speaker] = '';\n    }\n    transcriptAccumulator.current[speaker] += delta;\n\n    // Set a longer timeout to ensure complete sentences are accumulated\n    transcriptTimeoutRef.current[speaker] = setTimeout(() => {\n      const accumulatedText = transcriptAccumulator.current[speaker];\n      if (accumulatedText && accumulatedText.trim()) {\n        addToTranscript(speaker, accumulatedText.trim());\n        transcriptAccumulator.current[speaker] = '';\n      }\n    }, 1000); // 1 second delay to accumulate complete sentences\n  };\n\n  // Add message to response log\n  const addToLog = (message: string) => {\n    if (debugManager.isEnabled) {\n      setResponseLog(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`].slice(-20));\n    }\n  };\n  \n  // Queue audio chunk for proper sequential playback\n  const queueAudioChunk = async (audioData: string) => {\n    try {\n      console.log('🔊 QUEUE AUDIO CHUNK CALLED:', audioData.length, 'chars');\n      addToLog(`🔊 AI AUDIO CHUNK: ${audioData.length} chars (${(audioData.length * 0.75 / 1024).toFixed(1)}KB)`);\n      console.log('🔊 queueAudioChunk function executing...');\n\n      if (!playbackContextRef.current) {\n        console.log('🔊 Initializing playback context...');\n        await initializePlaybackContext();\n      }\n\n      if (!playbackContextRef.current) {\n        console.error('❌ No playback context available');\n        addToLog('❌ PLAYBACK ERROR: No audio context - cannot play AI voice');\n        return;\n      }\n\n      // Convert base64 to ArrayBuffer (PCM16 format)\n      const binaryString = atob(audioData);\n      const bytes = new Uint8Array(binaryString.length);\n      for (let i = 0; i < binaryString.length; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n      }\n      \n      console.log('🔊 Decoded audio data:', bytes.length, 'bytes');\n      addToLog(`🔊 Decoded ${bytes.length} bytes of audio data`);\n      \n      // Create audio buffer for 24kHz PCM16\n      const arrayBuffer = bytes.buffer.slice(bytes.byteOffset, bytes.byteOffset + bytes.byteLength);\n      const audioBuffer = playbackContextRef.current.createBuffer(1, arrayBuffer.byteLength / 2, 24000);\n      const channelData = audioBuffer.getChannelData(0);\n      \n      // Convert Int16 to Float32\n      const int16Array = new Int16Array(arrayBuffer);\n      for (let i = 0; i < int16Array.length; i++) {\n        channelData[i] = int16Array[i] / 32768.0; // Convert to -1.0 to 1.0 range\n      }\n      \n      console.log('🔊 Audio buffer created:', audioBuffer.length, 'samples,', audioBuffer.duration.toFixed(3), 'seconds');\n      addToLog(`🔊 Buffer: ${audioBuffer.length} samples, ${audioBuffer.duration.toFixed(3)}s`);\n      \n      // Add to queue\n      audioQueueRef.current.push(audioBuffer);\n      console.log('🔊 Queue size:', audioQueueRef.current.length);\n      addToLog(`📋 Audio queue size: ${audioQueueRef.current.length}`);\n      \n      // Start playing if not already playing\n      if (!isPlayingRef.current) {\n        console.log('🔊 Starting audio playback...');\n        addToLog('▶️ AI SPEAKING: Starting voice playback');\n        setAudioOutputPlaying(true);\n        playNextInQueue();\n      }\n    } catch (error) {\n      console.error('❌ Error queueing audio chunk:', error);\n      addToLog(`❌ Audio error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  };\n\n  // Play the next audio buffer in the queue\n  const playNextInQueue = () => {\n    if (!playbackContextRef.current || audioQueueRef.current.length === 0) {\n      isPlayingRef.current = false;\n      setAudioOutputPlaying(false);\n      console.log('🔊 Audio playback complete - queue empty');\n      addToLog('⏹️ AI FINISHED SPEAKING: Voice playback complete');\n      return;\n    }\n\n    isPlayingRef.current = true;\n    const audioBuffer = audioQueueRef.current.shift()!;\n    \n    console.log('🔊 Playing audio buffer:', audioBuffer.duration.toFixed(3), 'seconds');\n    addToLog(`▶️ Playing ${audioBuffer.duration.toFixed(3)}s of audio`);\n    \n    const source = playbackContextRef.current.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(playbackContextRef.current.destination);\n    \n    // Calculate when this should start relative to the audio timeline\n    const currentTime = playbackContextRef.current.currentTime;\n    const startTime = Math.max(currentTime, nextPlayTimeRef.current);\n    \n    source.start(startTime);\n    \n    // Calculate when the next audio should start\n    nextPlayTimeRef.current = startTime + audioBuffer.duration;\n    \n    // When this source ends, play the next one\n    source.onended = () => {\n      console.log('🔊 Audio chunk finished, playing next...');\n      playNextInQueue();\n    };\n  };\n  \n  // Play test audio from URL\n  const playTestAudio = (audioUrl: string) => {\n    try {\n      if (!testAudioRef.current) {\n        testAudioRef.current = new Audio();\n      }\n      \n      testAudioRef.current.src = audioUrl;\n      testAudioRef.current.play().catch(console.error);\n      \n      toast({\n        title: \"Test Audio Playing\",\n        description: \"If you can hear this, your audio output is working correctly.\",\n      });\n    } catch (error) {\n      console.error('Error playing test audio:', error);\n      toast({\n        title: \"Audio Playback Error\",\n        description: \"Failed to play test audio.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Fetch available microphone devices\n  const fetchMicrophoneDevices = async () => {\n    try {\n      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {\n        throw new Error(\"Media devices API not supported in this browser\");\n      }\n      \n      // Get permission first (required to see device labels)\n      await navigator.mediaDevices.getUserMedia({ audio: true });\n      \n      // List available devices\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const audioInputDevices = devices.filter(device => device.kind === 'audioinput');\n      \n      console.log(\"Available audio input devices:\", audioInputDevices);\n      setMicDevices(audioInputDevices);\n      \n      // Select the default device if available\n      if (audioInputDevices.length > 0) {\n        setSelectedMicDevice(audioInputDevices[0].deviceId);\n      }\n      \n      return audioInputDevices.length > 0;\n    } catch (error) {\n      console.error(\"Error fetching microphone devices:\", error);\n      toast({\n        title: \"Microphone Error\",\n        description: \"Failed to detect microphone devices. Please ensure microphone permissions are granted.\",\n        variant: \"destructive\",\n      });\n      return false;\n    }\n  };\n  \n  // Test audio recording setup\n  const testAudioRecording = async () => {\n    addToLog(\"Testing audio recording setup...\");\n    \n    try {\n      // 1. Check API support\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\"getUserMedia API not supported\");\n      }\n      addToLog(\"✓ getUserMedia API supported\");\n      setAudioSupported(true);\n      \n      // 2. Request microphone permission\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n          sampleRate: 24000\n        } \n      });\n      addToLog(\"✓ Microphone permission granted\");\n      setMicPermissionGranted(true);\n      setAudioStreamActive(true);\n      \n      // 3. Test audio context\n      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({\n        sampleRate: 24000\n      });\n      addToLog(`✓ Audio context created (sample rate: ${audioContext.sampleRate}Hz)`);\n      setAudioContextState(audioContext.state);\n      \n      // 4. Test audio processing\n      const source = audioContext.createMediaStreamSource(stream);\n      const analyzer = audioContext.createAnalyser();\n      analyzer.fftSize = 256;\n      source.connect(analyzer);\n      addToLog(\"✓ Audio analyzer connected\");\n      \n      // 5. Test level detection for 3 seconds\n      let maxLevel = 0;\n      const bufferLength = analyzer.frequencyBinCount;\n      const dataArray = new Uint8Array(bufferLength);\n      \n      const testDuration = 3000; // 3 seconds\n      const startTime = Date.now();\n      \n      const levelTest = () => {\n        if (Date.now() - startTime < testDuration) {\n          analyzer.getByteFrequencyData(dataArray);\n          let sum = 0;\n          for (let i = 0; i < bufferLength; i++) {\n            sum += dataArray[i];\n          }\n          const level = sum / bufferLength / 255;\n          maxLevel = Math.max(maxLevel, level);\n          setAudioInputLevel(level);\n          \n          requestAnimationFrame(levelTest);\n        } else {\n          // Test complete\n          setAudioInputLevel(0);\n          stream.getTracks().forEach(track => track.stop());\n          audioContext.close();\n          setAudioStreamActive(false);\n          \n          addToLog(`✓ Audio test complete (max level: ${(maxLevel * 100).toFixed(1)}%)`);\n          \n          if (maxLevel > 0.01) {\n            addToLog(\"✓ Audio input is working correctly\");\n            toast({\n              title: \"Audio Test Passed\",\n              description: `Microphone is working. Max level detected: ${(maxLevel * 100).toFixed(1)}%`,\n            });\n          } else {\n            addToLog(\"⚠ Low audio input detected - check microphone\");\n            toast({\n              title: \"Audio Test Warning\",\n              description: \"Low audio input detected. Please check your microphone volume.\",\n              variant: \"destructive\",\n            });\n          }\n        }\n      };\n      \n      addToLog(\"Testing audio input for 3 seconds - please speak...\");\n      toast({\n        title: \"Audio Test\",\n        description: \"Testing microphone for 3 seconds. Please speak into your microphone.\",\n      });\n      \n      // Resume audio context if needed\n      if (audioContext.state === 'suspended') {\n        await audioContext.resume();\n      }\n      \n      levelTest();\n      \n    } catch (error) {\n      console.error(\"Audio test failed:\", error);\n      addToLog(`✗ Audio test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      toast({\n        title: \"Audio Test Failed\",\n        description: error instanceof Error ? error.message : \"Failed to test audio recording\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Test WebSocket connection\n  const testWebSocketConnection = () => {\n    addToLog(\"Testing WebSocket connection...\");\n    \n    const testWs = new WebSocket(getWebSocketUrl());\n    \n    testWs.onopen = () => {\n      addToLog(\"✓ WebSocket connection successful\");\n      testWs.send(JSON.stringify({ type: \"ping\" }));\n    };\n    \n    testWs.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        addToLog(`✓ WebSocket message received: ${data.type}`);\n      } catch (error) {\n        addToLog(`✓ WebSocket raw message: ${event.data}`);\n      }\n      testWs.close();\n    };\n    \n    testWs.onclose = () => {\n      addToLog(\"✓ WebSocket test complete\");\n      toast({\n        title: \"WebSocket Test\",\n        description: \"WebSocket connection test completed successfully\",\n      });\n    };\n    \n    testWs.onerror = (error) => {\n      addToLog(\"✗ WebSocket connection failed\");\n      toast({\n        title: \"WebSocket Test Failed\",\n        description: \"Failed to connect to WebSocket server\",\n        variant: \"destructive\",\n      });\n    };\n  };\n\n  // Generate test audio from OpenAI\n  const generateTestAudio = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      addToLog(\"🎤 Requesting test audio generation...\");\n      \n      wsRef.current.send(JSON.stringify({\n        type: \"generate_test_audio\",\n        text: \"Hello, this is a test of the voice system. I am speaking clearly so you can hear me and test the audio processing.\"\n      }));\n      \n      toast({\n        title: \"Generating Test Audio\",\n        description: \"OpenAI is generating test speech audio...\",\n      });\n    } else {\n      toast({\n        title: \"Not Connected\",\n        description: \"Please start a session first\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Test complete round-trip audio flow\n  const testRoundTripAudio = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      addToLog(\"🔄 Starting round-trip audio test...\");\n      \n      wsRef.current.send(JSON.stringify({\n        type: \"test_round_trip\"\n      }));\n      \n      toast({\n        title: \"Round-Trip Test\",\n        description: \"Testing complete audio generation and processing flow...\",\n      });\n    } else {\n      toast({\n        title: \"Not Connected\",\n        description: \"Please start a session first\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Enhanced microphone level monitoring\n  const startMicrophoneLevelMonitoring = (forceStart: boolean = false) => {\n    addToLog(`Attempting to start microphone monitoring: recording=${isRecording}, forceStart=${forceStart}, analyzer=${!!analyzerRef.current}`);\n    \n    if (!analyzerRef.current) {\n      addToLog(\"⚠ No analyzer available for level monitoring\");\n      return;\n    }\n    \n    if (!isRecording && !forceStart) {\n      addToLog(\"⚠ Not recording, skipping level monitoring\");\n      return;\n    }\n    \n    addToLog(\"✓ Starting microphone level monitoring\");\n    const analyzer = analyzerRef.current;\n    const bufferLength = analyzer.frequencyBinCount;\n    const dataArray = new Uint8Array(bufferLength);\n    \n    const updateLevel = () => {\n      // Check current states\n      const currentlyRecording = isRecording;\n      const hasAnalyzer = !!analyzerRef.current;\n      const sessionIsActive = sessionActive;\n      \n      if (!currentlyRecording || !hasAnalyzer || !sessionIsActive) {\n        setAudioInputLevel(0);\n        addToLog(`Level monitoring stopped: recording=${currentlyRecording}, analyzer=${hasAnalyzer}, session=${sessionIsActive}`);\n        return;\n      }\n      \n      try {\n        analyzer.getByteFrequencyData(dataArray);\n        \n        // Calculate average frequency amplitude for visualization\n        let sum = 0;\n        for (let i = 0; i < bufferLength; i++) {\n          sum += dataArray[i];\n        }\n        const average = sum / bufferLength / 255;\n        \n        // Apply some amplification for better visibility\n        const amplified = Math.min(1, average * 3);\n        setAudioInputLevel(amplified);\n        \n        requestAnimationFrame(updateLevel);\n      } catch (error) {\n        console.error(\"Error updating audio level:\", error);\n        addToLog(`Error in level monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        setAudioInputLevel(0);\n      }\n    };\n    \n    updateLevel();\n  };\n\n  // Enhanced audio recording initialization with comprehensive error handling\n  const initializeAudioRecording = async () => {\n    try {\n      addToLog(\"🎤 Initializing audio recording...\");\n\n      // Check browser support\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error(\"Media devices API not supported in this browser\");\n      }\n\n      if (!window.AudioContext && !(window as any).webkitAudioContext) {\n        throw new Error(\"Web Audio API not supported in this browser\");\n      }\n\n      // Clean up any existing audio context\n      if (audioContextRef.current) {\n        try {\n          await audioContextRef.current.close();\n        } catch (e) {\n          console.warn(\"Error closing existing audio context:\", e);\n        }\n        audioContextRef.current = null;\n      }\n\n      // Request microphone access with optimal settings for speech recognition\n      const constraints: MediaStreamConstraints = {\n        audio: selectedMicDevice\n          ? {\n              deviceId: { exact: selectedMicDevice },\n              echoCancellation: true,\n              noiseSuppression: false,  // Disable to preserve speech clarity for OpenAI\n              autoGainControl: true,\n              sampleRate: { ideal: 24000, min: 16000, max: 48000 },\n              channelCount: 1,  // Mono audio for efficiency\n            }\n          : {\n              echoCancellation: true,\n              noiseSuppression: false,  // Disable to preserve speech clarity for OpenAI\n              autoGainControl: true,\n              sampleRate: { ideal: 24000, min: 16000, max: 48000 },\n              channelCount: 1,  // Mono audio for efficiency\n            }\n      };\n\n      console.log(\"🎤 Using audio constraints:\", constraints);\n      addToLog(`🎤 Requesting microphone access${selectedMicDevice ? ` (device: ${selectedMicDevice.substr(0, 8)}...)` : ''}`);\n\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      audioStreamRef.current = stream;\n      setAudioStreamActive(true);\n      setMicPermissionGranted(true);\n      addToLog(\"✅ Microphone access granted\");\n\n      // Log actual stream settings\n      const audioTrack = stream.getAudioTracks()[0];\n      if (audioTrack) {\n        const settings = audioTrack.getSettings();\n        addToLog(`🎤 Actual audio settings: ${settings.sampleRate}Hz, ${settings.channelCount} channel(s)`);\n        console.log(\"🎤 Audio track settings:\", settings);\n      }\n\n      // Initialize audio context with 24kHz for OpenAI Realtime API\n      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;\n      audioContextRef.current = new AudioContextClass({\n        sampleRate: 24000, // Match OpenAI Realtime API sample rate exactly\n      });\n\n      const audioContext = audioContextRef.current;\n      addToLog(`🎤 Audio context created: ${audioContext.sampleRate}Hz, state: ${audioContext.state}`);\n\n      // Resume if suspended (required for user interaction)\n      if (audioContext.state === 'suspended') {\n        addToLog(\"🎤 Resuming suspended audio context...\");\n        await audioContext.resume();\n      }\n      setAudioContextState(audioContext.state);\n      addToLog(`✅ Audio context ready: ${audioContext.state}`);\n\n      const source = audioContext.createMediaStreamSource(stream);\n\n      // Create analyzer for audio visualization\n      const analyzer = audioContext.createAnalyser();\n      analyzer.fftSize = 256;\n      analyzer.smoothingTimeConstant = 0.8;\n      analyzer.minDecibels = -90;\n      analyzer.maxDecibels = -10;\n      source.connect(analyzer);\n      analyzerRef.current = analyzer;\n      addToLog(\"✅ Audio analyzer connected\");\n\n      // Create processor for audio processing (using deprecated but reliable ScriptProcessorNode)\n      const processor = audioContext.createScriptProcessor(4096, 1, 1);\n      audioProcessorRef.current = processor;\n      \n      // Set up audio processor for real-time audio streaming\n      let audioFrameCount = 0;\n      addToLog(\"✓ Setting up audio processor callback\");\n      \n      processor.onaudioprocess = (e) => {\n        try {\n          audioFrameCount++;\n\n          // Debug: Log that processor is working\n          if (audioFrameCount <= 5) {\n            console.log(`🔄 Audio processor callback #${audioFrameCount}`);\n            addToLog(`🔄 Audio processor active: frame ${audioFrameCount}`);\n          }\n\n          // CRITICAL DEBUG: Log every 50 frames to confirm processor is running\n          if (audioFrameCount % 50 === 0) {\n            addToLog(`🔄 Audio processor still active: frame ${audioFrameCount}`);\n          }\n\n          // Check if we should be recording and sending audio\n          const wsConnected = wsRef.current !== null && wsRef.current.readyState === WebSocket.OPEN;\n          // FORCE RECORDING: If WebSocket is connected, always record (bypass broken state management)\n          const currentlyRecording = wsConnected;\n\n          // Log the first few callbacks to confirm it's working\n          if (audioFrameCount <= 3) {\n            addToLog(`🎤 Audio callback ${audioFrameCount}: recording=${currentlyRecording}, session=${sessionActive}, ws=${wsConnected}`);\n          }\n\n          // CRITICAL DEBUG: Log recording state every 50 frames\n          if (audioFrameCount % 50 === 0) {\n            addToLog(`🎤 Recording check frame ${audioFrameCount}: recording=${currentlyRecording}, session=${sessionActive}, ws=${wsConnected}, isRecording=${isRecording}`);\n          }\n\n          // Always update audio level for visual feedback, even if not recording\n          if (analyzerRef.current) {\n            try {\n              const analyzer = analyzerRef.current;\n              const bufferLength = analyzer.frequencyBinCount;\n              const dataArray = new Uint8Array(bufferLength);\n              analyzer.getByteFrequencyData(dataArray);\n\n              let sum = 0;\n              for (let i = 0; i < bufferLength; i++) {\n                sum += dataArray[i];\n              }\n              const level = (sum / bufferLength / 255) * 3; // Amplify for visibility\n              setAudioInputLevel(Math.min(1, level));\n\n              // Debug: Log audio level for first few frames\n              if (audioFrameCount <= 3) {\n                addToLog(`🔊 Visual level update: ${(level * 100).toFixed(1)}% (analyzer working)`);\n              }\n            } catch (analyzerError) {\n              console.warn(\"Error updating audio level:\", analyzerError);\n              if (audioFrameCount <= 3) {\n                addToLog(`❌ Visual level error: ${analyzerError instanceof Error ? analyzerError.message : 'Unknown'}`);\n              }\n            }\n          } else {\n            // No analyzer available\n            if (audioFrameCount <= 3) {\n              addToLog(`❌ No analyzer available for visual level update`);\n            }\n          }\n\n          if (currentlyRecording) {\n            // Get audio data from the input channel\n            const inputData = e.inputBuffer.getChannelData(0);\n\n            if (!inputData || inputData.length === 0) {\n              console.warn(\"No audio input data received\");\n              return;\n            }\n\n            // Calculate audio energy to detect if there's actual sound\n            let energy = 0;\n            for (let i = 0; i < inputData.length; i++) {\n              energy += inputData[i] * inputData[i];\n            }\n            const rms = Math.sqrt(energy / inputData.length);\n\n            // Convert Float32 audio to Int16 PCM for OpenAI with moderate gain\n            const int16Data = new Int16Array(inputData.length);\n            const gainMultiplier = 2.0; // Moderate amplification for clarity\n\n            for (let i = 0; i < inputData.length; i++) {\n              // Apply gain and convert float [-1,1] to int16 [-32768,32767] with proper clamping\n              let sample = inputData[i] * gainMultiplier;\n              sample = Math.max(-1, Math.min(1, sample)); // Clamp to prevent distortion\n              int16Data[i] = sample < 0 ? Math.round(sample * 32768) : Math.round(sample * 32767);\n            }\n\n            // Convert Int16Array to base64 for OpenAI Realtime API\n            const buffer = new ArrayBuffer(int16Data.length * 2);\n            const view = new DataView(buffer);\n            for (let i = 0; i < int16Data.length; i++) {\n              view.setInt16(i * 2, int16Data[i], true); // little-endian\n            }\n            const uint8Array = new Uint8Array(buffer);\n            const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array) as any));\n          \n            // HYBRID VAD: Use client-side detection to control audio streaming with silence gaps\n            const amplifiedRms = rms * gainMultiplier;\n            const speechThreshold = 0.01;\n            const isSpeechDetected = amplifiedRms > speechThreshold;\n\n            // Always send audio to OpenAI (including silence) for proper VAD processing\n            try {\n              wsRef.current!.send(JSON.stringify({\n                type: 'input_audio_buffer.append',\n                audio: base64Audio\n              }));\n\n              // Log successful audio transmission for first few frames\n              if (audioFrameCount <= 3) {\n                addToLog(`✅ Audio sent to OpenAI: ${base64Audio.length} chars, RMS=${amplifiedRms.toFixed(4)}`);\n              }\n\n              // CRITICAL DEBUG: Log every 100 frames to confirm audio is being sent\n              if (audioFrameCount % 100 === 0) {\n                addToLog(`📤 Audio transmission active: frame ${audioFrameCount}, ${base64Audio.length} chars, RMS=${amplifiedRms.toFixed(4)}`);\n              }\n            } catch (wsError) {\n              console.error(\"Error sending audio to WebSocket:\", wsError);\n              addToLog(`❌ Failed to send audio: ${wsError instanceof Error ? wsError.message : 'Unknown error'}`);\n            }\n\n            // Manage speech state with proper timing for visual feedback\n            if (isSpeechDetected && !isUserSpeaking) {\n              setIsUserSpeaking(true);\n              addToLog(\"🎤 SPEECH DETECTED: Audio streaming to OpenAI VAD\");\n\n              // Clear any existing silence timeout\n              if (silenceTimeoutRef.current) {\n                clearTimeout(silenceTimeoutRef.current);\n                silenceTimeoutRef.current = null;\n              }\n            } else if (!isSpeechDetected && isUserSpeaking) {\n              // Start silence timer for visual feedback only\n              if (!silenceTimeoutRef.current) {\n                silenceTimeoutRef.current = setTimeout(() => {\n                  setIsUserSpeaking(false);\n                  addToLog(\"🔇 SILENCE DETECTED: Waiting for OpenAI VAD to process speech end\");\n                  silenceTimeoutRef.current = null;\n                }, 1000); // 1 second delay for visual feedback\n              }\n            }\n\n            // Log speech detection status periodically with more useful info\n            if (audioFrameCount % 100 === 0) { // Reduced frequency to avoid spam\n              const energyLevel = amplifiedRms > 0.1 ? \"LOUD\" : amplifiedRms > 0.01 ? \"GOOD\" : amplifiedRms > 0.001 ? \"LOW\" : \"SILENT\";\n              const wsStatus = wsRef.current?.readyState === WebSocket.OPEN ? \"CONNECTED\" : \"DISCONNECTED\";\n              addToLog(`📊 MIC STATUS: Energy=${energyLevel} (${amplifiedRms.toFixed(4)}), WS=${wsStatus}, Frames=${audioFrameCount}`);\n            }\n\n            // Log very first few audio frames to confirm processor is working\n            if (audioFrameCount <= 10) {\n              console.log(`🔄 Audio processor frame ${audioFrameCount}: RMS=${amplifiedRms.toFixed(4)}, threshold=${speechThreshold}, detected=${isSpeechDetected}`);\n            }\n          }\n        } catch (processorError) {\n          console.error(\"Error in audio processor:\", processorError);\n          if (audioFrameCount <= 5) {\n            addToLog(`❌ Audio processor error: ${processorError instanceof Error ? processorError.message : 'Unknown error'}`);\n          }\n        }\n      };\n      \n      // Connect the processor properly for audio processing\n      source.connect(processor);\n\n      // CRITICAL FIX: ScriptProcessorNode must be connected to destination to work\n      // We create a gain node with zero volume to avoid feedback but enable processing\n      const silentGain = audioContext.createGain();\n      silentGain.gain.value = 0; // Silent output to prevent feedback\n      processor.connect(silentGain);\n      silentGain.connect(audioContext.destination);\n\n      addToLog(\"✓ Audio processor connected with silent output (enables processing, prevents feedback)\");\n      \n      // Store the analyzer for level monitoring\n      analyzerRef.current = analyzer;\n      addToLog(\"✓ Audio analyzer stored for monitoring\");\n      \n      return true;\n    } catch (error) {\n      console.error(\"Error initializing audio recording:\", error);\n      addToLog(`✗ Audio initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      setAudioStreamActive(false);\n      setMicPermissionGranted(false);\n      \n      toast({\n        title: \"Microphone error\",\n        description: error instanceof Error ? error.message : \"Failed to access microphone\",\n        variant: \"destructive\",\n      });\n      \n      return false;\n    }\n  };\n\n  // Stop audio recording\n  const stopAudioRecording = () => {\n    setIsRecording(false);\n    \n    // Clean up audio processing\n    if (audioProcessorRef.current) {\n      try {\n        audioProcessorRef.current.disconnect();\n      } catch (e) {\n        console.error(\"Error disconnecting audio processor:\", e);\n      }\n      audioProcessorRef.current = null;\n    }\n    \n    if (audioStreamRef.current) {\n      audioStreamRef.current.getTracks().forEach(track => track.stop());\n      audioStreamRef.current = null;\n    }\n  };\n  \n  // Test the voice output\n  const testAudio = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      setAudioOutputPlaying(true);\n      \n      // Send test audio request\n      wsRef.current.send(JSON.stringify({\n        type: \"test_audio\",\n        voice: selectedVoice,\n        speed: voiceSpeed,\n        text: \"This is a test of the voice output. Can you hear this clearly?\"\n      }));\n      \n      addToLog(`Sent test audio request (${selectedVoice}, speed: ${voiceSpeed})`);\n      \n      toast({\n        title: \"Testing Audio Output\",\n        description: `Playing test audio with ${selectedVoice} voice`,\n      });\n    } else {\n      // Create a WebSocket connection for testing if one doesn't exist\n      const ws = new WebSocket(getWebSocketUrl());\n      wsRef.current = ws;\n      \n      ws.onopen = () => {\n        setConnectionStatus(\"connected\");\n        addToLog(\"WebSocket connection established for audio test\");\n        \n        // Send test audio request\n        ws.send(JSON.stringify({\n          type: \"test_audio\",\n          voice: selectedVoice,\n          speed: voiceSpeed,\n          text: \"This is a test of the voice output. Can you hear this clearly?\"\n        }));\n        \n        setAudioOutputPlaying(true);\n        addToLog(`Sent test audio request (${selectedVoice}, speed: ${voiceSpeed})`);\n      };\n      \n      ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log(\"Test audio response:\", data);\n          \n          if (data.type === \"test_audio_response\") {\n            // Play the audio\n            const audioElement = new Audio(data.audioUrl);\n            testAudioRef.current = audioElement;\n            \n            audioElement.onplay = () => {\n              addToLog(`Playing test audio (${selectedVoice})`);\n            };\n            \n            audioElement.onended = () => {\n              addToLog(`Test audio playback completed`);\n              setAudioOutputPlaying(false);\n              ws.close();\n            };\n            \n            audioElement.onerror = (e) => {\n              console.error(\"Audio playback error:\", e);\n              setAudioOutputPlaying(false);\n              addToLog(`Error playing test audio`);\n              ws.close();\n            };\n            \n            // Start playback\n            audioElement.play()\n              .catch(err => {\n                console.error(\"Audio play error:\", err);\n                setAudioOutputPlaying(false);\n                addToLog(`Play error: ${err.message}`);\n              });\n          }\n        } catch (error) {\n          console.error(\"Error parsing WebSocket message:\", error);\n        }\n      };\n      \n      ws.onerror = (error) => {\n        console.error(\"WebSocket error during audio test:\", error);\n        setAudioOutputPlaying(false);\n        toast({\n          title: \"Connection Error\",\n          description: \"Failed to connect to server for audio test\",\n          variant: \"destructive\",\n        });\n      };\n    }\n  };\n\n  // End the session and get a summary\n  const endSession = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      // First end the session\n      wsRef.current.send(JSON.stringify({\n        type: \"conversationEnded\"\n      }));\n\n      debugManager.log('sessionManagement', \"Session ended, requesting summary...\");\n      if (debugManager.isEnabled) {\n        addToLog(\"Ended session, requesting summary...\");\n      }\n\n      // Stop recording\n      stopAudioRecording();\n\n      // Request summary after a brief delay to allow session cleanup\n      setTimeout(() => {\n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify({\n            type: \"summary_request\"\n          }));\n\n          debugManager.log('sessionManagement', \"Summary requested\");\n          if (debugManager.isEnabled) {\n            addToLog(\"Summary requested\");\n          }\n        }\n      }, 500);\n\n      toast({\n        title: \"Session ended\",\n        description: \"Generating session summary...\",\n      });\n    }\n  };\n  \n  // Export transcript as text\n  const exportTranscript = () => {\n    const textContent = transcript.map(t => \n      `[${t.timestamp.toLocaleTimeString()}] ${t.speaker}: ${t.text}`\n    ).join('\\n\\n');\n    \n    // Add summary if available\n    const fullContent = summary \n      ? `${textContent}\\n\\n--------\\nSUMMARY:\\n${summary}` \n      : textContent;\n    \n    // Create download link\n    const blob = new Blob([fullContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `voice-therapy-transcript-${new Date().toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n    \n    toast({\n      title: \"Transcript exported\",\n      description: \"The conversation transcript has been downloaded\",\n    });\n  };\n  \n  // Initialize diagnostics on component mount\n  useEffect(() => {\n    // Check audio support\n    const checkAudioSupport = () => {\n      const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n      const hasAudioContext = !!(window.AudioContext || (window as any).webkitAudioContext);\n      \n      setAudioSupported(hasGetUserMedia && hasAudioContext);\n      addToLog(`Audio API support: getUserMedia=${hasGetUserMedia}, AudioContext=${hasAudioContext}`);\n    };\n    \n    checkAudioSupport();\n    \n    // Fetch available microphone devices when the component mounts\n    fetchMicrophoneDevices();\n    \n    return () => {\n      // Stop recording if active\n      stopAudioRecording();\n      \n      // Close WebSocket connection\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n      \n      // Clear intervals\n      if (levelCheckIntervalRef.current) {\n        clearInterval(levelCheckIntervalRef.current);\n      }\n    };\n  }, []);\n  \n  // Start/stop microphone level monitoring when recording state changes\n  useEffect(() => {\n    addToLog(`Recording state changed: isRecording=${isRecording}, sessionActive=${sessionActive}, analyzer=${!!analyzerRef.current}`);\n    \n    if (isRecording && sessionActive) {\n      addToLog(\"✓ Starting microphone level monitoring due to recording state change\");\n      // Use multiple attempts with increasing delays to ensure state has updated\n      setTimeout(() => {\n        startMicrophoneLevelMonitoring(true); // Force start even if state check fails\n      }, 100);\n    } else {\n      setAudioInputLevel(0);\n      if (!isRecording) {\n        addToLog(\"Recording stopped - clearing audio input level\");\n      }\n    }\n  }, [isRecording, sessionActive]);\n\n  // Test debug speech detection\n  const testSpeechDetection = () => {\n    if (!isRecording || !sessionActive) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before testing speech detection.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    debugManager.log('audioProcessing', \"🔍 SPEECH DETECTION TEST STARTED - Say 'Hello' loudly into your microphone\");\n    if (debugManager.isEnabled) {\n      addToLog(\"🔍 SPEECH DETECTION TEST STARTED - Say 'Hello' loudly into your microphone\");\n    }\n\n    toast({\n      title: \"🔍 Speech Detection Test\",\n      description: \"Say 'Hello' loudly into your microphone. Check the debug log for detection status.\",\n    });\n\n    // Log speech detection status every second for 10 seconds\n    let testCount = 0;\n    const testInterval = setInterval(() => {\n      testCount++;\n      const bufferSize = speechBufferRef.current.length;\n      const speaking = isUserSpeaking;\n      const speechTime = speechStartTimeRef.current ? Date.now() - speechStartTimeRef.current : 0;\n\n      const message = `🔍 Test ${testCount}/10: Speaking=${speaking}, Buffer=${bufferSize} chunks, SpeechTime=${speechTime}ms`;\n      debugManager.log('audioProcessing', message);\n      if (debugManager.isEnabled) {\n        addToLog(message);\n      }\n\n      if (testCount >= 10) {\n        clearInterval(testInterval);\n        debugManager.log('audioProcessing', \"🔍 SPEECH DETECTION TEST COMPLETED\");\n        if (debugManager.isEnabled) {\n          addToLog(\"🔍 SPEECH DETECTION TEST COMPLETED\");\n        }\n        toast({\n          title: \"Test Complete\",\n          description: \"Check the debug log to see if your speech was detected. Look for 'Speaking=true' when you spoke.\",\n        });\n      }\n    }, 1000);\n  };\n\n  // Comprehensive voice system test (mimics the successful backend test)\n  const testVoiceSystemComprehensive = () => {\n    if (!sessionActive) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before running the comprehensive test.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    debugManager.log('audioProcessing', \"🚀 COMPREHENSIVE VOICE SYSTEM TEST STARTED\");\n    if (debugManager.isEnabled) {\n      addToLog(\"🚀 COMPREHENSIVE VOICE SYSTEM TEST STARTED\");\n    }\n\n    toast({\n      title: \"🚀 Comprehensive Test\",\n      description: \"Running full voice system test. Check the debug log for detailed results.\",\n    });\n\n    let testResults = {\n      sessionReady: sessionActive,\n      microphoneActive: isRecording,\n      audioContextReady: audioContextRef.current?.state === 'running',\n      websocketConnected: wsRef.current?.readyState === WebSocket.OPEN,\n      audioProcessorActive: !!audioProcessorRef.current,\n      analyzerActive: !!analyzerRef.current\n    };\n\n    const logSystemStatus = (message: string) => {\n      debugManager.log('audioProcessing', message);\n      if (debugManager.isEnabled) {\n        addToLog(message);\n      }\n    };\n\n    logSystemStatus(\"📊 SYSTEM STATUS CHECK:\");\n    logSystemStatus(`  Session Ready: ${testResults.sessionReady ? '✅' : '❌'}`);\n    logSystemStatus(`  Microphone Active: ${testResults.microphoneActive ? '✅' : '❌'}`);\n    logSystemStatus(`  Audio Context: ${testResults.audioContextReady ? '✅' : '❌'} (${audioContextRef.current?.state || 'none'})`);\n    logSystemStatus(`  WebSocket: ${testResults.websocketConnected ? '✅' : '❌'} (${wsRef.current?.readyState || 'none'})`);\n    logSystemStatus(`  Audio Processor: ${testResults.audioProcessorActive ? '✅' : '❌'}`);\n    logSystemStatus(`  Audio Analyzer: ${testResults.analyzerActive ? '✅' : '❌'}`);\n    logSystemStatus(`  Current Audio Level: ${(audioInputLevel * 100).toFixed(1)}%`);\n    logSystemStatus(`  Audio Stream: ${audioStreamRef.current ? '✅ Active' : '❌ None'}`);\n\n    // Additional debugging for audio pipeline\n    if (audioStreamRef.current) {\n      const tracks = audioStreamRef.current.getAudioTracks();\n      logSystemStatus(`  Audio Tracks: ${tracks.length} (${tracks.map(t => t.label || 'Unknown').join(', ')})`);\n      if (tracks.length > 0) {\n        const settings = tracks[0].getSettings();\n        logSystemStatus(`  Track Settings: ${settings.sampleRate}Hz, ${settings.channelCount}ch`);\n      }\n    }\n\n    // Test audio level detection using the same method as the visual indicator\n    if (analyzerRef.current) {\n      logSystemStatus(\"🎤 Testing audio level detection for 5 seconds...\");\n      let maxLevel = 0;\n      let levelTestCount = 0;\n\n      const levelTestInterval = setInterval(() => {\n        levelTestCount++;\n\n        // Use the same audio analysis method as the visual indicator\n        let currentLevel = 0;\n        try {\n          const analyzer = analyzerRef.current;\n          if (analyzer) {\n            const bufferLength = analyzer.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            analyzer.getByteFrequencyData(dataArray);\n\n            let sum = 0;\n            for (let i = 0; i < bufferLength; i++) {\n              sum += dataArray[i];\n            }\n            currentLevel = (sum / bufferLength / 255) * 3; // Same calculation as visual indicator\n            currentLevel = Math.min(1, currentLevel);\n          }\n        } catch (error) {\n          debugManager.warn('audioProcessing', \"Error reading audio level:\", error);\n          currentLevel = audioInputLevel; // Fallback to state variable\n        }\n\n        maxLevel = Math.max(maxLevel, currentLevel);\n\n        if (levelTestCount <= 5) {\n          logSystemStatus(`  Level test ${levelTestCount}/5: ${(currentLevel * 100).toFixed(1)}% (analyzer=${!!analyzerRef.current})`);\n        }\n\n        if (levelTestCount >= 5) {\n          clearInterval(levelTestInterval);\n          logSystemStatus(`✅ Audio level test complete. Max level: ${(maxLevel * 100).toFixed(1)}%`);\n\n          if (maxLevel > 0.05) {\n            logSystemStatus(\"✅ Audio input is working correctly\");\n          } else {\n            logSystemStatus(\"⚠️ Low audio input detected - check microphone volume or analyzer connection\");\n            logSystemStatus(`🔍 Debug: audioInputLevel=${(audioInputLevel * 100).toFixed(1)}%, analyzer=${!!analyzerRef.current}`);\n          }\n        }\n      }, 1000);\n    } else {\n      logSystemStatus(\"❌ No audio analyzer available - microphone may not be properly initialized\");\n    }\n\n    // Generate summary after 6 seconds\n    setTimeout(() => {\n      const allSystemsGo = Object.values(testResults).every(result => result);\n\n      logSystemStatus(\"🔍 COMPREHENSIVE TEST RESULTS:\");\n      if (allSystemsGo) {\n        logSystemStatus(\"✅ ALL SYSTEMS OPERATIONAL - Voice conversation should work!\");\n        toast({\n          title: \"✅ Test Passed\",\n          description: \"All systems are working correctly. Voice conversation should function properly.\",\n        });\n      } else {\n        logSystemStatus(\"❌ SOME SYSTEMS NOT READY - Check failed components above\");\n        toast({\n          title: \"⚠️ Test Issues\",\n          description: \"Some components are not working correctly. Check the debug log for details.\",\n          variant: \"destructive\",\n        });\n      }\n    }, 6000);\n  };\n\n  // Real-time audio flow debugging\n  const testRealTimeAudioFlow = () => {\n    if (!sessionActive) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before testing audio flow.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    const logRealTimeDebug = (message: string) => {\n      debugManager.log('audioProcessing', message);\n      if (debugManager.isEnabled) {\n        addToLog(message);\n      }\n    };\n\n    logRealTimeDebug(\"🔍 REAL-TIME AUDIO FLOW DEBUG STARTED\");\n    logRealTimeDebug(\"📢 SPEAK NOW - monitoring audio processing in real-time\");\n\n    toast({\n      title: \"🔍 Real-Time Debug\",\n      description: \"SPEAK NOW! Monitoring audio processing for 10 seconds.\",\n    });\n\n    let debugCount = 0;\n    let lastAudioFrameCount = 0;\n    let lastTransmissionCount = 0;\n    let audioTransmissionCount = 0;\n\n    // Monitor WebSocket messages for audio responses\n    const originalOnMessage = wsRef.current?.onmessage;\n    let speechStartReceived = false;\n    let transcriptionReceived = false;\n    let aiResponseReceived = false;\n\n    if (wsRef.current) {\n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n\n          switch (data.type) {\n            case 'input_audio_buffer.speech_started':\n              speechStartReceived = true;\n              logRealTimeDebug(\"🎤 REAL-TIME: OpenAI detected speech start!\");\n              break;\n            case 'input_audio_buffer.speech_stopped':\n              logRealTimeDebug(\"🔇 REAL-TIME: OpenAI detected speech stop!\");\n              break;\n            case 'conversation.item.input_audio_transcription.completed':\n              transcriptionReceived = true;\n              logRealTimeDebug(`📝 REAL-TIME: Transcription received: \"${data.transcript}\"`);\n              break;\n            case 'response.audio_transcript.done':\n              aiResponseReceived = true;\n              logRealTimeDebug(`🤖 REAL-TIME: AI response: \"${data.transcript}\"`);\n              break;\n          }\n        } catch (error) {\n          // Ignore parse errors\n        }\n\n        // Call original handler\n        if (originalOnMessage) {\n          originalOnMessage(event);\n        }\n      };\n    }\n\n    const debugInterval = setInterval(() => {\n      debugCount++;\n\n      // Check if audio processor is running\n      const currentFrameCount = audioFrameCount;\n      const framesProcessed = currentFrameCount - lastAudioFrameCount;\n      lastAudioFrameCount = currentFrameCount;\n\n      // Check current audio level\n      let currentLevel = 0;\n      if (analyzerRef.current) {\n        try {\n          const analyzer = analyzerRef.current;\n          const bufferLength = analyzer.frequencyBinCount;\n          const dataArray = new Uint8Array(bufferLength);\n          analyzer.getByteFrequencyData(dataArray);\n\n          let sum = 0;\n          for (let i = 0; i < bufferLength; i++) {\n            sum += dataArray[i];\n          }\n          currentLevel = (sum / bufferLength / 255) * 100;\n        } catch (error) {\n          // Ignore\n        }\n      }\n\n      logRealTimeDebug(`🔍 Debug ${debugCount}/10: Frames=${framesProcessed}/sec, Level=${currentLevel.toFixed(1)}%, Speech=${speechStartReceived}, Transcript=${transcriptionReceived}, AI=${aiResponseReceived}`);\n\n      if (debugCount >= 10) {\n        clearInterval(debugInterval);\n\n        // Restore original message handler\n        if (wsRef.current && originalOnMessage) {\n          wsRef.current.onmessage = originalOnMessage;\n        }\n\n        logRealTimeDebug(\"🔍 REAL-TIME DEBUG COMPLETE\");\n        logRealTimeDebug(\"📊 RESULTS:\");\n        logRealTimeDebug(`  Audio Processor: ${lastAudioFrameCount > 0 ? '✅ Running' : '❌ Not running'}`);\n        logRealTimeDebug(`  Speech Detection: ${speechStartReceived ? '✅ Working' : '❌ Not detected'}`);\n        logRealTimeDebug(`  Transcription: ${transcriptionReceived ? '✅ Working' : '❌ Not received'}`);\n        logRealTimeDebug(`  AI Response: ${aiResponseReceived ? '✅ Working' : '❌ Not received'}`);\n\n        if (!speechStartReceived && lastAudioFrameCount > 0) {\n          logRealTimeDebug(\"⚠️ ISSUE: Audio processor running but OpenAI not detecting speech\");\n          logRealTimeDebug(\"💡 TRY: Speak louder or check microphone sensitivity\");\n        } else if (!lastAudioFrameCount) {\n          logRealTimeDebug(\"❌ ISSUE: Audio processor not running - check audio context connection\");\n        }\n\n        toast({\n          title: \"Debug Complete\",\n          description: \"Check the debug log for detailed audio flow analysis.\",\n        });\n      }\n    }, 1000);\n  };\n\n  // Direct audio test - simpler approach\n  const testDirectAudioSend = () => {\n    if (!sessionActive || !wsRef.current) {\n      toast({\n        title: \"Start Session First\",\n        description: \"Please start an interactive session before testing direct audio.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    addToLog(\"🎯 DIRECT AUDIO TEST - Checking if audio reaches OpenAI\");\n\n    // Generate a simple test audio chunk (like our backend test)\n    const generateTestAudio = () => {\n      const sampleRate = 24000;\n      const duration = 0.1; // 100ms\n      const samples = Math.floor(sampleRate * duration);\n      const buffer = new ArrayBuffer(samples * 2);\n      const view = new DataView(buffer);\n\n      for (let i = 0; i < samples; i++) {\n        const t = i / sampleRate;\n        const frequency = 440; // A4 note\n        const sample = Math.sin(2 * Math.PI * frequency * t) * 0.5;\n        const int16Sample = Math.round(sample * 32767);\n        view.setInt16(i * 2, int16Sample, true);\n      }\n\n      const uint8Array = new Uint8Array(buffer);\n      return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n    };\n\n    // Send 5 test audio chunks\n    let chunkCount = 0;\n    const sendTestChunk = () => {\n      if (chunkCount >= 5) {\n        addToLog(\"🎯 Direct audio test complete - sent 5 chunks\");\n        return;\n      }\n\n      const testAudio = generateTestAudio();\n\n      try {\n        wsRef.current!.send(JSON.stringify({\n          type: 'input_audio_buffer.append',\n          audio: testAudio\n        }));\n\n        chunkCount++;\n        addToLog(`🎯 Sent test audio chunk ${chunkCount}/5: ${testAudio.length} chars`);\n\n        setTimeout(sendTestChunk, 200); // Send every 200ms\n      } catch (error) {\n        addToLog(`❌ Failed to send test audio: ${error instanceof Error ? error.message : 'Unknown'}`);\n      }\n    };\n\n    // Monitor for OpenAI responses\n    const originalOnMessage = wsRef.current.onmessage;\n    let responseCount = 0;\n\n    wsRef.current.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n\n        if (data.type.includes('input_audio_buffer') || data.type.includes('speech') || data.type.includes('response')) {\n          responseCount++;\n          addToLog(`🎯 OpenAI response ${responseCount}: ${data.type}`);\n        }\n      } catch (error) {\n        // Ignore parse errors\n      }\n\n      // Call original handler\n      if (originalOnMessage) {\n        originalOnMessage(event);\n      }\n    };\n\n    // Restore original handler after 10 seconds\n    setTimeout(() => {\n      if (wsRef.current && originalOnMessage) {\n        wsRef.current.onmessage = originalOnMessage;\n      }\n      addToLog(`🎯 Direct test complete. OpenAI responses received: ${responseCount}`);\n\n      if (responseCount === 0) {\n        addToLog(\"❌ ISSUE: No responses from OpenAI - check WebSocket connection or API\");\n      } else {\n        addToLog(\"✅ OpenAI is receiving and processing audio data\");\n      }\n    }, 10000);\n\n    sendTestChunk();\n\n    toast({\n      title: \"🎯 Direct Audio Test\",\n      description: \"Sending test audio directly to OpenAI. Check debug log for results.\",\n    });\n  };\n\n  return (\n    <div className=\"container mx-auto py-6 max-w-5xl\">\n      <h1 className=\"text-3xl font-bold mb-6\">AI Voice Therapy Assistant</h1>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <Card className=\"mb-6\">\n            <CardHeader className=\"flex flex-row items-center justify-between pb-2\">\n              <CardTitle>Real-Time Voice Interaction</CardTitle>\n              <div className=\"flex items-center space-x-2\">\n                <span className={`inline-block w-3 h-3 rounded-full ${\n                  connectionStatus === \"connected\" ? \"bg-green-500\" : \n                  connectionStatus === \"error\" ? \"bg-red-500\" : \"bg-yellow-500\"\n                }`}></span>\n                <span className=\"text-sm text-gray-500\">\n                  {connectionStatus === \"connected\" ? \"Connected\" : \n                   connectionStatus === \"error\" ? \"Error\" : \"Disconnected\"}\n                </span>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {!sessionActive ? (\n                <div className=\"flex flex-col items-center justify-center py-10\">\n                  {loadingStatus ? (\n                    <div className=\"flex flex-col items-center gap-3\">\n                      <RefreshCw className=\"animate-spin h-8 w-8 text-primary\" />\n                      <p>{loadingStatus}</p>\n                    </div>\n                  ) : (\n                    <div className=\"text-center\">\n                      <p className=\"text-lg mb-6\">Start an interactive real-time voice therapy session.</p>\n                                                          <div className=\"text-center\">\n                    <Button onClick={startInteractiveSession} className=\"px-6\" size=\"lg\">\n                      <Zap className=\"mr-2 h-5 w-5\" />\n                      Start Interactive Session\n                    </Button>\n                    <p className=\"text-sm text-gray-500 mt-4\">\n                      Microphone access is required for this feature.\n                    </p>\n                  </div>\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h3 className=\"font-medium\">Live Transcript</h3>\n                    <div className=\"flex items-center space-x-4\">\n                      {isRecording ? (\n                        <div className=\"flex items-center text-green-500\">\n                          <span className=\"inline-block w-2 h-2 rounded-full bg-green-500 mr-2 animate-pulse\"></span>\n                          <span className=\"text-sm\">Recording</span>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center text-red-500\">\n                          <span className=\"inline-block w-2 h-2 rounded-full bg-red-500 mr-2\"></span>\n                          <span className=\"text-sm\">Paused</span>\n                        </div>\n                      )}\n                      {audioOutputPlaying && (\n                        <div className=\"flex items-center text-blue-500\">\n                          <Volume2 className=\"h-4 w-4 mr-1 animate-pulse\" />\n                          <span className=\"text-sm\">AI Speaking</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <ScrollArea className=\"h-80 border rounded-md p-4\">\n                    {transcript.length === 0 ? (\n                      <div className=\"text-center text-gray-500 py-10\">\n                        <p>The conversation transcript will appear here</p>\n                        <p className=\"text-sm mt-2\">Start speaking to begin the session</p>\n                      </div>\n                    ) : (\n                      <div className=\"space-y-4\">\n                        {transcript.map((entry, index) => (\n                          <div key={index} className=\"mb-4\">\n                            <div className=\"flex items-center mb-1\">\n                              <span className=\"font-bold\">{entry.speaker}</span>\n                              <span className=\"text-gray-400 text-xs ml-2\">\n                                {entry.timestamp.toLocaleTimeString()}\n                              </span>\n                            </div>\n                            <div className={`px-4 py-2 rounded-lg ${\n                              entry.speaker === \"You\" \n                                ? \"bg-primary text-primary-foreground ml-0 mr-8\" \n                                : \"bg-secondary text-secondary-foreground ml-8 mr-0\"\n                            }`}>\n                              {entry.text}\n                            </div>\n                          </div>\n                        ))}\n                        <div ref={transcriptEndRef} />\n                      </div>\n                    )}\n                  </ScrollArea>\n                  \n                  {summary && (\n                    <div className=\"mt-4 p-4 border rounded-md bg-muted\">\n                      <h3 className=\"font-bold mb-2\">Session Summary</h3>\n                      <p>{summary}</p>\n                    </div>\n                  )}\n                </>\n              )}\n            </CardContent>\n            {sessionActive && (\n              <CardFooter className=\"flex justify-between\">\n                <div className=\"flex space-x-2\">\n                  <Button\n                    onClick={() => setIsRecording(prev => !prev)}\n                    variant={isRecording ? \"destructive\" : \"outline\"}\n                    className={isRecording ? \"bg-red-500 hover:bg-red-600\" : \"\"}\n                  >\n                    {isRecording ? (\n                      <>\n                        <MicOff className=\"mr-2 h-4 w-4\" /> \n                        Pause Mic\n                      </>\n                    ) : (\n                      <>\n                        <Mic className=\"mr-2 h-4 w-4\" /> \n                        Resume Mic\n                      </>\n                    )}\n                  </Button>\n\n                </div>\n                \n                <div className=\"space-x-2\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={exportTranscript}\n                    disabled={transcript.length === 0}\n                  >\n                    <Download className=\"mr-2 h-4 w-4\" />\n                    Export Transcript\n                  </Button>\n                  \n                  <Button \n                    variant=\"default\" \n                    onClick={endSession}\n                  >\n                    End Session & Summarize\n                  </Button>\n                </div>\n              </CardFooter>\n            )}\n          </Card>\n        </div>\n        \n        <div className=\"lg:col-span-1\">\n          <Tabs defaultValue=\"settings\">\n            <TabsList className=\"w-full\">\n              <TabsTrigger value=\"settings\" className=\"flex-1\">Settings</TabsTrigger>\n              <TabsTrigger value=\"debug\" className=\"flex-1\">Debug</TabsTrigger>\n              <TabsTrigger value=\"logs\" className=\"flex-1\">Logs</TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"settings\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Voice Assistant Settings</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <h3 className=\"text-md font-medium mb-2\">Audio Diagnostics</h3>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Browser Support:</span>\n                      <span className={audioSupported ? \"text-green-500\" : \"text-red-500\"}>\n                        {audioSupported ? \"✓ Supported\" : \"✗ Not Supported\"}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Microphone Permission:</span>\n                      <span className={micPermissionGranted ? \"text-green-500\" : \"text-gray-500\"}>\n                        {micPermissionGranted ? \"✓ Granted\" : \"Not Requested\"}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Audio Context:</span>\n                      <span className={audioContextState === \"running\" ? \"text-green-500\" : \"text-yellow-500\"}>\n                        {audioContextState}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Audio Stream:</span>\n                      <span className={audioStreamActive ? \"text-green-500\" : \"text-gray-500\"}>\n                        {audioStreamActive ? \"✓ Active\" : \"Inactive\"}\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <div className=\"space-x-2\">\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={testAudioRecording}\n                        disabled={sessionActive}\n                      >\n                        Test Recording\n                      </Button>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={testWebSocketConnection}\n                        disabled={sessionActive}\n                      >\n                        Test WebSocket\n                      </Button>\n                    </div>\n                    {sessionActive && (\n                      <div className=\"space-x-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={generateTestAudio}\n                        >\n                          Generate Test Audio\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={testRoundTripAudio}\n                        >\n                          Test Round-Trip\n                        </Button>\n                        {debugEnabled && (\n                          <>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testSpeechDetection}\n                              className=\"bg-yellow-50 border-yellow-300 text-yellow-800 hover:bg-yellow-100\"\n                            >\n                              🔍 Test Speech Detection\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testVoiceSystemComprehensive}\n                              className=\"bg-green-50 border-green-300 text-green-800 hover:bg-green-100\"\n                            >\n                              🚀 Comprehensive Test\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testRealTimeAudioFlow}\n                              className=\"bg-purple-50 border-purple-300 text-purple-800 hover:bg-purple-100\"\n                            >\n                              🔍 Real-Time Audio Debug\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={testDirectAudioSend}\n                              className=\"bg-red-50 border-red-300 text-red-800 hover:bg-red-100\"\n                            >\n                              🎯 Direct Audio Test\n                            </Button>\n                          </>\n                        )}\n                      </div>\n                    )}\n                  </div>\n\n                  <Separator className=\"my-4\" />\n\n                  <h3 className=\"text-md font-medium mb-2\">Microphone Settings</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"mic-select\">Microphone Device</Label>\n                    <div className=\"flex gap-2 items-center\">\n                      <Select\n                        value={selectedMicDevice}\n                        onValueChange={setSelectedMicDevice}\n                        disabled={sessionActive}\n                      >\n                        <SelectTrigger id=\"mic-select\" className=\"flex-1\">\n                          <SelectValue placeholder=\"Select microphone\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {micDevices.length === 0 ? (\n                            <SelectItem value=\"no-devices\" disabled>No microphones detected</SelectItem>\n                          ) : (\n                            micDevices.map((device) => (\n                              <SelectItem key={device.deviceId || 'unknown'} value={device.deviceId || 'default'}>\n                                {device.label || `Microphone ${(device.deviceId || 'unknown').substr(0, 5)}...`}\n                              </SelectItem>\n                            ))\n                          )}\n                        </SelectContent>\n                      </Select>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={fetchMicrophoneDevices}\n                        disabled={sessionActive}\n                      >\n                        <RefreshCw className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                    <p className=\"text-xs text-gray-500\">\n                      Select your preferred microphone device for audio input.\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <Label>Microphone Level</Label>\n                      <span className={`text-xs ${audioInputLevel > 0.1 ? 'text-green-500' : 'text-gray-500'}`}>\n                        {isRecording ? 'Active' : 'Inactive'}\n                      </span>\n                    </div>\n                    <div className=\"h-8 border rounded-md overflow-hidden\">\n                      <div\n                        className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-75`}\n                        style={{ width: `${Math.min(100, audioInputLevel * 100)}%` }}\n                      />\n                    </div>\n                    <p className=\"text-xs text-gray-500\">\n                      Visualizes microphone input level when recording.\n                    </p>\n                  </div>\n\n                  <Separator className=\"my-4\" />\n                  \n                  <h3 className=\"text-md font-medium mb-2\">Voice Output Settings</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"voice-select\">AI Voice</Label>\n                    <Select\n                      value={selectedVoice}\n                      onValueChange={setSelectedVoice}\n                      disabled={sessionActive || audioOutputPlaying}\n                    >\n                      <SelectTrigger id=\"voice-select\">\n                        <SelectValue placeholder=\"Select voice\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"alloy\">Alloy</SelectItem>\n                        <SelectItem value=\"echo\">Echo</SelectItem>\n                        <SelectItem value=\"fable\">Fable</SelectItem>\n                        <SelectItem value=\"onyx\">Onyx</SelectItem>\n                        <SelectItem value=\"nova\">Nova</SelectItem>\n                        <SelectItem value=\"shimmer\">Shimmer</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"voice-speed\">\n                      Voice Speed: {voiceSpeed.toFixed(2)}\n                    </Label>\n                    <Slider\n                      id=\"voice-speed\"\n                      min={0.5}\n                      max={2.0}\n                      step={0.05}\n                      value={[voiceSpeed]}\n                      onValueChange={(values) => setVoiceSpeed(values[0])}\n                      disabled={sessionActive || audioOutputPlaying}\n                    />\n                    <div className=\"flex justify-between text-xs text-gray-500\">\n                      <span>Slower</span>\n                      <span>Faster</span>\n                    </div>\n                  </div>\n                  \n                  <Button \n                    onClick={testAudio}\n                    disabled={audioOutputPlaying || (!wsRef.current && sessionActive)}\n                    className=\"w-full flex gap-2\"\n                  >\n                    {audioOutputPlaying ? (\n                      <RefreshCw className=\"h-4 w-4 animate-spin\" />\n                    ) : (\n                      <Volume2 className=\"h-4 w-4\" />\n                    )}\n                    {audioOutputPlaying ? \"Playing...\" : \"Test Audio Output\"}\n                  </Button>\n                  \n                  <Separator />\n                  \n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"temperature\">\n                      AI Creativity: {temperature.toFixed(2)}\n                    </Label>\n                    <Slider\n                      id=\"temperature\"\n                      min={0.1}\n                      max={1.0}\n                      step={0.05}\n                      value={[temperature]}\n                      onValueChange={(values) => setTemperature(values[0])}\n                      disabled={sessionActive}\n                    />\n                    <div className=\"flex justify-between text-xs text-gray-500\">\n                      <span>Precise</span>\n                      <span>Creative</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"debug\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Settings className=\"h-5 w-5\" />\n                    Debug Controls\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <Label htmlFor=\"debug-enabled\" className=\"text-sm font-medium\">\n                          Enable Debug Logging\n                        </Label>\n                        <p className=\"text-xs text-gray-500\">\n                          Show detailed debug information and logs\n                        </p>\n                      </div>\n                      <Switch\n                        id=\"debug-enabled\"\n                        checked={debugEnabled}\n                        onCheckedChange={(checked) => {\n                          setDebugEnabled(checked);\n                          debugManager.setEnabled(checked);\n                        }}\n                      />\n                    </div>\n\n                    {debugEnabled && (\n                      <>\n                        <Separator />\n                        <div className=\"space-y-3\">\n                          <h4 className=\"text-sm font-medium\">Debug Categories</h4>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">Real-time Conversation</Label>\n                              <p className=\"text-xs text-gray-500\">OpenAI Realtime API events</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.realTimeConversation}\n                              onCheckedChange={(checked) => debugManager.setCategory('realTimeConversation', checked)}\n                            />\n                          </div>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">Audio Processing</Label>\n                              <p className=\"text-xs text-gray-500\">Audio input/output processing</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.audioProcessing}\n                              onCheckedChange={(checked) => debugManager.setCategory('audioProcessing', checked)}\n                            />\n                          </div>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">WebSocket Events</Label>\n                              <p className=\"text-xs text-gray-500\">WebSocket connection events</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.websocketEvents}\n                              onCheckedChange={(checked) => debugManager.setCategory('websocketEvents', checked)}\n                            />\n                          </div>\n\n                          <div className=\"flex items-center justify-between\">\n                            <div>\n                              <Label className=\"text-sm\">Session Management</Label>\n                              <p className=\"text-xs text-gray-500\">Session lifecycle events</p>\n                            </div>\n                            <Switch\n                              checked={debugManager.sessionManagement}\n                              onCheckedChange={(checked) => debugManager.setCategory('sessionManagement', checked)}\n                            />\n                          </div>\n                        </div>\n\n                        <Separator />\n                        <div className=\"space-y-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => setResponseLog([])}\n                            className=\"w-full\"\n                          >\n                            Clear Debug Logs\n                          </Button>\n                        </div>\n                      </>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"logs\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>System Logs</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {!debugEnabled ? (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      <p>Debug logging is disabled.</p>\n                      <p className=\"text-sm mt-2\">Enable debug mode to see system logs.</p>\n                    </div>\n                  ) : (\n                    <div className=\"bg-black text-green-400 font-mono text-xs p-4 rounded-md h-80 overflow-y-auto\">\n                      {responseLog.length === 0 ? (\n                        <p>No logs yet - start a session to see debug output</p>\n                      ) : (\n                        responseLog.map((log, idx) => (\n                          <div key={idx}>{log}</div>\n                        ))\n                      )}\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminAITest;"}