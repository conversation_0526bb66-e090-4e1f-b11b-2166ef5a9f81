{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Sidebar.tsx"}, "originalCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\nimport { User } from \"@shared/schema\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface SidebarProps {\n  user?: User;\n  className?: string;\n  onLinkClick?: () => void;\n}\n\nexport default function Sidebar({ user, className, onLinkClick }: SidebarProps) {\n  const [location, setLocation] = useLocation();\n  const { logoutMutation } = useAuth();\n  const { toast } = useToast();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  // Determine which navigation items to show based on user role\n  const baseNavItems = [\n    { path: \"/dashboard\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/settings\", icon: \"settings\", label: \"Settings\" },\n  ];\n  \n  // Add role-specific navigation items\n  let navItems = [];\n  \n  if (user?.userRole === 'client') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/voice-therapy\", icon: \"mic\", label: \"Voice Therapy\" },\n    ];\n  } else if (user?.userRole === 'admin') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n      { path: \"/admin-dashboard\", icon: \"admin_panel_settings\", label: \"Admin Dashboard\" },\n      { path: \"/admin-ai-test\", icon: \"psychology\", label: \"AI Test Center\" },\n    ];\n  } else {\n    // Default for doctor role\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n    ];\n  }\n\n  const handleClick = () => {\n    if (onLinkClick) onLinkClick();\n  };\n  \n  const handleLogout = () => {\n    logoutMutation.mutate(undefined, {\n      onSuccess: () => {\n        toast({\n          title: \"Logged out successfully\",\n          description: \"You have been logged out of your account.\",\n        });\n        setLocation(\"/auth\");\n      },\n      onError: (error) => {\n        toast({\n          title: \"Logout failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      }\n    });\n  };\n\n  console.log('Sidebar rendering with user:', user);\n  console.log('Navigation items:', navItems);\n\n  return (\n    <aside className={cn(\"flex flex-col w-64 bg-white border-r border-gray-200\", className)} style={{ backgroundColor: 'white', borderRight: '1px solid #e5e7eb' }}>\n      <div className=\"flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-gray-50\" style={{ backgroundColor: '#f9fafb', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>\n        <div className=\"flex items-center\">\n          <img src={logoImage} alt=\"LamaMind Logo\" className=\"h-10 w-10 mr-2\" style={{ height: '40px', width: '40px', marginRight: '8px' }} />\n          <span className=\"text-lg font-semibold text-gray-900\" style={{ fontSize: '18px', fontWeight: '600', color: '#111827' }}>LamaMind</span>\n        </div>\n      </div>\n      <nav className=\"flex-1 px-3 py-4 space-y-1\">\n        {navItems.map((item) => (\n          <div key={item.path} style={{ marginBottom: '4px' }}>\n            <Link href={item.path} onClick={handleClick}>\n              <a\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  padding: '12px 16px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  borderRadius: '8px',\n                  textDecoration: 'none',\n                  backgroundColor: isLinkActive(item.path) ? '#2563eb' : 'transparent',\n                  color: isLinkActive(item.path) ? 'white' : '#374151',\n                  transition: 'all 0.15s ease',\n                  border: 'none',\n                  cursor: 'pointer'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isLinkActive(item.path)) {\n                    e.currentTarget.style.backgroundColor = '#f3f4f6';\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (!isLinkActive(item.path)) {\n                    e.currentTarget.style.backgroundColor = 'transparent';\n                  }\n                }}\n              >\n                <span\n                  className=\"material-icons\"\n                  style={{\n                    marginRight: '12px',\n                    fontSize: '18px',\n                    color: isLinkActive(item.path) ? 'white' : '#6b7280'\n                  }}\n                >\n                  {item.icon}\n                </span>\n                {item.label}\n              </a>\n            </Link>\n          </div>\n        ))}\n      </nav>\n\n      {user && (\n        <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white\">\n                <span className=\"text-sm font-medium\">\n                  {user.name.split(\" \").map(n => n[0]).join(\"\").toUpperCase()}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 capitalize\">{user.userRole}</p>\n            </div>\n            <div className=\"ml-auto flex\">\n              <Link href=\"/profile\">\n                <a\n                  className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1 block\"\n                  title=\"Profile\"\n                >\n                  <span className=\"material-icons text-sm\">person</span>\n                </a>\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1\"\n                title=\"Logout\"\n              >\n                <span className=\"material-icons text-sm\">logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n}\n", "modifiedCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\nimport { User } from \"@shared/schema\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface SidebarProps {\n  user?: User;\n  className?: string;\n  onLinkClick?: () => void;\n}\n\nexport default function Sidebar({ user, className, onLinkClick }: SidebarProps) {\n  const [location, setLocation] = useLocation();\n  const { logoutMutation } = useAuth();\n  const { toast } = useToast();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  // Determine which navigation items to show based on user role\n  const baseNavItems = [\n    { path: \"/dashboard\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/settings\", icon: \"settings\", label: \"Settings\" },\n  ];\n  \n  // Add role-specific navigation items\n  let navItems = [];\n  \n  if (user?.userRole === 'client') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/voice-therapy\", icon: \"mic\", label: \"Voice Therapy\" },\n    ];\n  } else if (user?.userRole === 'admin') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n      { path: \"/admin-dashboard\", icon: \"admin_panel_settings\", label: \"Admin Dashboard\" },\n      { path: \"/admin-ai-test\", icon: \"psychology\", label: \"AI Test Center\" },\n    ];\n  } else {\n    // Default for doctor role\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n    ];\n  }\n\n  const handleClick = () => {\n    if (onLinkClick) onLinkClick();\n  };\n  \n  const handleLogout = () => {\n    logoutMutation.mutate(undefined, {\n      onSuccess: () => {\n        toast({\n          title: \"Logged out successfully\",\n          description: \"You have been logged out of your account.\",\n        });\n        setLocation(\"/auth\");\n      },\n      onError: (error) => {\n        toast({\n          title: \"Logout failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      }\n    });\n  };\n\n  console.log('Sidebar rendering with user:', user);\n  console.log('Navigation items:', navItems);\n\n  return (\n    <aside className={cn(\"flex flex-col w-64 bg-white border-r border-gray-200\", className)} style={{ backgroundColor: 'white', borderRight: '1px solid #e5e7eb' }}>\n      <div className=\"flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-gray-50\" style={{ backgroundColor: '#f9fafb', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>\n        <div className=\"flex items-center\">\n          <img src={logoImage} alt=\"LamaMind Logo\" className=\"h-10 w-10 mr-2\" style={{ height: '40px', width: '40px', marginRight: '8px' }} />\n          <span className=\"text-lg font-semibold text-gray-900\" style={{ fontSize: '18px', fontWeight: '600', color: '#111827' }}>LamaMind</span>\n        </div>\n      </div>\n      <nav style={{ flex: 1, padding: '16px 12px' }}>\n        <div style={{ marginBottom: '16px', padding: '8px', backgroundColor: '#fef3c7', borderRadius: '4px', fontSize: '12px' }}>\n          DEBUG: Sidebar component is rendering\n        </div>\n        {navItems.map((item) => (\n          <div key={item.path} style={{ marginBottom: '8px' }}>\n            <Link href={item.path} onClick={handleClick}>\n              <a\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  padding: '12px 16px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  borderRadius: '8px',\n                  textDecoration: 'none',\n                  backgroundColor: isLinkActive(item.path) ? '#2563eb' : '#f8fafc',\n                  color: isLinkActive(item.path) ? 'white' : '#374151',\n                  border: '1px solid #e2e8f0',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  if (!isLinkActive(item.path)) {\n                    e.currentTarget.style.backgroundColor = '#e2e8f0';\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  if (!isLinkActive(item.path)) {\n                    e.currentTarget.style.backgroundColor = '#f8fafc';\n                  }\n                }}\n              >\n                <span\n                  className=\"material-icons\"\n                  style={{\n                    marginRight: '12px',\n                    fontSize: '20px',\n                    color: isLinkActive(item.path) ? 'white' : '#6b7280'\n                  }}\n                >\n                  {item.icon}\n                </span>\n                <span style={{ fontWeight: '500' }}>{item.label}</span>\n              </a>\n            </Link>\n          </div>\n        ))}\n      </nav>\n\n      {user && (\n        <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white\">\n                <span className=\"text-sm font-medium\">\n                  {user.name.split(\" \").map(n => n[0]).join(\"\").toUpperCase()}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 capitalize\">{user.userRole}</p>\n            </div>\n            <div className=\"ml-auto flex\">\n              <Link href=\"/profile\">\n                <a\n                  className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1 block\"\n                  title=\"Profile\"\n                >\n                  <span className=\"material-icons text-sm\">person</span>\n                </a>\n              </Link>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1\"\n                title=\"Logout\"\n              >\n                <span className=\"material-icons text-sm\">logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n}\n"}