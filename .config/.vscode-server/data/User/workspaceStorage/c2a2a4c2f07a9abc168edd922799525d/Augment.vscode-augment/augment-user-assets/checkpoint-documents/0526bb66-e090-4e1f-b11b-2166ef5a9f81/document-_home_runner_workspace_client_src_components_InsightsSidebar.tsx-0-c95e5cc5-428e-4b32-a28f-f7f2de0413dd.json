{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/InsightsSidebar.tsx"}, "originalCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { cn } from \"@/lib/utils\";\n\ninterface InsightsSidebarProps {\n  className?: string;\n}\n\nexport default function InsightsSidebar({ className }: InsightsSidebarProps) {\n  // Fetch dashboard stats for insights\n  const { data: stats } = useQuery({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch clients to get all themes\n  const { data: clients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Create aggregated data for themes across all clients\n  const allThemes = clients?.flatMap(client => client.themes || []) || [];\n  const themeCounts: Record<string, number> = {};\n  \n  allThemes.forEach(theme => {\n    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;\n  });\n  \n  // Sort theme entries by occurrence count\n  const sortedThemes = Object.entries(themeCounts)\n    .sort(([, countA], [, countB]) => countB - countA)\n    .slice(0, 5);\n  \n  // Weekly activity data (mocked for now)\n  const weeklyActivityData = [\n    { day: \"M\", percentage: 40 },\n    { day: \"T\", percentage: 60 },\n    { day: \"W\", percentage: 80 },\n    { day: \"T\", percentage: 30 },\n    { day: \"F\", percentage: 70 },\n    { day: \"S\", percentage: 10 },\n    { day: \"S\", percentage: 5 }\n  ];\n\n  return (\n    <aside className={cn(\"h-full w-64 bg-white border-l border-gray-200 overflow-y-auto\", className)}>\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Insights Overview</h3>\n        \n        {/* Weekly Activity Chart */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-neutral-dark mb-2\">Weekly Activity</h4>\n          <div className=\"bg-neutral-light p-2 rounded-md\">\n            <div className=\"flex items-end justify-between h-32\">\n              {weeklyActivityData.map((item) => (\n                <div key={item.day} className=\"flex flex-col items-center w-8\">\n                  <div \n                    className={`${item.day === \"S\" || item.day === \"S\" ? \"bg-primary-light opacity-50\" : \"bg-primary\"} w-4 rounded-t`} \n                    style={{ height: `${item.percentage}%` }}\n                  ></div>\n                  <span className=\"text-xs mt-1\">{item.day}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n        \n        {/* Common Themes */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-neutral-dark mb-2\">Common Themes</h4>\n          <div className=\"space-y-2\">\n            {sortedThemes.map(([name, count], index) => {\n              const maxCount = sortedThemes[0][1];\n              const percentage = Math.round((count / maxCount) * 100);\n              \n              return (\n                <div key={name} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-neutral-dark\">{name}</span>\n                  <div className=\"w-1/2 bg-neutral-light rounded-full h-2\">\n                    <div \n                      className=\"bg-primary rounded-full h-2\" \n                      style={{ width: `${percentage}%` }}\n                    ></div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n        \n        {/* Client Status Distribution */}\n        <div>\n          <h4 className=\"text-sm font-medium text-neutral-dark mb-2\">Client Status</h4>\n          <div className=\"grid grid-cols-3 gap-2 text-center\">\n            <div className=\"bg-success-light bg-opacity-20 p-2 rounded\">\n              <div className=\"text-lg font-semibold text-success\">\n                {stats?.improvingPercentage || 0}%\n              </div>\n              <div className=\"text-xs text-neutral-dark\">Improving</div>\n            </div>\n            <div className=\"bg-warning-light bg-opacity-20 p-2 rounded\">\n              <div className=\"text-lg font-semibold text-warning-dark\">\n                {stats ? Math.round(((stats.totalClients - stats.improving - 3) / stats.totalClients) * 100) : 0}%\n              </div>\n              <div className=\"text-xs text-neutral-dark\">Stable</div>\n            </div>\n            <div className=\"bg-error-light bg-opacity-20 p-2 rounded\">\n              <div className=\"text-lg font-semibold text-error\">\n                {stats ? Math.round((3 / stats.totalClients) * 100) : 0}%\n              </div>\n              <div className=\"text-xs text-neutral-dark\">Needs Attention</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </aside>\n  );\n}\n", "modifiedCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { cn } from \"@/lib/utils\";\n\ninterface InsightsSidebarProps {\n  className?: string;\n}\n\nexport default function InsightsSidebar({ className }: InsightsSidebarProps) {\n  // Fetch dashboard stats for insights\n  const { data: stats } = useQuery({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch clients to get all themes\n  const { data: clients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Create aggregated data for themes across all clients\n  const allThemes = clients?.flatMap(client => client.themes || []) || [];\n  const themeCounts: Record<string, number> = {};\n  \n  allThemes.forEach(theme => {\n    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;\n  });\n  \n  // Sort theme entries by occurrence count\n  const sortedThemes = Object.entries(themeCounts)\n    .sort(([, countA], [, countB]) => countB - countA)\n    .slice(0, 5);\n  \n  // Weekly activity data (mocked for now)\n  const weeklyActivityData = [\n    { day: \"M\", percentage: 40 },\n    { day: \"T\", percentage: 60 },\n    { day: \"W\", percentage: 80 },\n    { day: \"T\", percentage: 30 },\n    { day: \"F\", percentage: 70 },\n    { day: \"S\", percentage: 10 },\n    { day: \"S\", percentage: 5 }\n  ];\n\n  return (\n    <aside className={cn(\"h-full w-64 bg-white border-l border-gray-200 overflow-y-auto\", className)}>\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Insights Overview</h3>\n        \n        {/* Weekly Activity Chart */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-neutral-dark mb-2\">Weekly Activity</h4>\n          <div className=\"bg-neutral-light p-2 rounded-md\">\n            <div className=\"flex items-end justify-between h-32\">\n              {weeklyActivityData.map((item) => (\n                <div key={item.day} className=\"flex flex-col items-center w-8\">\n                  <div \n                    className={`${item.day === \"S\" || item.day === \"S\" ? \"bg-primary-light opacity-50\" : \"bg-primary\"} w-4 rounded-t`} \n                    style={{ height: `${item.percentage}%` }}\n                  ></div>\n                  <span className=\"text-xs mt-1\">{item.day}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n        \n        {/* Common Themes */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-neutral-dark mb-2\">Common Themes</h4>\n          <div className=\"space-y-2\">\n            {sortedThemes.map(([name, count], index) => {\n              const maxCount = sortedThemes[0][1];\n              const percentage = Math.round((count / maxCount) * 100);\n              \n              return (\n                <div key={name} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-neutral-dark\">{name}</span>\n                  <div className=\"w-1/2 bg-neutral-light rounded-full h-2\">\n                    <div \n                      className=\"bg-primary rounded-full h-2\" \n                      style={{ width: `${percentage}%` }}\n                    ></div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n        \n        {/* Client Status Distribution */}\n        <div>\n          <h4 className=\"text-sm font-medium text-neutral-dark mb-2\">Client Status</h4>\n          <div className=\"grid grid-cols-3 gap-2 text-center\">\n            <div className=\"bg-success-light bg-opacity-20 p-2 rounded\">\n              <div className=\"text-lg font-semibold text-success\">\n                {stats?.improvingPercentage || 0}%\n              </div>\n              <div className=\"text-xs text-neutral-dark\">Improving</div>\n            </div>\n            <div className=\"bg-warning-light bg-opacity-20 p-2 rounded\">\n              <div className=\"text-lg font-semibold text-warning-dark\">\n                {stats ? Math.round(((stats.totalClients - stats.improving - 3) / stats.totalClients) * 100) : 0}%\n              </div>\n              <div className=\"text-xs text-neutral-dark\">Stable</div>\n            </div>\n            <div className=\"bg-error-light bg-opacity-20 p-2 rounded\">\n              <div className=\"text-lg font-semibold text-error\">\n                {stats ? Math.round((3 / stats.totalClients) * 100) : 0}%\n              </div>\n              <div className=\"text-xs text-neutral-dark\">Needs Attention</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </aside>\n  );\n}\n"}