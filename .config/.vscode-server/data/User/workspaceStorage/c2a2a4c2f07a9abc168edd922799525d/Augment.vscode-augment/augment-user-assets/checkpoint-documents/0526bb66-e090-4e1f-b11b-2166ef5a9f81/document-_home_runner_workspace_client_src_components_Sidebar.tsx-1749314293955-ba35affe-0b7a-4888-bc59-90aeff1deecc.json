{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Sidebar.tsx"}, "originalCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\nimport { User } from \"@shared/schema\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface SidebarProps {\n  user?: User;\n  className?: string;\n  onLinkClick?: () => void;\n}\n\nexport default function Sidebar({ user, className, onLinkClick }: SidebarProps) {\n  const [location, setLocation] = useLocation();\n  const { logoutMutation } = useAuth();\n  const { toast } = useToast();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  // Determine which navigation items to show based on user role\n  const baseNavItems = [\n    { path: \"/dashboard\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/settings\", icon: \"settings\", label: \"Settings\" },\n  ];\n  \n  // Add role-specific navigation items\n  let navItems = [];\n  \n  if (user?.userRole === 'client') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/voice-therapy\", icon: \"mic\", label: \"Voice Therapy\" },\n    ];\n  } else if (user?.userRole === 'admin') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n      { path: \"/admin-dashboard\", icon: \"admin_panel_settings\", label: \"Admin Dashboard\" },\n      { path: \"/admin-ai-test\", icon: \"psychology\", label: \"AI Test Center\" },\n    ];\n  } else {\n    // Default for doctor role\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n    ];\n  }\n\n  const handleClick = () => {\n    if (onLinkClick) onLinkClick();\n  };\n  \n  const handleLogout = () => {\n    logoutMutation.mutate(undefined, {\n      onSuccess: () => {\n        toast({\n          title: \"Logged out successfully\",\n          description: \"You have been logged out of your account.\",\n        });\n        setLocation(\"/auth\");\n      },\n      onError: (error) => {\n        toast({\n          title: \"Logout failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      }\n    });\n  };\n\n  return (\n    <aside className={cn(\"flex flex-col w-64 bg-white shadow-md\", className)}>\n      <div className=\"flex items-center justify-center h-16 px-4 border-b border-gray-200\">\n        <div className=\"flex items-center\">\n          <img src={logoImage} alt=\"LamaMind Logo\" className=\"h-10 w-10 mr-2\" />\n        </div>\n      </div>\n      <nav className=\"flex-1 px-2 py-4 space-y-1\">\n        {navItems.map((item) => (\n          <Link\n            key={item.path}\n            href={item.path}\n            onClick={handleClick}\n          >\n            <a\n              className={cn(\n                \"flex items-center px-4 py-2 text-sm font-medium rounded-md group transition-colors\",\n                isLinkActive(item.path)\n                  ? \"text-white bg-blue-600\"\n                  : \"text-gray-700 hover:bg-gray-100 hover:text-gray-900\"\n              )}\n            >\n              <span\n                className={cn(\n                  \"material-icons mr-3\",\n                  isLinkActive(item.path) ? \"text-white\" : \"text-gray-500\"\n                )}\n              >\n                {item.icon}\n              </span>\n              {item.label}\n            </a>\n          </Link>\n        ))}\n      </nav>\n\n      {user && (\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white\">\n                <span className=\"text-sm font-medium\">\n                  {user.name.split(\" \").map(n => n[0]).join(\"\").toUpperCase()}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 capitalize\">{user.userRole}</p>\n            </div>\n            <div className=\"ml-auto flex\">\n              <Link href=\"/profile\">\n                <a\n                  className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1\"\n                  title=\"Profile\"\n                >\n                  <span className=\"material-icons text-sm\">person</span>\n                </a>\n              </Link>\n              <button \n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1\"\n                title=\"Logout\"\n              >\n                <span className=\"material-icons text-sm\">logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n}\n", "modifiedCode": "import { Link, useLocation } from \"wouter\";\nimport { cn } from \"@/lib/utils\";\nimport { User } from \"@shared/schema\";\nimport { useAuth } from \"@/hooks/use-auth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface SidebarProps {\n  user?: User;\n  className?: string;\n  onLinkClick?: () => void;\n}\n\nexport default function Sidebar({ user, className, onLinkClick }: SidebarProps) {\n  const [location, setLocation] = useLocation();\n  const { logoutMutation } = useAuth();\n  const { toast } = useToast();\n\n  const isLinkActive = (path: string) => {\n    if (path === \"/\" && location === \"/\") return true;\n    if (path !== \"/\" && location.startsWith(path)) return true;\n    return false;\n  };\n\n  // Determine which navigation items to show based on user role\n  const baseNavItems = [\n    { path: \"/dashboard\", icon: \"dashboard\", label: \"Dashboard\" },\n    { path: \"/settings\", icon: \"settings\", label: \"Settings\" },\n  ];\n  \n  // Add role-specific navigation items\n  let navItems = [];\n  \n  if (user?.userRole === 'client') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/voice-therapy\", icon: \"mic\", label: \"Voice Therapy\" },\n    ];\n  } else if (user?.userRole === 'admin') {\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n      { path: \"/admin-dashboard\", icon: \"admin_panel_settings\", label: \"Admin Dashboard\" },\n      { path: \"/admin-ai-test\", icon: \"psychology\", label: \"AI Test Center\" },\n    ];\n  } else {\n    // Default for doctor role\n    navItems = [\n      ...baseNavItems,\n      { path: \"/clients\", icon: \"people\", label: \"Clients\" },\n      { path: \"/notes\", icon: \"description\", label: \"Session Notes\" },\n      { path: \"/analytics\", icon: \"insights\", label: \"Analytics\" },\n    ];\n  }\n\n  const handleClick = () => {\n    if (onLinkClick) onLinkClick();\n  };\n  \n  const handleLogout = () => {\n    logoutMutation.mutate(undefined, {\n      onSuccess: () => {\n        toast({\n          title: \"Logged out successfully\",\n          description: \"You have been logged out of your account.\",\n        });\n        setLocation(\"/auth\");\n      },\n      onError: (error) => {\n        toast({\n          title: \"Logout failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      }\n    });\n  };\n\n  return (\n    <aside className={cn(\"flex flex-col w-64 bg-white border-r border-gray-200\", className)}>\n      <div className=\"flex items-center justify-center h-16 px-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex items-center\">\n          <img src={logoImage} alt=\"LamaMind Logo\" className=\"h-10 w-10 mr-2\" />\n          <span className=\"text-lg font-semibold text-gray-900\">LamaMind</span>\n        </div>\n      </div>\n      <nav className=\"flex-1 px-3 py-4 space-y-1\">\n        {navItems.map((item) => (\n          <Link\n            key={item.path}\n            href={item.path}\n            onClick={handleClick}\n          >\n            <a\n              className={cn(\n                \"flex items-center px-4 py-2 text-sm font-medium rounded-md group transition-colors\",\n                isLinkActive(item.path)\n                  ? \"text-white bg-blue-600\"\n                  : \"text-gray-700 hover:bg-gray-100 hover:text-gray-900\"\n              )}\n            >\n              <span\n                className={cn(\n                  \"material-icons mr-3\",\n                  isLinkActive(item.path) ? \"text-white\" : \"text-gray-500\"\n                )}\n              >\n                {item.icon}\n              </span>\n              {item.label}\n            </a>\n          </Link>\n        ))}\n      </nav>\n\n      {user && (\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white\">\n                <span className=\"text-sm font-medium\">\n                  {user.name.split(\" \").map(n => n[0]).join(\"\").toUpperCase()}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 capitalize\">{user.userRole}</p>\n            </div>\n            <div className=\"ml-auto flex\">\n              <Link href=\"/profile\">\n                <a\n                  className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1 mr-1\"\n                  title=\"Profile\"\n                >\n                  <span className=\"material-icons text-sm\">person</span>\n                </a>\n              </Link>\n              <button \n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full p-1\"\n                title=\"Logout\"\n              >\n                <span className=\"material-icons text-sm\">logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n}\n"}