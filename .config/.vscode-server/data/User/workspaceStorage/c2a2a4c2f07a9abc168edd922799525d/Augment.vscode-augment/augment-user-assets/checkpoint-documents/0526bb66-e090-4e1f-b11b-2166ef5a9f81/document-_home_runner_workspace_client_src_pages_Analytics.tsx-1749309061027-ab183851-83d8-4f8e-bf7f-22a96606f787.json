{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Analytics.tsx"}, "originalCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { ClientTheme, DashboardStats } from \"@shared/schema\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { getThemeColor } from \"@/lib/utils\";\nimport { \n  BarChart, Bar, \n  LineChart, Line, \n  XAxis, YAxis, \n  Tooltip, Legend, \n  ResponsiveContainer, \n  PieChart, Pie, Cell\n} from \"recharts\";\n\nexport default function Analytics() {\n  // Fetch dashboard stats\n  const { data: stats, isLoading: isLoadingStats } = useQuery<DashboardStats>({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch real analytics data\n  const { data: analytics, isLoading: isLoadingAnalytics } = useQuery({\n    queryKey: [\"/api/analytics\"],\n  });\n\n  // Fetch weekly activity data\n  const { data: weeklyActivity = [], isLoading: isLoadingWeekly } = useQuery({\n    queryKey: [\"/api/analytics/weekly\"],\n  });\n\n  // Fetch monthly trends data\n  const { data: monthlyTrends = [], isLoading: isLoadingMonthly } = useQuery({\n    queryKey: [\"/api/analytics/monthly\"],\n  });\n\n  // Fetch clients to get all themes\n  const { data: clients, isLoading: isLoadingClients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Create aggregated data for themes across all clients\n  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];\n  const themeCounts: Record<string, number> = {};\n\n  allThemes.forEach((theme: any) => {\n    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;\n  });\n\n  const themeData = Object.keys(themeCounts).map((name, index) => ({\n    name,\n    value: themeCounts[name],\n    color: getThemeColor(index).split(\" \")[0]\n  }));\n\n  // Client status distribution based on real data\n  const statusData = [\n    { name: \"Improving\", value: stats?.improving || 0, color: \"bg-success-light\" },\n    { name: \"Stable\", value: (stats?.totalClients || 0) - (stats?.improving || 0) - Math.max(0, Math.floor((stats?.totalClients || 0) * 0.1)), color: \"bg-warning-light\" },\n    { name: \"Needs Attention\", value: Math.max(0, Math.floor((stats?.totalClients || 0) * 0.1)), color: \"bg-error-light\" }\n  ];\n\n  const isLoading = isLoadingStats || isLoadingAnalytics || isLoadingWeekly || isLoadingMonthly;\n\n  return (\n    <div>\n      <div className=\"md:flex md:items-center md:justify-between mb-6\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-neutral-dark sm:text-3xl\">\n            Analytics\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Visualize insights and trends across your practice\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n        {/* Weekly Activity */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Weekly Activity</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={weeklyActivity as any[]}>\n                  <XAxis dataKey=\"week\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"sessions\" fill=\"#4A6FA5\" />\n                </BarChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Client Status Distribution */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Client Status Distribution</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoadingStats ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={statusData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={true}\n                    outerRadius={100}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}\n                  >\n                    {statusData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 gap-6 mb-6\">\n        {/* Monthly Trends */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Monthly Trends</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <LineChart data={monthlyTrends}>\n                  <XAxis dataKey=\"month\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line type=\"monotone\" dataKey=\"sessions\" stroke=\"#4A6FA5\" name=\"Sessions\" />\n                </LineChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Common Themes */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Common Themes</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoadingClients ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <div className=\"space-y-4\">\n                {themeData.slice(0, 5).map((theme, index) => (\n                  <div key={theme.name} className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-neutral-dark\">{theme.name}</span>\n                    <div className=\"w-2/3\">\n                      <div className=\"bg-neutral-light rounded-full h-2\">\n                        <div \n                          className=\"bg-primary rounded-full h-2\" \n                          style={{ width: `${(theme.value / Math.max(...themeData.map(t => t.value))) * 100}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Theme Distribution */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Theme Distribution</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoadingClients ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={themeData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    outerRadius={100}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                    label={({ name }) => name}\n                  >\n                    {themeData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n", "modifiedCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { ClientTheme, DashboardStats } from \"@shared/schema\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { getThemeColor } from \"@/lib/utils\";\nimport { \n  BarChart, Bar, \n  LineChart, Line, \n  XAxis, YAxis, \n  Tooltip, Legend, \n  ResponsiveContainer, \n  PieChart, Pie, Cell\n} from \"recharts\";\n\nexport default function Analytics() {\n  // Fetch dashboard stats\n  const { data: stats, isLoading: isLoadingStats } = useQuery<DashboardStats>({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch real analytics data\n  const { data: analytics, isLoading: isLoadingAnalytics } = useQuery({\n    queryKey: [\"/api/analytics\"],\n  });\n\n  // Fetch weekly activity data\n  const { data: weeklyActivity = [], isLoading: isLoadingWeekly } = useQuery({\n    queryKey: [\"/api/analytics/weekly\"],\n  });\n\n  // Fetch monthly trends data\n  const { data: monthlyTrends = [], isLoading: isLoadingMonthly } = useQuery({\n    queryKey: [\"/api/analytics/monthly\"],\n  });\n\n  // Fetch clients to get all themes\n  const { data: clients, isLoading: isLoadingClients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Create aggregated data for themes across all clients\n  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];\n  const themeCounts: Record<string, number> = {};\n\n  allThemes.forEach((theme: any) => {\n    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;\n  });\n\n  const themeData = Object.keys(themeCounts).map((name, index) => ({\n    name,\n    value: themeCounts[name],\n    color: getThemeColor(index).split(\" \")[0]\n  }));\n\n  // Client status distribution based on real data\n  const statusData = [\n    { name: \"Improving\", value: stats?.improving || 0, color: \"bg-success-light\" },\n    { name: \"Stable\", value: (stats?.totalClients || 0) - (stats?.improving || 0) - Math.max(0, Math.floor((stats?.totalClients || 0) * 0.1)), color: \"bg-warning-light\" },\n    { name: \"Needs Attention\", value: Math.max(0, Math.floor((stats?.totalClients || 0) * 0.1)), color: \"bg-error-light\" }\n  ];\n\n  const isLoading = isLoadingStats || isLoadingAnalytics || isLoadingWeekly || isLoadingMonthly;\n\n  return (\n    <div>\n      <div className=\"md:flex md:items-center md:justify-between mb-6\">\n        <div className=\"flex-1 min-w-0\">\n          <h2 className=\"text-2xl font-bold leading-7 text-neutral-dark sm:text-3xl\">\n            Analytics\n          </h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Visualize insights and trends across your practice\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n        {/* Weekly Activity */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Weekly Activity</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={weeklyActivity as any[]}>\n                  <XAxis dataKey=\"week\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Bar dataKey=\"sessions\" fill=\"#4A6FA5\" />\n                </BarChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Client Status Distribution */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Client Status Distribution</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoadingStats ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={statusData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={true}\n                    outerRadius={100}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}\n                  >\n                    {statusData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 gap-6 mb-6\">\n        {/* Monthly Trends */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Monthly Trends</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <LineChart data={monthlyTrends as any[]}>\n                  <XAxis dataKey=\"month\" />\n                  <YAxis />\n                  <Tooltip />\n                  <Legend />\n                  <Line type=\"monotone\" dataKey=\"sessions\" stroke=\"#4A6FA5\" name=\"Sessions\" />\n                </LineChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Common Themes */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Common Themes</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoadingClients ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <div className=\"space-y-4\">\n                {themeData.slice(0, 5).map((theme, index) => (\n                  <div key={theme.name} className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-neutral-dark\">{theme.name}</span>\n                    <div className=\"w-2/3\">\n                      <div className=\"bg-neutral-light rounded-full h-2\">\n                        <div \n                          className=\"bg-primary rounded-full h-2\" \n                          style={{ width: `${(theme.value / Math.max(...themeData.map(t => t.value))) * 100}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Theme Distribution */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Theme Distribution</CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoadingClients ? (\n              <Skeleton className=\"h-64 w-full\" />\n            ) : (\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={themeData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    outerRadius={100}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                    label={({ name }) => name}\n                  >\n                    {themeData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"}