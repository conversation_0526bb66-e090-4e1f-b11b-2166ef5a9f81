{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}, "originalCode": "import { \n  users, type User, type InsertUser,\n  clients, type Client, type InsertClient,\n  sessionNotes, type SessionNote, type InsertSessionNote,\n  clientThemes, type ClientTheme, type InsertClientTheme,\n  aiTherapyConversations, type AiTherapyConversation, type InsertAiTherapyConversation,\n  aiTherapyMessages, type AiTherapyMessage, type InsertAiTherapyMessage,\n  aiTherapySettings, type AiTherapySettings, type InsertAiTherapySettings,\n  doctorClients, type DoctorClient, type InsertDoctorClient,\n  invitationCodes, type InvitationCode, type InsertInvitationCode,\n  type Theme, type DashboardStats\n} from \"@shared/schema\";\nimport session from \"express-session\";\nimport createMemoryStore from \"memorystore\";\nimport { db } from \"./db\";\nimport { eq, desc, and, inArray, not, sql } from \"drizzle-orm\";\nimport PgSession from \"connect-pg-simple\";\nimport { pool } from \"./db\";\n\n// DatabaseStorage class that implements IStorage for PostgreSQL\nexport class DatabaseStorage implements IStorage {\n  sessionStore: session.Store;\n\n  constructor() {\n    // Initialize session store with PostgreSQL\n    const PgStore = PgSession(session);\n    this.sessionStore = new PgStore({\n      pool: pool as any,\n      tableName: 'session',\n      createTableIfMissing: true\n    });\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    const result = await db.select().from(users).where(eq(users.id, id));\n    return result[0];\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    const result = await db.select().from(users).where(eq(users.username, username));\n    return result[0];\n  }\n\n  async createUser(user: InsertUser): Promise<User> {\n    const result = await db.insert(users).values(user).returning();\n    return result[0];\n  }\n\n  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {\n    const result = await db.update(users).set(userData).where(eq(users.id, id)).returning();\n    return result[0];\n  }\n\n  async getAllUsers(): Promise<User[]> {\n    return await db.select().from(users);\n  }\n\n  async getAllClients(): Promise<Client[]> {\n    return await db.select().from(clients);\n  }\n\n  async getClientsByDoctor(doctorId: number): Promise<Client[]> {\n    const assignments = await db.select()\n      .from(doctorClients)\n      .where(eq(doctorClients.doctorId, doctorId));\n\n    const clientIds = assignments.map(a => a.clientId);\n    if (clientIds.length === 0) return [];\n\n    // Fix: Use inArray to get ALL clients for this doctor\n    return await db.select()\n      .from(clients)\n      .where(inArray(clients.id, clientIds));\n  }\n\n  async getAllConversations(): Promise<AiTherapyConversation[]> {\n    return await db.select()\n      .from(aiTherapyConversations)\n      .orderBy(desc(aiTherapyConversations.createdAt));\n  }\n  \n  // Doctor-Client assignment operations\n  async createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient> {\n    const result = await db.insert(doctorClients).values(assignment).returning();\n    return result[0];\n  }\n\n  async getAllDoctorClientRelationships(): Promise<DoctorClient[]> {\n    return await db.select().from(doctorClients);\n  }\n  \n  // Client operations\n  async getClient(id: number): Promise<Client | undefined> {\n    const result = await db.select().from(clients).where(eq(clients.id, id));\n    return result[0];\n  }\n\n  async getClientsByUserId(userId: number): Promise<Client[]> {\n    return await db.select().from(clients).where(eq(clients.userId, userId));\n  }\n\n  async getClientByUserId(userId: number): Promise<Client | undefined> {\n    const result = await db.select().from(clients).where(eq(clients.userId, userId));\n    return result[0];\n  }\n\n  async createClient(client: InsertClient): Promise<Client> {\n    const result = await db.insert(clients).values(client).returning();\n    return result[0];\n  }\n\n  async updateClientStatus(id: number, status: string): Promise<Client | undefined> {\n    const result = await db.update(clients)\n      .set({ status })\n      .where(eq(clients.id, id))\n      .returning();\n    return result[0];\n  }\n  \n  // Session notes operations\n  async getSessionNote(id: number): Promise<SessionNote | undefined> {\n    const result = await db.select().from(sessionNotes).where(eq(sessionNotes.id, id));\n    return result[0];\n  }\n\n  async getSessionNotesByClientId(clientId: number): Promise<SessionNote[]> {\n    return await db.select()\n      .from(sessionNotes)\n      .where(eq(sessionNotes.clientId, clientId))\n      .orderBy(desc(sessionNotes.createdAt));\n  }\n\n  async getRecentSessionNotes(userId: number, limit: number = 10): Promise<SessionNote[]> {\n    // This is a simplified implementation that just returns recent notes\n    // A more complete implementation would filter based on user access\n    return await db.select()\n      .from(sessionNotes)\n      .orderBy(desc(sessionNotes.createdAt))\n      .limit(limit);\n  }\n\n  async createSessionNote(note: InsertSessionNote): Promise<SessionNote> {\n    const result = await db.insert(sessionNotes).values({\n      ...note,\n      rawNotes: note.content, // Copy content to rawNotes for client compatibility\n      aiProcessed: false\n    }).returning();\n    return result[0];\n  }\n\n  async updateSessionNoteAnalysis(\n    id: number,\n    summary: string,\n    themes: Theme[],\n    recommendations: string[]\n  ): Promise<SessionNote | undefined> {\n    const result = await db.update(sessionNotes)\n      .set({\n        summary,\n        recommendations,\n        analyzed: true,\n        aiProcessed: true\n      })\n      .where(eq(sessionNotes.id, id))\n      .returning();\n      \n    if (result[0] && themes && themes.length > 0) {\n      for (const theme of themes) {\n        await this.updateOrCreateClientTheme(result[0].clientId, theme.name, theme.trend);\n      }\n    }\n    \n    return result[0];\n  }\n  \n  // Client themes operations\n  async getClientThemes(clientId: number): Promise<ClientTheme[]> {\n    return await db.select()\n      .from(clientThemes)\n      .where(eq(clientThemes.clientId, clientId))\n      .orderBy(desc(clientThemes.occurrences));\n  }\n\n  async updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme> {\n    // First try to find existing theme\n    const existingTheme = await db.select()\n      .from(clientThemes)\n      .where(and(\n        eq(clientThemes.clientId, clientId),\n        eq(clientThemes.name, name)\n      ));\n    \n    if (existingTheme.length > 0) {\n      // Update existing theme\n      const result = await db.update(clientThemes)\n        .set({\n          occurrences: existingTheme[0].occurrences + 1,\n          trend\n        })\n        .where(eq(clientThemes.id, existingTheme[0].id))\n        .returning();\n      return result[0];\n    } else {\n      // Create new theme\n      const result = await db.insert(clientThemes)\n        .values({\n          clientId,\n          name,\n          occurrences: 1,\n          trend\n        })\n        .returning();\n      return result[0];\n    }\n  }\n  \n  // AI Therapy Conversation operations\n  async getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined> {\n    const result = await db.select()\n      .from(aiTherapyConversations)\n      .where(eq(aiTherapyConversations.id, id));\n    return result[0];\n  }\n\n  async getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]> {\n    if (clientId) {\n      return await db.select()\n        .from(aiTherapyConversations)\n        .where(eq(aiTherapyConversations.clientId, clientId))\n        .orderBy(desc(aiTherapyConversations.createdAt));\n    } else {\n      return await db.select()\n        .from(aiTherapyConversations)\n        .where(eq(aiTherapyConversations.userId, userId))\n        .orderBy(desc(aiTherapyConversations.createdAt));\n    }\n  }\n\n  async createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation> {\n    const result = await db.insert(aiTherapyConversations)\n      .values({\n        ...conversation,\n        title: conversation.title || \"New Conversation\",\n        active: true,\n        aiProcessed: false\n      })\n      .returning();\n    return result[0];\n  }\n\n  async endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined> {\n    const result = await db.update(aiTherapyConversations)\n      .set({\n        summary,\n        active: false,\n        aiProcessed: true,\n        endedAt: new Date()\n      })\n      .where(eq(aiTherapyConversations.id, id))\n      .returning();\n    return result[0];\n  }\n  \n  // AI Therapy Message operations\n  async getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]> {\n    return await db.select()\n      .from(aiTherapyMessages)\n      .where(eq(aiTherapyMessages.conversationId, conversationId))\n      .orderBy(aiTherapyMessages.timestamp);\n  }\n\n  async createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage> {\n    const result = await db.insert(aiTherapyMessages)\n      .values(message)\n      .returning();\n    return result[0];\n  }\n  \n  // AI Therapy Settings operations\n  async getAiTherapySettings(): Promise<AiTherapySettings[]> {\n    return await db.select().from(aiTherapySettings);\n  }\n\n  async getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined> {\n    const result = await db.select()\n      .from(aiTherapySettings)\n      .where(eq(aiTherapySettings.active, true));\n    return result[0];\n  }\n\n  async createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings> {\n    // If this is going to be the active settings, deactivate all others\n    if (settings.active) {\n      await db.update(aiTherapySettings)\n        .set({ active: false })\n        .where(eq(aiTherapySettings.active, true));\n    }\n    \n    const result = await db.insert(aiTherapySettings)\n      .values(settings)\n      .returning();\n    return result[0];\n  }\n\n  async updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined> {\n    // If this update is to activate settings, deactivate all others\n    if (settings.active) {\n      await db.update(aiTherapySettings)\n        .set({ active: false })\n        .where(and(\n          eq(aiTherapySettings.active, true),\n          not(eq(aiTherapySettings.id, id))\n        ));\n    }\n    \n    const result = await db.update(aiTherapySettings)\n      .set({\n        ...settings,\n        updatedAt: new Date()\n      })\n      .where(eq(aiTherapySettings.id, id))\n      .returning();\n    return result[0];\n  }\n  \n  // Dashboard stats operations\n  async getDashboardStats(userId: number): Promise<DashboardStats> {\n    // Get all clients for this user\n    const clientsResult = await db.select().from(clients).where(eq(clients.userId, userId));\n    const totalClients = clientsResult.length;\n    \n    // Calculate how many clients are improving\n    const improving = clientsResult.filter(client => client.status === 'improving').length;\n    const improvingPercentage = totalClients > 0 ? Math.round((improving / totalClients) * 100) : 0;\n    \n    // Get client IDs for this user\n    const clientIds = clientsResult.map(client => client.id);\n    \n    // Calculate weekly notes (for the last 7 days)\n    const oneWeekAgo = new Date();\n    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n    \n    let weeklyNotes = 0;\n    let insights = 0;\n    \n    if (clientIds.length > 0) {\n      // Count weekly notes\n      // Use Drizzle ORM instead of raw SQL for better type safety\n      const notesCount = await db.select({ count: sql<number>`count(*)` })\n        .from(sessionNotes)\n        .where(and(\n          inArray(sessionNotes.clientId, clientIds),\n          sql`${sessionNotes.createdAt} >= ${oneWeekAgo}`\n        ));\n      weeklyNotes = notesCount[0]?.count || 0;\n\n      // Count analyzed notes (insights)\n      const insightsCount = await db.select({ count: sql<number>`count(*)` })\n        .from(sessionNotes)\n        .where(and(\n          inArray(sessionNotes.clientId, clientIds),\n          eq(sessionNotes.analyzed, true)\n        ));\n      insights = insightsCount[0]?.count || 0;\n    }\n    \n    return {\n      totalClients,\n      weeklyNotes,\n      insights,\n      improving,\n      improvingPercentage\n    };\n  }\n  \n  // Invitation code operations\n  async createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode> {\n    const result = await db.insert(invitationCodes)\n      .values(code)\n      .returning();\n    return result[0];\n  }\n\n  async getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined> {\n    const result = await db.select()\n      .from(invitationCodes)\n      .where(eq(invitationCodes.code, code));\n    return result[0];\n  }\n\n  async getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]> {\n    if (type) {\n      return await db.select()\n        .from(invitationCodes)\n        .where(and(\n          eq(invitationCodes.createdBy, userId),\n          eq(invitationCodes.type, type)\n        ));\n    } else {\n      return await db.select()\n        .from(invitationCodes)\n        .where(eq(invitationCodes.createdBy, userId));\n    }\n  }\n\n  async markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined> {\n    const result = await db.update(invitationCodes)\n      .set({\n        used: true,\n        usedBy: userId,\n        usedAt: new Date()\n      })\n      .where(eq(invitationCodes.code, code))\n      .returning();\n    return result[0];\n  }\n\n  async getAllInvitationCodes(): Promise<InvitationCode[]> {\n    return await db.select()\n      .from(invitationCodes)\n      .orderBy(desc(invitationCodes.createdAt));\n  }\n}\n\nexport interface IStorage {\n  // Session store for authentication\n  sessionStore: session.Store;\n  \n  // User operations\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;\n  getAllUsers(): Promise<User[]>;\n  getAllClients(): Promise<Client[]>;\n  getClientsByDoctor(doctorId: number): Promise<Client[]>;\n  getAllConversations(): Promise<AiTherapyConversation[]>;\n  \n  // Doctor-Client assignment operations\n  createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient>;\n  getAllDoctorClientRelationships(): Promise<DoctorClient[]>;\n  \n  // Client operations\n  getClient(id: number): Promise<Client | undefined>;\n  getClientsByUserId(userId: number): Promise<Client[]>;\n  getClientByUserId(userId: number): Promise<Client | undefined>;\n  createClient(client: InsertClient): Promise<Client>;\n  updateClientStatus(id: number, status: string): Promise<Client | undefined>;\n  \n  // Session notes operations\n  getSessionNote(id: number): Promise<SessionNote | undefined>;\n  getSessionNotesByClientId(clientId: number): Promise<SessionNote[]>;\n  getRecentSessionNotes(userId: number, limit?: number): Promise<SessionNote[]>;\n  createSessionNote(note: InsertSessionNote): Promise<SessionNote>;\n  updateSessionNoteAnalysis(\n    id: number, \n    summary: string, \n    themes: Theme[], \n    recommendations: string[]\n  ): Promise<SessionNote | undefined>;\n  \n  // Client themes operations\n  getClientThemes(clientId: number): Promise<ClientTheme[]>;\n  updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme>;\n  \n  // AI Therapy Conversation operations\n  getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined>;\n  getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]>;\n  createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation>;\n  endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined>;\n  \n  // AI Therapy Message operations\n  getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]>;\n  createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage>;\n  \n  // AI Therapy Settings operations\n  getAiTherapySettings(): Promise<AiTherapySettings[]>;\n  getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined>;\n  createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings>;\n  updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined>;\n  \n  // Dashboard stats operations\n  getDashboardStats(userId: number): Promise<DashboardStats>;\n  \n  // Invitation code operations\n  createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode>;\n  getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined>;\n  getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]>;\n  getAllInvitationCodes(): Promise<InvitationCode[]>;\n  markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined>;\n}\n\nexport class MemStorage implements IStorage {\n  sessionStore: session.Store;\n  \n  private users: Map<number, User>;\n  private clients: Map<number, Client>;\n  private sessionNotes: Map<number, SessionNote>;\n  private clientThemes: Map<number, ClientTheme>;\n  private aiConversations: Map<number, AiTherapyConversation>;\n  private aiMessages: Map<number, AiTherapyMessage>;\n  private aiSettings: Map<number, AiTherapySettings>;\n  private doctorClients: Map<number, DoctorClient>;\n  \n  private userId: number;\n  private clientId: number;\n  private noteId: number;\n  private themeId: number;\n  private conversationId: number;\n  private messageId: number;\n  private settingId: number;\n  private doctorClientId: number;\n  private invitationCodes: Map<number, InvitationCode>;\n  private invitationCodeId: number;\n\n  constructor() {\n    // Initialize session store\n    const MemoryStore = createMemoryStore(session);\n    this.sessionStore = new MemoryStore({\n      checkPeriod: 86400000 // Prune expired entries every 24h\n    });\n    \n    // Initialize data collections\n    this.users = new Map();\n    this.clients = new Map();\n    this.sessionNotes = new Map();\n    this.clientThemes = new Map();\n    this.aiConversations = new Map();\n    this.aiMessages = new Map();\n    this.aiSettings = new Map();\n    this.doctorClients = new Map();\n    this.invitationCodes = new Map();\n    \n    // Initialize counters for IDs\n    this.userId = 1;\n    this.clientId = 1;\n    this.noteId = 1;\n    this.themeId = 1;\n    this.conversationId = 1;\n    this.messageId = 1;\n    this.settingId = 1;\n    this.doctorClientId = 1;\n    this.invitationCodeId = 1;\n    \n    // Create a default therapist user\n    this.createUser({\n      username: \"drroberts\",\n      password: \"password123\", // In a real app, this would be hashed\n      name: \"Dr. Roberts\",\n      userRole: \"doctor\",\n      professionalRole: \"Psychologist\",\n      email: \"<EMAIL>\"\n    });\n    \n    // Create some default clients\n    const userId = 1;\n    this.createClient({\n      userId,\n      name: \"Jane Doe\",\n      status: \"improving\"\n    });\n    \n    this.createClient({\n      userId,\n      name: \"Michael Smith\",\n      status: \"stable\"\n    });\n    \n    this.createClient({\n      userId,\n      name: \"Alex Lee\",\n      status: \"needs_attention\"\n    });\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    return Array.from(this.users.values()).find(\n      (user) => user.username === username,\n    );\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const id = this.userId++;\n    const user: User = { \n      ...insertUser, \n      id, \n      userRole: insertUser.userRole || \"client\",\n      professionalRole: insertUser.professionalRole || null,\n      createdAt: new Date() \n    };\n    this.users.set(id, user);\n    return user;\n  }\n  \n  // Client operations\n  async getClient(id: number): Promise<Client | undefined> {\n    return this.clients.get(id);\n  }\n  \n  async getClientsByUserId(userId: number): Promise<Client[]> {\n    return Array.from(this.clients.values()).filter(\n      (client) => client.userId === userId\n    );\n  }\n  \n  async createClient(insertClient: InsertClient): Promise<Client> {\n    const id = this.clientId++;\n    const client: Client = {\n      ...insertClient,\n      id,\n      status: insertClient.status || \"stable\",\n      createdAt: new Date()\n    };\n    this.clients.set(id, client);\n    return client;\n  }\n  \n  async updateClientStatus(id: number, status: string): Promise<Client | undefined> {\n    const client = await this.getClient(id);\n    if (client) {\n      const updatedClient = { ...client, status };\n      this.clients.set(id, updatedClient);\n      return updatedClient;\n    }\n    return undefined;\n  }\n  \n  // Session notes operations\n  async getSessionNote(id: number): Promise<SessionNote | undefined> {\n    return this.sessionNotes.get(id);\n  }\n  \n  async getSessionNotesByClientId(clientId: number): Promise<SessionNote[]> {\n    return Array.from(this.sessionNotes.values())\n      .filter((note) => note.clientId === clientId)\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());\n  }\n  \n  async getRecentSessionNotes(userId: number, limit: number = 10): Promise<SessionNote[]> {\n    const user = await this.getUser(userId);\n    if (!user) return [];\n    \n    const notes = Array.from(this.sessionNotes.values())\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\n      .slice(0, limit);\n    \n    return notes;\n  }\n  \n  async createSessionNote(insertNote: InsertSessionNote): Promise<SessionNote> {\n    const id = this.noteId++;\n    const note: SessionNote = {\n      ...insertNote,\n      id,\n      summary: null,\n      recommendations: null,\n      analyzed: false,\n      createdAt: new Date(),\n      sessionDate: insertNote.sessionDate || new Date(),\n      duration: insertNote.duration || 50,\n      aiProcessed: false,\n      rawNotes: insertNote.content\n    };\n    this.sessionNotes.set(id, note);\n    return note;\n  }\n  \n  async updateSessionNoteAnalysis(\n    id: number, \n    summary: string, \n    themes: Theme[], \n    recommendations: string[]\n  ): Promise<SessionNote | undefined> {\n    const note = await this.getSessionNote(id);\n    if (note) {\n      const updatedNote: SessionNote = {\n        ...note,\n        summary,\n        recommendations,\n        analyzed: true\n      };\n      this.sessionNotes.set(id, updatedNote);\n      \n      // Update client themes based on analysis\n      if (themes && themes.length > 0) {\n        for (const theme of themes) {\n          await this.updateOrCreateClientTheme(note.clientId, theme.name, theme.trend);\n        }\n      }\n      \n      return updatedNote;\n    }\n    return undefined;\n  }\n  \n  // Client themes operations\n  async getClientThemes(clientId: number): Promise<ClientTheme[]> {\n    return Array.from(this.clientThemes.values())\n      .filter((theme) => theme.clientId === clientId)\n      .sort((a, b) => b.occurrences - a.occurrences);\n  }\n  \n  async updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme> {\n    const existingTheme = Array.from(this.clientThemes.values()).find(\n      (theme) => theme.clientId === clientId && theme.name === name\n    );\n    \n    if (existingTheme) {\n      const updatedTheme = {\n        ...existingTheme,\n        occurrences: existingTheme.occurrences + 1,\n        trend: trend\n      };\n      this.clientThemes.set(existingTheme.id, updatedTheme);\n      return updatedTheme;\n    } else {\n      const id = this.themeId++;\n      const newTheme: ClientTheme = {\n        id,\n        clientId,\n        name,\n        occurrences: 1,\n        trend,\n        createdAt: new Date()\n      };\n      this.clientThemes.set(id, newTheme);\n      return newTheme;\n    }\n  }\n  \n  // Client operations - added for AI therapy\n  async getClientByUserId(userId: number): Promise<Client | undefined> {\n    return Array.from(this.clients.values()).find(\n      (client) => client.userId === userId\n    );\n  }\n  \n  // AI Therapy Conversation operations\n  async getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined> {\n    return this.aiConversations.get(id);\n  }\n  \n  async getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]> {\n    let conversations = Array.from(this.aiConversations.values());\n    \n    if (clientId) {\n      conversations = conversations.filter(conv => conv.clientId === clientId);\n    } else {\n      // If no specific client ID, first we need to get all client IDs belonging to this user\n      const clients = await this.getClientsByUserId(userId);\n      const clientIds = clients.map(client => client.id);\n      conversations = conversations.filter(conv => clientIds.includes(conv.clientId));\n    }\n    \n    return conversations.sort((a, b) => {\n      return b.createdAt.getTime() - a.createdAt.getTime();\n    });\n  }\n  \n  async createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation> {\n    const id = this.conversationId++;\n    const newConversation: AiTherapyConversation = {\n      ...conversation,\n      id,\n      summary: null,\n      active: true,\n      aiProcessed: false,\n      createdAt: new Date(),\n      endedAt: null,\n      title: conversation.title || 'New Conversation'\n    };\n    \n    this.aiConversations.set(id, newConversation);\n    return newConversation;\n  }\n  \n  async endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined> {\n    const conversation = await this.getAiTherapyConversation(id);\n    if (!conversation) return undefined;\n    \n    const updatedConversation: AiTherapyConversation = {\n      ...conversation,\n      summary,\n      active: false,\n      aiProcessed: true,\n      endedAt: new Date()\n    };\n    \n    this.aiConversations.set(id, updatedConversation);\n    return updatedConversation;\n  }\n  \n  // AI Therapy Message operations\n  async getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]> {\n    return Array.from(this.aiMessages.values())\n      .filter(message => message.conversationId === conversationId)\n      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n  }\n  \n  async createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage> {\n    const id = this.messageId++;\n    const newMessage: AiTherapyMessage = {\n      ...message,\n      id,\n      timestamp: message.timestamp || new Date(),\n      createdAt: new Date(),\n      audioUrl: message.audioUrl || null\n    };\n    \n    this.aiMessages.set(id, newMessage);\n    return newMessage;\n  }\n  \n  // AI Therapy Settings operations\n  async getAiTherapySettings(): Promise<AiTherapySettings[]> {\n    return Array.from(this.aiSettings.values());\n  }\n  \n  async getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined> {\n    return Array.from(this.aiSettings.values()).find(settings => settings.active);\n  }\n  \n  async createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings> {\n    // If this is going to be the active settings, deactivate all others\n    if (settings.active) {\n      Array.from(this.aiSettings.entries()).forEach(([id, existingSettings]) => {\n        if (existingSettings.active) {\n          this.aiSettings.set(id, { ...existingSettings, active: false });\n        }\n      });\n    }\n    \n    const id = this.settingId++;\n    const newSettings: AiTherapySettings = {\n      ...settings,\n      id,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      model: settings.model || \"gpt-4o\",\n      temperature: settings.temperature || 70,\n      active: settings.active === undefined ? true : settings.active\n    };\n    \n    this.aiSettings.set(id, newSettings);\n    return newSettings;\n  }\n  \n  async updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined> {\n    const existingSettings = this.aiSettings.get(id);\n    if (!existingSettings) return undefined;\n    \n    // If this update is to activate settings, deactivate all others\n    if (settings.active) {\n      Array.from(this.aiSettings.entries()).forEach(([settingsId, currentSettings]) => {\n        if (settingsId !== id && currentSettings.active) {\n          this.aiSettings.set(settingsId, { ...currentSettings, active: false });\n        }\n      });\n    }\n    \n    const updatedSettings: AiTherapySettings = {\n      ...existingSettings,\n      ...settings,\n      updatedAt: new Date()\n    };\n    \n    this.aiSettings.set(id, updatedSettings);\n    return updatedSettings;\n  }\n  \n  // Dashboard stats operations\n  async getDashboardStats(userId: number): Promise<DashboardStats> {\n    const clients = await this.getClientsByUserId(userId);\n    const totalClients = clients.length;\n    \n    // Calculate how many clients are improving\n    const improving = clients.filter(client => client.status === 'improving').length;\n    const improvingPercentage = totalClients > 0 ? Math.round((improving / totalClients) * 100) : 0;\n    \n    // Calculate weekly notes (for the last 7 days)\n    const oneWeekAgo = new Date();\n    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n    \n    const clientIds = clients.map(client => client.id);\n    const allNotes = Array.from(this.sessionNotes.values());\n    \n    const weeklyNotes = allNotes.filter(note => \n      clientIds.includes(note.clientId) && \n      note.createdAt >= oneWeekAgo\n    ).length;\n    \n    // Calculate total insights (analyzed notes)\n    const insights = allNotes.filter(note => \n      clientIds.includes(note.clientId) && \n      note.analyzed\n    ).length;\n    \n    return {\n      totalClients,\n      weeklyNotes,\n      insights,\n      improving,\n      improvingPercentage\n    };\n  }\n  \n  // User update operations\n  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {\n    const user = await this.getUser(id);\n    if (!user) return undefined;\n    \n    const updatedUser = { ...user, ...userData };\n    this.users.set(id, updatedUser);\n    return updatedUser;\n  }\n  \n  // Get all users\n  async getAllUsers(): Promise<User[]> {\n    return Array.from(this.users.values());\n  }\n\n  async getAllClients(): Promise<Client[]> {\n    return Array.from(this.clients.values());\n  }\n\n  async getClientsByDoctor(doctorId: number): Promise<Client[]> {\n    const assignments = Array.from(this.doctorClients.values())\n      .filter(dc => dc.doctorId === doctorId);\n\n    const clientIds = assignments.map(a => a.clientId);\n    return Array.from(this.clients.values())\n      .filter(client => clientIds.includes(client.id));\n  }\n\n  async getAllConversations(): Promise<AiTherapyConversation[]> {\n    return Array.from(this.aiConversations.values())\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());\n  }\n  \n  // Doctor-Client operations\n  async createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient> {\n    const id = this.doctorClientId++;\n    const doctorClient: DoctorClient = {\n      ...assignment,\n      id,\n      createdAt: new Date()\n    };\n    this.doctorClients.set(id, doctorClient);\n    return doctorClient;\n  }\n  \n  async getAllDoctorClientRelationships(): Promise<DoctorClient[]> {\n    return Array.from(this.doctorClients.values());\n  }\n  \n  // Invitation code operations\n  async createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode> {\n    const id = this.invitationCodeId++;\n    const invitationCode: InvitationCode = {\n      ...code,\n      id,\n      used: false,\n      usedBy: null,\n      usedAt: null,\n      createdAt: new Date(),\n      expiresAt: code.expiresAt || null\n    };\n    this.invitationCodes.set(id, invitationCode);\n    return invitationCode;\n  }\n  \n  async getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined> {\n    return Array.from(this.invitationCodes.values()).find(\n      invitation => invitation.code === code\n    );\n  }\n  \n  async getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]> {\n    let invitations = Array.from(this.invitationCodes.values())\n      .filter(invitation => invitation.createdBy === userId);\n    \n    if (type) {\n      invitations = invitations.filter(invitation => invitation.type === type);\n    }\n    \n    return invitations;\n  }\n  \n  async markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined> {\n    const invitation = await this.getInvitationCodeByCode(code);\n    if (!invitation || invitation.used) return undefined;\n\n    const updatedInvitation: InvitationCode = {\n      ...invitation,\n      used: true,\n      usedBy: userId,\n      usedAt: new Date()\n    };\n\n    this.invitationCodes.set(invitation.id, updatedInvitation);\n    return updatedInvitation;\n  }\n\n  async getAllInvitationCodes(): Promise<InvitationCode[]> {\n    return Array.from(this.invitationCodes.values())\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());\n  }\n}\n\n// Use the memory storage implementation\n// Use the DatabaseStorage instead of MemStorage for production\nexport const storage = new DatabaseStorage();", "modifiedCode": "import { \n  users, type User, type InsertUser,\n  clients, type Client, type InsertClient,\n  sessionNotes, type SessionNote, type InsertSessionNote,\n  clientThemes, type ClientTheme, type InsertClientTheme,\n  aiTherapyConversations, type AiTherapyConversation, type InsertAiTherapyConversation,\n  aiTherapyMessages, type AiTherapyMessage, type InsertAiTherapyMessage,\n  aiTherapySettings, type AiTherapySettings, type InsertAiTherapySettings,\n  doctorClients, type DoctorClient, type InsertDoctorClient,\n  invitationCodes, type InvitationCode, type InsertInvitationCode,\n  type Theme, type DashboardStats\n} from \"@shared/schema\";\nimport session from \"express-session\";\nimport createMemoryStore from \"memorystore\";\nimport { db } from \"./db\";\nimport { eq, desc, and, inArray, not, sql } from \"drizzle-orm\";\nimport PgSession from \"connect-pg-simple\";\nimport { pool } from \"./db\";\n\n// DatabaseStorage class that implements IStorage for PostgreSQL\nexport class DatabaseStorage implements IStorage {\n  sessionStore: session.Store;\n\n  constructor() {\n    // Initialize session store with PostgreSQL\n    const PgStore = PgSession(session);\n    this.sessionStore = new PgStore({\n      pool: pool as any,\n      tableName: 'session',\n      createTableIfMissing: true\n    });\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    const result = await db.select().from(users).where(eq(users.id, id));\n    return result[0];\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    const result = await db.select().from(users).where(eq(users.username, username));\n    return result[0];\n  }\n\n  async createUser(user: InsertUser): Promise<User> {\n    const result = await db.insert(users).values(user).returning();\n    return result[0];\n  }\n\n  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {\n    const result = await db.update(users).set(userData).where(eq(users.id, id)).returning();\n    return result[0];\n  }\n\n  async getAllUsers(): Promise<User[]> {\n    return await db.select().from(users);\n  }\n\n  async getAllClients(): Promise<Client[]> {\n    return await db.select().from(clients);\n  }\n\n  async getClientsByDoctor(doctorId: number): Promise<Client[]> {\n    const assignments = await db.select()\n      .from(doctorClients)\n      .where(eq(doctorClients.doctorId, doctorId));\n\n    const clientIds = assignments.map(a => a.clientId);\n    if (clientIds.length === 0) return [];\n\n    // Fix: Use inArray to get ALL clients for this doctor\n    return await db.select()\n      .from(clients)\n      .where(inArray(clients.id, clientIds));\n  }\n\n  async getAllConversations(): Promise<AiTherapyConversation[]> {\n    return await db.select()\n      .from(aiTherapyConversations)\n      .orderBy(desc(aiTherapyConversations.createdAt));\n  }\n  \n  // Doctor-Client assignment operations\n  async createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient> {\n    const result = await db.insert(doctorClients).values(assignment).returning();\n    return result[0];\n  }\n\n  async getAllDoctorClientRelationships(): Promise<DoctorClient[]> {\n    return await db.select().from(doctorClients);\n  }\n  \n  // Client operations\n  async getClient(id: number): Promise<Client | undefined> {\n    const result = await db.select().from(clients).where(eq(clients.id, id));\n    return result[0];\n  }\n\n  async getClientsByUserId(userId: number): Promise<Client[]> {\n    return await db.select().from(clients).where(eq(clients.userId, userId));\n  }\n\n  async getClientByUserId(userId: number): Promise<Client | undefined> {\n    const result = await db.select().from(clients).where(eq(clients.userId, userId));\n    return result[0];\n  }\n\n  async createClient(client: InsertClient): Promise<Client> {\n    const result = await db.insert(clients).values(client).returning();\n    return result[0];\n  }\n\n  async updateClientStatus(id: number, status: string): Promise<Client | undefined> {\n    const result = await db.update(clients)\n      .set({ status })\n      .where(eq(clients.id, id))\n      .returning();\n    return result[0];\n  }\n  \n  // Session notes operations\n  async getSessionNote(id: number): Promise<SessionNote | undefined> {\n    const result = await db.select().from(sessionNotes).where(eq(sessionNotes.id, id));\n    return result[0];\n  }\n\n  async getSessionNotesByClientId(clientId: number): Promise<SessionNote[]> {\n    return await db.select()\n      .from(sessionNotes)\n      .where(eq(sessionNotes.clientId, clientId))\n      .orderBy(desc(sessionNotes.createdAt));\n  }\n\n  async getRecentSessionNotes(userId: number, limit: number = 10): Promise<SessionNote[]> {\n    // This is a simplified implementation that just returns recent notes\n    // A more complete implementation would filter based on user access\n    return await db.select()\n      .from(sessionNotes)\n      .orderBy(desc(sessionNotes.createdAt))\n      .limit(limit);\n  }\n\n  async createSessionNote(note: InsertSessionNote): Promise<SessionNote> {\n    const result = await db.insert(sessionNotes).values({\n      ...note,\n      rawNotes: note.content, // Copy content to rawNotes for client compatibility\n      aiProcessed: false\n    }).returning();\n    return result[0];\n  }\n\n  async updateSessionNoteAnalysis(\n    id: number,\n    summary: string,\n    themes: Theme[],\n    recommendations: string[]\n  ): Promise<SessionNote | undefined> {\n    const result = await db.update(sessionNotes)\n      .set({\n        summary,\n        recommendations,\n        analyzed: true,\n        aiProcessed: true\n      })\n      .where(eq(sessionNotes.id, id))\n      .returning();\n      \n    if (result[0] && themes && themes.length > 0) {\n      for (const theme of themes) {\n        await this.updateOrCreateClientTheme(result[0].clientId, theme.name, theme.trend);\n      }\n    }\n    \n    return result[0];\n  }\n  \n  // Client themes operations\n  async getClientThemes(clientId: number): Promise<ClientTheme[]> {\n    return await db.select()\n      .from(clientThemes)\n      .where(eq(clientThemes.clientId, clientId))\n      .orderBy(desc(clientThemes.occurrences));\n  }\n\n  async updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme> {\n    // First try to find existing theme\n    const existingTheme = await db.select()\n      .from(clientThemes)\n      .where(and(\n        eq(clientThemes.clientId, clientId),\n        eq(clientThemes.name, name)\n      ));\n    \n    if (existingTheme.length > 0) {\n      // Update existing theme\n      const result = await db.update(clientThemes)\n        .set({\n          occurrences: existingTheme[0].occurrences + 1,\n          trend\n        })\n        .where(eq(clientThemes.id, existingTheme[0].id))\n        .returning();\n      return result[0];\n    } else {\n      // Create new theme\n      const result = await db.insert(clientThemes)\n        .values({\n          clientId,\n          name,\n          occurrences: 1,\n          trend\n        })\n        .returning();\n      return result[0];\n    }\n  }\n  \n  // AI Therapy Conversation operations\n  async getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined> {\n    const result = await db.select()\n      .from(aiTherapyConversations)\n      .where(eq(aiTherapyConversations.id, id));\n    return result[0];\n  }\n\n  async getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]> {\n    if (clientId) {\n      return await db.select()\n        .from(aiTherapyConversations)\n        .where(eq(aiTherapyConversations.clientId, clientId))\n        .orderBy(desc(aiTherapyConversations.createdAt));\n    } else {\n      return await db.select()\n        .from(aiTherapyConversations)\n        .where(eq(aiTherapyConversations.userId, userId))\n        .orderBy(desc(aiTherapyConversations.createdAt));\n    }\n  }\n\n  async createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation> {\n    const result = await db.insert(aiTherapyConversations)\n      .values({\n        ...conversation,\n        title: conversation.title || \"New Conversation\",\n        active: true,\n        aiProcessed: false\n      })\n      .returning();\n    return result[0];\n  }\n\n  async endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined> {\n    const result = await db.update(aiTherapyConversations)\n      .set({\n        summary,\n        active: false,\n        aiProcessed: true,\n        endedAt: new Date()\n      })\n      .where(eq(aiTherapyConversations.id, id))\n      .returning();\n    return result[0];\n  }\n  \n  // AI Therapy Message operations\n  async getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]> {\n    return await db.select()\n      .from(aiTherapyMessages)\n      .where(eq(aiTherapyMessages.conversationId, conversationId))\n      .orderBy(aiTherapyMessages.timestamp);\n  }\n\n  async createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage> {\n    const result = await db.insert(aiTherapyMessages)\n      .values(message)\n      .returning();\n    return result[0];\n  }\n  \n  // AI Therapy Settings operations\n  async getAiTherapySettings(): Promise<AiTherapySettings[]> {\n    return await db.select().from(aiTherapySettings);\n  }\n\n  async getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined> {\n    const result = await db.select()\n      .from(aiTherapySettings)\n      .where(eq(aiTherapySettings.active, true));\n    return result[0];\n  }\n\n  async createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings> {\n    // If this is going to be the active settings, deactivate all others\n    if (settings.active) {\n      await db.update(aiTherapySettings)\n        .set({ active: false })\n        .where(eq(aiTherapySettings.active, true));\n    }\n    \n    const result = await db.insert(aiTherapySettings)\n      .values(settings)\n      .returning();\n    return result[0];\n  }\n\n  async updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined> {\n    // If this update is to activate settings, deactivate all others\n    if (settings.active) {\n      await db.update(aiTherapySettings)\n        .set({ active: false })\n        .where(and(\n          eq(aiTherapySettings.active, true),\n          not(eq(aiTherapySettings.id, id))\n        ));\n    }\n    \n    const result = await db.update(aiTherapySettings)\n      .set({\n        ...settings,\n        updatedAt: new Date()\n      })\n      .where(eq(aiTherapySettings.id, id))\n      .returning();\n    return result[0];\n  }\n  \n  // Dashboard stats operations\n  async getDashboardStats(userId: number): Promise<DashboardStats> {\n    // Get all clients for this user\n    const clientsResult = await db.select().from(clients).where(eq(clients.userId, userId));\n    const totalClients = clientsResult.length;\n    \n    // Calculate how many clients are improving\n    const improving = clientsResult.filter(client => client.status === 'improving').length;\n    const improvingPercentage = totalClients > 0 ? Math.round((improving / totalClients) * 100) : 0;\n    \n    // Get client IDs for this user\n    const clientIds = clientsResult.map(client => client.id);\n    \n    // Calculate weekly notes (for the last 7 days)\n    const oneWeekAgo = new Date();\n    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n    \n    let weeklyNotes = 0;\n    let insights = 0;\n    \n    if (clientIds.length > 0) {\n      // Count weekly notes\n      // Use Drizzle ORM instead of raw SQL for better type safety\n      const notesCount = await db.select({ count: sql<number>`count(*)` })\n        .from(sessionNotes)\n        .where(and(\n          inArray(sessionNotes.clientId, clientIds),\n          sql`${sessionNotes.createdAt} >= ${oneWeekAgo}`\n        ));\n      weeklyNotes = notesCount[0]?.count || 0;\n\n      // Count analyzed notes (insights)\n      const insightsCount = await db.select({ count: sql<number>`count(*)` })\n        .from(sessionNotes)\n        .where(and(\n          inArray(sessionNotes.clientId, clientIds),\n          eq(sessionNotes.analyzed, true)\n        ));\n      insights = insightsCount[0]?.count || 0;\n    }\n    \n    return {\n      totalClients,\n      weeklyNotes,\n      insights,\n      improving,\n      improvingPercentage\n    };\n  }\n  \n  // Invitation code operations\n  async createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode> {\n    const result = await db.insert(invitationCodes)\n      .values(code)\n      .returning();\n    return result[0];\n  }\n\n  async getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined> {\n    const result = await db.select()\n      .from(invitationCodes)\n      .where(eq(invitationCodes.code, code));\n    return result[0];\n  }\n\n  async getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]> {\n    if (type) {\n      return await db.select()\n        .from(invitationCodes)\n        .where(and(\n          eq(invitationCodes.createdBy, userId),\n          eq(invitationCodes.type, type)\n        ));\n    } else {\n      return await db.select()\n        .from(invitationCodes)\n        .where(eq(invitationCodes.createdBy, userId));\n    }\n  }\n\n  async markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined> {\n    const result = await db.update(invitationCodes)\n      .set({\n        used: true,\n        usedBy: userId,\n        usedAt: new Date()\n      })\n      .where(eq(invitationCodes.code, code))\n      .returning();\n    return result[0];\n  }\n\n  async getAllInvitationCodes(): Promise<InvitationCode[]> {\n    return await db.select()\n      .from(invitationCodes)\n      .orderBy(desc(invitationCodes.createdAt));\n  }\n}\n\nexport interface IStorage {\n  // Session store for authentication\n  sessionStore: session.Store;\n  \n  // User operations\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;\n  getAllUsers(): Promise<User[]>;\n  getAllClients(): Promise<Client[]>;\n  getClientsByDoctor(doctorId: number): Promise<Client[]>;\n  getAllConversations(): Promise<AiTherapyConversation[]>;\n  \n  // Doctor-Client assignment operations\n  createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient>;\n  getAllDoctorClientRelationships(): Promise<DoctorClient[]>;\n  \n  // Client operations\n  getClient(id: number): Promise<Client | undefined>;\n  getClientsByUserId(userId: number): Promise<Client[]>;\n  getClientByUserId(userId: number): Promise<Client | undefined>;\n  createClient(client: InsertClient): Promise<Client>;\n  updateClientStatus(id: number, status: string): Promise<Client | undefined>;\n  \n  // Session notes operations\n  getSessionNote(id: number): Promise<SessionNote | undefined>;\n  getSessionNotesByClientId(clientId: number): Promise<SessionNote[]>;\n  getRecentSessionNotes(userId: number, limit?: number): Promise<SessionNote[]>;\n  createSessionNote(note: InsertSessionNote): Promise<SessionNote>;\n  updateSessionNoteAnalysis(\n    id: number, \n    summary: string, \n    themes: Theme[], \n    recommendations: string[]\n  ): Promise<SessionNote | undefined>;\n  \n  // Client themes operations\n  getClientThemes(clientId: number): Promise<ClientTheme[]>;\n  updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme>;\n  \n  // AI Therapy Conversation operations\n  getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined>;\n  getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]>;\n  createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation>;\n  endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined>;\n  \n  // AI Therapy Message operations\n  getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]>;\n  createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage>;\n  \n  // AI Therapy Settings operations\n  getAiTherapySettings(): Promise<AiTherapySettings[]>;\n  getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined>;\n  createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings>;\n  updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined>;\n  \n  // Dashboard stats operations\n  getDashboardStats(userId: number): Promise<DashboardStats>;\n  \n  // Invitation code operations\n  createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode>;\n  getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined>;\n  getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]>;\n  getAllInvitationCodes(): Promise<InvitationCode[]>;\n  markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined>;\n}\n\nexport class MemStorage implements IStorage {\n  sessionStore: session.Store;\n  \n  private users: Map<number, User>;\n  private clients: Map<number, Client>;\n  private sessionNotes: Map<number, SessionNote>;\n  private clientThemes: Map<number, ClientTheme>;\n  private aiConversations: Map<number, AiTherapyConversation>;\n  private aiMessages: Map<number, AiTherapyMessage>;\n  private aiSettings: Map<number, AiTherapySettings>;\n  private doctorClients: Map<number, DoctorClient>;\n  \n  private userId: number;\n  private clientId: number;\n  private noteId: number;\n  private themeId: number;\n  private conversationId: number;\n  private messageId: number;\n  private settingId: number;\n  private doctorClientId: number;\n  private invitationCodes: Map<number, InvitationCode>;\n  private invitationCodeId: number;\n\n  constructor() {\n    // Initialize session store\n    const MemoryStore = createMemoryStore(session);\n    this.sessionStore = new MemoryStore({\n      checkPeriod: 86400000 // Prune expired entries every 24h\n    });\n    \n    // Initialize data collections\n    this.users = new Map();\n    this.clients = new Map();\n    this.sessionNotes = new Map();\n    this.clientThemes = new Map();\n    this.aiConversations = new Map();\n    this.aiMessages = new Map();\n    this.aiSettings = new Map();\n    this.doctorClients = new Map();\n    this.invitationCodes = new Map();\n    \n    // Initialize counters for IDs\n    this.userId = 1;\n    this.clientId = 1;\n    this.noteId = 1;\n    this.themeId = 1;\n    this.conversationId = 1;\n    this.messageId = 1;\n    this.settingId = 1;\n    this.doctorClientId = 1;\n    this.invitationCodeId = 1;\n    \n    // Create a default therapist user\n    this.createUser({\n      username: \"drroberts\",\n      password: \"password123\", // In a real app, this would be hashed\n      name: \"Dr. Roberts\",\n      userRole: \"doctor\",\n      professionalRole: \"Psychologist\",\n      email: \"<EMAIL>\"\n    });\n    \n    // Create some default clients\n    const userId = 1;\n    this.createClient({\n      userId,\n      name: \"Jane Doe\",\n      status: \"improving\"\n    });\n    \n    this.createClient({\n      userId,\n      name: \"Michael Smith\",\n      status: \"stable\"\n    });\n    \n    this.createClient({\n      userId,\n      name: \"Alex Lee\",\n      status: \"needs_attention\"\n    });\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    return Array.from(this.users.values()).find(\n      (user) => user.username === username,\n    );\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const id = this.userId++;\n    const user: User = { \n      ...insertUser, \n      id, \n      userRole: insertUser.userRole || \"client\",\n      professionalRole: insertUser.professionalRole || null,\n      createdAt: new Date() \n    };\n    this.users.set(id, user);\n    return user;\n  }\n  \n  // Client operations\n  async getClient(id: number): Promise<Client | undefined> {\n    return this.clients.get(id);\n  }\n  \n  async getClientsByUserId(userId: number): Promise<Client[]> {\n    return Array.from(this.clients.values()).filter(\n      (client) => client.userId === userId\n    );\n  }\n  \n  async createClient(insertClient: InsertClient): Promise<Client> {\n    const id = this.clientId++;\n    const client: Client = {\n      ...insertClient,\n      id,\n      status: insertClient.status || \"stable\",\n      createdAt: new Date()\n    };\n    this.clients.set(id, client);\n    return client;\n  }\n  \n  async updateClientStatus(id: number, status: string): Promise<Client | undefined> {\n    const client = await this.getClient(id);\n    if (client) {\n      const updatedClient = { ...client, status };\n      this.clients.set(id, updatedClient);\n      return updatedClient;\n    }\n    return undefined;\n  }\n  \n  // Session notes operations\n  async getSessionNote(id: number): Promise<SessionNote | undefined> {\n    return this.sessionNotes.get(id);\n  }\n  \n  async getSessionNotesByClientId(clientId: number): Promise<SessionNote[]> {\n    return Array.from(this.sessionNotes.values())\n      .filter((note) => note.clientId === clientId)\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());\n  }\n  \n  async getRecentSessionNotes(userId: number, limit: number = 10): Promise<SessionNote[]> {\n    const user = await this.getUser(userId);\n    if (!user) return [];\n    \n    const notes = Array.from(this.sessionNotes.values())\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\n      .slice(0, limit);\n    \n    return notes;\n  }\n  \n  async createSessionNote(insertNote: InsertSessionNote): Promise<SessionNote> {\n    const id = this.noteId++;\n    const note: SessionNote = {\n      ...insertNote,\n      id,\n      summary: null,\n      recommendations: null,\n      analyzed: false,\n      createdAt: new Date(),\n      sessionDate: insertNote.sessionDate || new Date(),\n      duration: insertNote.duration || 50,\n      aiProcessed: false,\n      rawNotes: insertNote.content\n    };\n    this.sessionNotes.set(id, note);\n    return note;\n  }\n  \n  async updateSessionNoteAnalysis(\n    id: number, \n    summary: string, \n    themes: Theme[], \n    recommendations: string[]\n  ): Promise<SessionNote | undefined> {\n    const note = await this.getSessionNote(id);\n    if (note) {\n      const updatedNote: SessionNote = {\n        ...note,\n        summary,\n        recommendations,\n        analyzed: true\n      };\n      this.sessionNotes.set(id, updatedNote);\n      \n      // Update client themes based on analysis\n      if (themes && themes.length > 0) {\n        for (const theme of themes) {\n          await this.updateOrCreateClientTheme(note.clientId, theme.name, theme.trend);\n        }\n      }\n      \n      return updatedNote;\n    }\n    return undefined;\n  }\n  \n  // Client themes operations\n  async getClientThemes(clientId: number): Promise<ClientTheme[]> {\n    return Array.from(this.clientThemes.values())\n      .filter((theme) => theme.clientId === clientId)\n      .sort((a, b) => b.occurrences - a.occurrences);\n  }\n  \n  async updateOrCreateClientTheme(clientId: number, name: string, trend: number): Promise<ClientTheme> {\n    const existingTheme = Array.from(this.clientThemes.values()).find(\n      (theme) => theme.clientId === clientId && theme.name === name\n    );\n    \n    if (existingTheme) {\n      const updatedTheme = {\n        ...existingTheme,\n        occurrences: existingTheme.occurrences + 1,\n        trend: trend\n      };\n      this.clientThemes.set(existingTheme.id, updatedTheme);\n      return updatedTheme;\n    } else {\n      const id = this.themeId++;\n      const newTheme: ClientTheme = {\n        id,\n        clientId,\n        name,\n        occurrences: 1,\n        trend,\n        createdAt: new Date()\n      };\n      this.clientThemes.set(id, newTheme);\n      return newTheme;\n    }\n  }\n  \n  // Client operations - added for AI therapy\n  async getClientByUserId(userId: number): Promise<Client | undefined> {\n    return Array.from(this.clients.values()).find(\n      (client) => client.userId === userId\n    );\n  }\n  \n  // AI Therapy Conversation operations\n  async getAiTherapyConversation(id: number): Promise<AiTherapyConversation | undefined> {\n    return this.aiConversations.get(id);\n  }\n  \n  async getAiTherapyConversations(userId: number, clientId?: number): Promise<AiTherapyConversation[]> {\n    let conversations = Array.from(this.aiConversations.values());\n    \n    if (clientId) {\n      conversations = conversations.filter(conv => conv.clientId === clientId);\n    } else {\n      // If no specific client ID, first we need to get all client IDs belonging to this user\n      const clients = await this.getClientsByUserId(userId);\n      const clientIds = clients.map(client => client.id);\n      conversations = conversations.filter(conv => clientIds.includes(conv.clientId));\n    }\n    \n    return conversations.sort((a, b) => {\n      return b.createdAt.getTime() - a.createdAt.getTime();\n    });\n  }\n  \n  async createAiTherapyConversation(conversation: InsertAiTherapyConversation): Promise<AiTherapyConversation> {\n    const id = this.conversationId++;\n    const newConversation: AiTherapyConversation = {\n      ...conversation,\n      id,\n      summary: null,\n      active: true,\n      aiProcessed: false,\n      createdAt: new Date(),\n      endedAt: null,\n      title: conversation.title || 'New Conversation'\n    };\n    \n    this.aiConversations.set(id, newConversation);\n    return newConversation;\n  }\n  \n  async endAiTherapyConversation(id: number, summary: string): Promise<AiTherapyConversation | undefined> {\n    const conversation = await this.getAiTherapyConversation(id);\n    if (!conversation) return undefined;\n    \n    const updatedConversation: AiTherapyConversation = {\n      ...conversation,\n      summary,\n      active: false,\n      aiProcessed: true,\n      endedAt: new Date()\n    };\n    \n    this.aiConversations.set(id, updatedConversation);\n    return updatedConversation;\n  }\n  \n  // AI Therapy Message operations\n  async getAiTherapyMessages(conversationId: number): Promise<AiTherapyMessage[]> {\n    return Array.from(this.aiMessages.values())\n      .filter(message => message.conversationId === conversationId)\n      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\n  }\n  \n  async createAiTherapyMessage(message: InsertAiTherapyMessage): Promise<AiTherapyMessage> {\n    const id = this.messageId++;\n    const newMessage: AiTherapyMessage = {\n      ...message,\n      id,\n      timestamp: message.timestamp || new Date(),\n      createdAt: new Date(),\n      audioUrl: message.audioUrl || null\n    };\n    \n    this.aiMessages.set(id, newMessage);\n    return newMessage;\n  }\n  \n  // AI Therapy Settings operations\n  async getAiTherapySettings(): Promise<AiTherapySettings[]> {\n    return Array.from(this.aiSettings.values());\n  }\n  \n  async getActiveAiTherapySettings(): Promise<AiTherapySettings | undefined> {\n    return Array.from(this.aiSettings.values()).find(settings => settings.active);\n  }\n  \n  async createAiTherapySettings(settings: InsertAiTherapySettings): Promise<AiTherapySettings> {\n    // If this is going to be the active settings, deactivate all others\n    if (settings.active) {\n      Array.from(this.aiSettings.entries()).forEach(([id, existingSettings]) => {\n        if (existingSettings.active) {\n          this.aiSettings.set(id, { ...existingSettings, active: false });\n        }\n      });\n    }\n    \n    const id = this.settingId++;\n    const newSettings: AiTherapySettings = {\n      ...settings,\n      id,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      model: settings.model || \"gpt-4o\",\n      temperature: settings.temperature || 70,\n      active: settings.active === undefined ? true : settings.active\n    };\n    \n    this.aiSettings.set(id, newSettings);\n    return newSettings;\n  }\n  \n  async updateAiTherapySettings(id: number, settings: Partial<AiTherapySettings>): Promise<AiTherapySettings | undefined> {\n    const existingSettings = this.aiSettings.get(id);\n    if (!existingSettings) return undefined;\n    \n    // If this update is to activate settings, deactivate all others\n    if (settings.active) {\n      Array.from(this.aiSettings.entries()).forEach(([settingsId, currentSettings]) => {\n        if (settingsId !== id && currentSettings.active) {\n          this.aiSettings.set(settingsId, { ...currentSettings, active: false });\n        }\n      });\n    }\n    \n    const updatedSettings: AiTherapySettings = {\n      ...existingSettings,\n      ...settings,\n      updatedAt: new Date()\n    };\n    \n    this.aiSettings.set(id, updatedSettings);\n    return updatedSettings;\n  }\n  \n  // Dashboard stats operations\n  async getDashboardStats(userId: number): Promise<DashboardStats> {\n    const clients = await this.getClientsByUserId(userId);\n    const totalClients = clients.length;\n    \n    // Calculate how many clients are improving\n    const improving = clients.filter(client => client.status === 'improving').length;\n    const improvingPercentage = totalClients > 0 ? Math.round((improving / totalClients) * 100) : 0;\n    \n    // Calculate weekly notes (for the last 7 days)\n    const oneWeekAgo = new Date();\n    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n    \n    const clientIds = clients.map(client => client.id);\n    const allNotes = Array.from(this.sessionNotes.values());\n    \n    const weeklyNotes = allNotes.filter(note => \n      clientIds.includes(note.clientId) && \n      note.createdAt >= oneWeekAgo\n    ).length;\n    \n    // Calculate total insights (analyzed notes)\n    const insights = allNotes.filter(note => \n      clientIds.includes(note.clientId) && \n      note.analyzed\n    ).length;\n    \n    return {\n      totalClients,\n      weeklyNotes,\n      insights,\n      improving,\n      improvingPercentage\n    };\n  }\n  \n  // User update operations\n  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {\n    const user = await this.getUser(id);\n    if (!user) return undefined;\n    \n    const updatedUser = { ...user, ...userData };\n    this.users.set(id, updatedUser);\n    return updatedUser;\n  }\n  \n  // Get all users\n  async getAllUsers(): Promise<User[]> {\n    return Array.from(this.users.values());\n  }\n\n  async getAllClients(): Promise<Client[]> {\n    return Array.from(this.clients.values());\n  }\n\n  async getClientsByDoctor(doctorId: number): Promise<Client[]> {\n    const assignments = Array.from(this.doctorClients.values())\n      .filter(dc => dc.doctorId === doctorId);\n\n    const clientIds = assignments.map(a => a.clientId);\n    return Array.from(this.clients.values())\n      .filter(client => clientIds.includes(client.id));\n  }\n\n  async getAllConversations(): Promise<AiTherapyConversation[]> {\n    return Array.from(this.aiConversations.values())\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());\n  }\n  \n  // Doctor-Client operations\n  async createDoctorClient(assignment: InsertDoctorClient): Promise<DoctorClient> {\n    const id = this.doctorClientId++;\n    const doctorClient: DoctorClient = {\n      ...assignment,\n      id,\n      createdAt: new Date()\n    };\n    this.doctorClients.set(id, doctorClient);\n    return doctorClient;\n  }\n  \n  async getAllDoctorClientRelationships(): Promise<DoctorClient[]> {\n    return Array.from(this.doctorClients.values());\n  }\n  \n  // Invitation code operations\n  async createInvitationCode(code: InsertInvitationCode): Promise<InvitationCode> {\n    const id = this.invitationCodeId++;\n    const invitationCode: InvitationCode = {\n      ...code,\n      id,\n      used: false,\n      usedBy: null,\n      usedAt: null,\n      createdAt: new Date(),\n      expiresAt: code.expiresAt || null\n    };\n    this.invitationCodes.set(id, invitationCode);\n    return invitationCode;\n  }\n  \n  async getInvitationCodeByCode(code: string): Promise<InvitationCode | undefined> {\n    return Array.from(this.invitationCodes.values()).find(\n      invitation => invitation.code === code\n    );\n  }\n  \n  async getInvitationCodesByCreator(userId: number, type?: 'doctor' | 'client'): Promise<InvitationCode[]> {\n    let invitations = Array.from(this.invitationCodes.values())\n      .filter(invitation => invitation.createdBy === userId);\n    \n    if (type) {\n      invitations = invitations.filter(invitation => invitation.type === type);\n    }\n    \n    return invitations;\n  }\n  \n  async markInvitationCodeAsUsed(code: string, userId: number): Promise<InvitationCode | undefined> {\n    const invitation = await this.getInvitationCodeByCode(code);\n    if (!invitation || invitation.used) return undefined;\n\n    const updatedInvitation: InvitationCode = {\n      ...invitation,\n      used: true,\n      usedBy: userId,\n      usedAt: new Date()\n    };\n\n    this.invitationCodes.set(invitation.id, updatedInvitation);\n    return updatedInvitation;\n  }\n\n  async getAllInvitationCodes(): Promise<InvitationCode[]> {\n    return Array.from(this.invitationCodes.values())\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());\n  }\n}\n\n// Use the memory storage implementation\n// Use the DatabaseStorage instead of MemStorage for production\nexport const storage = new DatabaseStorage();\n\n// Export pool for session store\nexport { pool } from \"./db\";"}