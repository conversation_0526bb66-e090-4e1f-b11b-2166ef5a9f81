{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/SessionNotesList.tsx"}, "originalCode": "import { Skeleton } from \"@/components/ui/skeleton\";\nimport { SessionNote, Theme } from \"@shared/schema\";\nimport { formatDate, getThemeColor, formatTrendIndicator } from \"@/lib/utils\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { Card } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Link } from \"wouter\";\n\ninterface SessionNotesListProps {\n  notes?: (SessionNote & { clientName?: string })[];\n  isLoading: boolean;\n  clientId?: number;\n  showFullDetails?: boolean;\n}\n\nexport default function SessionNotesList({ \n  notes, \n  isLoading, \n  clientId,\n  showFullDetails = false \n}: SessionNotesListProps) {\n  if (isLoading) {\n    return (\n      <Card className=\"shadow overflow-hidden rounded-lg divide-y divide-gray-200\">\n        {[1, 2].map((i) => (\n          <div key={i} className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <Skeleton className=\"h-4 w-40 mb-2\" />\n                <Skeleton className=\"h-3 w-32\" />\n              </div>\n              <Skeleton className=\"h-8 w-24\" />\n            </div>\n            \n            <div className=\"mb-4\">\n              <Skeleton className=\"h-3 w-24 mb-2\" />\n              <Skeleton className=\"h-20 w-full\" />\n            </div>\n            \n            <div className=\"mb-4\">\n              <Skeleton className=\"h-3 w-24 mb-2\" />\n              <div className=\"flex flex-wrap gap-1\">\n                <Skeleton className=\"h-6 w-20\" />\n                <Skeleton className=\"h-6 w-24\" />\n                <Skeleton className=\"h-6 w-16\" />\n              </div>\n            </div>\n            \n            <div>\n              <Skeleton className=\"h-3 w-40 mb-2\" />\n              <ul className=\"space-y-1\">\n                <li className=\"flex items-start\">\n                  <Skeleton className=\"h-5 w-5 mr-2\" />\n                  <Skeleton className=\"h-4 w-full\" />\n                </li>\n                <li className=\"flex items-start\">\n                  <Skeleton className=\"h-5 w-5 mr-2\" />\n                  <Skeleton className=\"h-4 w-full\" />\n                </li>\n              </ul>\n            </div>\n          </div>\n        ))}\n      </Card>\n    );\n  }\n\n  if (!notes || notes.length === 0) {\n    return (\n      <Card className=\"p-6 text-center\">\n        <p className=\"text-neutral-dark mb-4\">No session notes found</p>\n        <Link to=\"/notes\">\n          <Button>Create New Note</Button>\n        </Link>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"shadow overflow-hidden rounded-lg divide-y divide-gray-200\">\n      {notes.map((note) => {\n        // Filter notes by client if clientId is provided\n        if (clientId && note.clientId !== clientId) {\n          return null;\n        }\n        \n        return (\n          <div key={note.id} className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h4 className=\"text-sm font-medium text-neutral-dark\">\n                  Client: <span>{note.clientName}</span>\n                </h4>\n                <p className=\"text-xs text-gray-500\">\n                  {formatDate(note.sessionDate || note.createdAt)} • {note.duration} min session\n                </p>\n              </div>\n              <div>\n                <button className=\"text-sm font-medium text-primary hover:text-primary-dark\">\n                  View full note\n                </button>\n              </div>\n            </div>\n            \n            {note.aiProcessed || note.analyzed ? (\n              <>\n                <div className=\"mb-4\">\n                  <h5 className=\"text-xs uppercase font-medium text-gray-500 mb-3\">AI Summary</h5>\n                  <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg\">\n                    <div className=\"text-sm text-gray-800 leading-relaxed\">\n                      {note.summary ? (\n                        <div className=\"space-y-2\">\n                          {note.summary.split('\\n').filter(line => line.trim()).map((paragraph, index) => (\n                            <p key={index} className=\"mb-2 last:mb-0\">\n                              {paragraph.trim()}\n                            </p>\n                          ))}\n                        </div>\n                      ) : (\n                        <p className=\"text-gray-500 italic\">No summary available</p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Themes are now stored separately in clientThemes table */}\n                \n                {note.recommendations && note.recommendations.length > 0 && (\n                  <div>\n                    <h5 className=\"text-xs uppercase font-medium text-gray-500 mb-2\">\n                      AI Recommendations\n                    </h5>\n                    <ul className=\"text-sm text-neutral-dark space-y-1\">\n                      {note.recommendations.map((recommendation, index) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"material-icons text-accent mr-2 text-sm\">lightbulb</span>\n                          <span>{recommendation}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </>\n            ) : (\n              <div className=\"py-4 text-center\">\n                <p className=\"text-sm text-neutral-dark mb-2\">\n                  Note is currently being processed by AI\n                </p>\n                <div className=\"flex justify-center items-center text-primary\">\n                  <span className=\"material-icons animate-spin mr-2\">progress_activity</span>\n                  <span className=\"text-sm\">Processing...</span>\n                </div>\n              </div>\n            )}\n            \n            {showFullDetails && (\n              <Accordion type=\"single\" collapsible className=\"mt-4 w-full\">\n                <AccordionItem value=\"raw-notes\">\n                  <AccordionTrigger className=\"text-xs uppercase font-medium text-gray-500\">\n                    Original Notes\n                  </AccordionTrigger>\n                  <AccordionContent>\n                    <p className=\"text-sm text-neutral-dark whitespace-pre-line\">\n                      {note.content}\n                    </p>\n                  </AccordionContent>\n                </AccordionItem>\n              </Accordion>\n            )}\n          </div>\n        );\n      })}\n    </Card>\n  );\n}\n", "modifiedCode": "import { Skeleton } from \"@/components/ui/skeleton\";\nimport { SessionNote, Theme } from \"@shared/schema\";\nimport { formatDate, getThemeColor, formatTrendIndicator } from \"@/lib/utils\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { Card } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Link } from \"wouter\";\n\ninterface SessionNotesListProps {\n  notes?: (SessionNote & { clientName?: string })[];\n  isLoading: boolean;\n  clientId?: number;\n  showFullDetails?: boolean;\n}\n\nexport default function SessionNotesList({ \n  notes, \n  isLoading, \n  clientId,\n  showFullDetails = false \n}: SessionNotesListProps) {\n  if (isLoading) {\n    return (\n      <Card className=\"shadow overflow-hidden rounded-lg divide-y divide-gray-200\">\n        {[1, 2].map((i) => (\n          <div key={i} className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <Skeleton className=\"h-4 w-40 mb-2\" />\n                <Skeleton className=\"h-3 w-32\" />\n              </div>\n              <Skeleton className=\"h-8 w-24\" />\n            </div>\n            \n            <div className=\"mb-4\">\n              <Skeleton className=\"h-3 w-24 mb-2\" />\n              <Skeleton className=\"h-20 w-full\" />\n            </div>\n            \n            <div className=\"mb-4\">\n              <Skeleton className=\"h-3 w-24 mb-2\" />\n              <div className=\"flex flex-wrap gap-1\">\n                <Skeleton className=\"h-6 w-20\" />\n                <Skeleton className=\"h-6 w-24\" />\n                <Skeleton className=\"h-6 w-16\" />\n              </div>\n            </div>\n            \n            <div>\n              <Skeleton className=\"h-3 w-40 mb-2\" />\n              <ul className=\"space-y-1\">\n                <li className=\"flex items-start\">\n                  <Skeleton className=\"h-5 w-5 mr-2\" />\n                  <Skeleton className=\"h-4 w-full\" />\n                </li>\n                <li className=\"flex items-start\">\n                  <Skeleton className=\"h-5 w-5 mr-2\" />\n                  <Skeleton className=\"h-4 w-full\" />\n                </li>\n              </ul>\n            </div>\n          </div>\n        ))}\n      </Card>\n    );\n  }\n\n  if (!notes || notes.length === 0) {\n    return (\n      <Card className=\"p-6 text-center\">\n        <p className=\"text-neutral-dark mb-4\">No session notes found</p>\n        <Link to=\"/notes\">\n          <Button>Create New Note</Button>\n        </Link>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"shadow overflow-hidden rounded-lg divide-y divide-gray-200\">\n      {notes.map((note) => {\n        // Filter notes by client if clientId is provided\n        if (clientId && note.clientId !== clientId) {\n          return null;\n        }\n        \n        return (\n          <div key={note.id} className=\"px-4 py-5 sm:p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h4 className=\"text-sm font-medium text-neutral-dark\">\n                  Client: <span>{note.clientName}</span>\n                </h4>\n                <p className=\"text-xs text-gray-500\">\n                  {formatDate(note.sessionDate || note.createdAt)} • {note.duration} min session\n                </p>\n              </div>\n              <div>\n                <button className=\"text-sm font-medium text-primary hover:text-primary-dark\">\n                  View full note\n                </button>\n              </div>\n            </div>\n            \n            {note.aiProcessed || note.analyzed ? (\n              <>\n                <div className=\"mb-4\">\n                  <h5 className=\"text-xs uppercase font-medium text-gray-500 mb-3\">AI Summary</h5>\n                  <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg\">\n                    <div className=\"text-sm text-gray-800 leading-relaxed\">\n                      {note.summary ? (\n                        <div className=\"space-y-2\">\n                          {note.summary.split('\\n').filter(line => line.trim()).map((paragraph, index) => (\n                            <p key={index} className=\"mb-2 last:mb-0\">\n                              {paragraph.trim()}\n                            </p>\n                          ))}\n                        </div>\n                      ) : (\n                        <p className=\"text-gray-500 italic\">No summary available</p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Themes are now stored separately in clientThemes table */}\n                \n                {note.recommendations && note.recommendations.length > 0 && (\n                  <div>\n                    <h5 className=\"text-xs uppercase font-medium text-gray-500 mb-3\">\n                      AI Recommendations\n                    </h5>\n                    <div className=\"bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg\">\n                      <ul className=\"space-y-3\">\n                        {note.recommendations.map((recommendation, index) => (\n                          <li key={index} className=\"flex items-start\">\n                            <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5\">\n                              <svg className=\"w-3 h-3 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                                <path fillRule=\"evenodd\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" clipRule=\"evenodd\" />\n                              </svg>\n                            </div>\n                            <span className=\"text-sm text-gray-800 leading-relaxed\">{recommendation}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                )}\n              </>\n            ) : (\n              <div className=\"py-4 text-center\">\n                <p className=\"text-sm text-neutral-dark mb-2\">\n                  Note is currently being processed by AI\n                </p>\n                <div className=\"flex justify-center items-center text-primary\">\n                  <span className=\"material-icons animate-spin mr-2\">progress_activity</span>\n                  <span className=\"text-sm\">Processing...</span>\n                </div>\n              </div>\n            )}\n            \n            {showFullDetails && (\n              <Accordion type=\"single\" collapsible className=\"mt-4 w-full\">\n                <AccordionItem value=\"raw-notes\">\n                  <AccordionTrigger className=\"text-xs uppercase font-medium text-gray-500\">\n                    Original Notes\n                  </AccordionTrigger>\n                  <AccordionContent>\n                    <p className=\"text-sm text-neutral-dark whitespace-pre-line\">\n                      {note.content}\n                    </p>\n                  </AccordionContent>\n                </AccordionItem>\n              </Accordion>\n            )}\n          </div>\n        );\n      })}\n    </Card>\n  );\n}\n"}