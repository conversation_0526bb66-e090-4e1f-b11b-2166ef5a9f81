{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RealTimeVoiceChat.tsx"}, "originalCode": "import React, { useState, useRef, useEffect } from 'react';\nimport { useToast } from '@/hooks/use-toast';\nimport { Button } from '@/components/ui/button';\nimport { Mi<PERSON>, MicO<PERSON>, Bo<PERSON>, Loader2 } from 'lucide-react';\nimport { Card } from '@/components/ui/card';\nimport { debugManager } from '@/lib/debug-config';\n\ninterface RealTimeVoiceChatProps {\n  userId: number;\n  clientId?: number;\n  onConversationCreated?: (conversationId: number) => void;\n  onConversationEnded?: (summary: string, conversationId: number) => void;\n}\n\nconst RealTimeVoiceChat: React.FC<RealTimeVoiceChatProps> = ({ \n  userId, \n  clientId = 1,\n  onConversationCreated,\n  onConversationEnded \n}) => {\n  const { toast } = useToast();\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [messages, setMessages] = useState<{\n    role: 'user' | 'assistant';\n    content: string;\n    isStreaming?: boolean;\n  }[]>([]);\n  const [conversationId, setConversationId] = useState<number | null>(null);\n  const [wsStatus, setWsStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');\n  \n  const wsRef = useRef<WebSocket | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const micStreamRef = useRef<MediaStream | null>(null);\n  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);\n  const audioQueueRef = useRef<Float32Array[]>([]);\n  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Initialize WebSocket connection\n  useEffect(() => {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    \n    setWsStatus('connecting');\n    const ws = new WebSocket(wsUrl);\n    wsRef.current = ws;\n    \n    ws.onopen = () => {\n      setWsStatus('connected');\n      // Start the conversation\n      ws.send(JSON.stringify({\n        type: 'start',\n        userId,\n        clientId\n      }));\n    };\n    \n    ws.onclose = () => {\n      setWsStatus('disconnected');\n    };\n    \n    ws.onerror = () => {\n      setWsStatus('error');\n      toast({\n        title: \"Connection Error\",\n        description: \"Failed to connect to therapy server\",\n        variant: \"destructive\"\n      });\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        \n        if (data.type === 'ready') {\n          setConversationId(data.conversationId);\n          if (onConversationCreated) {\n            onConversationCreated(data.conversationId);\n          }\n          \n          // Add welcome message\n          setMessages([\n            {\n              role: 'assistant',\n              content: \"Hello, I'm your AI therapist. How can I help you today?\",\n              isStreaming: false\n            }\n          ]);\n        }\n        else if (data.type === 'stream') {\n          // Update the last assistant message with streaming content\n          setMessages(prev => {\n            const newMessages = [...prev];\n            const lastMessage = newMessages[newMessages.length - 1];\n            if (lastMessage.role === 'assistant') {\n              lastMessage.content = data.content;\n              lastMessage.isStreaming = true;\n            } else {\n              newMessages.push({\n                role: 'assistant',\n                content: data.content,\n                isStreaming: true\n              });\n            }\n            return newMessages;\n          });\n        }\n        else if (data.type === 'complete') {\n          // Mark the last assistant message as complete\n          setMessages(prev => {\n            const newMessages = [...prev];\n            const lastMessage = newMessages[newMessages.length - 1];\n            if (lastMessage.role === 'assistant') {\n              lastMessage.isStreaming = false;\n            }\n            return newMessages;\n          });\n        }\n        else if (data.type === 'summary') {\n          // Handle session summary\n          if (onConversationEnded && conversationId) {\n            onConversationEnded(data.content, conversationId);\n          }\n        }\n        else if (data.type === 'error') {\n          toast({\n            title: \"Error\",\n            description: data.message,\n            variant: \"destructive\"\n          });\n          setIsProcessing(false);\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    \n    return () => {\n      if (ws.readyState === WebSocket.OPEN) {\n        ws.close();\n      }\n    };\n  }, [userId, clientId, toast, onConversationCreated]);\n  \n  // Start real-time voice processing\n  const startRecording = async () => {\n    try {\n      // Request microphone access\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      micStreamRef.current = stream;\n      \n      // Create audio context\n      const audioContext = new AudioContext();\n      audioContextRef.current = audioContext;\n      \n      // Create audio source from microphone\n      const source = audioContext.createMediaStreamSource(stream);\n      \n      // Create script processor for real-time audio processing\n      const processor = audioContext.createScriptProcessor(4096, 1, 1);\n      audioProcessorRef.current = processor;\n      \n      // Process audio in real-time\n      processor.onaudioprocess = (e) => {\n        const inputData = e.inputBuffer.getChannelData(0);\n        audioQueueRef.current.push(new Float32Array(inputData));\n        \n        // Process audio queue every 100ms\n        if (!processingTimeoutRef.current) {\n          processingTimeoutRef.current = setTimeout(() => {\n            if (audioQueueRef.current.length > 0 && wsRef.current?.readyState === WebSocket.OPEN) {\n              // Convert audio data to base64 and send\n              const audioData = audioQueueRef.current;\n              audioQueueRef.current = [];\n              \n              // Send audio data to server for real-time processing\n              wsRef.current.send(JSON.stringify({\n                type: 'audio',\n                data: audioData\n              }));\n            }\n            processingTimeoutRef.current = null;\n          }, 100);\n        }\n      };\n      \n      // Connect processor to audio context\n      source.connect(processor);\n      processor.connect(audioContext.destination);\n      \n      setIsRecording(true);\n      toast({\n        title: \"Recording started\",\n        description: \"Speak naturally - the AI will respond in real-time\"\n      });\n    } catch (error) {\n      console.error('Error accessing microphone:', error);\n      toast({\n        title: \"Microphone Error\",\n        description: \"Could not access your microphone. Please check permissions.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n  \n  // Stop recording\n  const stopRecording = () => {\n    if (audioProcessorRef.current) {\n      audioProcessorRef.current.disconnect();\n      audioProcessorRef.current = null;\n    }\n    \n    if (micStreamRef.current) {\n      micStreamRef.current.getTracks().forEach(track => track.stop());\n      micStreamRef.current = null;\n    }\n    \n    if (audioContextRef.current) {\n      audioContextRef.current.close();\n      audioContextRef.current = null;\n    }\n    \n    if (processingTimeoutRef.current) {\n      clearTimeout(processingTimeoutRef.current);\n      processingTimeoutRef.current = null;\n    }\n    \n    setIsRecording(false);\n    toast({\n      title: \"Recording stopped\",\n      description: \"The conversation will continue in real-time\"\n    });\n  };\n\n  // End session and request summary\n  const endSession = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      // First end the session\n      wsRef.current.send(JSON.stringify({\n        type: \"conversationEnded\"\n      }));\n\n      // Stop recording\n      stopRecording();\n\n      // Request summary after a brief delay\n      setTimeout(() => {\n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify({\n            type: \"summary_request\"\n          }));\n        }\n      }, 500);\n\n      toast({\n        title: \"Session ended\",\n        description: \"Generating session summary...\",\n      });\n    }\n  };\n  \n  return (\n    <Card className=\"w-full max-w-3xl mx-auto\">\n      {/* Chat messages */}\n      <div className=\"h-[500px] overflow-y-auto p-4\">\n        {messages.map((message, index) => (\n          <div\n            key={index}\n            className={`mb-4 ${\n              message.role === \"user\" ? \"text-right\" : \"text-left\"\n            }`}\n          >\n            <div\n              className={`inline-block p-3 rounded-lg ${\n                message.role === \"user\"\n                  ? \"bg-blue-500 text-white\"\n                  : \"bg-gray-100 text-gray-800\"\n              }`}\n            >\n              {message.content}\n              {message.isStreaming && (\n                <span className=\"inline-block w-1 h-4 ml-1 bg-current animate-pulse\"></span>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {/* Controls */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <span\n              className={`h-2 w-2 rounded-full ${\n                wsStatus === 'connected'\n                  ? 'bg-green-500'\n                  : wsStatus === 'connecting'\n                  ? 'bg-yellow-500'\n                  : 'bg-red-500'\n              }`}\n            ></span>\n            <span className=\"text-sm text-gray-500\">\n              {wsStatus === 'connected' ? 'Connected' : wsStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}\n            </span>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button\n              onClick={isRecording ? stopRecording : startRecording}\n              className={`${\n                isRecording\n                  ? \"bg-red-500 hover:bg-red-600\"\n                  : \"bg-blue-500 hover:bg-blue-600\"\n              }`}\n              disabled={wsStatus !== 'connected'}\n            >\n              {isRecording ? (\n                <>\n                  <MicOff className=\"h-4 w-4 mr-2\" />\n                  Stop Recording\n                </>\n              ) : (\n                <>\n                  <Mic className=\"h-4 w-4 mr-2\" />\n                  Start Recording\n                </>\n              )}\n            </Button>\n\n            <Button\n              onClick={endSession}\n              variant=\"outline\"\n              disabled={wsStatus !== 'connected'}\n            >\n              End Session\n            </Button>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default RealTimeVoiceChat; ", "modifiedCode": "import React, { useState, useRef, useEffect } from 'react';\nimport { useToast } from '@/hooks/use-toast';\nimport { Button } from '@/components/ui/button';\nimport { Mi<PERSON>, MicO<PERSON>, Bo<PERSON>, Loader2 } from 'lucide-react';\nimport { Card } from '@/components/ui/card';\nimport { debugManager } from '@/lib/debug-config';\n\ninterface RealTimeVoiceChatProps {\n  userId: number;\n  clientId?: number;\n  onConversationCreated?: (conversationId: number) => void;\n  onConversationEnded?: (summary: string, conversationId: number) => void;\n}\n\nconst RealTimeVoiceChat: React.FC<RealTimeVoiceChatProps> = ({ \n  userId, \n  clientId = 1,\n  onConversationCreated,\n  onConversationEnded \n}) => {\n  const { toast } = useToast();\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [messages, setMessages] = useState<{\n    role: 'user' | 'assistant';\n    content: string;\n    isStreaming?: boolean;\n  }[]>([]);\n  const [conversationId, setConversationId] = useState<number | null>(null);\n  const [wsStatus, setWsStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');\n  \n  const wsRef = useRef<WebSocket | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const micStreamRef = useRef<MediaStream | null>(null);\n  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);\n  const audioQueueRef = useRef<Float32Array[]>([]);\n  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Initialize WebSocket connection\n  useEffect(() => {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    \n    setWsStatus('connecting');\n    const ws = new WebSocket(wsUrl);\n    wsRef.current = ws;\n    \n    ws.onopen = () => {\n      setWsStatus('connected');\n      // Start the conversation\n      ws.send(JSON.stringify({\n        type: 'start',\n        userId,\n        clientId\n      }));\n    };\n    \n    ws.onclose = () => {\n      setWsStatus('disconnected');\n    };\n    \n    ws.onerror = () => {\n      setWsStatus('error');\n      toast({\n        title: \"Connection Error\",\n        description: \"Failed to connect to therapy server\",\n        variant: \"destructive\"\n      });\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        \n        if (data.type === 'ready') {\n          setConversationId(data.conversationId);\n          if (onConversationCreated) {\n            onConversationCreated(data.conversationId);\n          }\n          \n          // Add welcome message\n          setMessages([\n            {\n              role: 'assistant',\n              content: \"Hello, I'm your AI therapist. How can I help you today?\",\n              isStreaming: false\n            }\n          ]);\n        }\n        else if (data.type === 'stream') {\n          // Update the last assistant message with streaming content\n          setMessages(prev => {\n            const newMessages = [...prev];\n            const lastMessage = newMessages[newMessages.length - 1];\n            if (lastMessage.role === 'assistant') {\n              lastMessage.content = data.content;\n              lastMessage.isStreaming = true;\n            } else {\n              newMessages.push({\n                role: 'assistant',\n                content: data.content,\n                isStreaming: true\n              });\n            }\n            return newMessages;\n          });\n        }\n        else if (data.type === 'complete') {\n          // Mark the last assistant message as complete\n          setMessages(prev => {\n            const newMessages = [...prev];\n            const lastMessage = newMessages[newMessages.length - 1];\n            if (lastMessage.role === 'assistant') {\n              lastMessage.isStreaming = false;\n            }\n            return newMessages;\n          });\n        }\n        else if (data.type === 'summary') {\n          // Handle session summary\n          if (onConversationEnded && conversationId) {\n            onConversationEnded(data.content, conversationId);\n          }\n        }\n        else if (data.type === 'error') {\n          toast({\n            title: \"Error\",\n            description: data.message,\n            variant: \"destructive\"\n          });\n          setIsProcessing(false);\n        }\n      } catch (error) {\n        debugManager.error('websocketEvents', 'Error parsing WebSocket message:', error);\n      }\n    };\n    \n    return () => {\n      if (ws.readyState === WebSocket.OPEN) {\n        ws.close();\n      }\n    };\n  }, [userId, clientId, toast, onConversationCreated]);\n  \n  // Start real-time voice processing\n  const startRecording = async () => {\n    try {\n      // Request microphone access\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      micStreamRef.current = stream;\n      \n      // Create audio context\n      const audioContext = new AudioContext();\n      audioContextRef.current = audioContext;\n      \n      // Create audio source from microphone\n      const source = audioContext.createMediaStreamSource(stream);\n      \n      // Create script processor for real-time audio processing\n      const processor = audioContext.createScriptProcessor(4096, 1, 1);\n      audioProcessorRef.current = processor;\n      \n      // Process audio in real-time\n      processor.onaudioprocess = (e) => {\n        const inputData = e.inputBuffer.getChannelData(0);\n        audioQueueRef.current.push(new Float32Array(inputData));\n        \n        // Process audio queue every 100ms\n        if (!processingTimeoutRef.current) {\n          processingTimeoutRef.current = setTimeout(() => {\n            if (audioQueueRef.current.length > 0 && wsRef.current?.readyState === WebSocket.OPEN) {\n              // Convert audio data to base64 and send\n              const audioData = audioQueueRef.current;\n              audioQueueRef.current = [];\n              \n              // Send audio data to server for real-time processing\n              wsRef.current.send(JSON.stringify({\n                type: 'audio',\n                data: audioData\n              }));\n            }\n            processingTimeoutRef.current = null;\n          }, 100);\n        }\n      };\n      \n      // Connect processor to audio context\n      source.connect(processor);\n      processor.connect(audioContext.destination);\n      \n      setIsRecording(true);\n      toast({\n        title: \"Recording started\",\n        description: \"Speak naturally - the AI will respond in real-time\"\n      });\n    } catch (error) {\n      console.error('Error accessing microphone:', error);\n      toast({\n        title: \"Microphone Error\",\n        description: \"Could not access your microphone. Please check permissions.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n  \n  // Stop recording\n  const stopRecording = () => {\n    if (audioProcessorRef.current) {\n      audioProcessorRef.current.disconnect();\n      audioProcessorRef.current = null;\n    }\n    \n    if (micStreamRef.current) {\n      micStreamRef.current.getTracks().forEach(track => track.stop());\n      micStreamRef.current = null;\n    }\n    \n    if (audioContextRef.current) {\n      audioContextRef.current.close();\n      audioContextRef.current = null;\n    }\n    \n    if (processingTimeoutRef.current) {\n      clearTimeout(processingTimeoutRef.current);\n      processingTimeoutRef.current = null;\n    }\n    \n    setIsRecording(false);\n    toast({\n      title: \"Recording stopped\",\n      description: \"The conversation will continue in real-time\"\n    });\n  };\n\n  // End session and request summary\n  const endSession = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      // First end the session\n      wsRef.current.send(JSON.stringify({\n        type: \"conversationEnded\"\n      }));\n\n      // Stop recording\n      stopRecording();\n\n      // Request summary after a brief delay\n      setTimeout(() => {\n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify({\n            type: \"summary_request\"\n          }));\n        }\n      }, 500);\n\n      toast({\n        title: \"Session ended\",\n        description: \"Generating session summary...\",\n      });\n    }\n  };\n  \n  return (\n    <Card className=\"w-full max-w-3xl mx-auto\">\n      {/* Chat messages */}\n      <div className=\"h-[500px] overflow-y-auto p-4\">\n        {messages.map((message, index) => (\n          <div\n            key={index}\n            className={`mb-4 ${\n              message.role === \"user\" ? \"text-right\" : \"text-left\"\n            }`}\n          >\n            <div\n              className={`inline-block p-3 rounded-lg ${\n                message.role === \"user\"\n                  ? \"bg-blue-500 text-white\"\n                  : \"bg-gray-100 text-gray-800\"\n              }`}\n            >\n              {message.content}\n              {message.isStreaming && (\n                <span className=\"inline-block w-1 h-4 ml-1 bg-current animate-pulse\"></span>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {/* Controls */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <span\n              className={`h-2 w-2 rounded-full ${\n                wsStatus === 'connected'\n                  ? 'bg-green-500'\n                  : wsStatus === 'connecting'\n                  ? 'bg-yellow-500'\n                  : 'bg-red-500'\n              }`}\n            ></span>\n            <span className=\"text-sm text-gray-500\">\n              {wsStatus === 'connected' ? 'Connected' : wsStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}\n            </span>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button\n              onClick={isRecording ? stopRecording : startRecording}\n              className={`${\n                isRecording\n                  ? \"bg-red-500 hover:bg-red-600\"\n                  : \"bg-blue-500 hover:bg-blue-600\"\n              }`}\n              disabled={wsStatus !== 'connected'}\n            >\n              {isRecording ? (\n                <>\n                  <MicOff className=\"h-4 w-4 mr-2\" />\n                  Stop Recording\n                </>\n              ) : (\n                <>\n                  <Mic className=\"h-4 w-4 mr-2\" />\n                  Start Recording\n                </>\n              )}\n            </Button>\n\n            <Button\n              onClick={endSession}\n              variant=\"outline\"\n              disabled={wsStatus !== 'connected'}\n            >\n              End Session\n            </Button>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default RealTimeVoiceChat; "}