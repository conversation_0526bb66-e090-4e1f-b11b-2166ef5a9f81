{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-microphone-browser.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Microphone Test for Real-Time Voice</title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n            background-color: #f5f5f5;\n        }\n        .container {\n            background: white;\n            padding: 20px;\n            border-radius: 10px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .status {\n            padding: 10px;\n            margin: 10px 0;\n            border-radius: 5px;\n            font-weight: bold;\n        }\n        .success { background-color: #d4edda; color: #155724; }\n        .error { background-color: #f8d7da; color: #721c24; }\n        .warning { background-color: #fff3cd; color: #856404; }\n        .info { background-color: #d1ecf1; color: #0c5460; }\n        button {\n            background-color: #007bff;\n            color: white;\n            border: none;\n            padding: 10px 20px;\n            border-radius: 5px;\n            cursor: pointer;\n            margin: 5px;\n        }\n        button:hover { background-color: #0056b3; }\n        button:disabled { background-color: #6c757d; cursor: not-allowed; }\n        .level-meter {\n            width: 100%;\n            height: 20px;\n            background-color: #e9ecef;\n            border-radius: 10px;\n            overflow: hidden;\n            margin: 10px 0;\n        }\n        .level-bar {\n            height: 100%;\n            background: linear-gradient(to right, #28a745, #ffc107, #dc3545);\n            width: 0%;\n            transition: width 0.1s ease;\n        }\n        #log {\n            background-color: #f8f9fa;\n            border: 1px solid #dee2e6;\n            border-radius: 5px;\n            padding: 10px;\n            height: 300px;\n            overflow-y: auto;\n            font-family: monospace;\n            font-size: 12px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>🎤 Real-Time Voice Conversation Test</h1>\n        <p>This test will verify that your microphone works correctly with the real-time voice system.</p>\n        \n        <div id=\"status-container\">\n            <div id=\"browser-support\" class=\"status info\">Checking browser support...</div>\n            <div id=\"mic-permission\" class=\"status warning\">Microphone permission not requested</div>\n            <div id=\"audio-context\" class=\"status warning\">Audio context not initialized</div>\n            <div id=\"websocket-status\" class=\"status warning\">WebSocket not connected</div>\n        </div>\n        \n        <div>\n            <button id=\"test-mic\" onclick=\"testMicrophone()\">Test Microphone</button>\n            <button id=\"test-websocket\" onclick=\"testWebSocket()\">Test WebSocket</button>\n            <button id=\"test-full-flow\" onclick=\"testFullFlow()\" disabled>Test Full Voice Flow</button>\n        </div>\n        \n        <div>\n            <h3>Microphone Level</h3>\n            <div class=\"level-meter\">\n                <div id=\"level-bar\" class=\"level-bar\"></div>\n            </div>\n            <span id=\"level-text\">0%</span>\n        </div>\n        \n        <div>\n            <h3>Test Log</h3>\n            <div id=\"log\"></div>\n        </div>\n    </div>\n\n    <script>\n        let audioContext = null;\n        let mediaStream = null;\n        let analyzer = null;\n        let processor = null;\n        let websocket = null;\n        let isRecording = false;\n\n        function log(message) {\n            const logDiv = document.getElementById('log');\n            const timestamp = new Date().toLocaleTimeString();\n            logDiv.innerHTML += `[${timestamp}] ${message}\\n`;\n            logDiv.scrollTop = logDiv.scrollHeight;\n            console.log(message);\n        }\n\n        function updateStatus(elementId, status, message) {\n            const element = document.getElementById(elementId);\n            element.className = `status ${status}`;\n            element.textContent = message;\n        }\n\n        // Check browser support\n        function checkBrowserSupport() {\n            log('🔍 Checking browser support...');\n            \n            const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n            const hasAudioContext = !!(window.AudioContext || window.webkitAudioContext);\n            const hasWebSocket = !!window.WebSocket;\n            \n            if (hasGetUserMedia && hasAudioContext && hasWebSocket) {\n                updateStatus('browser-support', 'success', '✅ Browser fully supported');\n                log('✅ Browser support: getUserMedia, AudioContext, and WebSocket available');\n                return true;\n            } else {\n                updateStatus('browser-support', 'error', '❌ Browser not supported');\n                log(`❌ Browser support missing: getUserMedia=${hasGetUserMedia}, AudioContext=${hasAudioContext}, WebSocket=${hasWebSocket}`);\n                return false;\n            }\n        }\n\n        // Test microphone access and audio processing\n        async function testMicrophone() {\n            try {\n                log('🎤 Testing microphone access...');\n                \n                // Request microphone permission\n                mediaStream = await navigator.mediaDevices.getUserMedia({\n                    audio: {\n                        echoCancellation: true,\n                        noiseSuppression: false,\n                        autoGainControl: true,\n                        sampleRate: { ideal: 24000, min: 16000, max: 48000 },\n                        channelCount: 1\n                    }\n                });\n                \n                updateStatus('mic-permission', 'success', '✅ Microphone access granted');\n                log('✅ Microphone access granted');\n                \n                // Create audio context\n                const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                audioContext = new AudioContextClass({ sampleRate: 24000 });\n                \n                if (audioContext.state === 'suspended') {\n                    await audioContext.resume();\n                }\n                \n                updateStatus('audio-context', 'success', `✅ Audio context ready (${audioContext.sampleRate}Hz)`);\n                log(`✅ Audio context created: ${audioContext.sampleRate}Hz, state: ${audioContext.state}`);\n                \n                // Set up audio processing\n                const source = audioContext.createMediaStreamSource(mediaStream);\n                analyzer = audioContext.createAnalyser();\n                analyzer.fftSize = 256;\n                source.connect(analyzer);\n                \n                processor = audioContext.createScriptProcessor(4096, 1, 1);\n                let frameCount = 0;\n                \n                processor.onaudioprocess = (e) => {\n                    frameCount++;\n                    \n                    // Update visual level meter\n                    const bufferLength = analyzer.frequencyBinCount;\n                    const dataArray = new Uint8Array(bufferLength);\n                    analyzer.getByteFrequencyData(dataArray);\n                    \n                    let sum = 0;\n                    for (let i = 0; i < bufferLength; i++) {\n                        sum += dataArray[i];\n                    }\n                    const level = (sum / bufferLength / 255) * 100;\n                    \n                    document.getElementById('level-bar').style.width = `${Math.min(100, level * 3)}%`;\n                    document.getElementById('level-text').textContent = `${level.toFixed(1)}%`;\n                    \n                    // Log first few frames\n                    if (frameCount <= 3) {\n                        log(`🔄 Audio processor frame ${frameCount}: level=${level.toFixed(2)}%`);\n                    }\n                    \n                    // Test audio data conversion\n                    if (frameCount === 5) {\n                        const inputData = e.inputBuffer.getChannelData(0);\n                        const int16Data = new Int16Array(inputData.length);\n                        \n                        for (let i = 0; i < inputData.length; i++) {\n                            let sample = inputData[i] * 2.0; // Gain\n                            sample = Math.max(-1, Math.min(1, sample));\n                            int16Data[i] = Math.round(sample * 32767);\n                        }\n                        \n                        const buffer = new ArrayBuffer(int16Data.length * 2);\n                        const view = new DataView(buffer);\n                        for (let i = 0; i < int16Data.length; i++) {\n                            view.setInt16(i * 2, int16Data[i], true);\n                        }\n                        const uint8Array = new Uint8Array(buffer);\n                        const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n                        \n                        log(`✅ Audio conversion test: ${base64Audio.length} chars of base64 audio data`);\n                    }\n                };\n                \n                source.connect(processor);\n                // Don't connect to destination to avoid feedback\n                \n                log('✅ Audio processing pipeline established');\n                isRecording = true;\n                \n                // Enable full flow test\n                document.getElementById('test-full-flow').disabled = false;\n                \n            } catch (error) {\n                updateStatus('mic-permission', 'error', '❌ Microphone access failed');\n                updateStatus('audio-context', 'error', '❌ Audio context failed');\n                log(`❌ Microphone test failed: ${error.message}`);\n            }\n        }\n\n        // Test WebSocket connection\n        function testWebSocket() {\n            try {\n                log('🌐 Testing WebSocket connection...');\n                \n                websocket = new WebSocket('ws://localhost:5000/ws');\n                \n                websocket.onopen = () => {\n                    updateStatus('websocket-status', 'success', '✅ WebSocket connected');\n                    log('✅ WebSocket connected to server');\n                    \n                    // Send test message\n                    websocket.send(JSON.stringify({\n                        type: 'ping',\n                        timestamp: new Date().toISOString()\n                    }));\n                };\n                \n                websocket.onmessage = (event) => {\n                    const data = JSON.parse(event.data);\n                    log(`📨 WebSocket message: ${data.type}`);\n                };\n                \n                websocket.onclose = () => {\n                    updateStatus('websocket-status', 'warning', '⚠️ WebSocket disconnected');\n                    log('⚠️ WebSocket connection closed');\n                };\n                \n                websocket.onerror = (error) => {\n                    updateStatus('websocket-status', 'error', '❌ WebSocket connection failed');\n                    log(`❌ WebSocket error: ${error}`);\n                };\n                \n            } catch (error) {\n                updateStatus('websocket-status', 'error', '❌ WebSocket test failed');\n                log(`❌ WebSocket test failed: ${error.message}`);\n            }\n        }\n\n        // Test full voice flow\n        function testFullFlow() {\n            if (!isRecording || !websocket || websocket.readyState !== WebSocket.OPEN) {\n                log('❌ Cannot test full flow: microphone or WebSocket not ready');\n                return;\n            }\n            \n            log('🚀 Starting full voice flow test...');\n            \n            // Send start message\n            websocket.send(JSON.stringify({\n                type: \"start\",\n                userId: \"test-user\",\n                clientId: \"test-client\",\n                useRealtimeAPI: true,\n                mode: \"realtime\",\n                behavior: {\n                    model: \"gpt-4o-realtime-preview-2024-10-01\",\n                    temperature: 0.7,\n                    voice: { voice: \"shimmer\", speed: 1.0 }\n                },\n                instructions: \"You are a test assistant. Respond briefly to test the voice system.\"\n            }));\n            \n            log('📤 Sent session start message');\n            \n            // Set up message handling for the test\n            websocket.onmessage = (event) => {\n                const data = JSON.parse(event.data);\n                \n                switch (data.type) {\n                    case 'ready':\n                        log('✅ Session ready - voice conversation active');\n                        break;\n                    case 'session.created':\n                        log(`✅ OpenAI session created: ${data.session?.id}`);\n                        break;\n                    case 'session.updated':\n                        log(`✅ Session configured with VAD: ${data.session?.turn_detection?.type}`);\n                        break;\n                    case 'input_audio_buffer.speech_started':\n                        log('🎤 Speech detection started');\n                        break;\n                    case 'input_audio_buffer.speech_stopped':\n                        log('🔇 Speech detection stopped');\n                        break;\n                    case 'conversation.item.input_audio_transcription.completed':\n                        log(`📝 User transcribed: \"${data.transcript}\"`);\n                        break;\n                    case 'response.audio_transcript.done':\n                        log(`🤖 AI response: \"${data.transcript}\"`);\n                        break;\n                    case 'response.audio.delta':\n                        log(`🔊 AI audio chunk: ${data.delta?.length || 0} chars`);\n                        break;\n                    default:\n                        // Log other important events\n                        if (['response.created', 'response.done'].includes(data.type)) {\n                            log(`📋 ${data.type}`);\n                        }\n                }\n            };\n        }\n\n        // Initialize on page load\n        window.onload = () => {\n            checkBrowserSupport();\n        };\n    </script>\n</body>\n</html>\n", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Microphone Test for Real-Time Voice</title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            max-width: 800px;\n            margin: 0 auto;\n            padding: 20px;\n            background-color: #f5f5f5;\n        }\n        .container {\n            background: white;\n            padding: 20px;\n            border-radius: 10px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .status {\n            padding: 10px;\n            margin: 10px 0;\n            border-radius: 5px;\n            font-weight: bold;\n        }\n        .success { background-color: #d4edda; color: #155724; }\n        .error { background-color: #f8d7da; color: #721c24; }\n        .warning { background-color: #fff3cd; color: #856404; }\n        .info { background-color: #d1ecf1; color: #0c5460; }\n        button {\n            background-color: #007bff;\n            color: white;\n            border: none;\n            padding: 10px 20px;\n            border-radius: 5px;\n            cursor: pointer;\n            margin: 5px;\n        }\n        button:hover { background-color: #0056b3; }\n        button:disabled { background-color: #6c757d; cursor: not-allowed; }\n        .level-meter {\n            width: 100%;\n            height: 20px;\n            background-color: #e9ecef;\n            border-radius: 10px;\n            overflow: hidden;\n            margin: 10px 0;\n        }\n        .level-bar {\n            height: 100%;\n            background: linear-gradient(to right, #28a745, #ffc107, #dc3545);\n            width: 0%;\n            transition: width 0.1s ease;\n        }\n        #log {\n            background-color: #f8f9fa;\n            border: 1px solid #dee2e6;\n            border-radius: 5px;\n            padding: 10px;\n            height: 300px;\n            overflow-y: auto;\n            font-family: monospace;\n            font-size: 12px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>🎤 Real-Time Voice Conversation Test</h1>\n        <p>This test will verify that your microphone works correctly with the real-time voice system.</p>\n        \n        <div id=\"status-container\">\n            <div id=\"browser-support\" class=\"status info\">Checking browser support...</div>\n            <div id=\"mic-permission\" class=\"status warning\">Microphone permission not requested</div>\n            <div id=\"audio-context\" class=\"status warning\">Audio context not initialized</div>\n            <div id=\"websocket-status\" class=\"status warning\">WebSocket not connected</div>\n        </div>\n        \n        <div>\n            <button id=\"test-mic\" onclick=\"testMicrophone()\">Test Microphone</button>\n            <button id=\"test-websocket\" onclick=\"testWebSocket()\">Test WebSocket</button>\n            <button id=\"test-full-flow\" onclick=\"testFullFlow()\" disabled>Test Full Voice Flow</button>\n        </div>\n        \n        <div>\n            <h3>Microphone Level</h3>\n            <div class=\"level-meter\">\n                <div id=\"level-bar\" class=\"level-bar\"></div>\n            </div>\n            <span id=\"level-text\">0%</span>\n        </div>\n        \n        <div>\n            <h3>Test Log</h3>\n            <div id=\"log\"></div>\n        </div>\n    </div>\n\n    <script>\n        let audioContext = null;\n        let mediaStream = null;\n        let analyzer = null;\n        let processor = null;\n        let websocket = null;\n        let isRecording = false;\n\n        function log(message) {\n            const logDiv = document.getElementById('log');\n            const timestamp = new Date().toLocaleTimeString();\n            logDiv.innerHTML += `[${timestamp}] ${message}\\n`;\n            logDiv.scrollTop = logDiv.scrollHeight;\n            console.log(message);\n        }\n\n        function updateStatus(elementId, status, message) {\n            const element = document.getElementById(elementId);\n            element.className = `status ${status}`;\n            element.textContent = message;\n        }\n\n        // Check browser support\n        function checkBrowserSupport() {\n            log('🔍 Checking browser support...');\n            \n            const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);\n            const hasAudioContext = !!(window.AudioContext || window.webkitAudioContext);\n            const hasWebSocket = !!window.WebSocket;\n            \n            if (hasGetUserMedia && hasAudioContext && hasWebSocket) {\n                updateStatus('browser-support', 'success', '✅ Browser fully supported');\n                log('✅ Browser support: getUserMedia, AudioContext, and WebSocket available');\n                return true;\n            } else {\n                updateStatus('browser-support', 'error', '❌ Browser not supported');\n                log(`❌ Browser support missing: getUserMedia=${hasGetUserMedia}, AudioContext=${hasAudioContext}, WebSocket=${hasWebSocket}`);\n                return false;\n            }\n        }\n\n        // Test microphone access and audio processing\n        async function testMicrophone() {\n            try {\n                log('🎤 Testing microphone access...');\n                \n                // Request microphone permission\n                mediaStream = await navigator.mediaDevices.getUserMedia({\n                    audio: {\n                        echoCancellation: true,\n                        noiseSuppression: false,\n                        autoGainControl: true,\n                        sampleRate: { ideal: 24000, min: 16000, max: 48000 },\n                        channelCount: 1\n                    }\n                });\n                \n                updateStatus('mic-permission', 'success', '✅ Microphone access granted');\n                log('✅ Microphone access granted');\n                \n                // Create audio context\n                const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                audioContext = new AudioContextClass({ sampleRate: 24000 });\n                \n                if (audioContext.state === 'suspended') {\n                    await audioContext.resume();\n                }\n                \n                updateStatus('audio-context', 'success', `✅ Audio context ready (${audioContext.sampleRate}Hz)`);\n                log(`✅ Audio context created: ${audioContext.sampleRate}Hz, state: ${audioContext.state}`);\n                \n                // Set up audio processing\n                const source = audioContext.createMediaStreamSource(mediaStream);\n                analyzer = audioContext.createAnalyser();\n                analyzer.fftSize = 256;\n                source.connect(analyzer);\n                \n                processor = audioContext.createScriptProcessor(4096, 1, 1);\n                let frameCount = 0;\n                \n                processor.onaudioprocess = (e) => {\n                    frameCount++;\n                    \n                    // Update visual level meter\n                    const bufferLength = analyzer.frequencyBinCount;\n                    const dataArray = new Uint8Array(bufferLength);\n                    analyzer.getByteFrequencyData(dataArray);\n                    \n                    let sum = 0;\n                    for (let i = 0; i < bufferLength; i++) {\n                        sum += dataArray[i];\n                    }\n                    const level = (sum / bufferLength / 255) * 100;\n                    \n                    document.getElementById('level-bar').style.width = `${Math.min(100, level * 3)}%`;\n                    document.getElementById('level-text').textContent = `${level.toFixed(1)}%`;\n                    \n                    // Log first few frames\n                    if (frameCount <= 3) {\n                        log(`🔄 Audio processor frame ${frameCount}: level=${level.toFixed(2)}%`);\n                    }\n                    \n                    // Test audio data conversion\n                    if (frameCount === 5) {\n                        const inputData = e.inputBuffer.getChannelData(0);\n                        const int16Data = new Int16Array(inputData.length);\n                        \n                        for (let i = 0; i < inputData.length; i++) {\n                            let sample = inputData[i] * 2.0; // Gain\n                            sample = Math.max(-1, Math.min(1, sample));\n                            int16Data[i] = Math.round(sample * 32767);\n                        }\n                        \n                        const buffer = new ArrayBuffer(int16Data.length * 2);\n                        const view = new DataView(buffer);\n                        for (let i = 0; i < int16Data.length; i++) {\n                            view.setInt16(i * 2, int16Data[i], true);\n                        }\n                        const uint8Array = new Uint8Array(buffer);\n                        const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n                        \n                        log(`✅ Audio conversion test: ${base64Audio.length} chars of base64 audio data`);\n                    }\n                };\n                \n                source.connect(processor);\n                // Don't connect to destination to avoid feedback\n                \n                log('✅ Audio processing pipeline established');\n                isRecording = true;\n                \n                // Enable full flow test\n                document.getElementById('test-full-flow').disabled = false;\n                \n            } catch (error) {\n                updateStatus('mic-permission', 'error', '❌ Microphone access failed');\n                updateStatus('audio-context', 'error', '❌ Audio context failed');\n                log(`❌ Microphone test failed: ${error.message}`);\n            }\n        }\n\n        // Test WebSocket connection\n        function testWebSocket() {\n            try {\n                log('🌐 Testing WebSocket connection...');\n                \n                websocket = new WebSocket('ws://localhost:5000/ws');\n                \n                websocket.onopen = () => {\n                    updateStatus('websocket-status', 'success', '✅ WebSocket connected');\n                    log('✅ WebSocket connected to server');\n                    \n                    // Send test message\n                    websocket.send(JSON.stringify({\n                        type: 'ping',\n                        timestamp: new Date().toISOString()\n                    }));\n                };\n                \n                websocket.onmessage = (event) => {\n                    const data = JSON.parse(event.data);\n                    log(`📨 WebSocket message: ${data.type}`);\n                };\n                \n                websocket.onclose = () => {\n                    updateStatus('websocket-status', 'warning', '⚠️ WebSocket disconnected');\n                    log('⚠️ WebSocket connection closed');\n                };\n                \n                websocket.onerror = (error) => {\n                    updateStatus('websocket-status', 'error', '❌ WebSocket connection failed');\n                    log(`❌ WebSocket error: ${error}`);\n                };\n                \n            } catch (error) {\n                updateStatus('websocket-status', 'error', '❌ WebSocket test failed');\n                log(`❌ WebSocket test failed: ${error.message}`);\n            }\n        }\n\n        // Test full voice flow\n        function testFullFlow() {\n            if (!isRecording || !websocket || websocket.readyState !== WebSocket.OPEN) {\n                log('❌ Cannot test full flow: microphone or WebSocket not ready');\n                return;\n            }\n            \n            log('🚀 Starting full voice flow test...');\n            \n            // Send start message\n            websocket.send(JSON.stringify({\n                type: \"start\",\n                userId: \"test-user\",\n                clientId: \"test-client\",\n                useRealtimeAPI: true,\n                mode: \"realtime\",\n                behavior: {\n                    model: \"gpt-4o-realtime-preview-2024-10-01\",\n                    temperature: 0.7,\n                    voice: { voice: \"shimmer\", speed: 1.0 }\n                },\n                instructions: \"You are a test assistant. Respond briefly to test the voice system.\"\n            }));\n            \n            log('📤 Sent session start message');\n            \n            // Set up message handling for the test\n            websocket.onmessage = (event) => {\n                const data = JSON.parse(event.data);\n                \n                switch (data.type) {\n                    case 'ready':\n                        log('✅ Session ready - voice conversation active');\n                        break;\n                    case 'session.created':\n                        log(`✅ OpenAI session created: ${data.session?.id}`);\n                        break;\n                    case 'session.updated':\n                        log(`✅ Session configured with VAD: ${data.session?.turn_detection?.type}`);\n                        break;\n                    case 'input_audio_buffer.speech_started':\n                        log('🎤 Speech detection started');\n                        break;\n                    case 'input_audio_buffer.speech_stopped':\n                        log('🔇 Speech detection stopped');\n                        break;\n                    case 'conversation.item.input_audio_transcription.completed':\n                        log(`📝 User transcribed: \"${data.transcript}\"`);\n                        break;\n                    case 'response.audio_transcript.done':\n                        log(`🤖 AI response: \"${data.transcript}\"`);\n                        break;\n                    case 'response.audio.delta':\n                        log(`🔊 AI audio chunk: ${data.delta?.length || 0} chars`);\n                        break;\n                    default:\n                        // Log other important events\n                        if (['response.created', 'response.done'].includes(data.type)) {\n                            log(`📋 ${data.type}`);\n                        }\n                }\n            };\n        }\n\n        // Initialize on page load\n        window.onload = () => {\n            checkBrowserSupport();\n        };\n    </script>\n</body>\n</html>\n"}