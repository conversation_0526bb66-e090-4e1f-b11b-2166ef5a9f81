{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/ClientDetail.tsx"}, "originalCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { useParams, useLocation } from \"wouter\";\nimport { SessionNote, ClientTheme } from \"@shared/schema\";\nimport SessionNotesList from \"@/components/SessionNotesList\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { getInitials, getStatusColor, getThemeColor } from \"@/lib/utils\";\nimport { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from \"recharts\";\n\ninterface ClientDetailParams {\n  id: string;\n}\n\nexport default function ClientDetail() {\n  const { id } = useParams<ClientDetailParams>();\n  const [, setLocation] = useLocation();\n  const clientId = parseInt(id);\n\n  // Fetch client details\n  const { data: client, isLoading: isLoadingClient } = useQuery({\n    queryKey: [`/api/clients/${clientId}`],\n  });\n\n  // Fetch client notes\n  const { data: notes, isLoading: isLoadingNotes } = useQuery<SessionNote[]>({\n    queryKey: [`/api/clients/${clientId}/notes`],\n  });\n\n  // Fetch client themes\n  const { data: themes, isLoading: isLoadingThemes } = useQuery<ClientTheme[]>({\n    queryKey: [`/api/clients/${clientId}/themes`],\n  });\n\n  if (isLoadingClient) {\n    return (\n      <div className=\"space-y-4\">\n        <Skeleton className=\"h-12 w-3/4\" />\n        <Skeleton className=\"h-4 w-1/2\" />\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-6\">\n          <Skeleton className=\"h-40 w-full\" />\n          <Skeleton className=\"h-40 w-full\" />\n          <Skeleton className=\"h-40 w-full\" />\n        </div>\n      </div>\n    );\n  }\n\n  if (!client) {\n    return (\n      <div className=\"text-center py-12\">\n        <h3 className=\"text-lg font-medium text-neutral-dark\">Client not found</h3>\n        <Button\n          className=\"mt-4\"\n          onClick={() => setLocation(\"/\")}\n        >\n          Return to Dashboard\n        </Button>\n      </div>\n    );\n  }\n\n  const status = getStatusColor(client.status);\n\n  // Prepare data for charts\n  const themesChartData = themes?.map((theme, index) => ({\n    name: theme.name,\n    value: theme.occurrences,\n    color: getThemeColor(index).split(\" \")[0]\n  })) || [];\n\n  const progressData = notes?.map(note => {\n    const date = new Date(note.sessionDate || note.createdAt);\n    return {\n      date: `${date.getMonth() + 1}/${date.getDate()}`,\n      value: 0 // Themes are now stored separately\n    };\n  }).reverse() || [];\n\n  return (\n    <div>\n      <div className=\"md:flex md:items-center md:justify-between mb-6\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0 h-16 w-16 rounded-full bg-primary-light flex items-center justify-center text-white font-medium text-xl\">\n            {getInitials(client.name)}\n          </div>\n          <div className=\"ml-4\">\n            <h2 className=\"text-2xl font-bold leading-7 text-neutral-dark\">\n              {client.name}\n            </h2>\n            <div className=\"mt-1 flex items-center\">\n              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bg} ${status.text}`}>\n                {status.label}\n              </div>\n              <span className=\"ml-2 text-sm text-gray-500\">\n                Client ID: {client.id}\n              </span>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-4 flex md:mt-0 md:ml-4 space-x-3\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setLocation(\"/\")}\n          >\n            <span className=\"material-icons text-sm mr-2\">arrow_back</span>\n            Back to Dashboard\n          </Button>\n          <Button\n            onClick={() => setLocation(\"/notes\")}\n          >\n            <span className=\"material-icons text-sm mr-2\">add</span>\n            New Session\n          </Button>\n        </div>\n      </div>\n\n      {/* Client overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Client Overview</h3>\n            <div className=\"space-y-3\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">Status</p>\n                <p className={`text-sm ${status.text} font-medium`}>{status.label}</p>\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">Sessions</p>\n                <p className=\"text-sm text-neutral-dark\">{notes?.length || 0} total sessions</p>\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">Key Themes</p>\n                <div className=\"flex flex-wrap gap-1 mt-1\">\n                  {isLoadingThemes ? (\n                    <Skeleton className=\"h-6 w-24\" />\n                  ) : (\n                    themes?.slice(0, 3).map((theme, index) => (\n                      <span \n                        key={theme.id} \n                        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getThemeColor(index)}`}\n                      >\n                        {theme.name}\n                      </span>\n                    ))\n                  )}\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Theme Distribution</h3>\n            {isLoadingThemes ? (\n              <div className=\"h-40 flex items-center justify-center\">\n                <Skeleton className=\"h-32 w-32 rounded-full\" />\n              </div>\n            ) : (\n              <div className=\"h-40\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <PieChart>\n                    <Pie\n                      data={themesChartData}\n                      cx=\"50%\"\n                      cy=\"50%\"\n                      innerRadius={30}\n                      outerRadius={60}\n                      paddingAngle={5}\n                      dataKey=\"value\"\n                      label={({ name }) => name}\n                    >\n                      {themesChartData.map((entry, index) => (\n                        <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />\n                      ))}\n                    </Pie>\n                    <Tooltip />\n                  </PieChart>\n                </ResponsiveContainer>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Session History</h3>\n            {isLoadingNotes ? (\n              <div className=\"h-40 flex items-center justify-center\">\n                <Skeleton className=\"h-32 w-full\" />\n              </div>\n            ) : (\n              <div className=\"h-40\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <BarChart data={progressData}>\n                    <XAxis dataKey=\"date\" />\n                    <YAxis />\n                    <Tooltip />\n                    <Bar dataKey=\"value\" fill=\"#4A6FA5\" />\n                  </BarChart>\n                </ResponsiveContainer>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Session notes */}\n      <div>\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Session Notes</h3>\n        <SessionNotesList \n          notes={notes} \n          isLoading={isLoadingNotes} \n          clientId={clientId} \n        />\n      </div>\n    </div>\n  );\n}\n", "modifiedCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { useParams, useLocation } from \"wouter\";\nimport { SessionNote, ClientTheme } from \"@shared/schema\";\nimport SessionNotesList from \"@/components/SessionNotesList\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { getInitials, getStatusColor, getThemeColor } from \"@/lib/utils\";\nimport { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip } from \"recharts\";\n\ninterface ClientDetailParams {\n  id: string;\n}\n\nexport default function ClientDetail() {\n  const { id } = useParams<ClientDetailParams>();\n  const [, setLocation] = useLocation();\n  const clientId = parseInt(id);\n\n  // Fetch client details\n  const { data: client, isLoading: isLoadingClient } = useQuery({\n    queryKey: [`/api/clients/${clientId}`],\n  });\n\n  // Fetch client notes\n  const { data: notes, isLoading: isLoadingNotes } = useQuery<SessionNote[]>({\n    queryKey: [`/api/clients/${clientId}/notes`],\n  });\n\n  // Fetch client themes\n  const { data: themes, isLoading: isLoadingThemes } = useQuery<ClientTheme[]>({\n    queryKey: [`/api/clients/${clientId}/themes`],\n  });\n\n  if (isLoadingClient) {\n    return (\n      <div className=\"space-y-4\">\n        <Skeleton className=\"h-12 w-3/4\" />\n        <Skeleton className=\"h-4 w-1/2\" />\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mt-6\">\n          <Skeleton className=\"h-40 w-full\" />\n          <Skeleton className=\"h-40 w-full\" />\n          <Skeleton className=\"h-40 w-full\" />\n        </div>\n      </div>\n    );\n  }\n\n  if (!client) {\n    return (\n      <div className=\"text-center py-12\">\n        <h3 className=\"text-lg font-medium text-neutral-dark\">Client not found</h3>\n        <Button\n          className=\"mt-4\"\n          onClick={() => setLocation(\"/\")}\n        >\n          Return to Dashboard\n        </Button>\n      </div>\n    );\n  }\n\n  const status = getStatusColor((client as any)?.status || 'stable');\n\n  // Prepare data for charts\n  const themesChartData = themes?.map((theme, index) => ({\n    name: theme.name,\n    value: theme.occurrences,\n    color: getThemeColor(index).split(\" \")[0]\n  })) || [];\n\n  const progressData = notes?.map(note => {\n    const date = new Date(note.sessionDate || note.createdAt);\n    return {\n      date: `${date.getMonth() + 1}/${date.getDate()}`,\n      value: 0 // Themes are now stored separately\n    };\n  }).reverse() || [];\n\n  return (\n    <div>\n      <div className=\"md:flex md:items-center md:justify-between mb-6\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0 h-16 w-16 rounded-full bg-primary-light flex items-center justify-center text-white font-medium text-xl\">\n            {getInitials(client.name)}\n          </div>\n          <div className=\"ml-4\">\n            <h2 className=\"text-2xl font-bold leading-7 text-neutral-dark\">\n              {client.name}\n            </h2>\n            <div className=\"mt-1 flex items-center\">\n              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bg} ${status.text}`}>\n                {status.label}\n              </div>\n              <span className=\"ml-2 text-sm text-gray-500\">\n                Client ID: {client.id}\n              </span>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-4 flex md:mt-0 md:ml-4 space-x-3\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setLocation(\"/\")}\n          >\n            <span className=\"material-icons text-sm mr-2\">arrow_back</span>\n            Back to Dashboard\n          </Button>\n          <Button\n            onClick={() => setLocation(\"/notes\")}\n          >\n            <span className=\"material-icons text-sm mr-2\">add</span>\n            New Session\n          </Button>\n        </div>\n      </div>\n\n      {/* Client overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Client Overview</h3>\n            <div className=\"space-y-3\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">Status</p>\n                <p className={`text-sm ${status.text} font-medium`}>{status.label}</p>\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">Sessions</p>\n                <p className=\"text-sm text-neutral-dark\">{notes?.length || 0} total sessions</p>\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-gray-500\">Key Themes</p>\n                <div className=\"flex flex-wrap gap-1 mt-1\">\n                  {isLoadingThemes ? (\n                    <Skeleton className=\"h-6 w-24\" />\n                  ) : (\n                    themes?.slice(0, 3).map((theme, index) => (\n                      <span \n                        key={theme.id} \n                        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getThemeColor(index)}`}\n                      >\n                        {theme.name}\n                      </span>\n                    ))\n                  )}\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Theme Distribution</h3>\n            {isLoadingThemes ? (\n              <div className=\"h-40 flex items-center justify-center\">\n                <Skeleton className=\"h-32 w-32 rounded-full\" />\n              </div>\n            ) : (\n              <div className=\"h-40\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <PieChart>\n                    <Pie\n                      data={themesChartData}\n                      cx=\"50%\"\n                      cy=\"50%\"\n                      innerRadius={30}\n                      outerRadius={60}\n                      paddingAngle={5}\n                      dataKey=\"value\"\n                      label={({ name }) => name}\n                    >\n                      {themesChartData.map((entry, index) => (\n                        <Cell key={`cell-${index}`} fill={entry.color.replace('bg-', '')} />\n                      ))}\n                    </Pie>\n                    <Tooltip />\n                  </PieChart>\n                </ResponsiveContainer>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Session History</h3>\n            {isLoadingNotes ? (\n              <div className=\"h-40 flex items-center justify-center\">\n                <Skeleton className=\"h-32 w-full\" />\n              </div>\n            ) : (\n              <div className=\"h-40\">\n                <ResponsiveContainer width=\"100%\" height=\"100%\">\n                  <BarChart data={progressData}>\n                    <XAxis dataKey=\"date\" />\n                    <YAxis />\n                    <Tooltip />\n                    <Bar dataKey=\"value\" fill=\"#4A6FA5\" />\n                  </BarChart>\n                </ResponsiveContainer>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Session notes */}\n      <div>\n        <h3 className=\"text-lg font-medium text-neutral-dark mb-4\">Session Notes</h3>\n        <SessionNotesList \n          notes={notes} \n          isLoading={isLoadingNotes} \n          clientId={clientId} \n        />\n      </div>\n    </div>\n  );\n}\n"}