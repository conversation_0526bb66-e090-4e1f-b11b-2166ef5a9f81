{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/schema.ts"}, "originalCode": "import { sql } from '@vercel/postgres';\nimport { type Theme } from '@shared/schema';\n\n// Voice configuration schema\nexport interface VoiceConfig {\n  id: number;\n  name: string;\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    questionInflection: number; // 0-1 scale\n    emotionalRange: number; // 0-1 scale\n    pauseDuration: number; // milliseconds\n  };\n  isDefault: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n// Conversation flow configuration\nexport interface ConversationConfig {\n  id: number;\n  name: string;\n  turnTaking: {\n    minSilenceDuration: number; // milliseconds\n    maxInterruptionGap: number; // milliseconds\n    backchannelFrequency: number; // 0-1 scale\n  };\n  responseStyle: {\n    maxResponseLength: number; // words\n    minResponseLength: number; // words\n    temperature: number; // 0-1 scale\n    presencePenalty: number; // 0-1 scale\n    frequencyPenalty: number; // 0-1 scale\n  };\n  isDefault: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n// Create tables for configurations\nexport async function createConfigTables() {\n  await sql`\n    CREATE TABLE IF NOT EXISTS voice_configs (\n      id SERIAL PRIMARY KEY,\n      name VARCHAR(255) NOT NULL,\n      voice VARCHAR(50) NOT NULL,\n      speed FLOAT NOT NULL,\n      pitch FLOAT NOT NULL,\n      emphasis VARCHAR(20) NOT NULL,\n      prosody JSONB NOT NULL,\n      is_default BOOLEAN DEFAULT FALSE,\n      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS conversation_configs (\n      id SERIAL PRIMARY KEY,\n      name VARCHAR(255) NOT NULL,\n      turn_taking JSONB NOT NULL,\n      response_style JSONB NOT NULL,\n      is_default BOOLEAN DEFAULT FALSE,\n      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP\n    );\n  `;\n}\n\n// Default configurations\nexport const DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  id: 1,\n  name: \"Default Voice\",\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: \"moderate\",\n  prosody: {\n    questionInflection: 0.8,\n    emotionalRange: 0.7,\n    pauseDuration: 300\n  },\n  isDefault: true,\n  createdAt: new Date(),\n  updatedAt: new Date()\n};\n\nexport const DEFAULT_CONVERSATION_CONFIG: ConversationConfig = {\n  id: 1,\n  name: \"Default Conversation\",\n  turnTaking: {\n    minSilenceDuration: 300,\n    maxInterruptionGap: 500,\n    backchannelFrequency: 0.3\n  },\n  responseStyle: {\n    maxResponseLength: 150,\n    minResponseLength: 50,\n    temperature: 0.8,\n    presencePenalty: 0.1,\n    frequencyPenalty: 0.1\n  },\n  isDefault: true,\n  createdAt: new Date(),\n  updatedAt: new Date()\n};\n\nexport interface ClientTheme {\n  id: number;\n  clientId: number;\n  name: string;\n  trend: number;\n  occurrences: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface AiTherapyConversation {\n  id: number;\n  clientId: number;\n  userId: number;\n  title: string;\n  active: boolean;\n  createdAt: Date;\n  startedAt: Date;\n  endedAt: Date | null;\n  summary: string | null;\n  aiProcessed: boolean | null;\n}\n\nexport interface AiTherapyMessage {\n  id: number;\n  conversationId: number;\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  createdAt: Date;\n  audioUrl: string | null;\n}\n\nexport interface AiTherapySettings {\n  id: number;\n  name: string;\n  active: boolean;\n  systemPrompt: string;\n  model: string;\n  temperature: number;\n  createdAt: Date;\n  updatedAt: Date;\n} ", "modifiedCode": "// This file is not used in the current implementation\nimport { type Theme } from '@shared/schema';\n\n// Voice configuration schema\nexport interface VoiceConfig {\n  id: number;\n  name: string;\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    questionInflection: number; // 0-1 scale\n    emotionalRange: number; // 0-1 scale\n    pauseDuration: number; // milliseconds\n  };\n  isDefault: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n// Conversation flow configuration\nexport interface ConversationConfig {\n  id: number;\n  name: string;\n  turnTaking: {\n    minSilenceDuration: number; // milliseconds\n    maxInterruptionGap: number; // milliseconds\n    backchannelFrequency: number; // 0-1 scale\n  };\n  responseStyle: {\n    maxResponseLength: number; // words\n    minResponseLength: number; // words\n    temperature: number; // 0-1 scale\n    presencePenalty: number; // 0-1 scale\n    frequencyPenalty: number; // 0-1 scale\n  };\n  isDefault: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n// Create tables for configurations\nexport async function createConfigTables() {\n  await sql`\n    CREATE TABLE IF NOT EXISTS voice_configs (\n      id SERIAL PRIMARY KEY,\n      name VARCHAR(255) NOT NULL,\n      voice VARCHAR(50) NOT NULL,\n      speed FLOAT NOT NULL,\n      pitch FLOAT NOT NULL,\n      emphasis VARCHAR(20) NOT NULL,\n      prosody JSONB NOT NULL,\n      is_default BOOLEAN DEFAULT FALSE,\n      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS conversation_configs (\n      id SERIAL PRIMARY KEY,\n      name VARCHAR(255) NOT NULL,\n      turn_taking JSONB NOT NULL,\n      response_style JSONB NOT NULL,\n      is_default BOOLEAN DEFAULT FALSE,\n      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP\n    );\n  `;\n}\n\n// Default configurations\nexport const DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  id: 1,\n  name: \"Default Voice\",\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: \"moderate\",\n  prosody: {\n    questionInflection: 0.8,\n    emotionalRange: 0.7,\n    pauseDuration: 300\n  },\n  isDefault: true,\n  createdAt: new Date(),\n  updatedAt: new Date()\n};\n\nexport const DEFAULT_CONVERSATION_CONFIG: ConversationConfig = {\n  id: 1,\n  name: \"Default Conversation\",\n  turnTaking: {\n    minSilenceDuration: 300,\n    maxInterruptionGap: 500,\n    backchannelFrequency: 0.3\n  },\n  responseStyle: {\n    maxResponseLength: 150,\n    minResponseLength: 50,\n    temperature: 0.8,\n    presencePenalty: 0.1,\n    frequencyPenalty: 0.1\n  },\n  isDefault: true,\n  createdAt: new Date(),\n  updatedAt: new Date()\n};\n\nexport interface ClientTheme {\n  id: number;\n  clientId: number;\n  name: string;\n  trend: number;\n  occurrences: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface AiTherapyConversation {\n  id: number;\n  clientId: number;\n  userId: number;\n  title: string;\n  active: boolean;\n  createdAt: Date;\n  startedAt: Date;\n  endedAt: Date | null;\n  summary: string | null;\n  aiProcessed: boolean | null;\n}\n\nexport interface AiTherapyMessage {\n  id: number;\n  conversationId: number;\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  createdAt: Date;\n  audioUrl: string | null;\n}\n\nexport interface AiTherapySettings {\n  id: number;\n  name: string;\n  active: boolean;\n  systemPrompt: string;\n  model: string;\n  temperature: number;\n  createdAt: Date;\n  updatedAt: Date;\n} "}