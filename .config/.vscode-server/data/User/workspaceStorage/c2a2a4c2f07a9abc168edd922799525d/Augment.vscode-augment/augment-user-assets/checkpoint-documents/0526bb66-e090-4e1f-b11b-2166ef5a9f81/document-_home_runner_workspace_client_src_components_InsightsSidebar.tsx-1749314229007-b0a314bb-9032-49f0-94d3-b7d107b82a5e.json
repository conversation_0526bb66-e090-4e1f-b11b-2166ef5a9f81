{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/InsightsSidebar.tsx"}, "originalCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { cn } from \"@/lib/utils\";\n\ninterface InsightsSidebarProps {\n  className?: string;\n}\n\nexport default function InsightsSidebar({ className }: InsightsSidebarProps) {\n  // Fetch dashboard stats for insights\n  const { data: stats } = useQuery({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch clients to get all themes\n  const { data: clients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Create aggregated data for themes across all clients\n  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];\n  const themeCounts: Record<string, number> = {};\n  \n  allThemes.forEach((theme: any) => {\n    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;\n  });\n  \n  // Sort theme entries by occurrence count\n  const sortedThemes = Object.entries(themeCounts)\n    .sort(([, countA], [, countB]) => countB - countA)\n    .slice(0, 5);\n  \n  // Weekly activity data (mocked for now)\n  const weeklyActivityData = [\n    { day: \"M\", percentage: 40 },\n    { day: \"T\", percentage: 60 },\n    { day: \"W\", percentage: 80 },\n    { day: \"T\", percentage: 30 },\n    { day: \"F\", percentage: 70 },\n    { day: \"S\", percentage: 10 },\n    { day: \"S\", percentage: 5 }\n  ];\n\n  return (\n    <aside className={cn(\"h-full w-64 bg-white border-l border-gray-200 overflow-y-auto\", className)}>\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Insights Overview</h3>\n        \n        {/* Weekly Activity Chart */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Weekly Activity</h4>\n          <div className=\"bg-gray-50 border border-gray-200 p-4 rounded-lg\">\n            <div className=\"flex items-end justify-between h-20 space-x-2\">\n              {weeklyActivityData.map((item, index) => (\n                <div key={`${item.day}-${index}`} className=\"flex flex-col items-center flex-1\">\n                  <div className=\"w-full flex flex-col justify-end h-16\">\n                    <div\n                      className={`${index >= 5 ? \"bg-blue-300\" : \"bg-blue-500\"} w-full rounded-t transition-all duration-300 min-h-[4px]`}\n                      style={{ height: `${Math.max(item.percentage, 10)}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-xs mt-2 text-gray-600 font-medium\">{item.day}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n        \n        {/* Common Themes */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Common Themes</h4>\n          <div className=\"bg-gray-50 border border-gray-200 p-4 rounded-lg\">\n            {sortedThemes.length > 0 ? (\n              <div className=\"space-y-3\">\n                {sortedThemes.map(([name, count], index) => {\n                  const maxCount = sortedThemes[0][1];\n                  const percentage = Math.round((count / maxCount) * 100);\n\n                  return (\n                    <div key={name} className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-800 font-medium\">{name}</span>\n                        <span className=\"text-xs text-gray-600 bg-gray-200 px-2 py-1 rounded\">{count}</span>\n                      </div>\n                      <div className=\"w-full bg-gray-300 rounded-full h-2\">\n                        <div\n                          className=\"bg-blue-500 rounded-full h-2 transition-all duration-300\"\n                          style={{ width: `${percentage}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center\">No themes available</p>\n            )}\n          </div>\n        </div>\n        \n        {/* Client Status Distribution */}\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Client Status</h4>\n          <div className=\"space-y-2\">\n            <div className=\"bg-green-50 border border-green-200 p-3 rounded-lg flex items-center justify-between\">\n              <div>\n                <div className=\"text-lg font-bold text-green-600\">\n                  {(stats as any)?.improvingPercentage || 0}%\n                </div>\n                <div className=\"text-sm text-green-700\">Improving</div>\n              </div>\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n            </div>\n            <div className=\"bg-yellow-50 border border-yellow-200 p-3 rounded-lg flex items-center justify-between\">\n              <div>\n                <div className=\"text-lg font-bold text-yellow-600\">\n                  {(stats as any) ? Math.round((((stats as any).totalClients - (stats as any).improving - 3) / (stats as any).totalClients) * 100) : 0}%\n                </div>\n                <div className=\"text-sm text-yellow-700\">Stable</div>\n              </div>\n              <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n            </div>\n            <div className=\"bg-red-50 border border-red-200 p-3 rounded-lg flex items-center justify-between\">\n              <div>\n                <div className=\"text-lg font-bold text-red-600\">\n                  {(stats as any) ? Math.round((3 / (stats as any).totalClients) * 100) : 0}%\n                </div>\n                <div className=\"text-sm text-red-700\">Needs Attention</div>\n              </div>\n              <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </aside>\n  );\n}\n", "modifiedCode": "import { useQuery } from \"@tanstack/react-query\";\nimport { cn } from \"@/lib/utils\";\n\ninterface InsightsSidebarProps {\n  className?: string;\n}\n\nexport default function InsightsSidebar({ className }: InsightsSidebarProps) {\n  // Fetch dashboard stats for insights\n  const { data: stats } = useQuery({\n    queryKey: [\"/api/dashboard/stats\"],\n  });\n\n  // Fetch clients to get all themes\n  const { data: clients } = useQuery({\n    queryKey: [\"/api/clients\"],\n  });\n\n  // Create aggregated data for themes across all clients\n  const allThemes = (clients as any[])?.flatMap((client: any) => client.themes || []) || [];\n  const themeCounts: Record<string, number> = {};\n  \n  allThemes.forEach((theme: any) => {\n    themeCounts[theme.name] = (themeCounts[theme.name] || 0) + theme.occurrences;\n  });\n  \n  // Sort theme entries by occurrence count\n  const sortedThemes = Object.entries(themeCounts)\n    .sort(([, countA], [, countB]) => countB - countA)\n    .slice(0, 5);\n  \n  // Weekly activity data (mocked for now)\n  const weeklyActivityData = [\n    { day: \"M\", percentage: 40 },\n    { day: \"T\", percentage: 60 },\n    { day: \"W\", percentage: 80 },\n    { day: \"T\", percentage: 30 },\n    { day: \"F\", percentage: 70 },\n    { day: \"S\", percentage: 10 },\n    { day: \"S\", percentage: 5 }\n  ];\n\n  return (\n    <aside className={cn(\"h-full w-80 bg-white border-l border-gray-200 overflow-y-auto\", className)}>\n      <div className=\"px-4 py-5 sm:p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Insights Overview</h3>\n        \n        {/* Weekly Activity Chart */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Weekly Activity</h4>\n          <div className=\"bg-gray-50 border border-gray-200 p-4 rounded-lg\">\n            <div className=\"flex items-end justify-between h-20 space-x-2\">\n              {weeklyActivityData.map((item, index) => (\n                <div key={`${item.day}-${index}`} className=\"flex flex-col items-center flex-1\">\n                  <div className=\"w-full flex flex-col justify-end h-16\">\n                    <div\n                      className={`${index >= 5 ? \"bg-blue-300\" : \"bg-blue-500\"} w-full rounded-t transition-all duration-300 min-h-[4px]`}\n                      style={{ height: `${Math.max(item.percentage, 10)}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-xs mt-2 text-gray-600 font-medium\">{item.day}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n        \n        {/* Common Themes */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Common Themes</h4>\n          <div className=\"bg-gray-50 border border-gray-200 p-4 rounded-lg\">\n            {sortedThemes.length > 0 ? (\n              <div className=\"space-y-3\">\n                {sortedThemes.map(([name, count], index) => {\n                  const maxCount = sortedThemes[0][1];\n                  const percentage = Math.round((count / maxCount) * 100);\n\n                  return (\n                    <div key={name} className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-800 font-medium\">{name}</span>\n                        <span className=\"text-xs text-gray-600 bg-gray-200 px-2 py-1 rounded\">{count}</span>\n                      </div>\n                      <div className=\"w-full bg-gray-300 rounded-full h-2\">\n                        <div\n                          className=\"bg-blue-500 rounded-full h-2 transition-all duration-300\"\n                          style={{ width: `${percentage}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            ) : (\n              <p className=\"text-sm text-gray-500 text-center\">No themes available</p>\n            )}\n          </div>\n        </div>\n        \n        {/* Client Status Distribution */}\n        <div>\n          <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Client Status</h4>\n          <div className=\"space-y-2\">\n            <div className=\"bg-green-50 border border-green-200 p-3 rounded-lg flex items-center justify-between\">\n              <div>\n                <div className=\"text-lg font-bold text-green-600\">\n                  {(stats as any)?.improvingPercentage || 0}%\n                </div>\n                <div className=\"text-sm text-green-700\">Improving</div>\n              </div>\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n            </div>\n            <div className=\"bg-yellow-50 border border-yellow-200 p-3 rounded-lg flex items-center justify-between\">\n              <div>\n                <div className=\"text-lg font-bold text-yellow-600\">\n                  {(stats as any) ? Math.round((((stats as any).totalClients - (stats as any).improving - 3) / (stats as any).totalClients) * 100) : 0}%\n                </div>\n                <div className=\"text-sm text-yellow-700\">Stable</div>\n              </div>\n              <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n            </div>\n            <div className=\"bg-red-50 border border-red-200 p-3 rounded-lg flex items-center justify-between\">\n              <div>\n                <div className=\"text-lg font-bold text-red-600\">\n                  {(stats as any) ? Math.round((3 / (stats as any).totalClients) * 100) : 0}%\n                </div>\n                <div className=\"text-sm text-red-700\">Needs Attention</div>\n              </div>\n              <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </aside>\n  );\n}\n"}