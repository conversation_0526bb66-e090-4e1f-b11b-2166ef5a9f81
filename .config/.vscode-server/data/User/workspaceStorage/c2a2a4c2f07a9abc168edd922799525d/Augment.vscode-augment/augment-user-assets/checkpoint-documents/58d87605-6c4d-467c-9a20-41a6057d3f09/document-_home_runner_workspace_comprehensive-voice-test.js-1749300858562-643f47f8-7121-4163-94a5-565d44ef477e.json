{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "comprehensive-voice-test.js"}, "originalCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\nimport fs from 'fs';\n\nconsole.log('🔍 COMPREHENSIVE REAL-TIME VOICE CONVERSATION DEBUG');\nconsole.log('=' * 60);\n\nconst ws = new WebSocket('ws://localhost:5000/ws');\n\nlet messageCount = 0;\nlet audioChunksSent = 0;\nlet speechStartDetected = false;\nlet speechStopDetected = false;\nlet transcriptionReceived = false;\nlet responseGenerated = false;\nlet sessionReady = false;\nlet audioResponseReceived = false;\nlet greetingReceived = false;\nconst sentMessages = [];\nconst receivedMessages = [];\nconst debugLog = [];\n\nfunction logEvent(event, details = '', important = false) {\n  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];\n  const logLine = `[${timestamp}] ${event}${details ? ': ' + details : ''}`;\n  console.log(important ? `🔥 ${logLine}` : logLine);\n  debugLog.push(logLine);\n}\n\n// Generate realistic test audio data (simulated speech)\nfunction generateTestAudioChunk(duration = 0.1) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * duration);\n  const buffer = new ArrayBuffer(samples * 2); // 16-bit PCM\n  const view = new DataView(buffer);\n  \n  // Generate speech-like audio with varying amplitude and frequency\n  for (let i = 0; i < samples; i++) {\n    // Create a complex waveform that resembles speech\n    const t = i / sampleRate;\n    const fundamental = 150; // Base frequency around human speech\n    const harmonics = Math.sin(2 * Math.PI * fundamental * t) * 0.3 +\n                     Math.sin(2 * Math.PI * fundamental * 2 * t) * 0.2 +\n                     Math.sin(2 * Math.PI * fundamental * 3 * t) * 0.1;\n    \n    // Add some noise and amplitude variation to make it more speech-like\n    const noise = (Math.random() - 0.5) * 0.1;\n    const envelope = Math.sin(t * 10) * 0.5 + 0.5; // Amplitude modulation\n    \n    const sample = (harmonics + noise) * envelope * 0.3; // Scale to reasonable level\n    const int16Sample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));\n    view.setInt16(i * 2, int16Sample, true); // little-endian\n  }\n  \n  const uint8Array = new Uint8Array(buffer);\n  return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n}\n\nws.on('open', () => {\n  logEvent('WebSocket connected', '', true);\n  \n  // Send start message to initialize Realtime API session\n  const startMessage = {\n    type: \"start\",\n    userId: \"debug-user\",\n    clientId: \"debug-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview-2024-10-01\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are Vale, an empathetic AI therapeutic assistant. When the session starts, greet the user warmly and ask how they're feeling today. Keep responses concise and natural for voice conversation.\"\n  };\n  \n  logEvent('Sending start message', '', true);\n  ws.send(JSON.stringify(startMessage));\n  sentMessages.push('start');\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    receivedMessages.push(message.type);\n    \n    logEvent(`Message ${messageCount}`, `${message.type}`, true);\n    \n    switch (message.type) {\n      case 'ready':\n        sessionReady = true;\n        logEvent('Session ready', 'Starting audio test in 2 seconds', true);\n        \n        // Wait for initial greeting, then start sending audio\n        setTimeout(() => {\n          if (!greetingReceived) {\n            logEvent('No greeting received', 'Starting audio test anyway', true);\n          }\n          startAudioTest();\n        }, 2000);\n        break;\n        \n      case 'session.created':\n        logEvent('OpenAI session created', `ID: ${message.session?.id || 'unknown'}`, true);\n        break;\n        \n      case 'session.updated':\n        const vadConfig = message.session?.turn_detection;\n        const vadType = vadConfig?.type || 'none';\n        logEvent('Session configured', `VAD: ${vadType}, threshold: ${vadConfig?.threshold || 'unknown'}`, true);\n        break;\n        \n      case 'input_audio_buffer.speech_started':\n        speechStartDetected = true;\n        logEvent('SPEECH STARTED', 'OpenAI detected speech in audio buffer', true);\n        break;\n        \n      case 'input_audio_buffer.speech_stopped':\n        speechStopDetected = true;\n        logEvent('SPEECH STOPPED', 'OpenAI detected end of speech', true);\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        transcriptionReceived = true;\n        const transcript = message.transcript || '';\n        logEvent('USER TRANSCRIBED', `\"${transcript}\"`, true);\n        break;\n        \n      case 'response.created':\n        responseGenerated = true;\n        logEvent('AI RESPONSE STARTED', 'OpenAI generating response', true);\n        break;\n        \n      case 'response.audio.delta':\n        audioResponseReceived = true;\n        logEvent('AI AUDIO CHUNK', `${message.delta?.length || 0} chars`);\n        break;\n        \n      case 'response.audio_transcript.done':\n        const aiTranscript = message.transcript || '';\n        if (aiTranscript && !greetingReceived) {\n          greetingReceived = true;\n          logEvent('AI GREETING RECEIVED', `\"${aiTranscript}\"`, true);\n        } else if (aiTranscript) {\n          logEvent('AI RESPONSE TRANSCRIBED', `\"${aiTranscript}\"`, true);\n        }\n        break;\n        \n      case 'response.done':\n        logEvent('RESPONSE COMPLETE', 'AI finished responding', true);\n        break;\n        \n      case 'error':\n        logEvent('ERROR', message.message || 'Unknown error', true);\n        break;\n        \n      default:\n        logEvent('Other message', message.type);\n    }\n  } catch (error) {\n    logEvent('Parse error', error.message, true);\n  }\n});\n\nfunction startAudioTest() {\n  logEvent('Starting audio test', 'Sending simulated speech audio', true);\n  \n  // Send multiple chunks of realistic audio data\n  const sendAudioChunk = (chunkIndex) => {\n    if (chunkIndex >= 10) {\n      logEvent('Audio test complete', `Sent ${audioChunksSent} chunks`, true);\n      \n      // Wait for response, then end test\n      setTimeout(() => {\n        endTest();\n      }, 5000);\n      return;\n    }\n    \n    const audioData = generateTestAudioChunk(0.1); // 100ms chunks\n    audioChunksSent++;\n    \n    ws.send(JSON.stringify({\n      type: 'input_audio_buffer.append',\n      audio: audioData\n    }));\n    \n    logEvent(`Audio chunk ${chunkIndex + 1}/10`, `${audioData.length} chars`);\n    \n    // Send next chunk after 100ms\n    setTimeout(() => sendAudioChunk(chunkIndex + 1), 100);\n  };\n  \n  sendAudioChunk(0);\n}\n\nfunction endTest() {\n  logEvent('Ending test', 'Generating final report', true);\n  \n  // Generate comprehensive report\n  const report = {\n    timestamp: new Date().toISOString(),\n    testResults: {\n      sessionReady,\n      greetingReceived,\n      audioChunksSent,\n      speechStartDetected,\n      speechStopDetected,\n      transcriptionReceived,\n      responseGenerated,\n      audioResponseReceived\n    },\n    messageFlow: {\n      totalMessages: messageCount,\n      sentMessages,\n      receivedMessages\n    },\n    debugLog\n  };\n  \n  console.log('\\n' + '='.repeat(60));\n  console.log('🔍 COMPREHENSIVE TEST RESULTS');\n  console.log('='.repeat(60));\n  \n  console.log('\\n📊 TEST RESULTS:');\n  console.log(`Session Ready:        ${formatResult(sessionReady)}`);\n  console.log(`AI Greeting:          ${formatResult(greetingReceived)}`);\n  console.log(`Audio Chunks Sent:    ${audioChunksSent}`);\n  console.log(`Speech Start:         ${formatResult(speechStartDetected)}`);\n  console.log(`Speech Stop:          ${formatResult(speechStopDetected)}`);\n  console.log(`Transcription:        ${formatResult(transcriptionReceived)}`);\n  console.log(`AI Response:          ${formatResult(responseGenerated)}`);\n  console.log(`Audio Response:       ${formatResult(audioResponseReceived)}`);\n  \n  console.log('\\n📋 MESSAGE FLOW:');\n  console.log(`Total Messages:       ${messageCount}`);\n  console.log(`Sent:                 ${sentMessages.join(', ')}`);\n  console.log(`Received:             ${receivedMessages.slice(0, 10).join(', ')}${receivedMessages.length > 10 ? '...' : ''}`);\n  \n  // Save detailed report\n  fs.writeFileSync('voice-test-report.json', JSON.stringify(report, null, 2));\n  console.log('\\n💾 Detailed report saved to: voice-test-report.json');\n  \n  // Diagnose issues\n  console.log('\\n🔧 DIAGNOSIS:');\n  if (!sessionReady) {\n    console.log('❌ Session failed to initialize - check server connection');\n  } else if (!greetingReceived) {\n    console.log('❌ AI greeting not received - check OpenAI API configuration');\n  } else if (audioChunksSent === 0) {\n    console.log('❌ No audio sent - check audio generation');\n  } else if (!speechStartDetected) {\n    console.log('❌ Speech not detected - check audio format or VAD settings');\n  } else if (!transcriptionReceived) {\n    console.log('❌ No transcription - check audio quality or OpenAI API');\n  } else if (!responseGenerated) {\n    console.log('❌ No AI response - check conversation flow');\n  } else {\n    console.log('✅ All systems working - conversation flow successful!');\n  }\n  \n  ws.close();\n}\n\nfunction formatResult(result) {\n  return result ? '✅ PASS' : '❌ FAIL';\n}\n\nws.on('close', (code, reason) => {\n  logEvent('WebSocket closed', `${code} ${reason.toString()}`, true);\n});\n\nws.on('error', (error) => {\n  logEvent('WebSocket error', error.message, true);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  logEvent('TEST TIMEOUT', 'Ending test due to timeout', true);\n  endTest();\n}, 30000); // 30 second timeout\n", "modifiedCode": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\nimport fs from 'fs';\n\nconsole.log('🔍 COMPREHENSIVE REAL-TIME VOICE CONVERSATION DEBUG');\nconsole.log('=' * 60);\n\nconst ws = new WebSocket('ws://localhost:5000/ws');\n\nlet messageCount = 0;\nlet audioChunksSent = 0;\nlet speechStartDetected = false;\nlet speechStopDetected = false;\nlet transcriptionReceived = false;\nlet responseGenerated = false;\nlet sessionReady = false;\nlet audioResponseReceived = false;\nlet greetingReceived = false;\nconst sentMessages = [];\nconst receivedMessages = [];\nconst debugLog = [];\n\nfunction logEvent(event, details = '', important = false) {\n  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];\n  const logLine = `[${timestamp}] ${event}${details ? ': ' + details : ''}`;\n  console.log(important ? `🔥 ${logLine}` : logLine);\n  debugLog.push(logLine);\n}\n\n// Generate realistic test audio data (simulated speech)\nfunction generateTestAudioChunk(duration = 0.1) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * duration);\n  const buffer = new ArrayBuffer(samples * 2); // 16-bit PCM\n  const view = new DataView(buffer);\n  \n  // Generate speech-like audio with varying amplitude and frequency\n  for (let i = 0; i < samples; i++) {\n    // Create a complex waveform that resembles speech\n    const t = i / sampleRate;\n    const fundamental = 150; // Base frequency around human speech\n    const harmonics = Math.sin(2 * Math.PI * fundamental * t) * 0.3 +\n                     Math.sin(2 * Math.PI * fundamental * 2 * t) * 0.2 +\n                     Math.sin(2 * Math.PI * fundamental * 3 * t) * 0.1;\n    \n    // Add some noise and amplitude variation to make it more speech-like\n    const noise = (Math.random() - 0.5) * 0.1;\n    const envelope = Math.sin(t * 10) * 0.5 + 0.5; // Amplitude modulation\n    \n    const sample = (harmonics + noise) * envelope * 0.3; // Scale to reasonable level\n    const int16Sample = Math.max(-32768, Math.min(32767, Math.floor(sample * 32767)));\n    view.setInt16(i * 2, int16Sample, true); // little-endian\n  }\n  \n  const uint8Array = new Uint8Array(buffer);\n  return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));\n}\n\nws.on('open', () => {\n  logEvent('WebSocket connected', '', true);\n  \n  // Send start message to initialize Realtime API session\n  const startMessage = {\n    type: \"start\",\n    userId: \"debug-user\",\n    clientId: \"debug-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n      model: \"gpt-4o-realtime-preview-2024-10-01\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"You are Vale, an empathetic AI therapeutic assistant. When the session starts, greet the user warmly and ask how they're feeling today. Keep responses concise and natural for voice conversation.\"\n  };\n  \n  logEvent('Sending start message', '', true);\n  ws.send(JSON.stringify(startMessage));\n  sentMessages.push('start');\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    receivedMessages.push(message.type);\n    \n    logEvent(`Message ${messageCount}`, `${message.type}`, true);\n    \n    switch (message.type) {\n      case 'ready':\n        sessionReady = true;\n        logEvent('Session ready', 'Starting audio test in 2 seconds', true);\n        \n        // Wait for initial greeting, then start sending audio\n        setTimeout(() => {\n          if (!greetingReceived) {\n            logEvent('No greeting received', 'Starting audio test anyway', true);\n          }\n          startAudioTest();\n        }, 2000);\n        break;\n        \n      case 'session.created':\n        logEvent('OpenAI session created', `ID: ${message.session?.id || 'unknown'}`, true);\n        break;\n        \n      case 'session.updated':\n        const vadConfig = message.session?.turn_detection;\n        const vadType = vadConfig?.type || 'none';\n        logEvent('Session configured', `VAD: ${vadType}, threshold: ${vadConfig?.threshold || 'unknown'}`, true);\n        break;\n        \n      case 'input_audio_buffer.speech_started':\n        speechStartDetected = true;\n        logEvent('SPEECH STARTED', 'OpenAI detected speech in audio buffer', true);\n        break;\n        \n      case 'input_audio_buffer.speech_stopped':\n        speechStopDetected = true;\n        logEvent('SPEECH STOPPED', 'OpenAI detected end of speech', true);\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        transcriptionReceived = true;\n        const transcript = message.transcript || '';\n        logEvent('USER TRANSCRIBED', `\"${transcript}\"`, true);\n        break;\n        \n      case 'response.created':\n        responseGenerated = true;\n        logEvent('AI RESPONSE STARTED', 'OpenAI generating response', true);\n        break;\n        \n      case 'response.audio.delta':\n        audioResponseReceived = true;\n        logEvent('AI AUDIO CHUNK', `${message.delta?.length || 0} chars`);\n        break;\n        \n      case 'response.audio_transcript.done':\n        const aiTranscript = message.transcript || '';\n        if (aiTranscript && !greetingReceived) {\n          greetingReceived = true;\n          logEvent('AI GREETING RECEIVED', `\"${aiTranscript}\"`, true);\n        } else if (aiTranscript) {\n          logEvent('AI RESPONSE TRANSCRIBED', `\"${aiTranscript}\"`, true);\n        }\n        break;\n        \n      case 'response.done':\n        logEvent('RESPONSE COMPLETE', 'AI finished responding', true);\n        break;\n        \n      case 'error':\n        logEvent('ERROR', message.message || 'Unknown error', true);\n        break;\n        \n      default:\n        logEvent('Other message', message.type);\n    }\n  } catch (error) {\n    logEvent('Parse error', error.message, true);\n  }\n});\n\nfunction startAudioTest() {\n  logEvent('Starting audio test', 'Sending simulated speech audio', true);\n  \n  // Send multiple chunks of realistic audio data\n  const sendAudioChunk = (chunkIndex) => {\n    if (chunkIndex >= 10) {\n      logEvent('Audio test complete', `Sent ${audioChunksSent} chunks`, true);\n      \n      // Wait for response, then end test\n      setTimeout(() => {\n        endTest();\n      }, 5000);\n      return;\n    }\n    \n    const audioData = generateTestAudioChunk(0.1); // 100ms chunks\n    audioChunksSent++;\n    \n    ws.send(JSON.stringify({\n      type: 'input_audio_buffer.append',\n      audio: audioData\n    }));\n    \n    logEvent(`Audio chunk ${chunkIndex + 1}/10`, `${audioData.length} chars`);\n    \n    // Send next chunk after 100ms\n    setTimeout(() => sendAudioChunk(chunkIndex + 1), 100);\n  };\n  \n  sendAudioChunk(0);\n}\n\nfunction endTest() {\n  logEvent('Ending test', 'Generating final report', true);\n  \n  // Generate comprehensive report\n  const report = {\n    timestamp: new Date().toISOString(),\n    testResults: {\n      sessionReady,\n      greetingReceived,\n      audioChunksSent,\n      speechStartDetected,\n      speechStopDetected,\n      transcriptionReceived,\n      responseGenerated,\n      audioResponseReceived\n    },\n    messageFlow: {\n      totalMessages: messageCount,\n      sentMessages,\n      receivedMessages\n    },\n    debugLog\n  };\n  \n  console.log('\\n' + '='.repeat(60));\n  console.log('🔍 COMPREHENSIVE TEST RESULTS');\n  console.log('='.repeat(60));\n  \n  console.log('\\n📊 TEST RESULTS:');\n  console.log(`Session Ready:        ${formatResult(sessionReady)}`);\n  console.log(`AI Greeting:          ${formatResult(greetingReceived)}`);\n  console.log(`Audio Chunks Sent:    ${audioChunksSent}`);\n  console.log(`Speech Start:         ${formatResult(speechStartDetected)}`);\n  console.log(`Speech Stop:          ${formatResult(speechStopDetected)}`);\n  console.log(`Transcription:        ${formatResult(transcriptionReceived)}`);\n  console.log(`AI Response:          ${formatResult(responseGenerated)}`);\n  console.log(`Audio Response:       ${formatResult(audioResponseReceived)}`);\n  \n  console.log('\\n📋 MESSAGE FLOW:');\n  console.log(`Total Messages:       ${messageCount}`);\n  console.log(`Sent:                 ${sentMessages.join(', ')}`);\n  console.log(`Received:             ${receivedMessages.slice(0, 10).join(', ')}${receivedMessages.length > 10 ? '...' : ''}`);\n  \n  // Save detailed report\n  fs.writeFileSync('voice-test-report.json', JSON.stringify(report, null, 2));\n  console.log('\\n💾 Detailed report saved to: voice-test-report.json');\n  \n  // Diagnose issues\n  console.log('\\n🔧 DIAGNOSIS:');\n  if (!sessionReady) {\n    console.log('❌ Session failed to initialize - check server connection');\n  } else if (!greetingReceived) {\n    console.log('❌ AI greeting not received - check OpenAI API configuration');\n  } else if (audioChunksSent === 0) {\n    console.log('❌ No audio sent - check audio generation');\n  } else if (!speechStartDetected) {\n    console.log('❌ Speech not detected - check audio format or VAD settings');\n  } else if (!transcriptionReceived) {\n    console.log('❌ No transcription - check audio quality or OpenAI API');\n  } else if (!responseGenerated) {\n    console.log('❌ No AI response - check conversation flow');\n  } else {\n    console.log('✅ All systems working - conversation flow successful!');\n  }\n  \n  ws.close();\n}\n\nfunction formatResult(result) {\n  return result ? '✅ PASS' : '❌ FAIL';\n}\n\nws.on('close', (code, reason) => {\n  logEvent('WebSocket closed', `${code} ${reason.toString()}`, true);\n});\n\nws.on('error', (error) => {\n  logEvent('WebSocket error', error.message, true);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  logEvent('TEST TIMEOUT', 'Ending test due to timeout', true);\n  endTest();\n}, 30000); // 30 second timeout\n"}