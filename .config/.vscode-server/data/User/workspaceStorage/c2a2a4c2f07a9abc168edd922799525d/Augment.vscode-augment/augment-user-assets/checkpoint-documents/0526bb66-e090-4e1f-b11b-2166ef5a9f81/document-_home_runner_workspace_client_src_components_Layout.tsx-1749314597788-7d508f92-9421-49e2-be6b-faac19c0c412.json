{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Layout.tsx"}, "originalCode": "import { ReactNode, useState } from \"react\";\nimport Sidebar from \"./Sidebar\";\nimport MobileNav from \"./MobileNav\";\nimport InsightsSidebar from \"./InsightsSidebar\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { User } from \"@shared/schema\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface LayoutProps {\n  children: ReactNode;\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [mobileNavOpen, setMobileNavOpen] = useState(false);\n\n  // Fetch user profile\n  const { data: user } = useQuery<User>({\n    queryKey: [\"/api/user\"],\n  });\n\n  return (\n    <div className=\"flex h-screen overflow-hidden\">\n      {/* Sidebar - desktop only */}\n      <Sidebar user={user} className=\"hidden md:flex md:flex-shrink-0\" />\n      \n      {/* Mobile Header */}\n      <div className=\"md:hidden bg-white w-full fixed top-0 left-0 z-10 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between h-16 px-4\">\n          <div className=\"flex items-center\">\n            <img src={logoImage} alt=\"LamaMind Logo\" className=\"h-8 w-8 mr-2\" />\n          </div>\n          <button \n            className=\"text-neutral-dark\"\n            onClick={() => setMobileNavOpen(!mobileNavOpen)}\n          >\n            <span className=\"material-icons\">menu</span>\n          </button>\n        </div>\n      </div>\n      \n      {/* Mobile Navigation Overlay */}\n      {mobileNavOpen && (\n        <div \n          className=\"md:hidden fixed inset-0 bg-black bg-opacity-50 z-20\"\n          onClick={() => setMobileNavOpen(false)}\n        >\n          <div \n            className=\"bg-white w-64 h-full\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <Sidebar user={user} onLinkClick={() => setMobileNavOpen(false)} />\n          </div>\n        </div>\n      )}\n      \n      {/* Main Content */}\n      <main className=\"flex-1 overflow-y-auto pt-16 md:pt-0\">\n        <div className=\"py-6 mx-auto px-4 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n      \n      {/* Mobile Nav */}\n      <MobileNav />\n    </div>\n  );\n}\n", "modifiedCode": "import { ReactNode, useState } from \"react\";\nimport Sidebar from \"./Sidebar\";\nimport MobileNav from \"./MobileNav\";\nimport InsightsSidebar from \"./InsightsSidebar\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { User } from \"@shared/schema\";\nimport logoImage from \"../assets/lamaMind-logo.png\";\n\ninterface LayoutProps {\n  children: ReactNode;\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [mobileNavOpen, setMobileNavOpen] = useState(false);\n\n  // Fetch user profile\n  const { data: user } = useQuery<User>({\n    queryKey: [\"/api/user\"],\n  });\n\n  return (\n    <div className=\"flex h-screen overflow-hidden\">\n      {/* Sidebar - desktop only */}\n      <Sidebar user={user} className=\"hidden md:flex md:flex-shrink-0\" />\n      \n      {/* Mobile Header */}\n      <div className=\"md:hidden bg-white w-full fixed top-0 left-0 z-10 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between h-16 px-4\">\n          <div className=\"flex items-center\">\n            <img src={logoImage} alt=\"LamaMind Logo\" className=\"h-8 w-8 mr-2\" />\n          </div>\n          <button\n            className=\"text-gray-700\"\n            onClick={() => setMobileNavOpen(!mobileNavOpen)}\n          >\n            <span className=\"material-icons\">menu</span>\n          </button>\n        </div>\n      </div>\n      \n      {/* Mobile Navigation Overlay */}\n      {mobileNavOpen && (\n        <div \n          className=\"md:hidden fixed inset-0 bg-black bg-opacity-50 z-20\"\n          onClick={() => setMobileNavOpen(false)}\n        >\n          <div \n            className=\"bg-white w-64 h-full\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <Sidebar user={user} onLinkClick={() => setMobileNavOpen(false)} />\n          </div>\n        </div>\n      )}\n      \n      {/* Main Content */}\n      <main className=\"flex-1 overflow-y-auto pt-16 md:pt-0\">\n        <div className=\"py-6 mx-auto px-4 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n      \n      {/* Mobile Nav */}\n      <MobileNav />\n    </div>\n  );\n}\n"}