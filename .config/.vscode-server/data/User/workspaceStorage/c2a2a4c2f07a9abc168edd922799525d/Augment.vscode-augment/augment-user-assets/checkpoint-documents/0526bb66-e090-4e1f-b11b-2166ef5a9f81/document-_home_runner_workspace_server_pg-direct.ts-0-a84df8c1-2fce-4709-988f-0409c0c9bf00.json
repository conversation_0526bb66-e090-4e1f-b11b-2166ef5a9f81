{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/pg-direct.ts"}, "originalCode": "/**\n * Direct PostgreSQL connection module for authentication\n * This provides a more reliable connection for auth operations\n */\n\nimport pg from 'pg';\nimport crypto from 'crypto';\nimport util from 'util';\n\nconst scryptAsync = util.promisify(crypto.scrypt);\n\n// Create a standard pg pool\nexport const pgPool = new pg.Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: {\n    rejectUnauthorized: false // Allow self-signed certs for development\n  },\n  max: 10,\n  idleTimeoutMillis: 30000,\n  connectionTimeoutMillis: 5000\n});\n\n// Test pool and log connection status\npgPool.on('connect', client => {\n  console.log('PostgreSQL direct connection established');\n});\n\npgPool.on('error', err => {\n  console.error('PostgreSQL direct connection error:', err);\n});\n\n// Hash password in the same format as auth.ts\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString('hex');\n  const derivedKey = await scryptAsync(password, salt, 64);\n  return `${derivedKey.toString('hex')}.${salt}`;\n}\n\n// Verify a password against stored hash\nexport async function verifyPassword(password: string, storedHash: string): Promise<boolean> {\n  console.log(`[AUTH] Verifying password against stored hash: ${storedHash.substring(0, 10)}...`);\n  \n  const [hash, salt] = storedHash.split('.');\n  if (!hash || !salt) {\n    console.log(`[AUTH] Invalid hash format, missing hash or salt`);\n    return false;\n  }\n  \n  try {\n    // Using scrypt directly to match exactly how we hash passwords\n    return new Promise((resolve, reject) => {\n      crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n        if (err) {\n          console.error(`[AUTH] Error during password verification:`, err);\n          return reject(err);\n        }\n        \n        const computedHash = derivedKey.toString('hex');\n        const match = computedHash === hash;\n        \n        console.log(`[AUTH] Password verification result: ${match ? 'match' : 'no match'}`);\n        if (!match) {\n          console.log(`[AUTH] Expected hash length: ${hash.length}, Computed hash length: ${computedHash.length}`);\n        }\n        \n        resolve(match);\n      });\n    });\n  } catch (error) {\n    console.error(`[AUTH] Unexpected error during password verification:`, error);\n    return false;\n  }\n}\n\n// Get user by username for authentication\nexport async function getUserByUsername(username: string) {\n  try {\n    const result = await pgPool.query(\n      'SELECT * FROM users WHERE username = $1',\n      [username]\n    );\n    \n    return result.rows[0] || null;\n  } catch (error) {\n    console.error('Error fetching user by username:', error);\n    return null;\n  }\n}\n\n// Direct authentication function\nexport async function authenticateUser(username: string, password: string) {\n  try {\n    // 1. Get user\n    const user = await getUserByUsername(username);\n    \n    if (!user) {\n      console.log(`[AUTH-DIRECT] User not found: ${username}`);\n      return null;\n    }\n    \n    // 2. Verify password\n    const isValid = await verifyPassword(password, user.password);\n    \n    if (!isValid) {\n      console.log(`[AUTH-DIRECT] Password invalid for user: ${username}`);\n      return null;\n    }\n    \n    console.log(`[AUTH-DIRECT] Authentication successful for user: ${username}`);\n    \n    // 3. Return user without password\n    const { password: _, ...userWithoutPassword } = user;\n    return userWithoutPassword;\n  } catch (error) {\n    console.error('[AUTH-DIRECT] Authentication error:', error);\n    return null;\n  }\n}", "modifiedCode": "/**\n * Direct PostgreSQL connection module for authentication\n * This provides a more reliable connection for auth operations\n */\n\nimport pg from 'pg';\nimport crypto from 'crypto';\nimport util from 'util';\n\nconst scryptAsync = util.promisify(crypto.scrypt);\n\n// Create a standard pg pool\nexport const pgPool = new pg.Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: {\n    rejectUnauthorized: false // Allow self-signed certs for development\n  },\n  max: 10,\n  idleTimeoutMillis: 30000,\n  connectionTimeoutMillis: 5000\n});\n\n// Test pool and log connection status\npgPool.on('connect', client => {\n  console.log('PostgreSQL direct connection established');\n});\n\npgPool.on('error', err => {\n  console.error('PostgreSQL direct connection error:', err);\n});\n\n// Hash password in the same format as auth.ts\nexport async function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString('hex');\n  const derivedKey = await scryptAsync(password, salt, 64);\n  return `${derivedKey.toString('hex')}.${salt}`;\n}\n\n// Verify a password against stored hash\nexport async function verifyPassword(password: string, storedHash: string): Promise<boolean> {\n  console.log(`[AUTH] Verifying password against stored hash: ${storedHash.substring(0, 10)}...`);\n  \n  const [hash, salt] = storedHash.split('.');\n  if (!hash || !salt) {\n    console.log(`[AUTH] Invalid hash format, missing hash or salt`);\n    return false;\n  }\n  \n  try {\n    // Using scrypt directly to match exactly how we hash passwords\n    return new Promise((resolve, reject) => {\n      crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n        if (err) {\n          console.error(`[AUTH] Error during password verification:`, err);\n          return reject(err);\n        }\n        \n        const computedHash = derivedKey.toString('hex');\n        const match = computedHash === hash;\n        \n        console.log(`[AUTH] Password verification result: ${match ? 'match' : 'no match'}`);\n        if (!match) {\n          console.log(`[AUTH] Expected hash length: ${hash.length}, Computed hash length: ${computedHash.length}`);\n        }\n        \n        resolve(match);\n      });\n    });\n  } catch (error) {\n    console.error(`[AUTH] Unexpected error during password verification:`, error);\n    return false;\n  }\n}\n\n// Get user by username for authentication\nexport async function getUserByUsername(username: string) {\n  try {\n    const result = await pgPool.query(\n      'SELECT * FROM users WHERE username = $1',\n      [username]\n    );\n    \n    return result.rows[0] || null;\n  } catch (error) {\n    console.error('Error fetching user by username:', error);\n    return null;\n  }\n}\n\n// Direct authentication function\nexport async function authenticateUser(username: string, password: string) {\n  try {\n    // 1. Get user\n    const user = await getUserByUsername(username);\n    \n    if (!user) {\n      console.log(`[AUTH-DIRECT] User not found: ${username}`);\n      return null;\n    }\n    \n    // 2. Verify password\n    const isValid = await verifyPassword(password, user.password);\n    \n    if (!isValid) {\n      console.log(`[AUTH-DIRECT] Password invalid for user: ${username}`);\n      return null;\n    }\n    \n    console.log(`[AUTH-DIRECT] Authentication successful for user: ${username}`);\n    \n    // 3. Return user without password\n    const { password: _, ...userWithoutPassword } = user;\n    return userWithoutPassword;\n  } catch (error) {\n    console.error('[AUTH-DIRECT] Authentication error:', error);\n    return null;\n  }\n}"}