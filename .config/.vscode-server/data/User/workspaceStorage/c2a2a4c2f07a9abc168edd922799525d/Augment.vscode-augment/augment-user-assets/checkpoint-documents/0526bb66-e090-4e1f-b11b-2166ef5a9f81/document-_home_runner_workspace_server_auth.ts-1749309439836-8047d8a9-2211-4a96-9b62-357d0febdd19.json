{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth.ts"}, "originalCode": "import passport from \"passport\";\nimport { Strategy as LocalStrategy } from \"passport-local\";\nimport { Express } from \"express\";\nimport session from \"express-session\";\nimport { scrypt, randomBytes, timingSafeEqual } from \"crypto\";\nimport { promisify } from \"util\";\nimport { storage } from \"./storage\";\nimport { User as SelectUser } from \"@shared/schema\";\n\ndeclare global {\n  namespace Express {\n    interface User extends SelectUser {}\n  }\n}\n\nconst scryptAsync = promisify(scrypt);\n\nexport async function hashPassword(password: string) {\n  const salt = randomBytes(16).toString(\"hex\");\n  const buf = (await scryptAsync(password, salt, 64)) as Buffer;\n  return `${buf.toString(\"hex\")}.${salt}`;\n}\n\nexport async function comparePasswords(supplied: string, stored: string) {\n  const [hashed, salt] = stored.split(\".\");\n  const hashedBuf = Buffer.from(hashed, \"hex\");\n  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;\n  return timingSafeEqual(hashedBuf, suppliedBuf);\n}\n\nexport function setupAuth(app: Express) {\n  const sessionSettings: session.SessionOptions = {\n    secret: process.env.SESSION_SECRET || \"therapy-notes-ai-secret\",\n    resave: false,\n    saveUninitialized: true,\n    cookie: {\n      maxAge: 24 * 60 * 60 * 1000, // 24 hours\n      secure: false, // TODO: Set to true in production with HTTPS\n      httpOnly: true\n    },\n    name: 'therapy-ai-sid'\n  };\n\n  app.set(\"trust proxy\", 1);\n  app.use(session(sessionSettings));\n  app.use(passport.initialize());\n  app.use(passport.session());\n\n  passport.use(\n    new LocalStrategy(async (username, password, done) => {\n      try {\n        console.log(`[AUTH] Login attempt: ${username}`);\n        const user = await storage.getUserByUsername(username);\n        \n        if (!user) {\n          console.log(`[AUTH] User not found: ${username}`);\n          return done(null, false, { message: 'User not found' });\n        }\n        \n        console.log(`[AUTH] User found: ${user.username} (ID: ${user.id})`);\n        const passwordMatch = await comparePasswords(password, user.password);\n        \n        if (!passwordMatch) {\n          console.log(`[AUTH] Password mismatch for user: ${username}`);\n          return done(null, false, { message: 'Invalid password' });\n        }\n        \n        console.log(`[AUTH] Login successful: ${username} (ID: ${user.id})`);\n        return done(null, user);\n      } catch (err) {\n        console.error(`[AUTH] Error during login:`, err);\n        return done(err);\n      }\n    }),\n  );\n\n  passport.serializeUser((user, done) => done(null, user.id));\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      const user = await storage.getUser(id);\n      done(null, user);\n    } catch (err) {\n      done(err);\n    }\n  });\n\n  app.post(\"/api/register\", async (req, res, next) => {\n    try {\n      const { invitationCode, ...userData } = req.body;\n      \n      // Prevent admin self-registration\n      if (userData.userRole === 'admin') {\n        return res.status(403).json({ message: \"Admin accounts can only be created by existing administrators\" });\n      }\n      \n      // Check if username already exists\n      const existingUser = await storage.getUserByUsername(userData.username);\n      if (existingUser) {\n        return res.status(400).json({ message: \"Username already exists\" });\n      }\n      \n      // Validate invitation code\n      if (!invitationCode) {\n        return res.status(400).json({ message: \"Invitation code is required\" });\n      }\n      \n      const code = await storage.getInvitationCodeByCode(invitationCode);\n      if (!code) {\n        return res.status(400).json({ message: \"Invalid invitation code\" });\n      }\n      \n      if (code.used) {\n        return res.status(400).json({ message: \"Invitation code has already been used\" });\n      }\n      \n      // Check if user role matches invitation code type\n      if (code.type !== userData.userRole) {\n        return res.status(400).json({ \n          message: `This invitation code is for ${code.type} accounts only` \n        });\n      }\n      \n      // Create the user\n      const user = await storage.createUser({\n        ...userData,\n        password: await hashPassword(userData.password),\n      });\n      \n      // Mark invitation code as used\n      await storage.markInvitationCodeAsUsed(invitationCode, user.id);\n      \n      // If user type is client, create a client record\n      if (userData.userRole === \"client\") {\n        await storage.createClient({\n          userId: user.id,\n          name: userData.name,\n          status: \"stable\"\n        });\n      }\n\n      req.login(user, (err) => {\n        if (err) return next(err);\n        res.status(201).json(user);\n      });\n    } catch (err) {\n      next(err);\n    }\n  });\n\n  app.post(\"/api/login\", (req, res, next) => {\n    console.log(`[AUTH] Login attempt at ${new Date().toISOString()}`);\n    console.log(`[AUTH] Login request body:`, req.body);\n    \n    passport.authenticate(\"local\", (err: Error | null, user: any, info: any) => {\n      if (err) {\n        console.error(`[AUTH] Login error:`, err);\n        return next(err);\n      }\n      \n      if (!user) {\n        console.log(`[AUTH] Authentication failed:`, info?.message || 'Invalid credentials');\n        return res.status(401).json({ \n          message: info?.message || \"Invalid credentials\",\n          time: new Date().toISOString()\n        });\n      }\n      \n      console.log(`[AUTH] User authenticated successfully:`, {\n        id: user.id,\n        username: user.username,\n        role: user.userRole\n      });\n      \n      req.login(user, (err: Error | null) => {\n        if (err) {\n          console.error(`[AUTH] Session creation error:`, err);\n          return next(err);\n        }\n        \n        console.log(`[AUTH] Login successful, session created`);\n        console.log(`[AUTH] Session ID:`, req.sessionID);\n        \n        // Return user info without password\n        const { password, ...safeUser } = user;\n        res.status(200).json(safeUser);\n      });\n    })(req, res, next);\n  });\n\n  app.post(\"/api/logout\", (req, res, next) => {\n    req.logout((err: Error | null) => {\n      if (err) return next(err);\n      res.sendStatus(200);\n    });\n  });\n\n  app.get(\"/api/user\", (req, res) => {\n    if (!req.isAuthenticated()) return res.status(401).json({ message: \"Not authenticated\" });\n    res.json(req.user);\n  });\n}", "modifiedCode": "import passport from \"passport\";\nimport { Strategy as LocalStrategy } from \"passport-local\";\nimport { Express } from \"express\";\nimport session from \"express-session\";\nimport { scrypt, randomBytes, timingSafeEqual } from \"crypto\";\nimport { promisify } from \"util\";\nimport { storage, pool } from \"./storage\";\nimport { User as SelectUser } from \"@shared/schema\";\nimport connectPgSimple from \"connect-pg-simple\";\n\ndeclare global {\n  namespace Express {\n    interface User extends SelectUser {}\n  }\n}\n\nconst scryptAsync = promisify(scrypt);\n\nexport async function hashPassword(password: string) {\n  const salt = randomBytes(16).toString(\"hex\");\n  const buf = (await scryptAsync(password, salt, 64)) as Buffer;\n  return `${buf.toString(\"hex\")}.${salt}`;\n}\n\nexport async function comparePasswords(supplied: string, stored: string) {\n  const [hashed, salt] = stored.split(\".\");\n  const hashedBuf = Buffer.from(hashed, \"hex\");\n  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;\n  return timingSafeEqual(hashedBuf, suppliedBuf);\n}\n\nexport function setupAuth(app: Express) {\n  const sessionSettings: session.SessionOptions = {\n    secret: process.env.SESSION_SECRET || \"therapy-notes-ai-secret\",\n    resave: false,\n    saveUninitialized: true,\n    cookie: {\n      maxAge: 24 * 60 * 60 * 1000, // 24 hours\n      secure: false, // TODO: Set to true in production with HTTPS\n      httpOnly: true\n    },\n    name: 'therapy-ai-sid'\n  };\n\n  app.set(\"trust proxy\", 1);\n  app.use(session(sessionSettings));\n  app.use(passport.initialize());\n  app.use(passport.session());\n\n  passport.use(\n    new LocalStrategy(async (username, password, done) => {\n      try {\n        console.log(`[AUTH] Login attempt: ${username}`);\n        const user = await storage.getUserByUsername(username);\n        \n        if (!user) {\n          console.log(`[AUTH] User not found: ${username}`);\n          return done(null, false, { message: 'User not found' });\n        }\n        \n        console.log(`[AUTH] User found: ${user.username} (ID: ${user.id})`);\n        const passwordMatch = await comparePasswords(password, user.password);\n        \n        if (!passwordMatch) {\n          console.log(`[AUTH] Password mismatch for user: ${username}`);\n          return done(null, false, { message: 'Invalid password' });\n        }\n        \n        console.log(`[AUTH] Login successful: ${username} (ID: ${user.id})`);\n        return done(null, user);\n      } catch (err) {\n        console.error(`[AUTH] Error during login:`, err);\n        return done(err);\n      }\n    }),\n  );\n\n  passport.serializeUser((user, done) => done(null, user.id));\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      const user = await storage.getUser(id);\n      done(null, user);\n    } catch (err) {\n      done(err);\n    }\n  });\n\n  app.post(\"/api/register\", async (req, res, next) => {\n    try {\n      const { invitationCode, ...userData } = req.body;\n      \n      // Prevent admin self-registration\n      if (userData.userRole === 'admin') {\n        return res.status(403).json({ message: \"Admin accounts can only be created by existing administrators\" });\n      }\n      \n      // Check if username already exists\n      const existingUser = await storage.getUserByUsername(userData.username);\n      if (existingUser) {\n        return res.status(400).json({ message: \"Username already exists\" });\n      }\n      \n      // Validate invitation code\n      if (!invitationCode) {\n        return res.status(400).json({ message: \"Invitation code is required\" });\n      }\n      \n      const code = await storage.getInvitationCodeByCode(invitationCode);\n      if (!code) {\n        return res.status(400).json({ message: \"Invalid invitation code\" });\n      }\n      \n      if (code.used) {\n        return res.status(400).json({ message: \"Invitation code has already been used\" });\n      }\n      \n      // Check if user role matches invitation code type\n      if (code.type !== userData.userRole) {\n        return res.status(400).json({ \n          message: `This invitation code is for ${code.type} accounts only` \n        });\n      }\n      \n      // Create the user\n      const user = await storage.createUser({\n        ...userData,\n        password: await hashPassword(userData.password),\n      });\n      \n      // Mark invitation code as used\n      await storage.markInvitationCodeAsUsed(invitationCode, user.id);\n      \n      // If user type is client, create a client record\n      if (userData.userRole === \"client\") {\n        await storage.createClient({\n          userId: user.id,\n          name: userData.name,\n          status: \"stable\"\n        });\n      }\n\n      req.login(user, (err) => {\n        if (err) return next(err);\n        res.status(201).json(user);\n      });\n    } catch (err) {\n      next(err);\n    }\n  });\n\n  app.post(\"/api/login\", (req, res, next) => {\n    console.log(`[AUTH] Login attempt at ${new Date().toISOString()}`);\n    console.log(`[AUTH] Login request body:`, req.body);\n    \n    passport.authenticate(\"local\", (err: Error | null, user: any, info: any) => {\n      if (err) {\n        console.error(`[AUTH] Login error:`, err);\n        return next(err);\n      }\n      \n      if (!user) {\n        console.log(`[AUTH] Authentication failed:`, info?.message || 'Invalid credentials');\n        return res.status(401).json({ \n          message: info?.message || \"Invalid credentials\",\n          time: new Date().toISOString()\n        });\n      }\n      \n      console.log(`[AUTH] User authenticated successfully:`, {\n        id: user.id,\n        username: user.username,\n        role: user.userRole\n      });\n      \n      req.login(user, (err: Error | null) => {\n        if (err) {\n          console.error(`[AUTH] Session creation error:`, err);\n          return next(err);\n        }\n        \n        console.log(`[AUTH] Login successful, session created`);\n        console.log(`[AUTH] Session ID:`, req.sessionID);\n        \n        // Return user info without password\n        const { password, ...safeUser } = user;\n        res.status(200).json(safeUser);\n      });\n    })(req, res, next);\n  });\n\n  app.post(\"/api/logout\", (req, res, next) => {\n    req.logout((err: Error | null) => {\n      if (err) return next(err);\n      res.sendStatus(200);\n    });\n  });\n\n  app.get(\"/api/user\", (req, res) => {\n    if (!req.isAuthenticated()) return res.status(401).json({ message: \"Not authenticated\" });\n    res.json(req.user);\n  });\n}"}