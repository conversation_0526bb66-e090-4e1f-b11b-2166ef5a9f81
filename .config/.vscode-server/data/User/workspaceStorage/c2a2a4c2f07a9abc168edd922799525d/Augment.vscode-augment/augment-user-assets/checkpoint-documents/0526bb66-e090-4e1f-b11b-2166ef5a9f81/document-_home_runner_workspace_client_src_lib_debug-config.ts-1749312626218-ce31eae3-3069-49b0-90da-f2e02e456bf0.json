{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/debug-config.ts"}, "originalCode": "// Global debug configuration for the application\ninterface DebugConfig {\n  enabled: boolean;\n  realTimeConversation: boolean;\n  audioProcessing: boolean;\n  websocketEvents: boolean;\n  sessionManagement: boolean;\n}\n\nclass DebugManager {\n  private config: DebugConfig = {\n    enabled: false, // Set to false by default\n    realTimeConversation: false,\n    audioProcessing: false,\n    websocketEvents: false,\n    sessionManagement: false,\n  };\n\n  // Enable/disable all debug logging\n  setEnabled(enabled: boolean) {\n    this.config.enabled = enabled;\n    this.config.realTimeConversation = enabled;\n    this.config.audioProcessing = enabled;\n    this.config.websocketEvents = enabled;\n    this.config.sessionManagement = enabled;\n  }\n\n  // Enable/disable specific debug categories\n  setCategory(category: keyof Omit<DebugConfig, 'enabled'>, enabled: boolean) {\n    this.config[category] = enabled;\n  }\n\n  // Check if debug is enabled globally\n  get isEnabled(): boolean {\n    return this.config.enabled;\n  }\n\n  // Check specific debug categories\n  get realTimeConversation(): boolean {\n    return this.config.enabled && this.config.realTimeConversation;\n  }\n\n  get audioProcessing(): boolean {\n    return this.config.enabled && this.config.audioProcessing;\n  }\n\n  get websocketEvents(): boolean {\n    return this.config.enabled && this.config.websocketEvents;\n  }\n\n  get sessionManagement(): boolean {\n    return this.config.enabled && this.config.sessionManagement;\n  }\n\n  // Debug logging methods\n  log(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {\n    if (this.config.enabled && this.config[category]) {\n      console.log(`[DEBUG:${category.toUpperCase()}]`, message, ...args);\n    }\n  }\n\n  error(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {\n    if (this.config.enabled && this.config[category]) {\n      console.error(`[DEBUG:${category.toUpperCase()}]`, message, ...args);\n    }\n  }\n\n  warn(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {\n    if (this.config.enabled && this.config[category]) {\n      console.warn(`[DEBUG:${category.toUpperCase()}]`, message, ...args);\n    }\n  }\n\n  // Get current config for UI display\n  getConfig(): DebugConfig {\n    return { ...this.config };\n  }\n}\n\n// Export singleton instance\nexport const debugManager = new DebugManager();\n\n// Convenience function for quick debug logging\nexport const debugLog = (category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) => {\n  debugManager.log(category, message, ...args);\n};\n\nexport default debugManager;\n", "modifiedCode": "// Global debug configuration for the application\ninterface DebugConfig {\n  enabled: boolean;\n  realTimeConversation: boolean;\n  audioProcessing: boolean;\n  websocketEvents: boolean;\n  sessionManagement: boolean;\n}\n\nclass DebugManager {\n  private config: DebugConfig = {\n    enabled: false, // Set to false by default\n    realTimeConversation: false,\n    audioProcessing: false,\n    websocketEvents: false,\n    sessionManagement: false,\n  };\n\n  // Enable/disable all debug logging\n  setEnabled(enabled: boolean) {\n    this.config.enabled = enabled;\n    this.config.realTimeConversation = enabled;\n    this.config.audioProcessing = enabled;\n    this.config.websocketEvents = enabled;\n    this.config.sessionManagement = enabled;\n\n    // Store in localStorage for persistence\n    localStorage.setItem('debug-enabled', enabled.toString());\n\n    // Override console methods when debug is disabled\n    if (!enabled) {\n      this.suppressConsoleLogging();\n    } else {\n      this.restoreConsoleLogging();\n    }\n  }\n\n  // Enable/disable specific debug categories\n  setCategory(category: keyof Omit<DebugConfig, 'enabled'>, enabled: boolean) {\n    this.config[category] = enabled;\n  }\n\n  // Check if debug is enabled globally\n  get isEnabled(): boolean {\n    return this.config.enabled;\n  }\n\n  // Check specific debug categories\n  get realTimeConversation(): boolean {\n    return this.config.enabled && this.config.realTimeConversation;\n  }\n\n  get audioProcessing(): boolean {\n    return this.config.enabled && this.config.audioProcessing;\n  }\n\n  get websocketEvents(): boolean {\n    return this.config.enabled && this.config.websocketEvents;\n  }\n\n  get sessionManagement(): boolean {\n    return this.config.enabled && this.config.sessionManagement;\n  }\n\n  // Debug logging methods\n  log(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {\n    if (this.config.enabled && this.config[category]) {\n      console.log(`[DEBUG:${category.toUpperCase()}]`, message, ...args);\n    }\n  }\n\n  error(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {\n    if (this.config.enabled && this.config[category]) {\n      console.error(`[DEBUG:${category.toUpperCase()}]`, message, ...args);\n    }\n  }\n\n  warn(category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) {\n    if (this.config.enabled && this.config[category]) {\n      console.warn(`[DEBUG:${category.toUpperCase()}]`, message, ...args);\n    }\n  }\n\n  // Get current config for UI display\n  getConfig(): DebugConfig {\n    return { ...this.config };\n  }\n}\n\n// Export singleton instance\nexport const debugManager = new DebugManager();\n\n// Convenience function for quick debug logging\nexport const debugLog = (category: keyof Omit<DebugConfig, 'enabled'>, message: string, ...args: any[]) => {\n  debugManager.log(category, message, ...args);\n};\n\nexport default debugManager;\n"}