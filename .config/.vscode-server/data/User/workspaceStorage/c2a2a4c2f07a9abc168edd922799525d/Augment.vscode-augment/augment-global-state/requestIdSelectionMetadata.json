[["3bcb702e-98fc-4a10-b60c-8bf0b39b5d50", {"value": {"selectedCode": "", "prefix": "import { WebSocket } from 'ws';\nimport { EventEmitter } from 'events';\n\ninterface RealtimeProxyConfig {\n  apiKey: string;\n  model: string;\n", "suffix": "  voice?: string;\n  instructions?: string;\n  temperature?: number;\n}\n\nexport class OpenAIRealtimeProxy extends EventEmitter {\n  private openaiWs: WebSocket | null = null;\n  private clientWs: WebSocket | null = null;\n  private config: RealtimeProxyConfig;\n  private isConnected = false;\n  private sessionId: string | null = null;\n  private isDisconnecting = false;\n  public onTranscriptUpdate?: (speaker: 'user' | 'ai', text: string) => void;\n  private currentAITranscript = '';\n  private currentUserTranscript = '';\n  private initialGreetingSent = false;\n  private hasActiveResponse = false;\n\n  constructor(config: RealtimeProxyConfig) {\n    super();\n    this.config = config;\n  }\n\n  async connect(clientWebSocket: WebSocket): Promise<void> {\n    this.clientWs = clientWebSocket;\n\n    try {\n      // Connect to OpenAI's Realtime API\n      const openaiUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model}`;\n      \n      this.openaiWs = new WebSocket(openaiUrl, {\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'OpenAI-Beta': 'realtime=v1'\n        }\n      });\n\n      this.setupOpenAIEventHandlers();\n      // Don't set up client event handlers here - let the main handler route messages\n\n      // Wait for OpenAI connection\n      await new Promise<void>((resolve, reject) => {\n        this.openaiWs!.once('open', () => {\n          console.log('Connected to OpenAI Realtime API');\n          this.isConnected = true;\n          this.initializeSession();\n          resolve();\n        });\n\n        this.openaiWs!.once('error', (error) => {\n          console.error('Failed to connect to OpenAI Realtime API:', error);\n          reject(error);\n        });\n      });\n\n    } catch (error) {\n      console.error('Error connecting to OpenAI Realtime API:', error);\n      throw error;\n    }\n  }\n\n  private setupOpenAIEventHandlers(): void {\n    if (!this.openaiWs) return;\n\n    this.openaiWs.on('message', (data) => {\n      try {\n        const message = JSON.parse(data.toString());\n        console.log('OpenAI → Client:', message.type);\n\n        // Handle specific message types\n        switch (message.type) {\n          case 'session.created':\n            this.sessionId = message.session.id;\n            console.log('OpenAI session created:', this.sessionId);\n            this.sendToClient(message); // Forward to client\n            break;\n          \n                  case 'session.updated':\n          console.log('OpenAI session updated');\n          \n          // Only trigger initial greeting once per session\n          if (!this.initialGreetingSent) {\n            this.initialGreetingSent = true;\n            \n            // Notify client that we're ready\n            this.sendToClient({\n              type: 'ready',\n              message: 'OpenAI Realtime session initialized'\n            });\n            \n            // Trigger initial AI greeting only once\n            console.log('Triggering initial AI greeting...');\n            this.sendToOpenAI({\n              type: 'conversation.item.create',\n              item: {\n                type: 'message',\n                role: 'system',\n                content: [\n                  {\n                    type: 'input_text',\n                    text: 'The user has just started a voice therapy session. Please greet them warmly and invite them to share what\\'s on their mind today. Keep it natural and conversational.'\n                  }\n                ]\n              }\n            });\n            \n            // Create response to trigger AI to speak\n            this.sendToOpenAI({\n              type: 'response.create'\n            });\n          } else {\n            console.log('Session configuration updated (VAD switch) - no new greeting needed');\n          }\n          \n          // Always forward session.updated to client\n          this.sendToClient(message);\n          break;\n\n          case 'conversation.item.input_audio_transcription.completed':\n            // User speech transcription - capture for summary\n            if (message.transcript && this.onTranscriptUpdate) {\n              console.log('Capturing user transcript:', message.transcript);\n              this.onTranscriptUpdate('user', message.transcript);\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.delta':\n            // Accumulate AI transcript\n            if (message.delta) {\n              this.currentAITranscript += message.delta;\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.done':\n            // AI speech transcription complete - capture for summary  \n            if (this.currentAITranscript && this.onTranscriptUpdate) {\n              console.log('Capturing AI transcript:', this.currentAITranscript);\n              this.onTranscriptUpdate('ai', this.currentAITranscript);\n              this.currentAITranscript = ''; // Reset for next response\n            }\n            this.sendToClient(message);\n            break;\n\n          case 'input_audio_buffer.speech_started':\n          case 'input_audio_buffer.speech_stopped':\n          case 'conversation.item.created':\n          case 'response.created':\n            // Track that we have an active response\n            this.hasActiveResponse = true;\n            this.sendToClient(message);\n            break;\n\n          case 'response.output_item.added':\n          case 'response.content_part.added':\n          case 'response.audio.delta':\n          case 'response.audio.done':\n          case 'response.text.delta':\n          case 'response.text.done':\n          case 'response.output_item.done':\n            // Forward these events directly to the client\n            this.sendToClient(message);\n            break;\n\n          case 'response.done':\n            // Response is complete, clear the active flag\n            this.hasActiveResponse = false;\n            this.sendToClient(message);\n            break;\n\n          case 'rate_limits.updated':\n            // Rate limit information - just log and forward\n            console.log('OpenAI rate limits updated');\n            this.sendToClient(message);\n            break;\n            \n          case 'response.content_part.done':\n            // Content part completion - forward to client\n            this.sendToClient(message);\n            break;\n\n          case 'error':\n            console.error('OpenAI API error:', message);\n            this.sendToClient({\n              type: 'error',\n              message: message.error?.message || 'OpenAI API error'\n            });\n            break;\n\n          default:\n            console.log('Unhandled OpenAI message type:', message.type);\n            // Forward unknown messages to client for debugging\n            this.sendToClient(message);\n        }\n      } catch (error) {\n        console.error('Error parsing OpenAI message:', error);\n      }\n    });\n\n    this.openaiWs.on('close', (code, reason) => {\n      console.log('OpenAI connection closed:', code, reason.toString());\n      this.isConnected = false;\n      \n      // Only send error message if this wasn't an intentional disconnect\n      if (!this.isDisconnecting) {\n        this.sendToClient({\n          type: 'error',\n          message: 'OpenAI connection closed'\n        });\n      }\n    });\n\n    this.openaiWs.on('error', (error) => {\n      console.error('OpenAI WebSocket error:', error);\n      this.sendToClient({\n        type: 'error',\n        message: 'OpenAI connection error'\n      });\n    });\n  }\n\n  private initializeSession(): void {\n    if (!this.openaiWs || !this.isConnected) return;\n\n    // Configure session with manual turn detection (client-side VAD)\n    const sessionConfig = {\n      type: 'session.update',\n      session: {\n        modalities: ['text', 'audio'],\n        instructions: this.config.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n        voice: this.config.voice || 'shimmer',\n        input_audio_format: 'pcm16',\n        output_audio_format: 'pcm16',\n        input_audio_transcription: {\n          model: 'whisper-1'\n        },\n        turn_detection: null, // Disable automatic turn detection - use client-side VAD\n        temperature: this.config.temperature || 0.7,\n        max_response_output_tokens: 1000\n      }\n    };\n\n    console.log('🎯 Configuring session with CLIENT-SIDE VAD (server VAD disabled)...');\n    console.log('📊 Turn Detection: DISABLED (manual commits from client)');\n    this.sendToOpenAI(sessionConfig);\n\n    console.log('✅ Client-side VAD configured - speech detection handled by browser');\n  }\n\n  private sendToOpenAI(message: any): void {\n    if (this.openaiWs && this.isConnected) {\n      this.openaiWs.send(JSON.stringify(message));\n    } else {\n      console.warn('Cannot send to OpenAI: not connected');\n    }\n  }\n\n  private sendToClient(message: any): void {\n    if (this.clientWs && this.clientWs.readyState === WebSocket.OPEN) {\n      this.clientWs.send(JSON.stringify(message));\n    }\n  }\n\n  private convertAudioFormat(audioData: number[]): string {\n    // Convert Int16Array audio data to base64 PCM16\n    const int16Array = new Int16Array(audioData);\n    const buffer = Buffer.from(int16Array.buffer);\n    return buffer.toString('base64');\n  }\n\n  private convertAudioArrayToBase64(audioData: number[], format?: string): string {\n    if (format === 'int16') {\n      // Already in correct format\n      const int16Array = new Int16Array(audioData);\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    } else {\n      // Assume float32, convert to int16\n      const int16Array = new Int16Array(audioData.length);\n      for (let i = 0; i < audioData.length; i++) {\n        const sample = Math.max(-1, Math.min(1, audioData[i]));\n        int16Array[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n      }\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    }\n  }\n\n  public disconnect(): void {\n    this.isDisconnecting = true;\n\n    if (this.openaiWs) {\n      this.openaiWs.close();\n      this.openaiWs = null;\n    }\n    this.isConnected = false;\n    this.sessionId = null;\n    this.hasActiveResponse = false;\n  }\n\n  public getSessionId(): string | null {\n    return this.sessionId;\n  }\n\n  public isSessionActive(): boolean {\n    return this.isConnected && this.sessionId !== null;\n  }\n  \n  // Handle messages routed from the main websocket handler\n  public handleClientMessage(message: any): void {\n    try {\n      console.log('Realtime proxy handling message:', message.type);\n\n      // Handle client messages\n      switch (message.type) {\n        case 'start':\n          // Client wants to start - we already initialized, just confirm\n          this.sendToClient({\n            type: 'ready',\n            message: 'Session already active'\n          });\n          break;\n\n        case 'generate_test_audio':\n          // Generate test audio for debugging\n          this.generateTestAudio(message.text || \"This is a test of the voice system. Please respond when ready.\");\n          break;\n\n        case 'test_round_trip':\n          // Test complete round-trip audio flow\n          this.testRoundTripAudio();\n          break;\n\n        case 'input_audio_buffer.append':\n          // Forward audio data to OpenAI with enhanced debugging\n          const audioData = message.audio_data || message.audio;\n          if (audioData) {\n            console.log(`📤 Forwarding audio to OpenAI: ${typeof audioData} (${typeof audioData === 'string' ? audioData.length + ' chars' : 'non-string'})`);\n            \n            // Enhanced audio validation and debugging\n            if (typeof audioData === 'string' && audioData.length > 0) {\n              try {\n                const decoded = Buffer.from(audioData, 'base64');\n                const samples = decoded.length / 2; // PCM16 = 2 bytes per sample\n                console.log(`📊 Audio info: ${decoded.length} bytes, ${samples} samples`);\n                \n                // Analyze audio content for speech-like characteristics\n                const int16Array = new Int16Array(decoded.buffer);\n                let energy = 0;\n                let peakCount = 0;\n                let avgAmplitude = 0;\n                \n                for (let i = 0; i < int16Array.length; i++) {\n                  const sample = Math.abs(int16Array[i]);\n                  energy += sample;\n                  avgAmplitude += sample;\n                  \n                  // Count peaks (potential speech indicators)\n                  if (sample > 1000) {\n                    peakCount++;\n                  }\n                }\n                \n                const avgEnergy = energy / int16Array.length;\n                avgAmplitude = avgAmplitude / int16Array.length;\n                const peakRatio = peakCount / int16Array.length;\n                \n                console.log(`🔊 Audio analysis: avg=${avgEnergy.toFixed(2)}, amp=${avgAmplitude.toFixed(2)}, peaks=${(peakRatio*100).toFixed(2)}%`);\n                \n                // Determine if this looks like speech\n                const looksLikeSpeech = avgEnergy > 100 && peakRatio > 0.01;\n                console.log(`🎯 Speech likelihood: ${looksLikeSpeech ? 'HIGH' : 'LOW'} (energy=${avgEnergy > 100}, peaks=${peakRatio > 0.01})`);\n                \n              } catch (e) {\n                console.warn('Failed to analyze audio data:', e instanceof Error ? e.message : 'Unknown error');\n              }\n            }\n            \n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: message.audio_data ? this.convertAudioFormat(message.audio_data) : message.audio\n            });\n          } else {\n            console.warn('No audio data in input_audio_buffer.append message');\n          }\n          break;\n\n        case 'input_audio_buffer.commit':\n          // Forward commit to OpenAI\n          this.sendToOpenAI(message);\n\n          // Since we're using manual VAD, automatically create a response after commit\n          // But only if there's no active response already\n          setTimeout(() => {\n            if (this.openaiWs && this.isConnected && !this.hasActiveResponse) {\n              console.log('🤖 Auto-creating response after manual audio commit');\n              this.sendToOpenAI({\n                type: 'response.create'\n              });\n            } else if (this.hasActiveResponse) {\n              console.log('⏸️ Skipping auto-response creation - response already active');\n            }\n          }, 100); // Small delay to ensure commit is processed\n          break;\n\n        case 'response.create':\n          // Only create response if none is active\n          if (!this.hasActiveResponse) {\n            this.sendToOpenAI(message);\n          } else {\n            console.log('⏸️ Skipping response.create - response already active');\n          }\n          break;\n\n        case 'response.cancel':\n          // Clear active response flag when cancelled\n          this.hasActiveResponse = false;\n          this.sendToOpenAI(message);\n          break;\n\n        case 'conversation.item.create':\n        case 'conversation.item.delete':\n        case 'conversation.item.truncate':\n          // Forward these commands directly to OpenAI\n          this.sendToOpenAI(message);\n          break;\n\n        case 'audio_chunk':\n          // Convert legacy audio_chunk to OpenAI format\n          if (message.audio_data && Array.isArray(message.audio_data)) {\n            const base64Audio = this.convertAudioArrayToBase64(message.audio_data, message.format);\n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: base64Audio\n            });\n          }\n          break;\n\n        case 'text':\n          // Convert text message to OpenAI conversation item\n          this.sendToOpenAI({\n            type: 'conversation.item.create',\n            item: {\n              type: 'message',\n              role: 'user',\n              content: [\n                {\n                  type: 'input_text',\n                  text: message.content\n                }\n              ]\n            }\n          });\n          // Trigger response\n          this.sendToOpenAI({\n            type: 'response.create'\n          });\n          break;\n\n        default:\n          console.log('Unhandled client message type in proxy:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling client message in proxy:', error);\n    }\n  }\n\n  // Add a method to generate test audio for debugging\n  public generateTestAudio(testPhrase: string = \"Hello, I am testing the voice system. Can you hear me?\"): void {\n    if (!this.openaiWs || !this.isConnected) {\n      console.warn('Cannot generate test audio: not connected to OpenAI');\n      return;\n    }\n\n    console.log('🎤 Generating test audio for debugging...');\n    \n    // Create a conversation item with the test phrase\n    this.sendToOpenAI({\n      type: 'conversation.item.create',\n      item: {\n        type: 'message',\n        role: 'user',\n        content: [\n          {\n            type: 'input_text',\n            text: testPhrase\n          }\n        ]\n      }\n    });\n    \n    // Trigger response\n    this.sendToOpenAI({\n      type: 'response.create'\n    });\n  }\n\n  // Add a method to test round-trip audio\n  public testRoundTripAudio(): void {\n    console.log('🔄 Starting round-trip audio test...');\n    \n    // Generate a test phrase that we can then try to \"speak\" back\n    this.generateTestAudio(\"Please say hello back to me when you're ready to test the voice system.\");\n  }\n} ", "path": "server/openai-realtime-proxy.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 2}}], ["b9966e87-894a-4cae-85ec-320819005422", {"value": {"selectedCode": "", "prefix": "import { WebSocket } from 'ws';\nimport { EventEmitter } from 'events';\n\ninterface RealtimeProxyConfig {\n  apiKey: string;\n  model: string;\n  voice?: string;\n  instructions?: string;\n  temperature?: number;\n}\n\nexport class OpenAIRealtimeProxy extends EventEmitter {\n  private openaiWs: WebSocket | null = null;\n  private clientWs: WebSocket | null = null;\n  private config: RealtimeProxyConfig;\n  private isConnected = false;\n  private sessionId: string | null = null;\n  private isDisconnecting = false;\n  public onTranscriptUpdate?: (speaker: 'user' | 'ai', text: string) => void;\n  private currentAITranscript = '';\n  private currentUserTranscript = '';\n  private initialGreetingSent = false;\n", "suffix": "  private hasActiveResponse = false;\n\n  constructor(config: RealtimeProxyConfig) {\n    super();\n    this.config = config;\n  }\n\n  async connect(clientWebSocket: WebSocket): Promise<void> {\n    this.clientWs = clientWebSocket;\n\n    try {\n      // Connect to OpenAI's Realtime API\n      const openaiUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model}`;\n      \n      this.openaiWs = new WebSocket(openaiUrl, {\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'OpenAI-Beta': 'realtime=v1'\n        }\n      });\n\n      this.setupOpenAIEventHandlers();\n      // Don't set up client event handlers here - let the main handler route messages\n\n      // Wait for OpenAI connection\n      await new Promise<void>((resolve, reject) => {\n        this.openaiWs!.once('open', () => {\n          console.log('Connected to OpenAI Realtime API');\n          this.isConnected = true;\n          this.initializeSession();\n          resolve();\n        });\n\n        this.openaiWs!.once('error', (error) => {\n          console.error('Failed to connect to OpenAI Realtime API:', error);\n          reject(error);\n        });\n      });\n\n    } catch (error) {\n      console.error('Error connecting to OpenAI Realtime API:', error);\n      throw error;\n    }\n  }\n\n  private setupOpenAIEventHandlers(): void {\n    if (!this.openaiWs) return;\n\n    this.openaiWs.on('message', (data) => {\n      try {\n        const message = JSON.parse(data.toString());\n        console.log('OpenAI → Client:', message.type);\n\n        // Handle specific message types\n        switch (message.type) {\n          case 'session.created':\n            this.sessionId = message.session.id;\n            console.log('OpenAI session created:', this.sessionId);\n            this.sendToClient(message); // Forward to client\n            break;\n          \n                  case 'session.updated':\n          console.log('OpenAI session updated');\n          \n          // Only trigger initial greeting once per session\n          if (!this.initialGreetingSent) {\n            this.initialGreetingSent = true;\n            \n            // Notify client that we're ready\n            this.sendToClient({\n              type: 'ready',\n              message: 'OpenAI Realtime session initialized'\n            });\n            \n            // Trigger initial AI greeting only once\n            console.log('Triggering initial AI greeting...');\n            this.sendToOpenAI({\n              type: 'conversation.item.create',\n              item: {\n                type: 'message',\n                role: 'system',\n                content: [\n                  {\n                    type: 'input_text',\n                    text: 'The user has just started a voice therapy session. Please greet them warmly and invite them to share what\\'s on their mind today. Keep it natural and conversational.'\n                  }\n                ]\n              }\n            });\n            \n            // Create response to trigger AI to speak\n            this.sendToOpenAI({\n              type: 'response.create'\n            });\n          } else {\n            console.log('Session configuration updated (VAD switch) - no new greeting needed');\n          }\n          \n          // Always forward session.updated to client\n          this.sendToClient(message);\n          break;\n\n          case 'conversation.item.input_audio_transcription.completed':\n            // User speech transcription - capture for summary\n            if (message.transcript && this.onTranscriptUpdate) {\n              console.log('Capturing user transcript:', message.transcript);\n              this.onTranscriptUpdate('user', message.transcript);\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.delta':\n            // Accumulate AI transcript\n            if (message.delta) {\n              this.currentAITranscript += message.delta;\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.done':\n            // AI speech transcription complete - capture for summary  \n            if (this.currentAITranscript && this.onTranscriptUpdate) {\n              console.log('Capturing AI transcript:', this.currentAITranscript);\n              this.onTranscriptUpdate('ai', this.currentAITranscript);\n              this.currentAITranscript = ''; // Reset for next response\n            }\n            this.sendToClient(message);\n            break;\n\n          case 'input_audio_buffer.speech_started':\n          case 'input_audio_buffer.speech_stopped':\n          case 'conversation.item.created':\n          case 'response.created':\n            // Track that we have an active response\n            this.hasActiveResponse = true;\n            this.sendToClient(message);\n            break;\n\n          case 'response.output_item.added':\n          case 'response.content_part.added':\n          case 'response.audio.delta':\n          case 'response.audio.done':\n          case 'response.text.delta':\n          case 'response.text.done':\n          case 'response.output_item.done':\n            // Forward these events directly to the client\n            this.sendToClient(message);\n            break;\n\n          case 'response.done':\n            // Response is complete, clear the active flag\n            this.hasActiveResponse = false;\n            this.sendToClient(message);\n            break;\n\n          case 'rate_limits.updated':\n            // Rate limit information - just log and forward\n            console.log('OpenAI rate limits updated');\n            this.sendToClient(message);\n            break;\n            \n          case 'response.content_part.done':\n            // Content part completion - forward to client\n            this.sendToClient(message);\n            break;\n\n          case 'error':\n            console.error('OpenAI API error:', message);\n            this.sendToClient({\n              type: 'error',\n              message: message.error?.message || 'OpenAI API error'\n            });\n            break;\n\n          default:\n            console.log('Unhandled OpenAI message type:', message.type);\n            // Forward unknown messages to client for debugging\n            this.sendToClient(message);\n        }\n      } catch (error) {\n        console.error('Error parsing OpenAI message:', error);\n      }\n    });\n\n    this.openaiWs.on('close', (code, reason) => {\n      console.log('OpenAI connection closed:', code, reason.toString());\n      this.isConnected = false;\n      \n      // Only send error message if this wasn't an intentional disconnect\n      if (!this.isDisconnecting) {\n        this.sendToClient({\n          type: 'error',\n          message: 'OpenAI connection closed'\n        });\n      }\n    });\n\n    this.openaiWs.on('error', (error) => {\n      console.error('OpenAI WebSocket error:', error);\n      this.sendToClient({\n        type: 'error',\n        message: 'OpenAI connection error'\n      });\n    });\n  }\n\n  private initializeSession(): void {\n    if (!this.openaiWs || !this.isConnected) return;\n\n    // Configure session with manual turn detection (client-side VAD)\n    const sessionConfig = {\n      type: 'session.update',\n      session: {\n        modalities: ['text', 'audio'],\n        instructions: this.config.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n        voice: this.config.voice || 'shimmer',\n        input_audio_format: 'pcm16',\n        output_audio_format: 'pcm16',\n        input_audio_transcription: {\n          model: 'whisper-1'\n        },\n        turn_detection: null, // Disable automatic turn detection - use client-side VAD\n        temperature: this.config.temperature || 0.7,\n        max_response_output_tokens: 1000\n      }\n    };\n\n    console.log('🎯 Configuring session with CLIENT-SIDE VAD (server VAD disabled)...');\n    console.log('📊 Turn Detection: DISABLED (manual commits from client)');\n    this.sendToOpenAI(sessionConfig);\n\n    console.log('✅ Client-side VAD configured - speech detection handled by browser');\n  }\n\n  private sendToOpenAI(message: any): void {\n    if (this.openaiWs && this.isConnected) {\n      this.openaiWs.send(JSON.stringify(message));\n    } else {\n      console.warn('Cannot send to OpenAI: not connected');\n    }\n  }\n\n  private sendToClient(message: any): void {\n    if (this.clientWs && this.clientWs.readyState === WebSocket.OPEN) {\n      this.clientWs.send(JSON.stringify(message));\n    }\n  }\n\n  private convertAudioFormat(audioData: number[]): string {\n    // Convert Int16Array audio data to base64 PCM16\n    const int16Array = new Int16Array(audioData);\n    const buffer = Buffer.from(int16Array.buffer);\n    return buffer.toString('base64');\n  }\n\n  private convertAudioArrayToBase64(audioData: number[], format?: string): string {\n    if (format === 'int16') {\n      // Already in correct format\n      const int16Array = new Int16Array(audioData);\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    } else {\n      // Assume float32, convert to int16\n      const int16Array = new Int16Array(audioData.length);\n      for (let i = 0; i < audioData.length; i++) {\n        const sample = Math.max(-1, Math.min(1, audioData[i]));\n        int16Array[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n      }\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    }\n  }\n\n  public disconnect(): void {\n    this.isDisconnecting = true;\n\n    if (this.openaiWs) {\n      this.openaiWs.close();\n      this.openaiWs = null;\n    }\n    this.isConnected = false;\n    this.sessionId = null;\n    this.hasActiveResponse = false;\n  }\n\n  public getSessionId(): string | null {\n    return this.sessionId;\n  }\n\n  public isSessionActive(): boolean {\n    return this.isConnected && this.sessionId !== null;\n  }\n  \n  // Handle messages routed from the main websocket handler\n  public handleClientMessage(message: any): void {\n    try {\n      console.log('Realtime proxy handling message:', message.type);\n\n      // Handle client messages\n      switch (message.type) {\n        case 'start':\n          // Client wants to start - we already initialized, just confirm\n          this.sendToClient({\n            type: 'ready',\n            message: 'Session already active'\n          });\n          break;\n\n        case 'generate_test_audio':\n          // Generate test audio for debugging\n          this.generateTestAudio(message.text || \"This is a test of the voice system. Please respond when ready.\");\n          break;\n\n        case 'test_round_trip':\n          // Test complete round-trip audio flow\n          this.testRoundTripAudio();\n          break;\n\n        case 'input_audio_buffer.append':\n          // Forward audio data to OpenAI with enhanced debugging\n          const audioData = message.audio_data || message.audio;\n          if (audioData) {\n            console.log(`📤 Forwarding audio to OpenAI: ${typeof audioData} (${typeof audioData === 'string' ? audioData.length + ' chars' : 'non-string'})`);\n            \n            // Enhanced audio validation and debugging\n            if (typeof audioData === 'string' && audioData.length > 0) {\n              try {\n                const decoded = Buffer.from(audioData, 'base64');\n                const samples = decoded.length / 2; // PCM16 = 2 bytes per sample\n                console.log(`📊 Audio info: ${decoded.length} bytes, ${samples} samples`);\n                \n                // Analyze audio content for speech-like characteristics\n                const int16Array = new Int16Array(decoded.buffer);\n                let energy = 0;\n                let peakCount = 0;\n                let avgAmplitude = 0;\n                \n                for (let i = 0; i < int16Array.length; i++) {\n                  const sample = Math.abs(int16Array[i]);\n                  energy += sample;\n                  avgAmplitude += sample;\n                  \n                  // Count peaks (potential speech indicators)\n                  if (sample > 1000) {\n                    peakCount++;\n                  }\n                }\n                \n                const avgEnergy = energy / int16Array.length;\n                avgAmplitude = avgAmplitude / int16Array.length;\n                const peakRatio = peakCount / int16Array.length;\n                \n                console.log(`🔊 Audio analysis: avg=${avgEnergy.toFixed(2)}, amp=${avgAmplitude.toFixed(2)}, peaks=${(peakRatio*100).toFixed(2)}%`);\n                \n                // Determine if this looks like speech\n                const looksLikeSpeech = avgEnergy > 100 && peakRatio > 0.01;\n                console.log(`🎯 Speech likelihood: ${looksLikeSpeech ? 'HIGH' : 'LOW'} (energy=${avgEnergy > 100}, peaks=${peakRatio > 0.01})`);\n                \n              } catch (e) {\n                console.warn('Failed to analyze audio data:', e instanceof Error ? e.message : 'Unknown error');\n              }\n            }\n            \n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: message.audio_data ? this.convertAudioFormat(message.audio_data) : message.audio\n            });\n          } else {\n            console.warn('No audio data in input_audio_buffer.append message');\n          }\n          break;\n\n        case 'input_audio_buffer.commit':\n          // Forward commit to OpenAI\n          this.sendToOpenAI(message);\n\n          // Since we're using manual VAD, automatically create a response after commit\n          // But only if there's no active response already\n          setTimeout(() => {\n            if (this.openaiWs && this.isConnected && !this.hasActiveResponse) {\n              console.log('🤖 Auto-creating response after manual audio commit');\n              this.sendToOpenAI({\n                type: 'response.create'\n              });\n            } else if (this.hasActiveResponse) {\n              console.log('⏸️ Skipping auto-response creation - response already active');\n            }\n          }, 100); // Small delay to ensure commit is processed\n          break;\n\n        case 'response.create':\n          // Only create response if none is active\n          if (!this.hasActiveResponse) {\n            this.sendToOpenAI(message);\n          } else {\n            console.log('⏸️ Skipping response.create - response already active');\n          }\n          break;\n\n        case 'response.cancel':\n          // Clear active response flag when cancelled\n          this.hasActiveResponse = false;\n          this.sendToOpenAI(message);\n          break;\n\n        case 'conversation.item.create':\n        case 'conversation.item.delete':\n        case 'conversation.item.truncate':\n          // Forward these commands directly to OpenAI\n          this.sendToOpenAI(message);\n          break;\n\n        case 'audio_chunk':\n          // Convert legacy audio_chunk to OpenAI format\n          if (message.audio_data && Array.isArray(message.audio_data)) {\n            const base64Audio = this.convertAudioArrayToBase64(message.audio_data, message.format);\n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: base64Audio\n            });\n          }\n          break;\n\n        case 'text':\n          // Convert text message to OpenAI conversation item\n          this.sendToOpenAI({\n            type: 'conversation.item.create',\n            item: {\n              type: 'message',\n              role: 'user',\n              content: [\n                {\n                  type: 'input_text',\n                  text: message.content\n                }\n              ]\n            }\n          });\n          // Trigger response\n          this.sendToOpenAI({\n            type: 'response.create'\n          });\n          break;\n\n        default:\n          console.log('Unhandled client message type in proxy:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling client message in proxy:', error);\n    }\n  }\n\n  // Add a method to generate test audio for debugging\n  public generateTestAudio(testPhrase: string = \"Hello, I am testing the voice system. Can you hear me?\"): void {\n    if (!this.openaiWs || !this.isConnected) {\n      console.warn('Cannot generate test audio: not connected to OpenAI');\n      return;\n    }\n\n    console.log('🎤 Generating test audio for debugging...');\n    \n    // Create a conversation item with the test phrase\n    this.sendToOpenAI({\n      type: 'conversation.item.create',\n      item: {\n        type: 'message',\n        role: 'user',\n        content: [\n          {\n            type: 'input_text',\n            text: testPhrase\n          }\n        ]\n      }\n    });\n    \n    // Trigger response\n    this.sendToOpenAI({\n      type: 'response.create'\n    });\n  }\n\n  // Add a method to test round-trip audio\n  public testRoundTripAudio(): void {\n    console.log('🔄 Starting round-trip audio test...');\n    \n    // Generate a test phrase that we can then try to \"speak\" back\n    this.generateTestAudio(\"Please say hello back to me when you're ready to test the voice system.\");\n  }\n} ", "path": "server/openai-realtime-proxy.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 2}}], ["db087dd7-a6cc-4dcc-b630-1809fd8b2604", {"value": {"selectedCode": "", "prefix": "import { WebSocket } from 'ws';\nimport { EventEmitter } from 'events';\n\ninterface RealtimeProxyConfig {\n  apiKey: string;\n  model: string;\n  voice?: string;\n  instructions?: string;\n  temperature?: number;\n}\n\nexport class OpenAIRealtimeProxy extends EventEmitter {\n  private openaiWs: WebSocket | null = null;\n  private clientWs: WebSocket | null = null;\n  private config: RealtimeProxyConfig;\n  private isConnected = false;\n  private sessionId: string | null = null;\n  private isDisconnecting = false;\n  public onTranscriptUpdate?: (speaker: 'user' | 'ai', text: string) => void;\n  private currentAITranscript = '';\n  private currentUserTranscript = '';\n  private initialGreetingSent = false;\n  private hasActiveResponse = false;\n", "suffix": "\n  constructor(config: RealtimeProxyConfig) {\n    super();\n    this.config = config;\n  }\n\n  async connect(clientWebSocket: WebSocket): Promise<void> {\n    this.clientWs = clientWebSocket;\n\n    try {\n      // Connect to OpenAI's Realtime API\n      const openaiUrl = `wss://api.openai.com/v1/realtime?model=${this.config.model}`;\n      \n      this.openaiWs = new WebSocket(openaiUrl, {\n        headers: {\n          'Authorization': `Bearer ${this.config.apiKey}`,\n          'OpenAI-Beta': 'realtime=v1'\n        }\n      });\n\n      this.setupOpenAIEventHandlers();\n      // Don't set up client event handlers here - let the main handler route messages\n\n      // Wait for OpenAI connection\n      await new Promise<void>((resolve, reject) => {\n        this.openaiWs!.once('open', () => {\n          console.log('Connected to OpenAI Realtime API');\n          this.isConnected = true;\n          this.initializeSession();\n          resolve();\n        });\n\n        this.openaiWs!.once('error', (error) => {\n          console.error('Failed to connect to OpenAI Realtime API:', error);\n          reject(error);\n        });\n      });\n\n    } catch (error) {\n      console.error('Error connecting to OpenAI Realtime API:', error);\n      throw error;\n    }\n  }\n\n  private setupOpenAIEventHandlers(): void {\n    if (!this.openaiWs) return;\n\n    this.openaiWs.on('message', (data) => {\n      try {\n        const message = JSON.parse(data.toString());\n        console.log('OpenAI → Client:', message.type);\n\n        // Handle specific message types\n        switch (message.type) {\n          case 'session.created':\n            this.sessionId = message.session.id;\n            console.log('OpenAI session created:', this.sessionId);\n            this.sendToClient(message); // Forward to client\n            break;\n          \n                  case 'session.updated':\n          console.log('OpenAI session updated');\n          \n          // Only trigger initial greeting once per session\n          if (!this.initialGreetingSent) {\n            this.initialGreetingSent = true;\n            \n            // Notify client that we're ready\n            this.sendToClient({\n              type: 'ready',\n              message: 'OpenAI Realtime session initialized'\n            });\n            \n            // Trigger initial AI greeting only once\n            console.log('Triggering initial AI greeting...');\n            this.sendToOpenAI({\n              type: 'conversation.item.create',\n              item: {\n                type: 'message',\n                role: 'system',\n                content: [\n                  {\n                    type: 'input_text',\n                    text: 'The user has just started a voice therapy session. Please greet them warmly and invite them to share what\\'s on their mind today. Keep it natural and conversational.'\n                  }\n                ]\n              }\n            });\n            \n            // Create response to trigger AI to speak\n            this.sendToOpenAI({\n              type: 'response.create'\n            });\n          } else {\n            console.log('Session configuration updated (VAD switch) - no new greeting needed');\n          }\n          \n          // Always forward session.updated to client\n          this.sendToClient(message);\n          break;\n\n          case 'conversation.item.input_audio_transcription.completed':\n            // User speech transcription - capture for summary\n            if (message.transcript && this.onTranscriptUpdate) {\n              console.log('Capturing user transcript:', message.transcript);\n              this.onTranscriptUpdate('user', message.transcript);\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.delta':\n            // Accumulate AI transcript\n            if (message.delta) {\n              this.currentAITranscript += message.delta;\n            }\n            this.sendToClient(message);\n            break;\n            \n          case 'response.audio_transcript.done':\n            // AI speech transcription complete - capture for summary  \n            if (this.currentAITranscript && this.onTranscriptUpdate) {\n              console.log('Capturing AI transcript:', this.currentAITranscript);\n              this.onTranscriptUpdate('ai', this.currentAITranscript);\n              this.currentAITranscript = ''; // Reset for next response\n            }\n            this.sendToClient(message);\n            break;\n\n          case 'input_audio_buffer.speech_started':\n          case 'input_audio_buffer.speech_stopped':\n          case 'conversation.item.created':\n          case 'response.created':\n            // Track that we have an active response\n            this.hasActiveResponse = true;\n            this.sendToClient(message);\n            break;\n\n          case 'response.output_item.added':\n          case 'response.content_part.added':\n          case 'response.audio.delta':\n          case 'response.audio.done':\n          case 'response.text.delta':\n          case 'response.text.done':\n          case 'response.output_item.done':\n            // Forward these events directly to the client\n            this.sendToClient(message);\n            break;\n\n          case 'response.done':\n            // Response is complete, clear the active flag\n            this.hasActiveResponse = false;\n            this.sendToClient(message);\n            break;\n\n          case 'rate_limits.updated':\n            // Rate limit information - just log and forward\n            console.log('OpenAI rate limits updated');\n            this.sendToClient(message);\n            break;\n            \n          case 'response.content_part.done':\n            // Content part completion - forward to client\n            this.sendToClient(message);\n            break;\n\n          case 'error':\n            console.error('OpenAI API error:', message);\n            this.sendToClient({\n              type: 'error',\n              message: message.error?.message || 'OpenAI API error'\n            });\n            break;\n\n          default:\n            console.log('Unhandled OpenAI message type:', message.type);\n            // Forward unknown messages to client for debugging\n            this.sendToClient(message);\n        }\n      } catch (error) {\n        console.error('Error parsing OpenAI message:', error);\n      }\n    });\n\n    this.openaiWs.on('close', (code, reason) => {\n      console.log('OpenAI connection closed:', code, reason.toString());\n      this.isConnected = false;\n      \n      // Only send error message if this wasn't an intentional disconnect\n      if (!this.isDisconnecting) {\n        this.sendToClient({\n          type: 'error',\n          message: 'OpenAI connection closed'\n        });\n      }\n    });\n\n    this.openaiWs.on('error', (error) => {\n      console.error('OpenAI WebSocket error:', error);\n      this.sendToClient({\n        type: 'error',\n        message: 'OpenAI connection error'\n      });\n    });\n  }\n\n  private initializeSession(): void {\n    if (!this.openaiWs || !this.isConnected) return;\n\n    // Configure session with manual turn detection (client-side VAD)\n    const sessionConfig = {\n      type: 'session.update',\n      session: {\n        modalities: ['text', 'audio'],\n        instructions: this.config.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',\n        voice: this.config.voice || 'shimmer',\n        input_audio_format: 'pcm16',\n        output_audio_format: 'pcm16',\n        input_audio_transcription: {\n          model: 'whisper-1'\n        },\n        turn_detection: null, // Disable automatic turn detection - use client-side VAD\n        temperature: this.config.temperature || 0.7,\n        max_response_output_tokens: 1000\n      }\n    };\n\n    console.log('🎯 Configuring session with CLIENT-SIDE VAD (server VAD disabled)...');\n    console.log('📊 Turn Detection: DISABLED (manual commits from client)');\n    this.sendToOpenAI(sessionConfig);\n\n    console.log('✅ Client-side VAD configured - speech detection handled by browser');\n  }\n\n  private sendToOpenAI(message: any): void {\n    if (this.openaiWs && this.isConnected) {\n      this.openaiWs.send(JSON.stringify(message));\n    } else {\n      console.warn('Cannot send to OpenAI: not connected');\n    }\n  }\n\n  private sendToClient(message: any): void {\n    if (this.clientWs && this.clientWs.readyState === WebSocket.OPEN) {\n      this.clientWs.send(JSON.stringify(message));\n    }\n  }\n\n  private convertAudioFormat(audioData: number[]): string {\n    // Convert Int16Array audio data to base64 PCM16\n    const int16Array = new Int16Array(audioData);\n    const buffer = Buffer.from(int16Array.buffer);\n    return buffer.toString('base64');\n  }\n\n  private convertAudioArrayToBase64(audioData: number[], format?: string): string {\n    if (format === 'int16') {\n      // Already in correct format\n      const int16Array = new Int16Array(audioData);\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    } else {\n      // Assume float32, convert to int16\n      const int16Array = new Int16Array(audioData.length);\n      for (let i = 0; i < audioData.length; i++) {\n        const sample = Math.max(-1, Math.min(1, audioData[i]));\n        int16Array[i] = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);\n      }\n      const buffer = Buffer.from(int16Array.buffer);\n      return buffer.toString('base64');\n    }\n  }\n\n  public disconnect(): void {\n    this.isDisconnecting = true;\n\n    if (this.openaiWs) {\n      this.openaiWs.close();\n      this.openaiWs = null;\n    }\n    this.isConnected = false;\n    this.sessionId = null;\n    this.hasActiveResponse = false;\n  }\n\n  public getSessionId(): string | null {\n    return this.sessionId;\n  }\n\n  public isSessionActive(): boolean {\n    return this.isConnected && this.sessionId !== null;\n  }\n  \n  // Handle messages routed from the main websocket handler\n  public handleClientMessage(message: any): void {\n    try {\n      console.log('Realtime proxy handling message:', message.type);\n\n      // Handle client messages\n      switch (message.type) {\n        case 'start':\n          // Client wants to start - we already initialized, just confirm\n          this.sendToClient({\n            type: 'ready',\n            message: 'Session already active'\n          });\n          break;\n\n        case 'generate_test_audio':\n          // Generate test audio for debugging\n          this.generateTestAudio(message.text || \"This is a test of the voice system. Please respond when ready.\");\n          break;\n\n        case 'test_round_trip':\n          // Test complete round-trip audio flow\n          this.testRoundTripAudio();\n          break;\n\n        case 'input_audio_buffer.append':\n          // Forward audio data to OpenAI with enhanced debugging\n          const audioData = message.audio_data || message.audio;\n          if (audioData) {\n            console.log(`📤 Forwarding audio to OpenAI: ${typeof audioData} (${typeof audioData === 'string' ? audioData.length + ' chars' : 'non-string'})`);\n            \n            // Enhanced audio validation and debugging\n            if (typeof audioData === 'string' && audioData.length > 0) {\n              try {\n                const decoded = Buffer.from(audioData, 'base64');\n                const samples = decoded.length / 2; // PCM16 = 2 bytes per sample\n                console.log(`📊 Audio info: ${decoded.length} bytes, ${samples} samples`);\n                \n                // Analyze audio content for speech-like characteristics\n                const int16Array = new Int16Array(decoded.buffer);\n                let energy = 0;\n                let peakCount = 0;\n                let avgAmplitude = 0;\n                \n                for (let i = 0; i < int16Array.length; i++) {\n                  const sample = Math.abs(int16Array[i]);\n                  energy += sample;\n                  avgAmplitude += sample;\n                  \n                  // Count peaks (potential speech indicators)\n                  if (sample > 1000) {\n                    peakCount++;\n                  }\n                }\n                \n                const avgEnergy = energy / int16Array.length;\n                avgAmplitude = avgAmplitude / int16Array.length;\n                const peakRatio = peakCount / int16Array.length;\n                \n                console.log(`🔊 Audio analysis: avg=${avgEnergy.toFixed(2)}, amp=${avgAmplitude.toFixed(2)}, peaks=${(peakRatio*100).toFixed(2)}%`);\n                \n                // Determine if this looks like speech\n                const looksLikeSpeech = avgEnergy > 100 && peakRatio > 0.01;\n                console.log(`🎯 Speech likelihood: ${looksLikeSpeech ? 'HIGH' : 'LOW'} (energy=${avgEnergy > 100}, peaks=${peakRatio > 0.01})`);\n                \n              } catch (e) {\n                console.warn('Failed to analyze audio data:', e instanceof Error ? e.message : 'Unknown error');\n              }\n            }\n            \n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: message.audio_data ? this.convertAudioFormat(message.audio_data) : message.audio\n            });\n          } else {\n            console.warn('No audio data in input_audio_buffer.append message');\n          }\n          break;\n\n        case 'input_audio_buffer.commit':\n          // Forward commit to OpenAI\n          this.sendToOpenAI(message);\n\n          // Since we're using manual VAD, automatically create a response after commit\n          // But only if there's no active response already\n          setTimeout(() => {\n            if (this.openaiWs && this.isConnected && !this.hasActiveResponse) {\n              console.log('🤖 Auto-creating response after manual audio commit');\n              this.sendToOpenAI({\n                type: 'response.create'\n              });\n            } else if (this.hasActiveResponse) {\n              console.log('⏸️ Skipping auto-response creation - response already active');\n            }\n          }, 100); // Small delay to ensure commit is processed\n          break;\n\n        case 'response.create':\n          // Only create response if none is active\n          if (!this.hasActiveResponse) {\n            this.sendToOpenAI(message);\n          } else {\n            console.log('⏸️ Skipping response.create - response already active');\n          }\n          break;\n\n        case 'response.cancel':\n          // Clear active response flag when cancelled\n          this.hasActiveResponse = false;\n          this.sendToOpenAI(message);\n          break;\n\n        case 'conversation.item.create':\n        case 'conversation.item.delete':\n        case 'conversation.item.truncate':\n          // Forward these commands directly to OpenAI\n          this.sendToOpenAI(message);\n          break;\n\n        case 'audio_chunk':\n          // Convert legacy audio_chunk to OpenAI format\n          if (message.audio_data && Array.isArray(message.audio_data)) {\n            const base64Audio = this.convertAudioArrayToBase64(message.audio_data, message.format);\n            this.sendToOpenAI({\n              type: 'input_audio_buffer.append',\n              audio: base64Audio\n            });\n          }\n          break;\n\n        case 'text':\n          // Convert text message to OpenAI conversation item\n          this.sendToOpenAI({\n            type: 'conversation.item.create',\n            item: {\n              type: 'message',\n              role: 'user',\n              content: [\n                {\n                  type: 'input_text',\n                  text: message.content\n                }\n              ]\n            }\n          });\n          // Trigger response\n          this.sendToOpenAI({\n            type: 'response.create'\n          });\n          break;\n\n        default:\n          console.log('Unhandled client message type in proxy:', message.type);\n      }\n    } catch (error) {\n      console.error('Error handling client message in proxy:', error);\n    }\n  }\n\n  // Add a method to generate test audio for debugging\n  public generateTestAudio(testPhrase: string = \"Hello, I am testing the voice system. Can you hear me?\"): void {\n    if (!this.openaiWs || !this.isConnected) {\n      console.warn('Cannot generate test audio: not connected to OpenAI');\n      return;\n    }\n\n    console.log('🎤 Generating test audio for debugging...');\n    \n    // Create a conversation item with the test phrase\n    this.sendToOpenAI({\n      type: 'conversation.item.create',\n      item: {\n        type: 'message',\n        role: 'user',\n        content: [\n          {\n            type: 'input_text',\n            text: testPhrase\n          }\n        ]\n      }\n    });\n    \n    // Trigger response\n    this.sendToOpenAI({\n      type: 'response.create'\n    });\n  }\n\n  // Add a method to test round-trip audio\n  public testRoundTripAudio(): void {\n    console.log('🔄 Starting round-trip audio test...');\n    \n    // Generate a test phrase that we can then try to \"speak\" back\n    this.generateTestAudio(\"Please say hello back to me when you're ready to test the voice system.\");\n  }\n} ", "path": "server/openai-realtime-proxy.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 2}}], ["2843bd14-8c57-4060-a09e-0e4f898b76da", {"value": {"selectedCode": "      model: \"gpt-4o-realtime-preview\",", "prefix": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Full Real-time Conversation...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationTurns = 0;\nconst maxTurns = 3; // Test 3 conversation turns\n\n// Generate realistic speech-like audio data\nfunction generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * durationMs / 1000);\n  const audioBuffer = new Int16Array(samples);\n  \n  // Generate more realistic speech-like patterns\n  for (let i = 0; i < samples; i++) {\n    // Create speech-like patterns with varying amplitude\n    const time = i / sampleRate;\n    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency\n    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude\n    \n    // Add some noise and harmonics to make it more speech-like\n    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n    const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;\n    \n    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);\n  }\n  \n  const buffer = Buffer.from(audioBuffer.buffer);\n  return buffer.toString('base64');\n}\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-user\",\n    clientId: \"test-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n", "suffix": "\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"<PERSON> are <PERSON>, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing.\"\n  };\n  \n  console.log('📤 Starting conversation...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events to reduce noise\n    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        conversationTurns++;\n        console.log(`✅ AI response ${conversationTurns} completed`);\n        \n        if (conversationTurns < maxTurns) {\n          // Send user response after a short delay\n          setTimeout(() => {\n            console.log(`🎤 Sending user response ${conversationTurns}...`);\n            \n            // Generate speech-like audio\n            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech\n            \n            // Send audio in chunks to simulate real streaming\n            const chunkSize = 8000; // 8KB chunks\n            const chunks = [];\n            for (let i = 0; i < audioData.length; i += chunkSize) {\n              chunks.push(audioData.slice(i, i + chunkSize));\n            }\n            \n            console.log(`📤 Sending ${chunks.length} audio chunks...`);\n            \n            // Send chunks with small delays\n            chunks.forEach((chunk, index) => {\n              setTimeout(() => {\n                ws.send(JSON.stringify({\n                  type: 'input_audio_buffer.append',\n                  audio: chunk\n                }));\n                \n                // Commit after the last chunk\n                if (index === chunks.length - 1) {\n                  setTimeout(() => {\n                    console.log('📤 Committing audio...');\n                    ws.send(JSON.stringify({\n                      type: 'input_audio_buffer.commit'\n                    }));\n                  }, 50);\n                }\n              }, index * 20); // 20ms between chunks\n            });\n            \n          }, 1000); // Wait 1 second before responding\n        } else {\n          // End conversation after max turns\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Completed ${conversationTurns} conversation turns`);\n          console.log(`📊 Total messages processed: ${messageCount}`);\n          console.log('🎉 Real-time conversation test PASSED!');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - conversation test failed');\n  ws.close();\n  process.exit(1);\n}, 60000); // 60 second timeout\n", "path": "test-full-conversation.js", "language": "javascript", "prefixBegin": 0, "suffixEnd": 0}}], ["d3394c24-0083-44e2-b533-f9616bb02e04", {"value": {"selectedCode": "      model: \"gpt-4o-realtime-preview\",", "prefix": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Full Real-time Conversation...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationTurns = 0;\nconst maxTurns = 3; // Test 3 conversation turns\n\n// Generate realistic speech-like audio data\nfunction generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * durationMs / 1000);\n  const audioBuffer = new Int16Array(samples);\n  \n  // Generate more realistic speech-like patterns\n  for (let i = 0; i < samples; i++) {\n    // Create speech-like patterns with varying amplitude\n    const time = i / sampleRate;\n    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency\n    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude\n    \n    // Add some noise and harmonics to make it more speech-like\n    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n    const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;\n    \n    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);\n  }\n  \n  const buffer = Buffer.from(audioBuffer.buffer);\n  return buffer.toString('base64');\n}\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-user\",\n    clientId: \"test-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n", "suffix": "\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"<PERSON> are <PERSON>, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing.\"\n  };\n  \n  console.log('📤 Starting conversation...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events to reduce noise\n    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        conversationTurns++;\n        console.log(`✅ AI response ${conversationTurns} completed`);\n        \n        if (conversationTurns < maxTurns) {\n          // Send user response after a short delay\n          setTimeout(() => {\n            console.log(`🎤 Sending user response ${conversationTurns}...`);\n            \n            // Generate speech-like audio\n            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech\n            \n            // Send audio in chunks to simulate real streaming\n            const chunkSize = 8000; // 8KB chunks\n            const chunks = [];\n            for (let i = 0; i < audioData.length; i += chunkSize) {\n              chunks.push(audioData.slice(i, i + chunkSize));\n            }\n            \n            console.log(`📤 Sending ${chunks.length} audio chunks...`);\n            \n            // Send chunks with small delays\n            chunks.forEach((chunk, index) => {\n              setTimeout(() => {\n                ws.send(JSON.stringify({\n                  type: 'input_audio_buffer.append',\n                  audio: chunk\n                }));\n                \n                // Commit after the last chunk\n                if (index === chunks.length - 1) {\n                  setTimeout(() => {\n                    console.log('📤 Committing audio...');\n                    ws.send(JSON.stringify({\n                      type: 'input_audio_buffer.commit'\n                    }));\n                  }, 50);\n                }\n              }, index * 20); // 20ms between chunks\n            });\n            \n          }, 1000); // Wait 1 second before responding\n        } else {\n          // End conversation after max turns\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Completed ${conversationTurns} conversation turns`);\n          console.log(`📊 Total messages processed: ${messageCount}`);\n          console.log('🎉 Real-time conversation test PASSED!');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - conversation test failed');\n  ws.close();\n  process.exit(1);\n}, 60000); // 60 second timeout\n", "path": "test-full-conversation.js", "language": "javascript", "prefixBegin": 0, "suffixEnd": 0}}], ["803953f9-01f1-478e-9349-9b4ad6326a3b", {"value": {"selectedCode": "      model: \"gpt-4o-realtime-preview-2025-06-03\",", "prefix": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Full Real-time Conversation...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationTurns = 0;\nconst maxTurns = 3; // Test 3 conversation turns\n\n// Generate realistic speech-like audio data\nfunction generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * durationMs / 1000);\n  const audioBuffer = new Int16Array(samples);\n  \n  // Generate more realistic speech-like patterns\n  for (let i = 0; i < samples; i++) {\n    // Create speech-like patterns with varying amplitude\n    const time = i / sampleRate;\n    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency\n    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude\n    \n    // Add some noise and harmonics to make it more speech-like\n    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n    const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;\n    \n    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);\n  }\n  \n  const buffer = Buffer.from(audioBuffer.buffer);\n  return buffer.toString('base64');\n}\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-user\",\n    clientId: \"test-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n", "suffix": "\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"<PERSON> are <PERSON>, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing.\"\n  };\n  \n  console.log('📤 Starting conversation...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events to reduce noise\n    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        conversationTurns++;\n        console.log(`✅ AI response ${conversationTurns} completed`);\n        \n        if (conversationTurns < maxTurns) {\n          // Send user response after a short delay\n          setTimeout(() => {\n            console.log(`🎤 Sending user response ${conversationTurns}...`);\n            \n            // Generate speech-like audio\n            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech\n            \n            // Send audio in chunks to simulate real streaming\n            const chunkSize = 8000; // 8KB chunks\n            const chunks = [];\n            for (let i = 0; i < audioData.length; i += chunkSize) {\n              chunks.push(audioData.slice(i, i + chunkSize));\n            }\n            \n            console.log(`📤 Sending ${chunks.length} audio chunks...`);\n            \n            // Send chunks with small delays\n            chunks.forEach((chunk, index) => {\n              setTimeout(() => {\n                ws.send(JSON.stringify({\n                  type: 'input_audio_buffer.append',\n                  audio: chunk\n                }));\n                \n                // Commit after the last chunk\n                if (index === chunks.length - 1) {\n                  setTimeout(() => {\n                    console.log('📤 Committing audio...');\n                    ws.send(JSON.stringify({\n                      type: 'input_audio_buffer.commit'\n                    }));\n                  }, 50);\n                }\n              }, index * 20); // 20ms between chunks\n            });\n            \n          }, 1000); // Wait 1 second before responding\n        } else {\n          // End conversation after max turns\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Completed ${conversationTurns} conversation turns`);\n          console.log(`📊 Total messages processed: ${messageCount}`);\n          console.log('🎉 Real-time conversation test PASSED!');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - conversation test failed');\n  ws.close();\n  process.exit(1);\n}, 60000); // 60 second timeout\n", "path": "test-full-conversation.js", "language": "javascript", "prefixBegin": 0, "suffixEnd": 0}}], ["f730734d-1574-4981-a4d3-5013616274ce", {"value": {"selectedCode": "", "prefix": "#!/usr/bin/env node\n\nimport { WebSocket } from 'ws';\n\nconst WEBSOCKET_URL = 'ws://localhost:5000/ws';\n\nconsole.log('🗣️ Testing Full Real-time Conversation...');\nconsole.log('📡 Connecting to:', WEBSOCKET_URL);\n\nconst ws = new WebSocket(WEBSOCKET_URL);\n\nlet messageCount = 0;\nlet conversationTurns = 0;\nconst maxTurns = 3; // Test 3 conversation turns\n\n// Generate realistic speech-like audio data\nfunction generateSpeechAudio(durationMs = 2000, speechIntensity = 0.3) {\n  const sampleRate = 24000;\n  const samples = Math.floor(sampleRate * durationMs / 1000);\n  const audioBuffer = new Int16Array(samples);\n  \n  // Generate more realistic speech-like patterns\n  for (let i = 0; i < samples; i++) {\n    // Create speech-like patterns with varying amplitude\n    const time = i / sampleRate;\n    const frequency = 200 + Math.sin(time * 10) * 50; // Varying frequency\n    const amplitude = speechIntensity * (0.5 + 0.5 * Math.sin(time * 3)); // Varying amplitude\n    \n    // Add some noise and harmonics to make it more speech-like\n    const signal = Math.sin(2 * Math.PI * frequency * time) * amplitude;\n    const noise = (Math.random() - 0.5) * 0.1 * amplitude;\n    const harmonic = Math.sin(2 * Math.PI * frequency * 2 * time) * amplitude * 0.3;\n    \n    audioBuffer[i] = Math.floor((signal + noise + harmonic) * 16000);\n  }\n  \n  const buffer = Buffer.from(audioBuffer.buffer);\n  return buffer.toString('base64');\n}\n\nws.on('open', () => {\n  console.log('✅ WebSocket connected');\n  \n  const startMessage = {\n    type: \"start\",\n    userId: \"test-user\",\n    clientId: \"test-client\",\n    useRealtimeAPI: true,\n    mode: \"realtime\",\n    behavior: {\n", "suffix": "      model: \"gpt-4o-realtime-preview-2025-06-03\",\n      temperature: 0.7,\n      voice: {\n        voice: \"shimmer\",\n        speed: 1.0\n      }\n    },\n    instructions: \"<PERSON> are <PERSON>, an empathetic AI therapeutic assistant. Keep responses very brief (1-2 sentences) for this test. Ask simple questions to keep the conversation flowing.\"\n  };\n  \n  console.log('📤 Starting conversation...');\n  ws.send(JSON.stringify(startMessage));\n});\n\nws.on('message', (data) => {\n  messageCount++;\n  try {\n    const message = JSON.parse(data.toString());\n    \n    // Only log important events to reduce noise\n    if (['ready', 'response.audio_transcript.done', 'response.done', 'conversation.item.input_audio_transcription.completed', 'error'].includes(message.type)) {\n      console.log(`📥 ${message.type}`);\n    }\n    \n    switch (message.type) {\n      case 'ready':\n        console.log('✅ Session ready! Waiting for AI greeting...');\n        break;\n        \n      case 'response.audio_transcript.done':\n        console.log(`🤖 AI: \"${message.transcript}\"`);\n        break;\n        \n      case 'response.done':\n        conversationTurns++;\n        console.log(`✅ AI response ${conversationTurns} completed`);\n        \n        if (conversationTurns < maxTurns) {\n          // Send user response after a short delay\n          setTimeout(() => {\n            console.log(`🎤 Sending user response ${conversationTurns}...`);\n            \n            // Generate speech-like audio\n            const audioData = generateSpeechAudio(1500, 0.4); // 1.5 seconds of speech\n            \n            // Send audio in chunks to simulate real streaming\n            const chunkSize = 8000; // 8KB chunks\n            const chunks = [];\n            for (let i = 0; i < audioData.length; i += chunkSize) {\n              chunks.push(audioData.slice(i, i + chunkSize));\n            }\n            \n            console.log(`📤 Sending ${chunks.length} audio chunks...`);\n            \n            // Send chunks with small delays\n            chunks.forEach((chunk, index) => {\n              setTimeout(() => {\n                ws.send(JSON.stringify({\n                  type: 'input_audio_buffer.append',\n                  audio: chunk\n                }));\n                \n                // Commit after the last chunk\n                if (index === chunks.length - 1) {\n                  setTimeout(() => {\n                    console.log('📤 Committing audio...');\n                    ws.send(JSON.stringify({\n                      type: 'input_audio_buffer.commit'\n                    }));\n                  }, 50);\n                }\n              }, index * 20); // 20ms between chunks\n            });\n            \n          }, 1000); // Wait 1 second before responding\n        } else {\n          // End conversation after max turns\n          setTimeout(() => {\n            console.log('📤 Ending conversation...');\n            ws.send(JSON.stringify({\n              type: 'conversationEnded'\n            }));\n          }, 1000);\n        }\n        break;\n        \n      case 'conversation.item.input_audio_transcription.completed':\n        console.log(`🎯 User speech transcribed: \"${message.transcript}\"`);\n        break;\n        \n      case 'end_ack':\n        console.log('✅ Conversation ended successfully');\n        \n        setTimeout(() => {\n          console.log('\\n=== CONVERSATION TEST SUMMARY ===');\n          console.log(`✅ Completed ${conversationTurns} conversation turns`);\n          console.log(`📊 Total messages processed: ${messageCount}`);\n          console.log('🎉 Real-time conversation test PASSED!');\n          ws.close();\n        }, 500);\n        break;\n        \n      case 'error':\n        console.error('❌ Error:', message.message);\n        break;\n    }\n  } catch (error) {\n    console.error('❌ Error parsing message:', error);\n  }\n});\n\nws.on('close', (code, reason) => {\n  console.log(`🔌 Connection closed: ${code} ${reason.toString()}`);\n});\n\nws.on('error', (error) => {\n  console.error('❌ WebSocket error:', error);\n});\n\n// Timeout to prevent hanging\nsetTimeout(() => {\n  console.log('⏰ Test timeout - conversation test failed');\n  ws.close();\n  process.exit(1);\n}, 60000); // 60 second timeout\n", "path": "test-full-conversation.js", "language": "javascript", "prefixBegin": 0, "suffixEnd": 0}}], ["2de8c06b-2205-4e72-acbd-9469b826b6eb", {"value": {"selectedCode": "console.log(`Using model: gpt-4o for regular calls`);", "prefix": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst openai = new OpenAI({ \n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\n", "suffix": "\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\", // newest model as of May 2024\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(\"Using gpt-4o for therapeutic conversation\");\n    const stream = await openai.chat.completions.create({\n      model: \"gpt-4o\", // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n", "path": "server/openai.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["5a1c4bb8-4f2d-4dc7-84b1-b6a34a8e7a86", {"value": {"selectedCode": "", "prefix": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst CHAT_MODEL = \"gpt-4o\";\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\nconsole.log(`Using model: ${CHAT_MODEL} for regular calls`);\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n", "suffix": "}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\", // newest model as of May 2024\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(\"Using gpt-4o for therapeutic conversation\");\n    const stream = await openai.chat.completions.create({\n      model: \"gpt-4o\", // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n", "path": "server/openai.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["ee85c881-636f-4242-9dda-167945d20bb2", {"value": {"selectedCode": "console.log(`Using model: ${CHAT_MODEL} for regular calls`);", "prefix": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst CHAT_MODEL = \"gpt-4o\";\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\n", "suffix": "\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n      model: CHAT_MODEL, // newest model as of May 2024\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(\"Using gpt-4o for therapeutic conversation\");\n    const stream = await openai.chat.completions.create({\n      model: \"gpt-4o\", // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n", "path": "server/openai.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["462caee8-fb9e-4df2-b3ed-24f7c6cedd79", {"value": {"selectedCode": "      model: CHAT_MODEL, // newest model as of May 2024", "prefix": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst CHAT_MODEL = \"gpt-4o\";\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\nconsole.log(`Using model: ${CHAT_MODEL} for regular calls`);\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n", "suffix": "\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(`Using ${CHAT_MODEL} for therapeutic conversation`);\n    const stream = await openai.chat.completions.create({\n      model: CHAT_MODEL, // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: \"gpt-4o\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n", "path": "server/openai.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["19690098-7fbb-4ea9-94ec-633155dfc7f2", {"value": {"selectedCode": "      model: CHAT_MODEL, // newest model as of May 2024", "prefix": "import OpenAI from \"openai\";\nimport { type Theme } from \"@shared/schema\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { randomUUID } from \"crypto\";\nimport { WebSocket } from \"ws\";\nimport { AIResponse, ConversationMessage } from './types';\n\n// the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024. do not change this unless explicitly requested by the user\nconst CHAT_MODEL = \"gpt-4o\";\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY || \"\"\n});\n\n// Debug OpenAI configuration\nconsole.log(`OpenAI SDK Version: ${openai.constructor.name}`);\nconsole.log(`Using model: ${CHAT_MODEL} for regular calls`);\nconsole.log(`API Key configured: ${process.env.OPENAI_API_KEY ? 'Yes' : 'No'}`);\n\n// Ensure audio uploads directory exists\nconst UPLOADS_DIR = path.join(process.cwd(), \"uploads\");\nconst AUDIO_DIR = path.join(UPLOADS_DIR, \"audio\");\n\nif (!fs.existsSync(UPLOADS_DIR)) {\n  fs.mkdirSync(UPLOADS_DIR, { recursive: true });\n}\nif (!fs.existsSync(AUDIO_DIR)) {\n  fs.mkdirSync(AUDIO_DIR, { recursive: true });\n}\n\ninterface NoteAnalysisResult {\n  summary: string;\n  themes: Theme[];\n  recommendations: string[];\n}\n\ninterface TextToSpeechResult {\n  audioUrl: string;\n}\n\ninterface TranscriptionResult {\n  text: string;\n}\n\ninterface AIConversationResponse {\n  message: string;\n  audioUrl?: string;\n}\n\ninterface VoiceConfig {\n  voice: string;\n  speed: number;\n  pitch: number;\n  emphasis: 'strong' | 'moderate' | 'subtle';\n  prosody: {\n    emotionalRange: number;\n    questionInflection: number;\n    pauseDuration: number;\n  };\n}\n\ninterface ConversationConfig {\n  turnTaking: {\n    backchannelFrequency: number;\n    minSilenceDuration: number;\n    maxInterruptionGap: number;\n  };\n  responseStyle: {\n    minResponseLength: number;\n    maxResponseLength: number;\n    temperature: number;\n    presencePenalty: number;\n    frequencyPenalty: number;\n  };\n}\n\n// Default voice configuration\nconst DEFAULT_VOICE_CONFIG: VoiceConfig = {\n  voice: \"shimmer\",\n  speed: 1.05,\n  pitch: 1.0,\n  emphasis: 'moderate',\n  prosody: {\n    emotionalRange: 0.6,\n    questionInflection: 0.7,\n    pauseDuration: 300\n  }\n};\n\nexport async function analyzeTherapyNotes(\n  notes: string,\n  previousThemes: string[] = []\n): Promise<NoteAnalysisResult> {\n  try {\n    // Check if API key is available\n    if (!openai.apiKey) {\n      console.error(\"OpenAI API key is missing\");\n      throw new Error(\"OpenAI API key is missing\");\n    }\n\n    // Create a prompt that includes previousThemes for trend detection\n    const prompt = `\n    You are a professional AI assistant for mental health professionals.\n    \n    Analyze the following therapy session notes and provide:\n    1. A concise summary of the key points (max 150 words)\n    2. Up to 5 key themes or topics from the session, with a trend indicator for each theme:\n       - Provide a positive number (e.g., +5, +10, +25) if the theme shows improvement\n       - Provide a negative number (e.g., -5, -10, -15) if the theme shows regression\n       - Provide 0 if the theme is new or stable\n       - Mark as \"New\" if the theme wasn't in previous sessions\n    3. 3-5 actionable recommendations or insights for the therapist\n    \n    Previous session themes (for reference): ${previousThemes.join(\", \")}\n    \n    Session notes:\n    ${notes}\n    \n    Respond with JSON in this exact format:\n    {\n      \"summary\": \"concise summary here\",\n      \"themes\": [\n        {\"name\": \"theme1\", \"occurrences\": number, \"trend\": number},\n        {\"name\": \"theme2\", \"occurrences\": number, \"trend\": number}\n      ],\n      \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"]\n    }\n    `;\n\n    const response = await openai.chat.completions.create({\n", "suffix": "\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a HIPAA-compliant AI assistant specialized in analyzing therapy session notes.\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    \n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result: NoteAnalysisResult = JSON.parse(content);\n    \n    // Ensure the result has the correct structure\n    if (!result.summary || !result.themes || !result.recommendations) {\n      throw new Error(\"Incomplete response from OpenAI\");\n    }\n\n    return result;\n  } catch (error) {\n    console.error(\"Error analyzing therapy notes:\", error);\n    // Return a fallback result if API call fails\n    return {\n      summary: \"Error analyzing notes. Please try again.\",\n      themes: [],\n      recommendations: [\"Unable to generate recommendations due to an error.\"]\n    };\n  }\n}\n\n// Function to transcribe audio files with enhanced features for Vale-like interaction\nexport async function transcribeAudio(audioBuffer: Buffer, filename?: string): Promise<TranscriptionResult> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    console.log(`Transcribing audio buffer of size: ${audioBuffer.length} bytes`);\n    \n    // Check if the buffer is empty or too small\n    if (audioBuffer.length < 100) {\n      console.error('Audio buffer is too small or empty:', audioBuffer.length);\n      throw new Error('Audio buffer is too small or empty');\n    }\n\n    // Save the audio buffer to a temporary file\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    \n    // Ensure the uploads directory exists\n    if (!fs.existsSync(AUDIO_DIR)) {\n      fs.mkdirSync(AUDIO_DIR, { recursive: true });\n      console.log(`Created directory: ${AUDIO_DIR}`);\n    }\n    \n    // Save the buffer with proper binary encoding\n    fs.writeFileSync(filePath, audioBuffer);\n    console.log(`Saved audio file to ${filePath}, size: ${fs.statSync(filePath).size} bytes`);\n    \n    // Create a readable stream from the file\n    const audioFile = fs.createReadStream(filePath);\n    \n    // Transcribe the audio using Whisper model for reliability\n    console.log(\"Starting transcription with whisper-1 model\");\n    \n    // Check if OpenAI API key is configured\n    if (!process.env.OPENAI_API_KEY) {\n      console.error(\"OPENAI_API_KEY is not configured\");\n      throw new Error(\"OpenAI API key is not configured\");\n    }\n    \n    const transcription = await openai.audio.transcriptions.create({\n      file: audioFile,\n      model: \"whisper-1\", // Using reliable Whisper model for transcription\n      language: \"en\", // Specify language to improve accuracy\n      prompt: \"This is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.\",\n      response_format: \"text\", // Plaintext output for simplicity\n      temperature: 0.3 // Lower temperature for higher accuracy in medical/therapeutic context\n    });\n    \n    console.log(\"Transcription raw result:\", transcription);\n    \n    // Clean up the temporary file\n    fs.unlinkSync(filePath);\n    \n    // Get the text content properly\n    const transcriptionText = typeof transcription === 'string' \n      ? transcription \n      : (transcription as any).text || '';\n    \n    // Post-process the transcribed text\n    const processedText = postProcessTranscription(transcriptionText);\n    console.log(\"Processed transcription:\", processedText);\n    \n    return { text: processedText };\n  } catch (error) {\n    console.error(\"Error transcribing audio:\", error);\n    // Keep the file for debugging if it exists\n    const tempFilename = filename || `${randomUUID()}.webm`;\n    const filePath = path.join(AUDIO_DIR, tempFilename);\n    if (fs.existsSync(filePath)) {\n      console.log(`Audio file preserved at ${filePath} for debugging`);\n    }\n    return { text: \"Error transcribing audio. Please try again.\" };\n  }\n}\n\n// Helper function to post-process transcription for therapy-specific terminology\nfunction postProcessTranscription(text: string): string {\n  const corrections: Record<string, string> = {\n    // Common therapy term corrections that might be misheard\n    \"see bt\": \"CBT\", // Cognitive Behavioral Therapy\n    \"mindful ness\": \"mindfulness\",\n    \"coping mechanisms\": \"coping mechanisms\",\n    \"cognitive distortions\": \"cognitive distortions\",\n    \"anxiety attacks\": \"anxiety attacks\",\n    \"panic attacks\": \"panic attacks\"\n    // Add more domain-specific corrections as needed\n  };\n  \n  let processedText = text;\n  \n  // Apply corrections\n  for (const [incorrect, correct] of Object.entries(corrections)) {\n    const regex = new RegExp(`\\\\b${incorrect}\\\\b`, 'gi');\n    processedText = processedText.replace(regex, correct);\n  }\n  \n  return processedText;\n}\n\n// Enhanced prosody control\nfunction enhanceTextForValeVoice(text: string, config: VoiceConfig): string {\n  const { prosody } = config;\n  \n  // Add prosody markers based on configuration\n  const enhancedText = text\n    // Add emotional emphasis based on config\n    .replace(/\\b(feel|felt|feeling|emotion|emotional)\\b/gi, (match) => {\n      const emphasis = prosody.emotionalRange > 0.7 ? '!' : prosody.emotionalRange > 0.4 ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Add question inflection\n    .replace(/\\?/g, (match) => {\n      const inflection = prosody.questionInflection > 0.7 ? '↗' : '↗';\n      return `${match}${inflection}`;\n    })\n    \n    // Add pauses based on sentence structure\n    .replace(/\\.\\s+([A-Z])/g, (match) => {\n      const pause = prosody.pauseDuration > 400 ? '.↘' : prosody.pauseDuration > 200 ? '.' : ',';\n      return `${pause} $1`;\n    })\n    \n    // Add emphasis to important words\n    .replace(/\\b(important|crucial|significant|key)\\b/gi, (match) => {\n      const emphasis = config.emphasis === 'strong' ? '!' : config.emphasis === 'moderate' ? '.' : ',';\n      return `${match}${emphasis}`;\n    })\n    \n    // Clean up redundant markers\n    .replace(/↗↗/g, '↗')\n    .replace(/↘↘/g, '↘')\n    .replace(/!!/g, '!')\n    .replace(/\\.\\./g, '.')\n    .replace(/,,/g, ',');\n    \n  return enhancedText;\n}\n\n// Streaming transcription service optimized for real-time\nexport class StreamingTranscriptionService {\n  private buffer: Buffer[] = [];\n  private lastProcessedTime = 0;\n  private transcriptionContext = '';\n  private isProcessing = false;\n  private readonly CHUNK_SIZE = 500; // ms of audio per chunk\n  private readonly MIN_CHUNKS = 5; // Minimum chunks to process\n\n  async processAudioChunk(chunk: Buffer): Promise<TranscriptionResult> {\n    this.buffer.push(chunk);\n    \n    // Process if we have enough audio\n    if (this.buffer.length >= this.MIN_CHUNKS && !this.isProcessing) {\n      this.isProcessing = true;\n      try {\n        const audioBuffer = Buffer.concat(this.buffer);\n        \n        // Create a temporary file for transcription\n        const tempFile = path.join(AUDIO_DIR, `${randomUUID()}.webm`);\n        fs.writeFileSync(tempFile, audioBuffer);\n        \n        // Transcribe with context using Whisper for reliability\n        const transcription = await openai.audio.transcriptions.create({\n          file: fs.createReadStream(tempFile),\n          model: \"whisper-1\", // Using the reliable Whisper model\n          language: \"en\",\n          prompt: `Previous context: ${this.transcriptionContext}\\nThis is a therapy conversation. Common terms may include: anxiety, depression, mindfulness, coping mechanism, emotional regulation, cognitive behavioral therapy.`,\n          response_format: \"text\", \n          temperature: 0.3 // Lower temperature for higher accuracy in therapeutic context\n        });\n        \n        // Clean up temp file\n        fs.unlinkSync(tempFile);\n        \n        // Update context with new transcription\n        this.transcriptionContext = transcription;\n        \n        // Clear processed chunks\n        this.buffer = [];\n        \n        return { text: transcription };\n      } finally {\n        this.isProcessing = false;\n      }\n    }\n    \n    return { text: '' };\n  }\n\n  reset() {\n    this.buffer = [];\n    this.transcriptionContext = '';\n    this.lastProcessedTime = 0;\n  }\n}\n\n// Real-time streaming response optimized for gpt-4o-mini-realtime-preview\nexport async function streamAITherapistResponse(\n  ws: WebSocket,\n  userMessage: string,\n  conversationHistory: ConversationMessage[],\n  shouldStream: boolean,\n  systemPrompt?: string,\n  voiceConfig?: VoiceConfig,\n  conversationConfig?: ConversationConfig\n): Promise<AIResponse> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    if (ws.readyState !== WebSocket.OPEN) {\n      throw new Error(\"WebSocket is not open\");\n    }\n\n    // Use a more compact history for real-time interactions\n    // This is critical for fast, responsive AI in therapy contexts\n    const optimizedHistory = conversationHistory.slice(-4); // Keep last 4 messages for context\n    \n    const messages = [\n      {\n        role: \"system\",\n        content: systemPrompt || createSystemPrompt(conversationConfig)\n      },\n      ...optimizedHistory,\n      {\n        role: \"user\",\n        content: userMessage\n      }\n    ];\n\n    // Convert messages to proper format for OpenAI API\n    const formattedMessages = messages.map(msg => ({\n      role: msg.role as \"system\" | \"user\" | \"assistant\", \n      content: msg.content\n    })) as any; // Type assertion needed to match OpenAI's types\n    \n    // Use the most appropriate model for our real-time therapy application\n    console.log(`Using ${CHAT_MODEL} for therapeutic conversation`);\n    const stream = await openai.chat.completions.create({\n      model: CHAT_MODEL, // the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024\n      messages: formattedMessages,\n      max_tokens: conversationConfig?.responseStyle.maxResponseLength || 150,\n      temperature: conversationConfig?.responseStyle.temperature || 0.7,\n      presence_penalty: conversationConfig?.responseStyle.presencePenalty || 0.1,\n      frequency_penalty: conversationConfig?.responseStyle.frequencyPenalty || 0.1,\n      top_p: 0.9,\n      stream: true // Always stream for better real-time experience\n    });\n\n    let fullResponse = \"\";\n    let currentSentence = \"\";\n    let isFirstChunk = true;\n\n    // Process stream with optimized sentence detection for chat completions\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content || \"\";\n      if (content) {\n        fullResponse += content;\n        currentSentence += content;\n\n        // Detect sentence boundaries for natural response\n        if (currentSentence.match(/[.!?]\\s*$/) || \n            currentSentence.length > 50) { // Also break on long phrases\n          // Send complete sentence\n          ws.send(JSON.stringify({\n            type: \"text\",\n            content: currentSentence\n          }));\n\n          // Generate audio for the sentence\n          if (shouldStream) {\n            await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n          }\n\n          currentSentence = \"\";\n        }\n      }\n    }\n\n    // Handle any remaining text\n    if (currentSentence) {\n      ws.send(JSON.stringify({\n        type: \"text\",\n        content: currentSentence\n      }));\n      if (shouldStream) {\n        await streamAudioResponse(ws, currentSentence, voiceConfig || DEFAULT_VOICE_CONFIG);\n      }\n    }\n\n    return {\n      message: fullResponse,\n      audioUrl: undefined\n    };\n  } catch (error) {\n    console.error(\"Error in streamAITherapistResponse:\", error);\n    if (ws.readyState === WebSocket.OPEN) {\n      ws.send(JSON.stringify({ \n        type: \"error\",\n        message: \"Error processing request\"\n      }));\n    }\n    throw error;\n  }\n}\n\n// Enhanced real-time audio streaming\nasync function streamAudioResponse(\n  ws: WebSocket,\n  text: string,\n  voiceConfig: VoiceConfig\n): Promise<void> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key not configured\");\n    }\n\n    // Validate inputs before sending to OpenAI\n    const sanitizedText = text?.trim() || \"I'm sorry, I couldn't process that properly.\";\n    \n    // Ensure voice is one of the valid OpenAI voices\n    const validVoice = [\"alloy\", \"echo\", \"fable\", \"onyx\", \"nova\", \"shimmer\"].includes(voiceConfig.voice)\n      ? voiceConfig.voice\n      : \"shimmer\"; // Default to shimmer if invalid\n    \n    // Ensure speed is within valid range\n    const validSpeed = typeof voiceConfig.speed === 'number' && \n                      voiceConfig.speed >= 0.5 && \n                      voiceConfig.speed <= 2.0\n      ? voiceConfig.speed\n      : 1.1; // Default to 1.1 if invalid\n    \n    console.log(`Generating speech with voice=${validVoice}, speed=${validSpeed.toFixed(2)}`);\n    \n    // Add SSML-like enhancements if needed through text preprocessing\n    const enhancedText = enhanceTextForValeVoice(sanitizedText, voiceConfig);\n\n    // Generate speech with TTS-1 (reliable production model)\n    const audioResponse = await openai.audio.speech.create({\n      model: \"tts-1\", // Using the stable production TTS model\n      voice: validVoice,\n      input: enhancedText,\n      speed: validSpeed,\n      response_format: \"mp3\"\n    });\n\n    // Stream audio in optimized chunks with better error handling\n    const buffer = Buffer.from(await audioResponse.arrayBuffer());\n    \n    // Log successful audio generation\n    console.log(`Generated ${buffer.length} bytes of audio for \"${sanitizedText.substring(0, 50)}...\"`);\n    \n    const chunkSize = 4096; // Balanced chunk size for streaming\n    \n    for (let i = 0; i < buffer.length; i += chunkSize) {\n      const chunk = buffer.slice(i, i + chunkSize);\n      ws.send(JSON.stringify({\n        type: \"audio_chunk\",\n        audioData: chunk.toString('base64')\n      }));\n    }\n  } catch (error) {\n    console.error(\"Error streaming audio:\", error);\n    throw error;\n  }\n}\n\n// Helper function to create system prompt based on conversation config\nfunction createSystemPrompt(config?: ConversationConfig): string {\n  const defaultPrompt = \n    \"You are LamaMind, an empathetic AI therapist with a bright and inquisitive personality. \" +\n    \"Your therapeutic approach combines warmth with curiosity-driven exploration. \" +\n    \"Speak in a bright, engaging tone. Convey warmth through your word choice and phrasing. \" +\n    \"Ask thoughtful questions with a curious, exploratory tone. \" +\n    \"Use conversational language with occasional upward inflections at the end of questions to show interest. \" +\n    \"Keep responses concise (2-3 sentences max) as this is a voice conversation. \" +\n    \"Never diagnose conditions or prescribe treatments. \" +\n    \"Respond authentically, as if having a real conversation rather than giving scripted responses.\";\n\n  if (!config) return defaultPrompt;\n\n  // Enhance prompt based on conversation config\n  return defaultPrompt + \"\\n\" +\n    `Response Style Guidelines:\n    - Keep responses between ${config.responseStyle.minResponseLength} and ${config.responseStyle.maxResponseLength} words\n    - Use a conversational tone with ${config.turnTaking.backchannelFrequency * 100}% backchannel responses\n    - Maintain natural pauses of ${config.turnTaking.minSilenceDuration}ms between turns\n    - Allow for interruptions with gaps up to ${config.turnTaking.maxInterruptionGap}ms`;\n}\n\n// Function to summarize a completed AI therapy conversation with Vale-like insightful tone\nexport async function summarizeTherapyConversation(\n  conversationHistory: Array<{role: \"user\" | \"assistant\" | \"system\", content: string}>\n): Promise<string> {\n  try {\n    if (!openai.apiKey) {\n      throw new Error(\"OpenAI API key is missing\");\n    }\n    \n    // Create a readable format of the conversation for the AI to analyze\n    const conversationText = conversationHistory\n      .filter(msg => msg.role !== \"system\")\n      .map(msg => `${msg.role === \"user\" ? \"Client\" : \"AI Therapist\"}: ${msg.content}`)\n      .join(\"\\n\\n\");\n    \n    const response = await openai.chat.completions.create({\n      model: CHAT_MODEL,\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are LamaMind's summary module with a bright, inquisitive analytical approach. \" +\n            \"When summarizing therapy conversations, maintain a thoughtful and insightful tone while being professionally thorough. \" +\n            \"Summarize the following therapy conversation in 3-4 paragraphs, with a focus on: \" +\n            \"1) Key themes and patterns that emerged during the conversation \" +\n            \"2) The client's emotional states and shifts throughout the session \" +\n            \"3) Important insights or breakthroughs that occurred \" +\n            \"4) Specific areas that might benefit from deeper exploration in future sessions \" +\n            \"5) Any notable therapeutic techniques that were particularly effective \" +\n            \"Balance clinical precision with warmth and understanding in your analysis.\"\n        },\n        {\n          role: \"user\",\n          content: `Please summarize this therapy conversation for the therapist's notes, highlighting patterns and potential next steps:\\n\\n${conversationText}`\n        }\n      ],\n      max_tokens: 500,\n      temperature: 0.7, // Balanced between consistency and creativity\n      top_p: 0.9, // Focus on highest probability tokens while allowing some variety\n    });\n    \n    return response.choices[0].message.content || \n      \"Unable to generate a summary of the conversation.\";\n  } catch (error) {\n    console.error(\"Error summarizing therapy conversation:\", error);\n    return \"Error generating conversation summary. Please review the conversation manually.\";\n  }\n}\n", "path": "server/openai.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["90bddb46-32f6-40d1-9e0d-e5c04fb6c1c4", {"value": {"selectedCode": "", "prefix": "import React, { useState, useRef, useEffect } from 'react';\n", "suffix": "import { useToast } from '@/hooks/use-toast';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Loader2 } from 'lucide-react';\nimport { Card } from '@/components/ui/card';\n\ninterface RealTimeVoiceChatProps {\n  userId: number;\n  clientId?: number;\n  onConversationCreated?: (conversationId: number) => void;\n  onConversationEnded?: (summary: string, conversationId: number) => void;\n}\n\nconst RealTimeVoiceChat: React.FC<RealTimeVoiceChatProps> = ({ \n  userId, \n  clientId = 1,\n  onConversationCreated,\n  onConversationEnded \n}) => {\n  const { toast } = useToast();\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [messages, setMessages] = useState<{\n    role: 'user' | 'assistant';\n    content: string;\n    isStreaming?: boolean;\n  }[]>([]);\n  const [conversationId, setConversationId] = useState<number | null>(null);\n  const [wsStatus, setWsStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');\n  \n  const wsRef = useRef<WebSocket | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const micStreamRef = useRef<MediaStream | null>(null);\n  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);\n  const audioQueueRef = useRef<Float32Array[]>([]);\n  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  \n  // Initialize WebSocket connection\n  useEffect(() => {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    \n    setWsStatus('connecting');\n    const ws = new WebSocket(wsUrl);\n    wsRef.current = ws;\n    \n    ws.onopen = () => {\n      setWsStatus('connected');\n      // Start the conversation\n      ws.send(JSON.stringify({\n        type: 'start',\n        userId,\n        clientId\n      }));\n    };\n    \n    ws.onclose = () => {\n      setWsStatus('disconnected');\n    };\n    \n    ws.onerror = () => {\n      setWsStatus('error');\n      toast({\n        title: \"Connection Error\",\n        description: \"Failed to connect to therapy server\",\n        variant: \"destructive\"\n      });\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const data = JSON.parse(event.data);\n        \n        if (data.type === 'ready') {\n          setConversationId(data.conversationId);\n          if (onConversationCreated) {\n            onConversationCreated(data.conversationId);\n          }\n          \n          // Add welcome message\n          setMessages([\n            {\n              role: 'assistant',\n              content: \"Hello, I'm your AI therapist. How can I help you today?\",\n              isStreaming: false\n            }\n          ]);\n        }\n        else if (data.type === 'stream') {\n          // Update the last assistant message with streaming content\n          setMessages(prev => {\n            const newMessages = [...prev];\n            const lastMessage = newMessages[newMessages.length - 1];\n            if (lastMessage.role === 'assistant') {\n              lastMessage.content = data.content;\n              lastMessage.isStreaming = true;\n            } else {\n              newMessages.push({\n                role: 'assistant',\n                content: data.content,\n                isStreaming: true\n              });\n            }\n            return newMessages;\n          });\n        }\n        else if (data.type === 'complete') {\n          // Mark the last assistant message as complete\n          setMessages(prev => {\n            const newMessages = [...prev];\n            const lastMessage = newMessages[newMessages.length - 1];\n            if (lastMessage.role === 'assistant') {\n              lastMessage.isStreaming = false;\n            }\n            return newMessages;\n          });\n        }\n        else if (data.type === 'summary') {\n          // Handle session summary\n          if (onConversationEnded && conversationId) {\n            onConversationEnded(data.content, conversationId);\n          }\n        }\n        else if (data.type === 'error') {\n          toast({\n            title: \"Error\",\n            description: data.message,\n            variant: \"destructive\"\n          });\n          setIsProcessing(false);\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    \n    return () => {\n      if (ws.readyState === WebSocket.OPEN) {\n        ws.close();\n      }\n    };\n  }, [userId, clientId, toast, onConversationCreated]);\n  \n  // Start real-time voice processing\n  const startRecording = async () => {\n    try {\n      // Request microphone access\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      micStreamRef.current = stream;\n      \n      // Create audio context\n      const audioContext = new AudioContext();\n      audioContextRef.current = audioContext;\n      \n      // Create audio source from microphone\n      const source = audioContext.createMediaStreamSource(stream);\n      \n      // Create script processor for real-time audio processing\n      const processor = audioContext.createScriptProcessor(4096, 1, 1);\n      audioProcessorRef.current = processor;\n      \n      // Process audio in real-time\n      processor.onaudioprocess = (e) => {\n        const inputData = e.inputBuffer.getChannelData(0);\n        audioQueueRef.current.push(new Float32Array(inputData));\n        \n        // Process audio queue every 100ms\n        if (!processingTimeoutRef.current) {\n          processingTimeoutRef.current = setTimeout(() => {\n            if (audioQueueRef.current.length > 0 && wsRef.current?.readyState === WebSocket.OPEN) {\n              // Convert audio data to base64 and send\n              const audioData = audioQueueRef.current;\n              audioQueueRef.current = [];\n              \n              // Send audio data to server for real-time processing\n              wsRef.current.send(JSON.stringify({\n                type: 'audio',\n                data: audioData\n              }));\n            }\n            processingTimeoutRef.current = null;\n          }, 100);\n        }\n      };\n      \n      // Connect processor to audio context\n      source.connect(processor);\n      processor.connect(audioContext.destination);\n      \n      setIsRecording(true);\n      toast({\n        title: \"Recording started\",\n        description: \"Speak naturally - the AI will respond in real-time\"\n      });\n    } catch (error) {\n      console.error('Error accessing microphone:', error);\n      toast({\n        title: \"Microphone Error\",\n        description: \"Could not access your microphone. Please check permissions.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n  \n  // Stop recording\n  const stopRecording = () => {\n    if (audioProcessorRef.current) {\n      audioProcessorRef.current.disconnect();\n      audioProcessorRef.current = null;\n    }\n    \n    if (micStreamRef.current) {\n      micStreamRef.current.getTracks().forEach(track => track.stop());\n      micStreamRef.current = null;\n    }\n    \n    if (audioContextRef.current) {\n      audioContextRef.current.close();\n      audioContextRef.current = null;\n    }\n    \n    if (processingTimeoutRef.current) {\n      clearTimeout(processingTimeoutRef.current);\n      processingTimeoutRef.current = null;\n    }\n    \n    setIsRecording(false);\n    toast({\n      title: \"Recording stopped\",\n      description: \"The conversation will continue in real-time\"\n    });\n  };\n\n  // End session and request summary\n  const endSession = () => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      // First end the session\n      wsRef.current.send(JSON.stringify({\n        type: \"conversationEnded\"\n      }));\n\n      // Stop recording\n      stopRecording();\n\n      // Request summary after a brief delay\n      setTimeout(() => {\n        if (wsRef.current?.readyState === WebSocket.OPEN) {\n          wsRef.current.send(JSON.stringify({\n            type: \"summary_request\"\n          }));\n        }\n      }, 500);\n\n      toast({\n        title: \"Session ended\",\n        description: \"Generating session summary...\",\n      });\n    }\n  };\n  \n  return (\n    <Card className=\"w-full max-w-3xl mx-auto\">\n      {/* Chat messages */}\n      <div className=\"h-[500px] overflow-y-auto p-4\">\n        {messages.map((message, index) => (\n          <div\n            key={index}\n            className={`mb-4 ${\n              message.role === \"user\" ? \"text-right\" : \"text-left\"\n            }`}\n          >\n            <div\n              className={`inline-block p-3 rounded-lg ${\n                message.role === \"user\"\n                  ? \"bg-blue-500 text-white\"\n                  : \"bg-gray-100 text-gray-800\"\n              }`}\n            >\n              {message.content}\n              {message.isStreaming && (\n                <span className=\"inline-block w-1 h-4 ml-1 bg-current animate-pulse\"></span>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {/* Controls */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <span\n              className={`h-2 w-2 rounded-full ${\n                wsStatus === 'connected'\n                  ? 'bg-green-500'\n                  : wsStatus === 'connecting'\n                  ? 'bg-yellow-500'\n                  : 'bg-red-500'\n              }`}\n            ></span>\n            <span className=\"text-sm text-gray-500\">\n              {wsStatus === 'connected' ? 'Connected' : wsStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}\n            </span>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button\n              onClick={isRecording ? stopRecording : startRecording}\n              className={`${\n                isRecording\n                  ? \"bg-red-500 hover:bg-red-600\"\n                  : \"bg-blue-500 hover:bg-blue-600\"\n              }`}\n              disabled={wsStatus !== 'connected'}\n            >\n              {isRecording ? (\n                <>\n                  <MicOff className=\"h-4 w-4 mr-2\" />\n                  Stop Recording\n                </>\n              ) : (\n                <>\n                  <Mic className=\"h-4 w-4 mr-2\" />\n                  Start Recording\n                </>\n              )}\n            </Button>\n\n            <Button\n              onClick={endSession}\n              variant=\"outline\"\n              disabled={wsStatus !== 'connected'}\n            >\n              End Session\n            </Button>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default RealTimeVoiceChat; ", "path": "client/src/components/RealTimeVoiceChat.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 34}}]]