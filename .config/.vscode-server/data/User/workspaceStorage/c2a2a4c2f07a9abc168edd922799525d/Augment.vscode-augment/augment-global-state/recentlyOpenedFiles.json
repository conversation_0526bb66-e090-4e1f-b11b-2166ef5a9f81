[["/home/<USER>/workspace/server/openai-realtime-proxy.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/openai-realtime-proxy.ts"}}], ["/home/<USER>/workspace/test-full-conversation.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test-full-conversation.js"}}], ["/home/<USER>/workspace/server/openai.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/openai.ts"}}], ["/home/<USER>/workspace/client/src/components/VoiceChat.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/VoiceChat.tsx"}}], ["/home/<USER>/workspace/client/src/components/RealTimeVoiceChat.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RealTimeVoiceChat.tsx"}}], ["/home/<USER>/workspace/client/src/pages/VoiceTherapy.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/VoiceTherapy.tsx"}}], ["/home/<USER>/workspace/browser-test.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "browser-test.js"}}], ["/home/<USER>/workspace/client/src/pages/AdminAITest.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AdminAITest.tsx"}}]]