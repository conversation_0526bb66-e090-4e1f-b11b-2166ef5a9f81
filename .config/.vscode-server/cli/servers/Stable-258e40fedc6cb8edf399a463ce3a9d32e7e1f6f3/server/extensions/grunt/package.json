{"name": "grunt", "publisher": "vscode", "description": "Extension to add Grunt capabilities to VS Code.", "displayName": "Grunt support for VS Code", "version": "1.0.0", "private": true, "icon": "images/grunt.png", "license": "MIT", "engines": {"vscode": "*"}, "categories": ["Other"], "main": "./dist/main", "activationEvents": ["onTaskType:grunt"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": true}}, "contributes": {"configuration": {"id": "grunt", "type": "object", "title": "<PERSON><PERSON><PERSON>", "properties": {"grunt.autoDetect": {"scope": "application", "type": "string", "enum": ["off", "on"], "default": "off", "description": "%config.grunt.autoDetect%"}}}, "taskDefinitions": [{"type": "grunt", "required": ["task"], "properties": {"task": {"type": "string", "description": "%grunt.taskDefinition.type.description%"}, "args": {"type": "array", "description": "%grunt.taskDefinition.args.description%"}, "file": {"type": "string", "description": "%grunt.taskDefinition.file.description%"}}, "when": "shellExecutionSupported"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}