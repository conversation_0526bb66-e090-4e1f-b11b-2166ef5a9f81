{"name": "css-language-features", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.77.0"}, "icon": "icons/css.png", "activationEvents": ["onLanguage:css", "onLanguage:less", "onLanguage:scss", "onCommand:_css.applyCodeAction"], "main": "./client/dist/node/cssClientMain", "browser": "./client/dist/browser/cssClientMain", "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "categories": ["Programming Languages"], "contributes": {"configuration": [{"order": 22, "id": "css", "title": "%css.title%", "properties": {"css.customData": {"type": "array", "markdownDescription": "%css.customData.desc%", "default": [], "items": {"type": "string"}, "scope": "resource"}, "css.completion.triggerPropertyValueCompletion": {"type": "boolean", "scope": "resource", "default": true, "description": "%css.completion.triggerPropertyValueCompletion.desc%"}, "css.completion.completePropertyWithSemicolon": {"type": "boolean", "scope": "resource", "default": true, "description": "%css.completion.completePropertyWithSemicolon.desc%"}, "css.validate": {"type": "boolean", "scope": "resource", "default": true, "description": "%css.validate.desc%"}, "css.hover.documentation": {"type": "boolean", "scope": "resource", "default": true, "description": "%css.hover.documentation%"}, "css.hover.references": {"type": "boolean", "scope": "resource", "default": true, "description": "%css.hover.references%"}, "css.lint.compatibleVendorPrefixes": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.compatibleVendorPrefixes.desc%"}, "css.lint.vendorPrefix": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%css.lint.vendorPrefix.desc%"}, "css.lint.duplicateProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.duplicateProperties.desc%"}, "css.lint.emptyRules": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%css.lint.emptyRules.desc%"}, "css.lint.importStatement": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.importStatement.desc%"}, "css.lint.boxModel": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%css.lint.boxModel.desc%"}, "css.lint.universalSelector": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%css.lint.universalSelector.desc%"}, "css.lint.zeroUnits": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.zeroUnits.desc%"}, "css.lint.fontFaceProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "%css.lint.fontFaceProperties.desc%"}, "css.lint.hexColorLength": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "error", "description": "%css.lint.hexColorLength.desc%"}, "css.lint.argumentsInColorFunction": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "error", "description": "%css.lint.argumentsInColorFunction.desc%"}, "css.lint.unknownProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%css.lint.unknownProperties.desc%"}, "css.lint.validProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}, "scope": "resource", "default": [], "description": "%css.lint.validProperties.desc%"}, "css.lint.ieHack": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.ieHack.desc%"}, "css.lint.unknownVendorSpecificProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.unknownVendorSpecificProperties.desc%"}, "css.lint.propertyIgnoredDueToDisplay": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "%css.lint.propertyIgnoredDueToDisplay.desc%"}, "css.lint.important": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%css.lint.important.desc%"}, "css.lint.float": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%css.lint.float.desc%"}, "css.lint.idSelector": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%css.lint.idSelector.desc%"}, "css.lint.unknownAtRules": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%css.lint.unknownAtRules.desc%"}, "css.trace.server": {"type": "string", "scope": "window", "enum": ["off", "messages", "verbose"], "default": "off", "description": "%css.trace.server.desc%"}, "css.format.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%css.format.enable.desc%"}, "css.format.newlineBetweenSelectors": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%css.format.newlineBetweenSelectors.desc%"}, "css.format.newlineBetweenRules": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%css.format.newlineBetweenRules.desc%"}, "css.format.spaceAroundSelectorSeparator": {"type": "boolean", "scope": "resource", "default": false, "markdownDescription": "%css.format.spaceAroundSelectorSeparator.desc%"}, "css.format.braceStyle": {"type": "string", "scope": "resource", "default": "collapse", "enum": ["collapse", "expand"], "markdownDescription": "%css.format.braceStyle.desc%"}, "css.format.preserveNewLines": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%css.format.preserveNewLines.desc%"}, "css.format.maxPreserveNewLines": {"type": ["number", "null"], "scope": "resource", "default": null, "markdownDescription": "%css.format.maxPreserveNewLines.desc%"}}}, {"id": "scss", "order": 24, "title": "%scss.title%", "properties": {"scss.completion.triggerPropertyValueCompletion": {"type": "boolean", "scope": "resource", "default": true, "description": "%scss.completion.triggerPropertyValueCompletion.desc%"}, "scss.completion.completePropertyWithSemicolon": {"type": "boolean", "scope": "resource", "default": true, "description": "%scss.completion.completePropertyWithSemicolon.desc%"}, "scss.validate": {"type": "boolean", "scope": "resource", "default": true, "description": "%scss.validate.desc%"}, "scss.hover.documentation": {"type": "boolean", "scope": "resource", "default": true, "description": "%scss.hover.documentation%"}, "scss.hover.references": {"type": "boolean", "scope": "resource", "default": true, "description": "%scss.hover.references%"}, "scss.lint.compatibleVendorPrefixes": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.compatibleVendorPrefixes.desc%"}, "scss.lint.vendorPrefix": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%scss.lint.vendorPrefix.desc%"}, "scss.lint.duplicateProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.duplicateProperties.desc%"}, "scss.lint.emptyRules": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%scss.lint.emptyRules.desc%"}, "scss.lint.importStatement": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.importStatement.desc%"}, "scss.lint.boxModel": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%scss.lint.boxModel.desc%"}, "scss.lint.universalSelector": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%scss.lint.universalSelector.desc%"}, "scss.lint.zeroUnits": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.zeroUnits.desc%"}, "scss.lint.fontFaceProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "%scss.lint.fontFaceProperties.desc%"}, "scss.lint.hexColorLength": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "error", "description": "%scss.lint.hexColorLength.desc%"}, "scss.lint.argumentsInColorFunction": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "error", "description": "%scss.lint.argumentsInColorFunction.desc%"}, "scss.lint.unknownProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%scss.lint.unknownProperties.desc%"}, "scss.lint.validProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}, "scope": "resource", "default": [], "description": "%scss.lint.validProperties.desc%"}, "scss.lint.ieHack": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.ieHack.desc%"}, "scss.lint.unknownVendorSpecificProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.unknownVendorSpecificProperties.desc%"}, "scss.lint.propertyIgnoredDueToDisplay": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "%scss.lint.propertyIgnoredDueToDisplay.desc%"}, "scss.lint.important": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%scss.lint.important.desc%"}, "scss.lint.float": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%scss.lint.float.desc%"}, "scss.lint.idSelector": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%scss.lint.idSelector.desc%"}, "scss.lint.unknownAtRules": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%scss.lint.unknownAtRules.desc%"}, "scss.format.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%scss.format.enable.desc%"}, "scss.format.newlineBetweenSelectors": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%scss.format.newlineBetweenSelectors.desc%"}, "scss.format.newlineBetweenRules": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%scss.format.newlineBetweenRules.desc%"}, "scss.format.spaceAroundSelectorSeparator": {"type": "boolean", "scope": "resource", "default": false, "markdownDescription": "%scss.format.spaceAroundSelectorSeparator.desc%"}, "scss.format.braceStyle": {"type": "string", "scope": "resource", "default": "collapse", "enum": ["collapse", "expand"], "markdownDescription": "%scss.format.braceStyle.desc%"}, "scss.format.preserveNewLines": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%scss.format.preserveNewLines.desc%"}, "scss.format.maxPreserveNewLines": {"type": ["number", "null"], "scope": "resource", "default": null, "markdownDescription": "%scss.format.maxPreserveNewLines.desc%"}}}, {"id": "less", "order": 23, "type": "object", "title": "%less.title%", "properties": {"less.completion.triggerPropertyValueCompletion": {"type": "boolean", "scope": "resource", "default": true, "description": "%less.completion.triggerPropertyValueCompletion.desc%"}, "less.completion.completePropertyWithSemicolon": {"type": "boolean", "scope": "resource", "default": true, "description": "%less.completion.completePropertyWithSemicolon.desc%"}, "less.validate": {"type": "boolean", "scope": "resource", "default": true, "description": "%less.validate.desc%"}, "less.hover.documentation": {"type": "boolean", "scope": "resource", "default": true, "description": "%less.hover.documentation%"}, "less.hover.references": {"type": "boolean", "scope": "resource", "default": true, "description": "%less.hover.references%"}, "less.lint.compatibleVendorPrefixes": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.compatibleVendorPrefixes.desc%"}, "less.lint.vendorPrefix": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%less.lint.vendorPrefix.desc%"}, "less.lint.duplicateProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.duplicateProperties.desc%"}, "less.lint.emptyRules": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%less.lint.emptyRules.desc%"}, "less.lint.importStatement": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.importStatement.desc%"}, "less.lint.boxModel": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%less.lint.boxModel.desc%"}, "less.lint.universalSelector": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%less.lint.universalSelector.desc%"}, "less.lint.zeroUnits": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.zeroUnits.desc%"}, "less.lint.fontFaceProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "%less.lint.fontFaceProperties.desc%"}, "less.lint.hexColorLength": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "error", "description": "%less.lint.hexColorLength.desc%"}, "less.lint.argumentsInColorFunction": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "error", "description": "%less.lint.argumentsInColorFunction.desc%"}, "less.lint.unknownProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%less.lint.unknownProperties.desc%"}, "less.lint.validProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string"}, "scope": "resource", "default": [], "description": "%less.lint.validProperties.desc%"}, "less.lint.ieHack": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.ieHack.desc%"}, "less.lint.unknownVendorSpecificProperties": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.unknownVendorSpecificProperties.desc%"}, "less.lint.propertyIgnoredDueToDisplay": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "markdownDescription": "%less.lint.propertyIgnoredDueToDisplay.desc%"}, "less.lint.important": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%less.lint.important.desc%"}, "less.lint.float": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "markdownDescription": "%less.lint.float.desc%"}, "less.lint.idSelector": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "ignore", "description": "%less.lint.idSelector.desc%"}, "less.lint.unknownAtRules": {"type": "string", "scope": "resource", "enum": ["ignore", "warning", "error"], "default": "warning", "description": "%less.lint.unknownAtRules.desc%"}, "less.format.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%less.format.enable.desc%"}, "less.format.newlineBetweenSelectors": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%less.format.newlineBetweenSelectors.desc%"}, "less.format.newlineBetweenRules": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%less.format.newlineBetweenRules.desc%"}, "less.format.spaceAroundSelectorSeparator": {"type": "boolean", "scope": "resource", "default": false, "markdownDescription": "%less.format.spaceAroundSelectorSeparator.desc%"}, "less.format.braceStyle": {"type": "string", "scope": "resource", "default": "collapse", "enum": ["collapse", "expand"], "markdownDescription": "%less.format.braceStyle.desc%"}, "less.format.preserveNewLines": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%less.format.preserveNewLines.desc%"}, "less.format.maxPreserveNewLines": {"type": ["number", "null"], "scope": "resource", "default": null, "markdownDescription": "%less.format.maxPreserveNewLines.desc%"}}}], "configurationDefaults": {"[css]": {"editor.suggest.insertMode": "replace"}, "[scss]": {"editor.suggest.insertMode": "replace"}, "[less]": {"editor.suggest.insertMode": "replace"}}, "jsonValidation": [{"fileMatch": "*.css-data.json", "url": "https://raw.githubusercontent.com/microsoft/vscode-css-languageservice/master/docs/customData.schema.json"}, {"fileMatch": "package.json", "url": "./schemas/package.schema.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}