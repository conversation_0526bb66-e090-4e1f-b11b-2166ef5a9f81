import{S as de,i as he,s as we,V as j,c as H,e as x,n as I,h as L,y as C,D as N,a3 as G,z as S,f as X,u as c,q as E,t as $,r as D,B as _,af as oe,ag as O,a1 as _e,a6 as Y,ac as ye,F as ke,a7 as xe,C as Q,w as Le,E as T}from"./SpinnerAugment-JC8TPhVf.js";import{e as W,u as Re,o as ve}from"./BaseButton-C1unVd78.js";import{G as Ie,g as Ee,t as De,a as qe,M as be,A as Ae,b as Fe,R as Me,c as He,S as Ne,d as Te,e as je,f as Be,h as ze,C as Ge,i as We,U as Pe,j as Ce,E as Ue,k as Ve,l as Je}from"./RemoteAgentRetry-wzAOG5IN.js";import"./Content-xvE836E_.js";import{S as Z,i as se,a as Ke,b as Oe,c as Qe,d as Xe,e as Ye,f as Ze,g as et,h as tt,j as nt,k as rt,E as ot}from"./lodash-l00D6itj.js";import"./folder-IxOJUoWR.js";import{R as st}from"./open-in-new-window-CkR7J3XO.js";import{R as at}from"./types-Cgd-nZOV.js";import"./isObjectLike-BA2QYXi-.js";import{S as lt}from"./main-panel-zHcgjx1c.js";import{aF as it,aG as mt}from"./AugmentMessage-BNt-hbbl.js";import"./MaterialIcon-D8Nb6HkU.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-DiI90jLk.js";import"./Keybindings-C19Zl3Ej.js";import"./pen-to-square-3TLxExQu.js";import"./exclamation-triangle-DfKf7sb_.js";import"./CardAugment-D2Hs6UjS.js";import"./TextTooltipAugment-WghC7pXE.js";import"./IconButtonAugment-D_WR2I26.js";import"./index-Yat2JVWz.js";import"./augment-logo-E1jEbeRV.js";import"./ButtonAugment-PWVCCDdn.js";import"./folder-opened-bSDyFrZo.js";import"./expand-C7dSG_GJ.js";import"./diff-utils-Bh9TYMbV.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CK0xjdO4.js";import"./layer-group-DMm-CNlu.js";import"./github-C1jNNmyr.js";import"./types-B5Ac2hek.js";import"./chat-types-NgqNgjwU.js";import"./globals-D0QH3NT1.js";import"./rules-parser-D2d7xQ-G.js";import"./file-paths-BcSg4gks.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-BYCw5p2g.js";import"./design-system-init-Bf1-mlh4.js";import"./StatusIndicator-BiyeFzqm.js";import"./index-CW7fyhvB.js";import"./await_block-C0teov-5.js";import"./ellipsis-ce3_p-Q7.js";import"./Filespan-DeFTcAEj.js";import"./terminal-BBUsFUTj.js";import"./VSCodeCodicon-zeLUoeQd.js";import"./chat-flags-model-S_DCcmJS.js";import"./mcp-logo-TxRUeZhS.js";import"./IconFilePath-BjUJjRTr.js";import"./LanguageIcon-CA8dtZ_C.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-D5YyJVGd.js";import"./chevron-down-B-gSyyd4.js";function ct(o){let t;return{c(){t=j("div"),t.innerHTML='<span class="c-paused-remote-agent__text svelte-cye2b6">This agent is paused and will resume when you send a new message</span>',H(t,"class","c-paused-remote-agent svelte-cye2b6")},m(n,e){x(n,t,e)},p:I,i:I,o:I,d(n){n&&L(t)}}}class ut extends de{constructor(t){super(),he(this,t,null,ct,we,{})}}function ae(o,t,n){const e=o.slice();e[37]=t[n],e[40]=n;const r=e[40]+1===e[12].length;return e[38]=r,e}function le(o,t,n){const e=o.slice();e[41]=t[n].turn,e[42]=t[n].idx;const r=e[42]+1===e[13].length;return e[43]=r,e}function ie(o){let t,n;return t=new Ae({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function $t(o){let t,n,e,r;const s=[Ct,Mt],m=[];function p(l,i){return l[17].enableRichCheckpointInfo?0:1}return t=p(o),n=m[t]=s[t](o),{c(){n.c(),e=T()},m(l,i){m[t].m(l,i),x(l,e,i),r=!0},p(l,i){let a=t;t=p(l),t===a?m[t].p(l,i):(E(),$(m[a],1,1,()=>{m[a]=null}),D(),n=m[t],n?n.p(l,i):(n=m[t]=s[t](l),n.c()),c(n,1),n.m(e.parentNode,e))},i(l){r||(c(n),r=!0)},o(l){$(n),r=!1},d(l){l&&L(e),m[t].d(l)}}}function pt(o){let t,n;return t=new Ge({props:{group:o[37],chatModel:o[1],turn:o[41],turnIndex:o[42],isLastTurn:o[43],messageListContainer:o[0]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[37]),2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.turn=e[41]),4096&r[0]&&(s.turnIndex=e[42]),12288&r[0]&&(s.isLastTurn=e[43]),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function gt(o){let t,n;return t=new We({props:{stage:o[41].stage,iterationId:o[41].iterationId,stageCount:o[41].stageCount}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.stage=e[41].stage),4096&r[0]&&(s.iterationId=e[41].iterationId),4096&r[0]&&(s.stageCount=e[41].stageCount),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function ft(o){let t,n;return t=new Pe({props:{chatModel:o[1],msg:o[41].response_text??""}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.msg=e[41].response_text??""),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function dt(o){let t,n;return t=new it({props:{group:o[37],markdown:o[41].response_text??"",messageListContainer:o[0]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[37]),4096&r[0]&&(s.markdown=e[41].response_text??""),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function ht(o){let t,n;function e(){return o[30](o[41])}return t=new Ce({props:{turn:o[41],preamble:lt,resendTurn:e,$$slots:{default:[St]},$$scope:{ctx:o}}}),{c(){C(t.$$.fragment)},m(r,s){S(t,r,s),n=!0},p(r,s){o=r;const m={};4096&s[0]&&(m.turn=o[41]),4100&s[0]&&(m.resendTurn=e),69632&s[0]|32768&s[1]&&(m.$$scope={dirty:s,ctx:o}),t.$set(m)},i(r){n||(c(t.$$.fragment,r),n=!0)},o(r){$(t.$$.fragment,r),n=!1},d(r){_(t,r)}}}function wt(o){let t,n;return t=new Ue({props:{flagsModel:o[14],turn:o[41]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};16384&r[0]&&(s.flagsModel=e[14]),4096&r[0]&&(s.turn=e[41]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function yt(o){let t,n;return t=new Ce({props:{turn:o[41]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[41]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Mt(o){let t,n;return t=new Ve({props:{turn:o[41]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[41]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Ct(o){let t,n;return t=new Je({props:{turn:o[41]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[41]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function St(o){let t,n;return t=new mt({props:{conversationModel:o[16],turn:o[41]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};65536&r[0]&&(s.conversationModel=e[16]),4096&r[0]&&(s.turn=e[41]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function me(o){let t,n,e,r;function s(){return o[31](o[41])}return{c(){t=j("div"),H(t,"class","c-msg-list__turn-seen")},m(m,p){x(m,t,p),e||(r=Y(n=He.call(null,t,{onSeen:s,track:o[41].seen_state!==Z.seen})),e=!0)},p(m,p){o=m,n&&ye(n.update)&&4096&p[0]&&n.update.call(null,{onSeen:s,track:o[41].seen_state!==Z.seen})},d(m){m&&L(t),e=!1,r()}}}function ce(o,t){let n,e,r,s,m,p,l,i,a,g,u,f,y,h,R=se(t[41]);const b=[yt,wt,ht,dt,ft,gt,pt,$t],v=[];function q(w,M){return 4096&M[0]&&(e=null),4096&M[0]&&(r=null),4096&M[0]&&(s=null),4096&M[0]&&(m=null),4096&M[0]&&(p=null),4096&M[0]&&(l=null),4096&M[0]&&(i=null),4096&M[0]&&(a=null),e==null&&(e=!!Ke(w[41])),e?0:(r==null&&(r=!!Oe(w[41])),r?1:(s==null&&(s=!!Qe(w[41])),s?2:(m==null&&(m=!!Xe(w[41])),m?3:(p==null&&(p=!!Ye(w[41])),p?4:(l==null&&(l=!!Ze(w[41])),l?5:(i==null&&(i=!!(et(w[41])||tt(w[41])||nt(w[41]))),i?6:(a==null&&(a=!(!rt(w[41])||w[41].status!==ot.success)),a?7:-1)))))))}~(g=q(t,[-1,-1]))&&(u=v[g]=b[g](t));let k=R&&me(t);return{key:o,first:null,c(){n=T(),u&&u.c(),f=N(),k&&k.c(),y=T(),this.first=n},m(w,M){x(w,n,M),~g&&v[g].m(w,M),x(w,f,M),k&&k.m(w,M),x(w,y,M),h=!0},p(w,M){let A=g;g=q(t=w,M),g===A?~g&&v[g].p(t,M):(u&&(E(),$(v[A],1,1,()=>{v[A]=null}),D()),~g?(u=v[g],u?u.p(t,M):(u=v[g]=b[g](t),u.c()),c(u,1),u.m(f.parentNode,f)):u=null),4096&M[0]&&(R=se(t[41])),R?k?k.p(t,M):(k=me(t),k.c(),k.m(y.parentNode,y)):k&&(k.d(1),k=null)},i(w){h||(c(u),h=!0)},o(w){$(u),h=!1},d(w){w&&(L(n),L(f),L(y)),~g&&v[g].d(w),k&&k.d(w)}}}function ue(o){let t,n,e,r,s;const m=[It,vt,Rt,Lt,xt,kt,_t],p=[];function l(a,g){return a[9]?0:a[5].retryMessage?1:a[5].showResumingRemoteAgent?2:a[5].showGeneratingResponse?3:a[5].showAwaitingUserInput?4:a[5].showStopped?5:a[8]?6:-1}~(t=l(o))&&(n=p[t]=m[t](o));let i=o[5].showRunningSpacer&&$e();return{c(){n&&n.c(),e=N(),i&&i.c(),r=T()},m(a,g){~t&&p[t].m(a,g),x(a,e,g),i&&i.m(a,g),x(a,r,g),s=!0},p(a,g){let u=t;t=l(a),t===u?~t&&p[t].p(a,g):(n&&(E(),$(p[u],1,1,()=>{p[u]=null}),D()),~t?(n=p[t],n?n.p(a,g):(n=p[t]=m[t](a),n.c()),c(n,1),n.m(e.parentNode,e)):n=null),a[5].showRunningSpacer?i||(i=$e(),i.c(),i.m(r.parentNode,r)):i&&(i.d(1),i=null)},i(a){s||(c(n),s=!0)},o(a){$(n),s=!1},d(a){a&&(L(e),L(r)),~t&&p[t].d(a),i&&i.d(a)}}}function _t(o){let t,n;return t=new ut({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:I,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function kt(o){let t,n;return t=new Ne({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:I,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function xt(o){let t,n;return t=new Te({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:I,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Lt(o){let t,n;return t=new je({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:I,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Rt(o){let t,n;return t=new Be({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:I,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function vt(o){let t,n;return t=new ze({props:{message:o[5].retryMessage}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};32&r[0]&&(s.message=e[5].retryMessage),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function It(o){let t,n;return t=new Me({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function $e(o){let t;return{c(){t=j("div"),H(t,"class","c-agent-running-spacer svelte-t9khzq")},m(n,e){x(n,t,e)},d(n){n&&L(t)}}}function Et(o){let t,n,e,r=[],s=new Map,m=W(o[37]);const p=i=>i[41].request_id??`no-request-id-${i[42]}`;for(let i=0;i<m.length;i+=1){let a=le(o,m,i),g=p(a);s.set(g,r[i]=ce(g,a))}let l=o[38]&&ue(o);return{c(){for(let i=0;i<r.length;i+=1)r[i].c();t=N(),l&&l.c(),n=T()},m(i,a){for(let g=0;g<r.length;g+=1)r[g]&&r[g].m(i,a);x(i,t,a),l&&l.m(i,a),x(i,n,a),e=!0},p(i,a){17002503&a[0]&&(m=W(i[37]),E(),r=Re(r,a,p,1,i,m,s,t.parentNode,ve,ce,t,le),D()),i[38]?l?(l.p(i,a),4096&a[0]&&c(l,1)):(l=ue(i),l.c(),c(l,1),l.m(n.parentNode,n)):l&&(E(),$(l,1,1,()=>{l=null}),D())},i(i){if(!e){for(let a=0;a<m.length;a+=1)c(r[a]);c(l),e=!0}},o(i){for(let a=0;a<r.length;a+=1)$(r[a]);$(l),e=!1},d(i){i&&(L(t),L(n));for(let a=0;a<r.length;a+=1)r[a].d(i);l&&l.d(i)}}}function pe(o){let t,n;return t=new Fe({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[38],userControlsScroll:o[3],requestId:o[37][0].turn.request_id,releaseScroll:o[32],messageListContainer:o[0],minHeight:o[38]?o[7]:0,$$slots:{default:[Et]},$$scope:{ctx:o}}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.isLastItem=e[38]),8&r[0]&&(s.userControlsScroll=e[3]),4096&r[0]&&(s.requestId=e[37][0].turn.request_id),8&r[0]&&(s.releaseScroll=e[32]),1&r[0]&&(s.messageListContainer=e[0]),4224&r[0]&&(s.minHeight=e[38]?e[7]:0),226087&r[0]|32768&r[1]&&(s.$$scope={dirty:r,ctx:e}),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function ge(o){let t,n;return t=new Me({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Dt(o){let t,n,e,r,s,m,p,l=o[10]&&ie(),i=W(o[12]),a=[];for(let f=0;f<i.length;f+=1)a[f]=pe(ae(o,i,f));const g=f=>$(a[f],1,1,()=>{a[f]=null});let u=!o[13].length&&o[9]&&ge(o);return{c(){t=j("div"),l&&l.c(),n=N();for(let f=0;f<a.length;f+=1)a[f].c();e=N(),u&&u.c(),H(t,"class","c-msg-list svelte-t9khzq"),G(t,"c-msg-list--minimal",!o[17].fullFeatured)},m(f,y){x(f,t,y),l&&l.m(t,null),X(t,n);for(let h=0;h<a.length;h+=1)a[h]&&a[h].m(t,null);X(t,e),u&&u.m(t,null),o[33](t),s=!0,m||(p=[Y(De.call(null,t,{onScrollIntoBottom:o[21],onScrollAwayFromBottom:o[22],onScroll:o[34]})),Y(r=qe.call(null,t,{onHeightChange:o[35]}))],m=!0)},p(f,y){if(f[10]?l?1024&y[0]&&c(l,1):(l=ie(),l.c(),c(l,1),l.m(t,n)):l&&(E(),$(l,1,1,()=>{l=null}),D()),17003439&y[0]){let h;for(i=W(f[12]),h=0;h<i.length;h+=1){const R=ae(f,i,h);a[h]?(a[h].p(R,y),c(a[h],1)):(a[h]=pe(R),a[h].c(),c(a[h],1),a[h].m(t,e))}for(E(),h=i.length;h<a.length;h+=1)g(h);D()}!f[13].length&&f[9]?u?(u.p(f,y),8704&y[0]&&c(u,1)):(u=ge(f),u.c(),c(u,1),u.m(t,null)):u&&(E(),$(u,1,1,()=>{u=null}),D()),r&&ye(r.update)&&16&y[0]&&r.update.call(null,{onHeightChange:f[35]}),(!s||131072&y[0])&&G(t,"c-msg-list--minimal",!f[17].fullFeatured)},i(f){if(!s){c(l);for(let y=0;y<i.length;y+=1)c(a[y]);c(u),s=!0}},o(f){$(l),a=a.filter(Boolean);for(let y=0;y<a.length;y+=1)$(a[y]);$(u),s=!1},d(f){f&&L(t),l&&l.d(),ke(a,f),u&&u.d(),o[33](null),m=!1,xe(p)}}}function fe(o){let t,n;return t=new be({props:{messageListElement:o[0],showScrollDown:o[6]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};1&r[0]&&(s.messageListElement=e[0]),64&r[0]&&(s.showScrollDown=e[6]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function qt(o){let t,n,e,r;n=new Ie({props:{$$slots:{default:[Dt]},$$scope:{ctx:o}}});let s=o[11]&&fe(o);return{c(){t=j("div"),C(n.$$.fragment),e=N(),s&&s.c(),H(t,"class","c-msg-list-container svelte-t9khzq"),H(t,"data-testid","chat-message-list"),G(t,"c-msg-list--minimal",!o[17].fullFeatured)},m(m,p){x(m,t,p),S(n,t,null),X(t,e),s&&s.m(t,null),r=!0},p(m,p){const l={};227263&p[0]|32768&p[1]&&(l.$$scope={dirty:p,ctx:m}),n.$set(l),m[11]?s?(s.p(m,p),2048&p[0]&&c(s,1)):(s=fe(m),s.c(),c(s,1),s.m(t,null)):s&&(E(),$(s,1,1,()=>{s=null}),D()),(!r||131072&p[0])&&G(t,"c-msg-list--minimal",!m[17].fullFeatured)},i(m){r||(c(n.$$.fragment,m),c(s),r=!0)},o(m){$(n.$$.fragment,m),$(s),r=!1},d(m){m&&L(t),_(n),s&&s.d()}}}function bt(o,t,n){let e,r,s,m,p,l,i,a,g,u,f,y,h,R,b,v,q,k,w=I,M=I,A=()=>(M(),M=Q(B,d=>n(29,q=d)),B),P=I;o.$$.on_destroy.push(()=>w()),o.$$.on_destroy.push(()=>M()),o.$$.on_destroy.push(()=>P());let{chatModel:B}=t;A();let{onboardingWorkspaceModel:U}=t,{msgListElement:z}=t;const Se=oe("agentConversationModel"),{agentExchangeStatus:ee,isCurrConversationAgentic:te}=Se;O(o,ee,d=>n(28,v=d)),O(o,te,d=>n(27,b=d));const ne=oe(st.key);O(o,ne,d=>n(26,R=d));let F=!1;function V(){n(3,F=!0)}_e(()=>{var d;((d=h.lastExchange)==null?void 0:d.seen_state)===Z.unseen&&V()});let J=0;const re=d=>h.markSeen(d);return o.$$set=d=>{"chatModel"in d&&A(n(1,B=d.chatModel)),"onboardingWorkspaceModel"in d&&n(2,U=d.onboardingWorkspaceModel),"msgListElement"in d&&n(0,z=d.msgListElement)},o.$$.update=()=>{var d;536870912&o.$$.dirty[0]&&(n(15,e=q.currentConversationModel),w(),w=Q(e,K=>n(16,h=K))),536870912&o.$$.dirty[0]&&(n(14,r=q.flags),P(),P=Q(r,K=>n(17,k=K))),1006632960&o.$$.dirty[0]&&n(25,s=Ee(q,v,b,R)),33554432&o.$$.dirty[0]&&n(13,m=s.chatHistory),33554432&o.$$.dirty[0]&&n(12,p=s.groupedChatHistory),33554432&o.$$.dirty[0]&&n(5,l=s.lastGroupConfig),33554432&o.$$.dirty[0]&&n(11,i=s.doShowFloatingButtons),33554432&o.$$.dirty[0]&&n(10,a=s.doShowAgentSetupLogs),32&o.$$.dirty[0]&&n(9,g=l.remoteAgentErrorConfig),67108864&o.$$.dirty[0]&&n(8,u=R.isActive&&((d=R.currentAgent)==null?void 0:d.workspace_status)===at.workspacePaused),16&o.$$.dirty[0]&&n(7,f=J),8&o.$$.dirty[0]&&n(6,y=F)},[z,B,U,F,J,l,y,f,u,g,a,i,p,m,r,e,h,k,ee,te,ne,function(){n(3,F=!1)},function(){n(3,F=!0)},V,re,s,R,b,v,q,d=>U.retryProjectSummary(d),d=>re(d),()=>n(3,F=!0),function(d){Le[d?"unshift":"push"](()=>{z=d,n(0,z)})},d=>{d<=1&&V()},d=>n(4,J=d)]}class Fn extends de{constructor(t){super(),he(this,t,bt,qt,we,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Fn as default};
