var Cr=Object.defineProperty;var Ai=n=>{throw TypeError(n)};var Er=(n,t,e)=>t in n?Cr(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var h=(n,t,e)=>Er(n,typeof t!="symbol"?t+"":t,e),Un=(n,t,e)=>t.has(n)||Ai("Cannot "+e);var u=(n,t,e)=>(Un(n,t,"read from private field"),e?e.call(n):t.get(n)),X=(n,t,e)=>t.has(n)?Ai("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(n):t.set(n,e),U=(n,t,e,s)=>(Un(n,t,"write to private field"),s?s.call(n,e):t.set(n,e),e),D=(n,t,e)=>(Un(n,t,"access private method"),e);var on=(n,t,e,s)=>({set _(i){U(n,t,i,e)},get _(){return u(n,t,s)}});import{S as Ir,W as Et,e as ws,u as zo,o as qo}from"./BaseButton-C1unVd78.js";import{d as Mr,T as $n}from"./Content-xvE836E_.js";import{S as st,i as nt,s as it,a as W,b as ct,K as $e,L as Te,M as Se,N as Ce,h as E,d as bt,O as Ee,g as Vt,n as z,j as ut,ai as yt,ak as mt,aj as vn,az as zn,c as y,e as R,f as ht,V as pt,q as It,t as $,r as Mt,u as k,y as F,z as O,B as N,am as Ho,D as Ft,T as Bo,G as ce,H as wi,aA as Ze,ab as rn,R as Ms,a3 as ae,a5 as Oe,X as As,Y as Ds,Z as Rs,a7 as Ar,E as Yt,a8 as vt,w as Fe,a1 as Dr,a0 as Rr,al as Xs,x as ys,A as ks,$ as Tn,C as Go,_ as yi,an as Fr,ag as jo,F as Or,af as Nr}from"./SpinnerAugment-JC8TPhVf.js";import{g as le,A as rs,E as Rt,q as hs,k as qs,S as Fs,C as Os,r as Wo,n as Lr,s as qn,t as Pr,u as Di,v as Ri,w as Ur,x as Fi,y as zr,z as Oi,B as qr,D as Ni,F as Hn,G as Hr}from"./lodash-l00D6itj.js";import{a as Br,D as Gr,b as jr,c as Wr,d as Vr,C as Yr,T as Xr}from"./github-C1jNNmyr.js";import{F as Zr,P as Qr}from"./folder-opened-bSDyFrZo.js";import{C as Kr,i as Jr,j as Li}from"./rules-parser-D2d7xQ-G.js";import{C as xe,P as ie,a as ke,I as Ss,E as ta}from"./chat-types-NgqNgjwU.js";import{C as ea,b as qt,A as sa,a as na}from"./types-BSMhNRWH.js";import{T as Ye,a as Pi,b as Bn}from"./open-in-new-window-CkR7J3XO.js";import{I as Js}from"./IconButtonAugment-D_WR2I26.js";import{C as ia,T as oa,D as se}from"./index-Yat2JVWz.js";import{T as ra}from"./TextAreaAugment-BYCw5p2g.js";import{P as aa,g as la,d as ca,e as Ui,i as da,f as ua}from"./diff-utils-Bh9TYMbV.js";import{T as ki}from"./TextTooltipAugment-WghC7pXE.js";function ha(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 224c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64h168v86.1l-23-23c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0l64-64c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-23 23V224h168zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64c0-35.3-28.7-64-64-64h-74.3c4.8 16 2.2 33.8-7.7 48h82c8.8 0 16 7.2 16 16v64c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-64c0-8.8 7.2-16 16-16h82c-9.9-14.2-12.5-32-7.7-48z"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function pa(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class Vo extends st{constructor(t){super(),nt(this,t,pa,ha,it,{})}}function fa(n){var t;return((t=n.extraData)==null?void 0:t.isAutofix)===!0}function ps(n){var t;return((t=n.extraData)==null?void 0:t.isAgentConversation)===!0}var Ct=(n=>(n[n.active=0]="active",n[n.inactive=1]="inactive",n))(Ct||{}),ga=(n=>(n.normal="Normal",n.autofixCommand="AutofixCommand",n.autofixPrompt="AutofixPrompt",n))(ga||{});function ma(n,t,e=1e3){let s=null,i=0;const o=yt(t),r=()=>{const a=(()=>{const l=Date.now();if(s!==null&&l-i<e)return s;const c=n();return s=c,i=l,c})();o.set(a)};return{subscribe:o.subscribe,resetCache:()=>{s=null,r()},updateStore:r}}var Yo=(n=>(n[n.unset=0]="unset",n[n.positive=1]="positive",n[n.negative=2]="negative",n))(Yo||{});function va(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let os={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function zi(n){os=n}const Xo=/[&<>"']/,ba=new RegExp(Xo.source,"g"),Zo=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,_a=new RegExp(Zo.source,"g"),wa={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},qi=n=>wa[n];function ne(n,t){if(t){if(Xo.test(n))return n.replace(ba,qi)}else if(Zo.test(n))return n.replace(_a,qi);return n}const ya=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function ka(n){return n.replace(ya,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const xa=/(^|[^\[])\^/g;function ot(n,t){let e=typeof n=="string"?n:n.source;t=t||"";const s={replace:(i,o)=>{let r=typeof o=="string"?o:o.source;return r=r.replace(xa,"$1"),e=e.replace(i,r),s},getRegex:()=>new RegExp(e,t)};return s}function Hi(n){try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const Hs={exec:()=>null};function Bi(n,t){const e=n.replace(/\|/g,(i,o,r)=>{let a=!1,l=o;for(;--l>=0&&r[l]==="\\";)a=!a;return a?"|":" |"}).split(/ \|/);let s=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;s<e.length;s++)e[s]=e[s].trim().replace(/\\\|/g,"|");return e}function an(n,t,e){const s=n.length;if(s===0)return"";let i=0;for(;i<s;){const o=n.charAt(s-i-1);if(o!==t||e){if(o===t||!e)break;i++}else i++}return n.slice(0,s-i)}function Gi(n,t,e,s){const i=t.href,o=t.title?ne(t.title):null,r=n[1].replace(/\\([\[\]])/g,"$1");if(n[0].charAt(0)!=="!"){s.state.inLink=!0;const a={type:"link",raw:e,href:i,title:o,text:r,tokens:s.inlineTokens(r)};return s.state.inLink=!1,a}return{type:"image",raw:e,href:i,title:o,text:ne(r)}}class Sn{constructor(t){h(this,"options");h(this,"rules");h(this,"lexer");this.options=t||os}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const s=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?s:an(s,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const s=e[0],i=function(o,r){const a=o.match(/^(\s+)(?:```)/);if(a===null)return r;const l=a[1];return r.split(`
`).map(c=>{const d=c.match(/^\s+/);if(d===null)return c;const[p]=d;return p.length>=l.length?c.slice(l.length):c}).join(`
`)}(s,e[3]||"");return{type:"code",raw:s,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:i}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let s=e[2].trim();if(/#$/.test(s)){const i=an(s,"#");this.options.pedantic?s=i.trim():i&&!/ $/.test(i)||(s=i.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const s=an(e[0].replace(/^ *>[ \t]?/gm,""),`
`),i=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(s);return this.lexer.state.top=i,{type:"blockquote",raw:e[0],tokens:o,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const i=s.length>1,o={type:"list",raw:"",ordered:i,start:i?+s.slice(0,-1):"",loose:!1,items:[]};s=i?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=i?s:"[*+-]");const r=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",l="",c=!1;for(;t;){let d=!1;if(!(e=r.exec(t))||this.rules.block.hr.test(t))break;a=e[0],t=t.substring(a.length);let p=e[2].split(`
`,1)[0].replace(/^\t+/,v=>" ".repeat(3*v.length)),f=t.split(`
`,1)[0],g=0;this.options.pedantic?(g=2,l=p.trimStart()):(g=e[2].search(/[^ ]/),g=g>4?1:g,l=p.slice(g),g+=e[1].length);let b=!1;if(!p&&/^ *$/.test(f)&&(a+=f+`
`,t=t.substring(f.length+1),d=!0),!d){const v=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),x=new RegExp(`^ {0,${Math.min(3,g-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),A=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:\`\`\`|~~~)`),L=new RegExp(`^ {0,${Math.min(3,g-1)}}#`);for(;t;){const w=t.split(`
`,1)[0];if(f=w,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),A.test(f)||L.test(f)||v.test(f)||x.test(t))break;if(f.search(/[^ ]/)>=g||!f.trim())l+=`
`+f.slice(g);else{if(b||p.search(/[^ ]/)>=4||A.test(p)||L.test(p)||x.test(p))break;l+=`
`+f}b||f.trim()||(b=!0),a+=w+`
`,t=t.substring(w.length+1),p=f.slice(g)}}o.loose||(c?o.loose=!0:/\n *\n *$/.test(a)&&(c=!0));let m,_=null;this.options.gfm&&(_=/^\[[ xX]\] /.exec(l),_&&(m=_[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),o.items.push({type:"list_item",raw:a,task:!!_,checked:m,loose:!1,text:l,tokens:[]}),o.raw+=a}o.items[o.items.length-1].raw=a.trimEnd(),o.items[o.items.length-1].text=l.trimEnd(),o.raw=o.raw.trimEnd();for(let d=0;d<o.items.length;d++)if(this.lexer.state.top=!1,o.items[d].tokens=this.lexer.blockTokens(o.items[d].text,[]),!o.loose){const p=o.items[d].tokens.filter(g=>g.type==="space"),f=p.length>0&&p.some(g=>/\n.*\n/.test(g.raw));o.loose=f}if(o.loose)for(let d=0;d<o.items.length;d++)o.items[d].loose=!0;return o}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const s=e[1].toLowerCase().replace(/\s+/g," "),i=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",o=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:s,raw:e[0],href:i,title:o}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const s=Bi(e[1]),i=e[2].replace(/^\||\| *$/g,"").split("|"),o=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],r={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===i.length){for(const a of i)/^ *-+: *$/.test(a)?r.align.push("right"):/^ *:-+: *$/.test(a)?r.align.push("center"):/^ *:-+ *$/.test(a)?r.align.push("left"):r.align.push(null);for(const a of s)r.header.push({text:a,tokens:this.lexer.inline(a)});for(const a of o)r.rows.push(Bi(a,r.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return r}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const s=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:s,tokens:this.lexer.inline(s)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:ne(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const s=e[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;const r=an(s.slice(0,-1),"\\");if((s.length-r.length)%2==0)return}else{const r=function(a,l){if(a.indexOf(l[1])===-1)return-1;let c=0;for(let d=0;d<a.length;d++)if(a[d]==="\\")d++;else if(a[d]===l[0])c++;else if(a[d]===l[1]&&(c--,c<0))return d;return-1}(e[2],"()");if(r>-1){const a=(e[0].indexOf("!")===0?5:4)+e[1].length+r;e[2]=e[2].substring(0,r),e[0]=e[0].substring(0,a).trim(),e[3]=""}}let i=e[2],o="";if(this.options.pedantic){const r=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);r&&(i=r[1],o=r[3])}else o=e[3]?e[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(i=this.options.pedantic&&!/>$/.test(s)?i.slice(1):i.slice(1,-1)),Gi(e,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:o&&o.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const i=e[(s[2]||s[1]).replace(/\s+/g," ").toLowerCase()];if(!i){const o=s[0].charAt(0);return{type:"text",raw:o,text:o}}return Gi(s,i,s[0],this.lexer)}}emStrong(t,e,s=""){let i=this.rules.inline.emStrongLDelim.exec(t);if(i&&!(i[3]&&s.match(/[\p{L}\p{N}]/u))&&(!(i[1]||i[2])||!s||this.rules.inline.punctuation.exec(s))){const o=[...i[0]].length-1;let r,a,l=o,c=0;const d=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(d.lastIndex=0,e=e.slice(-1*t.length+o);(i=d.exec(e))!=null;){if(r=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!r)continue;if(a=[...r].length,i[3]||i[4]){l+=a;continue}if((i[5]||i[6])&&o%3&&!((o+a)%3)){c+=a;continue}if(l-=a,l>0)continue;a=Math.min(a,a+l+c);const p=[...i[0]][0].length,f=t.slice(0,o+i.index+p+a);if(Math.min(o,a)%2){const b=f.slice(1,-1);return{type:"em",raw:f,text:b,tokens:this.lexer.inlineTokens(b)}}const g=f.slice(2,-2);return{type:"strong",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let s=e[2].replace(/\n/g," ");const i=/[^ ]/.test(s),o=/^ /.test(s)&&/ $/.test(s);return i&&o&&(s=s.substring(1,s.length-1)),s=ne(s,!0),{type:"codespan",raw:e[0],text:s}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let s,i;return e[2]==="@"?(s=ne(e[1]),i="mailto:"+s):(s=ne(e[1]),i=s),{type:"link",raw:e[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}url(t){var s;let e;if(e=this.rules.inline.url.exec(t)){let i,o;if(e[2]==="@")i=ne(e[0]),o="mailto:"+i;else{let r;do r=e[0],e[0]=((s=this.rules.inline._backpedal.exec(e[0]))==null?void 0:s[0])??"";while(r!==e[0]);i=ne(e[0]),o=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let s;return s=this.lexer.state.inRawBlock?e[0]:ne(e[0]),{type:"text",raw:e[0],text:s}}}}const tn=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Qo=/(?:[*+-]|\d{1,9}[.)])/,Ko=ot(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Qo).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),xi=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,$i=/(?!\s*\])(?:\\.|[^\[\]\\])+/,$a=ot(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",$i).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Ta=ot(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Qo).getRegex(),Fn="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ti=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Sa=ot("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ti).replace("tag",Fn).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ji=ot(xi).replace("hr",tn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fn).getRegex(),Si={blockquote:ot(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ji).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:$a,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:tn,html:Sa,lheading:Ko,list:Ta,newline:/^(?: *(?:\n|$))+/,paragraph:ji,table:Hs,text:/^[^\n]+/},Wi=ot("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",tn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fn).getRegex(),Ca={...Si,table:Wi,paragraph:ot(xi).replace("hr",tn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Wi).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fn).getRegex()},Ea={...Si,html:ot(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ti).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Hs,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:ot(xi).replace("hr",tn).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ko).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Jo=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,tr=/^( {2,}|\\)\n(?!\s*$)/,en="\\p{P}\\p{S}",Ia=ot(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,en).getRegex(),Ma=ot(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,en).getRegex(),Aa=ot("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,en).getRegex(),Da=ot("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,en).getRegex(),Ra=ot(/\\([punct])/,"gu").replace(/punct/g,en).getRegex(),Fa=ot(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Oa=ot(Ti).replace("(?:-->|$)","-->").getRegex(),Na=ot("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Oa).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Cn=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,La=ot(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Cn).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Vi=ot(/^!?\[(label)\]\[(ref)\]/).replace("label",Cn).replace("ref",$i).getRegex(),Yi=ot(/^!?\[(ref)\](?:\[\])?/).replace("ref",$i).getRegex(),Ci={_backpedal:Hs,anyPunctuation:Ra,autolink:Fa,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:tr,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Hs,emStrongLDelim:Ma,emStrongRDelimAst:Aa,emStrongRDelimUnd:Da,escape:Jo,link:La,nolink:Yi,punctuation:Ia,reflink:Vi,reflinkSearch:ot("reflink|nolink(?!\\()","g").replace("reflink",Vi).replace("nolink",Yi).getRegex(),tag:Na,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Hs},Pa={...Ci,link:ot(/^!?\[(label)\]\((.*?)\)/).replace("label",Cn).getRegex(),reflink:ot(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Cn).getRegex()},si={...Ci,escape:ot(Jo).replace("])","~|])").getRegex(),url:ot(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ua={...si,br:ot(tr).replace("{2,}","*").getRegex(),text:ot(si.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ln={normal:Si,gfm:Ca,pedantic:Ea},Cs={normal:Ci,gfm:si,breaks:Ua,pedantic:Pa};class Me{constructor(t){h(this,"tokens");h(this,"options");h(this,"state");h(this,"tokenizer");h(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||os,this.options.tokenizer=this.options.tokenizer||new Sn,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:ln.normal,inline:Cs.normal};this.options.pedantic?(e.block=ln.pedantic,e.inline=Cs.pedantic):this.options.gfm&&(e.block=ln.gfm,this.options.breaks?e.inline=Cs.breaks:e.inline=Cs.gfm),this.tokenizer.rules=e}static get rules(){return{block:ln,inline:Cs}}static lex(t,e){return new Me(e).lex(t)}static lexInline(t,e){return new Me(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const s=this.inlineQueue[e];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let s,i,o,r;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(a,l,c)=>l+"    ".repeat(c.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>!!(s=a.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.space(t))t=t.substring(s.raw.length),s.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(s);else if(s=this.tokenizer.code(t))t=t.substring(s.raw.length),i=e[e.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?e.push(s):(i.raw+=`
`+s.raw,i.text+=`
`+s.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(s=this.tokenizer.fences(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.heading(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.hr(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.blockquote(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.list(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.html(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.def(t))t=t.substring(s.raw.length),i=e[e.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title}):(i.raw+=`
`+s.raw,i.text+=`
`+s.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(s=this.tokenizer.table(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.lheading(t))t=t.substring(s.raw.length),e.push(s);else{if(o=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let c;this.options.extensions.startBlock.forEach(d=>{c=d.call({lexer:this},l),typeof c=="number"&&c>=0&&(a=Math.min(a,c))}),a<1/0&&a>=0&&(o=t.substring(0,a+1))}if(this.state.top&&(s=this.tokenizer.paragraph(o)))i=e[e.length-1],r&&i.type==="paragraph"?(i.raw+=`
`+s.raw,i.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):e.push(s),r=o.length!==t.length,t=t.substring(s.raw.length);else if(s=this.tokenizer.text(t))t=t.substring(s.raw.length),i=e[e.length-1],i&&i.type==="text"?(i.raw+=`
`+s.raw,i.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):e.push(s);else if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s,i,o,r,a,l,c=t;if(this.tokens.links){const d=Object.keys(this.tokens.links);if(d.length>0)for(;(r=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)d.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(r=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(r=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,r.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(a||(l=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(d=>!!(s=d.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.escape(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.tag(t))t=t.substring(s.raw.length),i=e[e.length-1],i&&s.type==="text"&&i.type==="text"?(i.raw+=s.raw,i.text+=s.text):e.push(s);else if(s=this.tokenizer.link(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(s.raw.length),i=e[e.length-1],i&&s.type==="text"&&i.type==="text"?(i.raw+=s.raw,i.text+=s.text):e.push(s);else if(s=this.tokenizer.emStrong(t,c,l))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.codespan(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.br(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.del(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.autolink(t))t=t.substring(s.raw.length),e.push(s);else if(this.state.inLink||!(s=this.tokenizer.url(t))){if(o=t,this.options.extensions&&this.options.extensions.startInline){let d=1/0;const p=t.slice(1);let f;this.options.extensions.startInline.forEach(g=>{f=g.call({lexer:this},p),typeof f=="number"&&f>=0&&(d=Math.min(d,f))}),d<1/0&&d>=0&&(o=t.substring(0,d+1))}if(s=this.tokenizer.inlineText(o))t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(l=s.raw.slice(-1)),a=!0,i=e[e.length-1],i&&i.type==="text"?(i.raw+=s.raw,i.text+=s.text):e.push(s);else if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}throw new Error(d)}}else t=t.substring(s.raw.length),e.push(s);return e}}class En{constructor(t){h(this,"options");this.options=t||os}code(t,e,s){var o;const i=(o=(e||"").match(/^\S*/))==null?void 0:o[0];return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="language-'+ne(i)+'">'+(s?t:ne(t,!0))+`</code></pre>
`:"<pre><code>"+(s?t:ne(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,s){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,s){const i=e?"ol":"ul";return"<"+i+(e&&s!==1?' start="'+s+'"':"")+`>
`+t+"</"+i+`>
`}listitem(t,e,s){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,s){const i=Hi(t);if(i===null)return s;let o='<a href="'+(t=i)+'"';return e&&(o+=' title="'+e+'"'),o+=">"+s+"</a>",o}image(t,e,s){const i=Hi(t);if(i===null)return s;let o=`<img src="${t=i}" alt="${s}"`;return e&&(o+=` title="${e}"`),o+=">",o}text(t){return t}}class Ei{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,s){return""+s}image(t,e,s){return""+s}br(){return""}}class Ae{constructor(t){h(this,"options");h(this,"renderer");h(this,"textRenderer");this.options=t||os,this.options.renderer=this.options.renderer||new En,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ei}static parse(t,e){return new Ae(e).parse(t)}static parseInline(t,e){return new Ae(e).parseInline(t)}parse(t,e=!0){let s="";for(let i=0;i<t.length;i++){const o=t[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]){const r=o,a=this.options.extensions.renderers[r.type].call({parser:this},r);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(r.type)){s+=a||"";continue}}switch(o.type){case"space":continue;case"hr":s+=this.renderer.hr();continue;case"heading":{const r=o;s+=this.renderer.heading(this.parseInline(r.tokens),r.depth,ka(this.parseInline(r.tokens,this.textRenderer)));continue}case"code":{const r=o;s+=this.renderer.code(r.text,r.lang,!!r.escaped);continue}case"table":{const r=o;let a="",l="";for(let d=0;d<r.header.length;d++)l+=this.renderer.tablecell(this.parseInline(r.header[d].tokens),{header:!0,align:r.align[d]});a+=this.renderer.tablerow(l);let c="";for(let d=0;d<r.rows.length;d++){const p=r.rows[d];l="";for(let f=0;f<p.length;f++)l+=this.renderer.tablecell(this.parseInline(p[f].tokens),{header:!1,align:r.align[f]});c+=this.renderer.tablerow(l)}s+=this.renderer.table(a,c);continue}case"blockquote":{const r=o,a=this.parse(r.tokens);s+=this.renderer.blockquote(a);continue}case"list":{const r=o,a=r.ordered,l=r.start,c=r.loose;let d="";for(let p=0;p<r.items.length;p++){const f=r.items[p],g=f.checked,b=f.task;let m="";if(f.task){const _=this.renderer.checkbox(!!g);c?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=_+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=_+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:_+" "}):m+=_+" "}m+=this.parse(f.tokens,c),d+=this.renderer.listitem(m,b,!!g)}s+=this.renderer.list(d,a,l);continue}case"html":{const r=o;s+=this.renderer.html(r.text,r.block);continue}case"paragraph":{const r=o;s+=this.renderer.paragraph(this.parseInline(r.tokens));continue}case"text":{let r=o,a=r.tokens?this.parseInline(r.tokens):r.text;for(;i+1<t.length&&t[i+1].type==="text";)r=t[++i],a+=`
`+(r.tokens?this.parseInline(r.tokens):r.text);s+=e?this.renderer.paragraph(a):a;continue}default:{const r='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(r),"";throw new Error(r)}}}return s}parseInline(t,e){e=e||this.renderer;let s="";for(let i=0;i<t.length;i++){const o=t[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]){const r=this.options.extensions.renderers[o.type].call({parser:this},o);if(r!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type)){s+=r||"";continue}}switch(o.type){case"escape":{const r=o;s+=e.text(r.text);break}case"html":{const r=o;s+=e.html(r.text);break}case"link":{const r=o;s+=e.link(r.href,r.title,this.parseInline(r.tokens,e));break}case"image":{const r=o;s+=e.image(r.href,r.title,r.text);break}case"strong":{const r=o;s+=e.strong(this.parseInline(r.tokens,e));break}case"em":{const r=o;s+=e.em(this.parseInline(r.tokens,e));break}case"codespan":{const r=o;s+=e.codespan(r.text);break}case"br":s+=e.br();break;case"del":{const r=o;s+=e.del(this.parseInline(r.tokens,e));break}case"text":{const r=o;s+=e.text(r.text);break}default:{const r='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(r),"";throw new Error(r)}}}return s}}class Bs{constructor(t){h(this,"options");this.options=t||os}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}h(Bs,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var is,ni,er,No;const Je=new(No=class{constructor(...n){X(this,is);h(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});h(this,"options",this.setOptions);h(this,"parse",D(this,is,ni).call(this,Me.lex,Ae.parse));h(this,"parseInline",D(this,is,ni).call(this,Me.lexInline,Ae.parseInline));h(this,"Parser",Ae);h(this,"Renderer",En);h(this,"TextRenderer",Ei);h(this,"Lexer",Me);h(this,"Tokenizer",Sn);h(this,"Hooks",Bs);this.use(...n)}walkTokens(n,t){var s,i;let e=[];for(const o of n)switch(e=e.concat(t.call(this,o)),o.type){case"table":{const r=o;for(const a of r.header)e=e.concat(this.walkTokens(a.tokens,t));for(const a of r.rows)for(const l of a)e=e.concat(this.walkTokens(l.tokens,t));break}case"list":{const r=o;e=e.concat(this.walkTokens(r.items,t));break}default:{const r=o;(i=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&i[r.type]?this.defaults.extensions.childTokens[r.type].forEach(a=>{const l=r[a].flat(1/0);e=e.concat(this.walkTokens(l,t))}):r.tokens&&(e=e.concat(this.walkTokens(r.tokens,t)))}}return e}use(...n){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return n.forEach(e=>{const s={...e};if(s.async=this.defaults.async||s.async||!1,e.extensions&&(e.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const o=t.renderers[i.name];t.renderers[i.name]=o?function(...r){let a=i.renderer.apply(this,r);return a===!1&&(a=o.apply(this,r)),a}:i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const o=t[i.level];o?o.unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),s.extensions=t),e.renderer){const i=this.defaults.renderer||new En(this.defaults);for(const o in e.renderer){if(!(o in i))throw new Error(`renderer '${o}' does not exist`);if(o==="options")continue;const r=o,a=e.renderer[r],l=i[r];i[r]=(...c)=>{let d=a.apply(i,c);return d===!1&&(d=l.apply(i,c)),d||""}}s.renderer=i}if(e.tokenizer){const i=this.defaults.tokenizer||new Sn(this.defaults);for(const o in e.tokenizer){if(!(o in i))throw new Error(`tokenizer '${o}' does not exist`);if(["options","rules","lexer"].includes(o))continue;const r=o,a=e.tokenizer[r],l=i[r];i[r]=(...c)=>{let d=a.apply(i,c);return d===!1&&(d=l.apply(i,c)),d}}s.tokenizer=i}if(e.hooks){const i=this.defaults.hooks||new Bs;for(const o in e.hooks){if(!(o in i))throw new Error(`hook '${o}' does not exist`);if(o==="options")continue;const r=o,a=e.hooks[r],l=i[r];Bs.passThroughHooks.has(o)?i[r]=c=>{if(this.defaults.async)return Promise.resolve(a.call(i,c)).then(p=>l.call(i,p));const d=a.call(i,c);return l.call(i,d)}:i[r]=(...c)=>{let d=a.apply(i,c);return d===!1&&(d=l.apply(i,c)),d}}s.hooks=i}if(e.walkTokens){const i=this.defaults.walkTokens,o=e.walkTokens;s.walkTokens=function(r){let a=[];return a.push(o.call(this,r)),i&&(a=a.concat(i.call(this,r))),a}}this.defaults={...this.defaults,...s}}),this}setOptions(n){return this.defaults={...this.defaults,...n},this}lexer(n,t){return Me.lex(n,t??this.defaults)}parser(n,t){return Ae.parse(n,t??this.defaults)}},is=new WeakSet,ni=function(n,t){return(e,s)=>{const i={...s},o={...this.defaults,...i};this.defaults.async===!0&&i.async===!1&&(o.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),o.async=!0);const r=D(this,is,er).call(this,!!o.silent,!!o.async);if(e==null)return r(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return r(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(o.hooks&&(o.hooks.options=o),o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(e):e).then(a=>n(a,o)).then(a=>o.hooks?o.hooks.processAllTokens(a):a).then(a=>o.walkTokens?Promise.all(this.walkTokens(a,o.walkTokens)).then(()=>a):a).then(a=>t(a,o)).then(a=>o.hooks?o.hooks.postprocess(a):a).catch(r);try{o.hooks&&(e=o.hooks.preprocess(e));let a=n(e,o);o.hooks&&(a=o.hooks.processAllTokens(a)),o.walkTokens&&this.walkTokens(a,o.walkTokens);let l=t(a,o);return o.hooks&&(l=o.hooks.postprocess(l)),l}catch(a){return r(a)}}},er=function(n,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,n){const s="<p>An error occurred:</p><pre>"+ne(e.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(e);throw e}},No);function et(n,t){return Je.parse(n,t)}et.options=et.setOptions=function(n){return Je.setOptions(n),et.defaults=Je.defaults,zi(et.defaults),et},et.getDefaults=va,et.defaults=os,et.use=function(...n){return Je.use(...n),et.defaults=Je.defaults,zi(et.defaults),et},et.walkTokens=function(n,t){return Je.walkTokens(n,t)},et.parseInline=Je.parseInline,et.Parser=Ae,et.parser=Ae.parse,et.Renderer=En,et.TextRenderer=Ei,et.Lexer=Me,et.lexer=Me.lex,et.Tokenizer=Sn,et.Hooks=Bs,et.parse=et,et.options,et.setOptions,et.use,et.walkTokens,et.parseInline,Ae.parse,Me.lex;const zd=async(n,t)=>{if(!ps(n)||t.chatItemType!==void 0||!(t!=null&&t.request_message))return;const e=ea.create();e.setFlag(qt.start);try{await za(n,t,e)}catch(s){e.setFlag(qt.exceptionThrown),console.error("Failed to classify and distill memories",s)}finally{e.setFlag(qt.end),n.extensionClient.reportAgentSessionEvent({eventName:sa.classifyAndDistill,conversationId:n.id,eventData:{classifyAndDistillData:e}})}},za=async(n,t,e)=>{const s=crypto.randomUUID();e.setRequestId(qt.memoriesRequestId,s);const i=mt(n).id;e.setFlag(qt.startSendSilentExchange);const{responseText:o,requestId:r}=await n.sendSilentExchange({model_id:n.selectedModelId??void 0,request_message:t.request_message,disableRetrieval:!0,disableSelectedCodeDetails:!0,memoriesInfo:{isClassifyAndDistill:!0}});if(e.setStringStats(qt.sendSilentExchangeResponseStats,o),r?e.setRequestId(qt.sendSilentExchangeRequestId,r):e.setFlag(qt.noRequestId),mt(n).id!==i)return void e.setFlag(qt.conversationChanged);let a;try{let c=o;try{const d=et.lexer(o);d.length===1&&d[0].type==="code"&&d[0].text&&(c=d[0].text)}catch(d){console.warn("Markdown lexing failed during response parsing, attempting to parse as raw string:",d)}a=JSON.parse(c)}catch{throw e.setFlag(qt.invalidResponse),new Error("Invalid response from classify and distill")}if(typeof a.explanation!="string"||typeof a.content!="string"||typeof a.worthRemembering!="boolean")throw e.setFlag(qt.invalidResponse),new Error("Invalid response from classify and distill");e.setStringStats(qt.explanationStats,a.explanation),e.setStringStats(qt.contentStats,a.content),e.setFlag(qt.worthRemembering,a.worthRemembering);const l=a.worthRemembering?a.content:void 0;l&&Ha(n,l,s,e)},qd=n=>{var s;const t=n.chatHistory.at(-1);if(!t||!le(t))return rs.notRunning;if(!(t.status===Rt.success||t.status===Rt.failed||t.status===Rt.cancelled))return rs.running;const e=(((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===xe.TOOL_USE&&!!i.tool_use))??[]).at(-1);if(!e)return rs.notRunning;switch(n.getToolUseState(t.request_id,e.tool_use.tool_use_id).phase){case Ye.runnable:return rs.awaitingUserAction;case Ye.cancelled:return rs.notRunning;default:return rs.running}},ii=n=>le(n)&&!!n.request_message,On=n=>n.chatHistory.findLast(t=>ii(t)),Hd=n=>{const t=On(n);if(t!=null&&t.structured_output_nodes){const e=t.structured_output_nodes.find(s=>s.type===xe.AGENT_MEMORY);if(e)try{const{memoriesRequestId:s,memory:i}=JSON.parse(e.content);return{memoriesRequestId:s,memory:i}}catch(s){return void console.error("Failed to parse JSON from agent memory node",s)}}},Bd=n=>qa(n,t=>{var e;return!!((e=t.structured_output_nodes)!=null&&e.some(s=>{var i;return s.type===xe.TOOL_USE&&((i=s.tool_use)==null?void 0:i.tool_name)==="remember"}))}).length>0,qa=(n,t)=>{const e=On(n);return e!=null&&e.request_id?n.historyFrom(e.request_id,!0).filter(s=>le(s)&&(!t||t(s))):[]},Gd=n=>{var s;const t=n.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!le(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===xe.TOOL_USE))??[];for(const i of e)if(i.tool_use&&n.getToolUseState(t.request_id,i.tool_use.tool_use_id).phase===Ye.runnable)return n.updateToolUseState({requestId:t.request_id,toolUseId:i.tool_use.tool_use_id,phase:Ye.cancelled}),!0;return!1},Ha=(n,t,e,s)=>{const i=JSON.stringify({memoriesRequestId:e,memory:t}),o=On(n);o!=null&&o.request_id?(s.setRequestId(qt.lastUserExchangeRequestId,o.request_id),n.updateChatItem(o.request_id,{...o,structured_output_nodes:[...o.structured_output_nodes??[],{id:0,type:xe.AGENT_MEMORY,content:i}]})):s.setFlag(qt.noLastUserExchangeRequestId)},jd=(n,t)=>{const e=On(n);if(!(e!=null&&e.request_id)||e.request_id!==t)return!1;const s=(e.structured_output_nodes||[]).filter(i=>i.type!==xe.AGENT_MEMORY);return s.length!==(e.structured_output_nodes||[]).length&&(n.updateChatItem(t,{...e,structured_output_nodes:s}),!0)},Zt="__NEW_AGENT__";function Ba(n,t){const e=n.customPersonalityPrompts;if(e)switch(t){case ie.DEFAULT:if(e.agent&&e.agent.trim()!=="")return e.agent;break;case ie.PROTOTYPER:if(e.prototyper&&e.prototyper.trim()!=="")return e.prototyper;break;case ie.BRAINSTORM:if(e.brainstorm&&e.brainstorm.trim()!=="")return e.brainstorm;break;case ie.REVIEWER:if(e.reviewer&&e.reviewer.trim()!=="")return e.reviewer}return Ga[t]}const Ga={[ie.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[ie.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[ie.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[ie.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};class lt{constructor(t,e,s,i){h(this,"_state");h(this,"_subscribers",new Set);h(this,"_focusModel",new Zr);h(this,"_onSendExchangeListeners",[]);h(this,"_onNewConversationListeners",[]);h(this,"_onHistoryDeleteListeners",[]);h(this,"_onBeforeChangeConversationListeners",[]);h(this,"_totalCharactersCacheThrottleMs",1e3);h(this,"_totalCharactersStore");h(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));h(this,"setConversation",(t,e=!0,s=!0)=>{const i=t.id!==this._state.id;i&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([r,a])=>{if(a.requestId&&a.toolUseId){const{requestId:l,toolUseId:c}=Pi(r);return l===a.requestId&&c===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",r,"but object has ",Bn(a)),[r,a]}return[r,{...a,...Pi(r)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const o=lt.isEmpty(t);if(i&&o){const r=this._state.draftExchange;r&&(t.draftExchange=r)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(le)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(r=>r(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(r=>r())),!0});h(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});h(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});h(this,"setName",t=>{this.update({name:t})});h(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});h(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});h(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Bn(t)]:t}})});h(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:Ye.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[Bn({requestId:t,toolUseId:e})]||{phase:Ye.new});h(this,"getLastToolUseState",()=>{var s,i;const t=this.lastExchange;if(!t)return{phase:Ye.unknown};const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(o=>o.type===xe.TOOL_USE))??[]).at(-1);return e?this.getToolUseState(t.request_id,(i=e.tool_use)==null?void 0:i.tool_use_id):{phase:Ye.unknown}});h(this,"addExchange",t=>{const e=[...this._state.chatHistory,t];let s;le(t)&&(s=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Yo.unset,feedbackNote:""}}:void 0),this.update({chatHistory:e,...s?{feedbackStates:s}:{},lastUrl:void 0})});h(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});h(this,"updateExchangeById",(t,e,s=!1)=>{var a;const i=this.exchangeWithRequestId(e);if(i===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(i.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=[...i.structured_output_nodes??[],...t.structured_output_nodes??[]]),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...i.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const o=(a=(t.structured_output_nodes||[]).find(l=>l.type===xe.MAIN_TEXT_FINISHED))==null?void 0:a.content;o&&o!==t.response_text&&(t.response_text=o);let r=this._state.isShareable||hs({...i,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===e?{...l,...t}:l),isShareable:r}),!0});h(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!e.request_id||!t.has(e.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});h(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});h(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),i=s.map(o=>o.request_id).filter(o=>o!==void 0);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:i}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(r=>r(o))})});h(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});h(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});h(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});h(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});h(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Rt.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));h(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});h(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);h(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});h(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:Fs.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});h(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));h(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});h(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});h(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(i=>t.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(t.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});h(this,"saveDraftExchange",(t,e)=>{var r,a,l;const s=t!==((r=this.draftExchange)==null?void 0:r.request_message),i=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!i)return;const o=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:o,status:Rt.draft}})});h(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});h(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var r,a;const s=!this.name&&this.chatHistory.length===1&&((r=this.firstExchange)==null?void 0:r.request_id)===this.chatHistory[0].request_id,i=ps(this)&&((a=this._state.extraData)==null?void 0:a.hasAgentOnboarded)&&(o=this.chatHistory,o.filter(l=>ii(l))).length===2;var o;this._chatFlagModel.summaryTitles&&(s||i)&&this.updateConversationTitle()}).finally(()=>{var s;ps(this)&&this._extensionClient.reportAgentRequestEvent({eventName:na.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});h(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Rt.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});h(this,"sendInstructionExchange",async(t,e)=>{let s=`temp-fe-${crypto.randomUUID()}`;const i={status:Rt.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Fs.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const o of this._extensionClient.sendInstructionMessage(i,e)){if(!this.updateExchangeById(o,s,!0))return;s=o.request_id||s}});h(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});h(this,"sendSummaryExchange",()=>{const t={status:Rt.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Os.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});h(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const e={status:Rt.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Fs.unseen,chatItemType:Os.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});h(this,"sendExchange",async(t,e=!1)=>{var r;this.updateLastInteraction();let s=`temp-fe-${crypto.randomUUID()}`,i=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&lt.isNew(this._state)){const a=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,a)}catch(c){console.error("Failed to migrate conversation checkpoints:",c)}this._state={...this._state,id:a},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(a),this._subscribers.forEach(c=>c(this))}t=Xi(t);let o={status:Rt.sent,request_id:s,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:i,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:Fs.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(a=>a(o)),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},s,!1);for await(const a of this.sendUserMessage(s,o,e)){if(((r=this.exchangeWithRequestId(s))==null?void 0:r.status)!==Rt.sent||!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});h(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Rt.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Kr.chatUseSuggestedQuestion)});h(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});h(this,"recoverExchange",async t=>{var i;if(!t.request_id||t.status!==Rt.sent)return;let e=t.request_id;const s=(i=t.structured_output_nodes)==null?void 0:i.filter(o=>o.type===xe.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const o of this.getChatStream(t)){if(!this.updateExchangeById(o,e,!0))return;e=o.request_id||e}});h(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{le(e)&&this._loadContextFromExchange(e)})});h(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});h(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{le(e)&&this._unloadContextFromExchange(e)})});h(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});h(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});h(this,"_jsonToStructuredRequest",t=>{const e=[],s=o=>{var a;const r=e.at(-1);if((r==null?void 0:r.type)===ke.TEXT){const l=((a=r.text_node)==null?void 0:a.content)??"",c={...r,text_node:{content:l+o}};e[e.length-1]=c}else e.push({id:e.length,type:ke.TEXT,text_node:{content:o}})},i=o=>{var r,a,l,c;if(o.type==="doc"||o.type==="paragraph")for(const d of o.content??[])i(d);else if(o.type==="hardBreak")s(`
`);else if(o.type==="text")s(o.text??"");else if(o.type==="image"){if(typeof((r=o.attrs)==null?void 0:r.src)!="string")return void console.error("Image source is not a string: ",(a=o.attrs)==null?void 0:a.src);if(o.attrs.isLoading)return;const d=(l=o.attrs)==null?void 0:l.title,p=this._fileNameToImageFormat(d);e.push({id:e.length,type:ke.IMAGE_ID,image_id_node:{image_id:o.attrs.src,format:p}})}else if(o.type==="mention"){const d=(c=o.attrs)==null?void 0:c.data;d&&Wo(d)?e.push({id:e.length,type:ke.TEXT,text_node:{content:Ba(this._chatFlagModel,d.personality.type)}}):s(`@\`${(d==null?void 0:d.name)??(d==null?void 0:d.id)}\``)}};return i(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=i,this._state={...lt.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return ma(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,i)=>s+i,0))||0)<=4?ie.PROTOTYPER:ie.DEFAULT}catch(e){return console.error("Error determining persona type:",e),ie.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:ie.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var i;const e=this._filterToExchanges(t);let s;return s=fa(t)?"Autofix Chat":ps(t)?"New Agent":"New Chat",lt.toSentenceCase(t.name||((i=e[0])==null?void 0:i.request_message)||s)}static _filterToExchanges(t){return t.chatHistory.filter(e=>le(e))}static isNew(t){return t.id===Zt}static isEmpty(t){var e;return t.chatHistory.filter(s=>le(s)).length===0&&!((e=t.draftExchange)!=null&&e.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?lt.lastMessageTimestamp(t):e==="lastInteractedAt"?lt.lastInteractedAt(t):lt.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){const e=this._filterToExchanges(t);if(e.length===0)return this.createdAt(t);const s=e[e.length-1];return s.timestamp?new Date(s.timestamp):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!lt.isEmpty(t)||lt.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const i of this._onBeforeChangeConversationListeners){const o=i(t,s);o!==void 0&&(s=o)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return lt.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??ie.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return lt.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return lt.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=i=>Array.isArray(i)?i.some(e):!!i&&(i.type==="image"||!(!i.content||!Array.isArray(i.content))&&i.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const t=lt._filterToExchanges(this);return t.length===0?null:t[0]}get lastExchange(){const t=lt._filterToExchanges(this);return t.length===0?null:t[t.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>le(t)&&t.status===Rt.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>hs(t)||qs(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=[];for(const s of t)if(hs(s))e.push(Wa(s));else if(qs(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=ja(s,1),o={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};e.push(o)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Rt.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,i="";const o=await this._addIdeStateNode(Xi({...t,request_id:e,status:Rt.sent,timestamp:new Date().toISOString()}));for await(const r of this.sendUserMessage(e,o,!0))r.response_text&&(i+=r.response_text),r.request_id&&(s=r.request_id);return{responseText:i,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}async*sendUserMessage(t,e,s){var d;const i=this._specialContextInputModel.chatActiveContext;let o;if(e.chatHistory!==void 0)o=e.chatHistory;else{let p=this.successfulMessages;if(e.chatItemType===Os.summaryTitle){const f=p.findIndex(g=>g.chatItemType!==Os.agentOnboarding&&ii(g));f!==-1&&(p=p.slice(f))}o=this._convertHistoryToExchanges(p)}let r=this.personaType;if(e.structured_request_nodes){const p=e.structured_request_nodes.find(f=>f.type===ke.CHANGE_PERSONALITY);p&&p.change_personality_node&&(r=p.change_personality_node.personality_type)}const a={text:e.request_message,chatHistory:o,silent:s,modelId:e.model_id,context:i,userSpecifiedFiles:i.userSpecifiedFiles,externalSourceIds:(d=i.externalSources)==null?void 0:d.map(p=>p.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:r,conversationId:this.id,createdTimestamp:Date.now()},l=this._createStreamStateHandlers(t,a,{flags:this._chatFlagModel}),c=this._extensionClient.startChatStreamWithRetry(t,a,{flags:this._chatFlagModel});for await(const p of c){let f=p;for(const g of l)f=g.handleChunk(f)??f;yield f}for(const p of l)yield*p.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}_fileNameToImageFormat(t){var s;switch((s=t.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return Ss.JPEG;case"png":return Ss.PNG;case"gif":return Ss.GIF;case"webp":return Ss.WEBP;default:return Ss.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let e=(t.structured_request_nodes??[]).filter(i=>i.type!==ke.IDE_STATE);const s=await this._extensionClient.getChatRequestIdeState();return s?(e=[...e,{id:sr(e)+1,type:ke.IDE_STATE,ide_state_node:s}],{...t,structured_request_nodes:e}):t}}function ja(n,t){const e=(qs(n),n.fromTimestamp),s=(qs(n),n.toTimestamp),i=qs(n)&&n.revertTarget!==void 0;return{id:t,type:ke.CHECKPOINT_REF,checkpoint_ref_node:{request_id:n.request_id||"",from_timestamp:e,to_timestamp:s,source:i?ta.CHECKPOINT_REVERT:void 0}}}function Wa(n){const t=(n.structured_output_nodes??[]).filter(e=>e.type===xe.RAW_RESPONSE||e.type===xe.TOOL_USE);return{request_message:n.request_message,response_text:n.response_text??"",request_id:n.request_id||"",request_nodes:n.structured_request_nodes??[],response_nodes:t}}function sr(n){return n.length>0?Math.max(...n.map(t=>t.id)):0}function Xi(n){var t;if(n.request_message.length>0&&!((t=n.structured_request_nodes)!=null&&t.some(e=>e.type===ke.TEXT))){let e=n.structured_request_nodes??[];return e=[...e,{id:sr(e)+1,type:ke.TEXT,text_node:{content:n.request_message}}],{...n,structured_request_nodes:e}}return n}class Va{constructor(t=!0,e=setTimeout){h(this,"_notify",new Set);h(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});h(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});h(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});h(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,i){if(!t&&!i)return()=>{};const o={timeout:t,notify:e,once:s,date:i};return this._notify.add(o),this._schedule(o),()=>{this._clearTimeout(o),this._notify.delete(o)}}}class Ya{constructor(t=0,e=0,s=new Va,i=yt("busy"),o=yt(!1)){h(this,"unsubNotify");h(this,"unsubMessage");h(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});h(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=o,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var bn=(n=>(n.send="send",n.addTask="addTask",n))(bn||{});const Wd=[{id:"send",label:"Send to Agent",icon:Qr,description:"Send message to agent"},{id:"addTask",label:"Add Task",icon:Vo,description:"Add task with the message content"}];class Xa{constructor(){h(this,"_mode",yt(bn.send));h(this,"_currentMode",bn.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(bn).includes(t)&&this._mode.set(t)}}const cn=yt("idle");var Za=(n=>(n.manual="manual",n.auto="auto",n))(Za||{});class Qa{constructor(t,e,s,i={}){h(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});h(this,"extensionClient");h(this,"_chatFlagsModel");h(this,"_currConversationModel");h(this,"_chatModeModel");h(this,"_sendModeModel");h(this,"subscribers",new Set);h(this,"idleMessageModel",new Ya);h(this,"isPanelCollapsed");h(this,"agentExecutionMode");h(this,"sortConversationsBy");h(this,"displayedAnnouncements");h(this,"onLoaded",async()=>{var s,i;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&t.enableBackgroundAgents;this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??Gr,bigSyncThreshold:t.bigSyncThreshold??jr,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Wr,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??Ir.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:t.enableBackgroundAgents??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,clientAnnouncement:t.clientAnnouncement??""}),e&&this.onDoUseNewDraftFunctionalityChanged(),(i=(s=this.options).onLoaded)==null||i.call(s),this.notifySubscribers()});h(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));h(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==qn&&this.currentConversationId!==qn||(delete this._state.conversations[qn],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===Zt||lt.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});h(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const i=s.chatHistory.some(o=>hs(o));t[e]={...s,isShareable:i}}this._state.conversations=t});h(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[i,o]of Object.entries(e))o.isPinned&&s.add(i);this.setState(this._state),this.notifySubscribers()});h(this,"saveImmediate",()=>{this._host.setState(this._state)});h(this,"setState",Mr(t=>{this._host.setState({...t,isPanelCollapsed:mt(this.isPanelCollapsed),agentExecutionMode:mt(this.agentExecutionMode),sortConversationsBy:mt(this.sortConversationsBy),displayedAnnouncements:mt(this.displayedAnnouncements),sendMode:this._sendModeModel.getCurrentMode()})},1e3,{maxWait:15e3}));h(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});h(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));h(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[Zt];if(this.currentConversationId&&this.currentConversationId!==Zt&&this._state.conversations[this.currentConversationId]&&lt.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:Zt};this._state.conversations[Zt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=Zt,this._currConversationModel.setConversation(e)}});h(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Os.educateFeatures,request_id:crypto.randomUUID(),seen_state:Fs.seen})});h(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});h(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let i;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=Zt),i=this._state.conversations[t]??lt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===Zt&&(i.id=Zt),s!=null&&s.newTaskUuid&&(i.rootTaskUuid=s.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(ps(this._currConversationModel)?"agent":"chat"),i=lt.create({personaType:await this._currConversationModel.decidePersonaType()})):i=this._state.conversations[t]??lt.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid});const o=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!o,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});h(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[Zt]});h(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});h(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});h(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;cn.set("copying");const s=e==null?void 0:e.chatHistory,i=s.reduce((a,l)=>(hs(l)&&a.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),a),[]);if(i.length===0)throw new Error("No chat history to share");const o=lt.getDisplayName(e),r=await this.extensionClient.saveChat(t,i,o);if(r.data){let a=r.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});h(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void cn.set("idle");navigator.clipboard.writeText(e),cn.set("copied")}catch{cn.set("failed")}});h(this,"deleteConversations",async(t,e=void 0,s=[],i)=>{const o=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${o>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const r=new Set(t);this.deleteConversationIds(r)}if(s.length>0&&i)for(const r of s)try{await i.deleteAgent(r,!0)}catch(a){console.error(`Failed to delete remote agent ${r}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});h(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});h(this,"deleteConversationIds",async t=>{var s;const e=[];for(const i of t){const o=((s=this._state.conversations[i])==null?void 0:s.requestIds)??[];e.push(...o)}for(const i of Object.values(this._state.conversations))if(t.has(i.id)){for(const r of i.chatHistory)le(r)&&this.deleteImagesInExchange(r);const o=i.draftExchange;o&&this.deleteImagesInExchange(o)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([i])=>!t.has(i)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t)})});h(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});h(this,"findImagesInJson",t=>{const e=[],s=i=>{var o;if(i.type==="image"&&((o=i.attrs)!=null&&o.src))e.push(i.attrs.src);else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const r of i.content)s(r)};return s(t),e});h(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===ke.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));h(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});h(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});h(this,"smartPaste",(t,e,s,i)=>{const o=this._currConversationModel.historyTo(t,!0).filter(r=>hs(r)).map(r=>({request_message:r.request_message,response_text:r.response_text||"",request_id:r.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:o,targetFile:s??void 0,options:i})});h(this,"saveImage",async t=>await this.extensionClient.saveImage(t));h(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));h(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new Br(i.initialFlags),this.extensionClient=new Lr(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new lt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this._sendModeModel=new Xa,this.initialize(i.initialConversation);const o=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=yt(o),this._chatFlagsModel.enableAgentAutoMode?this.agentExecutionMode=yt(this._state.agentExecutionMode??"manual"):this.agentExecutionMode=yt("manual"),this.sortConversationsBy=yt(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=yt(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}orderedConversations(t,e="desc",s){const i=t||this._state.sortConversationsBy||"lastMessageTimestamp";let o=Object.values(this._state.conversations);return s&&(o=o.filter(s)),o.sort((r,a)=>{const l=lt.getTime(r,i).getTime(),c=lt.getTime(a,i).getTime();return e==="asc"?l-c:c-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===Zt)return!1;const i=!lt.isValid(this.conversations[s]),o=ps(this.conversations[s]);return i&&(t==="agent"&&o||t==="chat"&&!o||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===Et.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):s.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}h(Qa,"NEW_AGENT_KEY",Zt);const as=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Zi=new Set,oi=typeof process=="object"&&process?process:{},nr=(n,t,e,s)=>{typeof oi.emitWarning=="function"?oi.emitWarning(n,t,e,s):console.error(`[${e}] ${t}: ${n}`)};let In=globalThis.AbortController,Qi=globalThis.AbortSignal;var Lo;if(In===void 0){Qi=class{constructor(){h(this,"onabort");h(this,"_onabort",[]);h(this,"reason");h(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},In=class{constructor(){h(this,"signal",new Qi);t()}abort(e){var s,i;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const o of this.signal._onabort)o(e);(i=(s=this.signal).onabort)==null||i.call(s,e)}}};let n=((Lo=oi.env)==null?void 0:Lo.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{n&&(n=!1,nr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const qe=n=>n&&n===Math.floor(n)&&n>0&&isFinite(n),ir=n=>qe(n)?n<=Math.pow(2,8)?Uint8Array:n<=Math.pow(2,16)?Uint16Array:n<=Math.pow(2,32)?Uint32Array:n<=Number.MAX_SAFE_INTEGER?_n:null:null;class _n extends Array{constructor(t){super(t),this.fill(0)}}var ms;const es=class es{constructor(t,e){h(this,"heap");h(this,"length");if(!u(es,ms))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=ir(t);if(!e)return[];U(es,ms,!0);const s=new es(t,e);return U(es,ms,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};ms=new WeakMap,X(es,ms,!1);let ri=es;var Po,Uo,fe,Kt,ge,me,vs,bs,St,ve,wt,at,G,Bt,Jt,zt,At,be,Dt,_e,we,te,ye,Ve,Gt,I,li,ss,Ne,Qs,ee,or,ns,_s,Ks,He,Be,ci,wn,yn,rt,di,Ns,Ge,ui;const Mi=class Mi{constructor(t){X(this,I);X(this,fe);X(this,Kt);X(this,ge);X(this,me);X(this,vs);X(this,bs);h(this,"ttl");h(this,"ttlResolution");h(this,"ttlAutopurge");h(this,"updateAgeOnGet");h(this,"updateAgeOnHas");h(this,"allowStale");h(this,"noDisposeOnSet");h(this,"noUpdateTTL");h(this,"maxEntrySize");h(this,"sizeCalculation");h(this,"noDeleteOnFetchRejection");h(this,"noDeleteOnStaleGet");h(this,"allowStaleOnFetchAbort");h(this,"allowStaleOnFetchRejection");h(this,"ignoreFetchAbort");X(this,St);X(this,ve);X(this,wt);X(this,at);X(this,G);X(this,Bt);X(this,Jt);X(this,zt);X(this,At);X(this,be);X(this,Dt);X(this,_e);X(this,we);X(this,te);X(this,ye);X(this,Ve);X(this,Gt);X(this,ss,()=>{});X(this,Ne,()=>{});X(this,Qs,()=>{});X(this,ee,()=>!1);X(this,ns,t=>{});X(this,_s,(t,e,s)=>{});X(this,Ks,(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});h(this,Po,"LRUCache");const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:o,updateAgeOnGet:r,updateAgeOnHas:a,allowStale:l,dispose:c,disposeAfter:d,noDisposeOnSet:p,noUpdateTTL:f,maxSize:g=0,maxEntrySize:b=0,sizeCalculation:m,fetchMethod:_,memoMethod:v,noDeleteOnFetchRejection:x,noDeleteOnStaleGet:A,allowStaleOnFetchRejection:L,allowStaleOnFetchAbort:w,ignoreFetchAbort:K}=t;if(e!==0&&!qe(e))throw new TypeError("max option must be a nonnegative integer");const V=e?ir(e):Array;if(!V)throw new Error("invalid max value: "+e);if(U(this,fe,e),U(this,Kt,g),this.maxEntrySize=b||u(this,Kt),this.sizeCalculation=m,this.sizeCalculation){if(!u(this,Kt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(v!==void 0&&typeof v!="function")throw new TypeError("memoMethod must be a function if defined");if(U(this,bs,v),_!==void 0&&typeof _!="function")throw new TypeError("fetchMethod must be a function if specified");if(U(this,vs,_),U(this,Ve,!!_),U(this,wt,new Map),U(this,at,new Array(e).fill(void 0)),U(this,G,new Array(e).fill(void 0)),U(this,Bt,new V(e)),U(this,Jt,new V(e)),U(this,zt,0),U(this,At,0),U(this,be,ri.create(e)),U(this,St,0),U(this,ve,0),typeof c=="function"&&U(this,ge,c),typeof d=="function"?(U(this,me,d),U(this,Dt,[])):(U(this,me,void 0),U(this,Dt,void 0)),U(this,ye,!!u(this,ge)),U(this,Gt,!!u(this,me)),this.noDisposeOnSet=!!p,this.noUpdateTTL=!!f,this.noDeleteOnFetchRejection=!!x,this.allowStaleOnFetchRejection=!!L,this.allowStaleOnFetchAbort=!!w,this.ignoreFetchAbort=!!K,this.maxEntrySize!==0){if(u(this,Kt)!==0&&!qe(u(this,Kt)))throw new TypeError("maxSize must be a positive integer if specified");if(!qe(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");D(this,I,or).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!A,this.updateAgeOnGet=!!r,this.updateAgeOnHas=!!a,this.ttlResolution=qe(i)||i===0?i:1,this.ttlAutopurge=!!o,this.ttl=s||0,this.ttl){if(!qe(this.ttl))throw new TypeError("ttl must be a positive integer if specified");D(this,I,li).call(this)}if(u(this,fe)===0&&this.ttl===0&&u(this,Kt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!u(this,fe)&&!u(this,Kt)){const M="LRU_CACHE_UNBOUNDED";(S=>!Zi.has(S))(M)&&(Zi.add(M),nr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",M,Mi))}}static unsafeExposeInternals(t){return{starts:u(t,we),ttls:u(t,te),sizes:u(t,_e),keyMap:u(t,wt),keyList:u(t,at),valList:u(t,G),next:u(t,Bt),prev:u(t,Jt),get head(){return u(t,zt)},get tail(){return u(t,At)},free:u(t,be),isBackgroundFetch:e=>{var s;return D(s=t,I,rt).call(s,e)},backgroundFetch:(e,s,i,o)=>{var r;return D(r=t,I,yn).call(r,e,s,i,o)},moveToTail:e=>{var s;return D(s=t,I,Ns).call(s,e)},indexes:e=>{var s;return D(s=t,I,He).call(s,e)},rindexes:e=>{var s;return D(s=t,I,Be).call(s,e)},isStale:e=>{var s;return u(s=t,ee).call(s,e)}}}get max(){return u(this,fe)}get maxSize(){return u(this,Kt)}get calculatedSize(){return u(this,ve)}get size(){return u(this,St)}get fetchMethod(){return u(this,vs)}get memoMethod(){return u(this,bs)}get dispose(){return u(this,ge)}get disposeAfter(){return u(this,me)}getRemainingTTL(t){return u(this,wt).has(t)?1/0:0}*entries(){for(const t of D(this,I,He).call(this))u(this,G)[t]===void 0||u(this,at)[t]===void 0||D(this,I,rt).call(this,u(this,G)[t])||(yield[u(this,at)[t],u(this,G)[t]])}*rentries(){for(const t of D(this,I,Be).call(this))u(this,G)[t]===void 0||u(this,at)[t]===void 0||D(this,I,rt).call(this,u(this,G)[t])||(yield[u(this,at)[t],u(this,G)[t]])}*keys(){for(const t of D(this,I,He).call(this)){const e=u(this,at)[t];e===void 0||D(this,I,rt).call(this,u(this,G)[t])||(yield e)}}*rkeys(){for(const t of D(this,I,Be).call(this)){const e=u(this,at)[t];e===void 0||D(this,I,rt).call(this,u(this,G)[t])||(yield e)}}*values(){for(const t of D(this,I,He).call(this))u(this,G)[t]===void 0||D(this,I,rt).call(this,u(this,G)[t])||(yield u(this,G)[t])}*rvalues(){for(const t of D(this,I,Be).call(this))u(this,G)[t]===void 0||D(this,I,rt).call(this,u(this,G)[t])||(yield u(this,G)[t])}[(Uo=Symbol.iterator,Po=Symbol.toStringTag,Uo)](){return this.entries()}find(t,e={}){for(const s of D(this,I,He).call(this)){const i=u(this,G)[s],o=D(this,I,rt).call(this,i)?i.__staleWhileFetching:i;if(o!==void 0&&t(o,u(this,at)[s],this))return this.get(u(this,at)[s],e)}}forEach(t,e=this){for(const s of D(this,I,He).call(this)){const i=u(this,G)[s],o=D(this,I,rt).call(this,i)?i.__staleWhileFetching:i;o!==void 0&&t.call(e,o,u(this,at)[s],this)}}rforEach(t,e=this){for(const s of D(this,I,Be).call(this)){const i=u(this,G)[s],o=D(this,I,rt).call(this,i)?i.__staleWhileFetching:i;o!==void 0&&t.call(e,o,u(this,at)[s],this)}}purgeStale(){let t=!1;for(const e of D(this,I,Be).call(this,{allowStale:!0}))u(this,ee).call(this,e)&&(D(this,I,Ge).call(this,u(this,at)[e],"expire"),t=!0);return t}info(t){const e=u(this,wt).get(t);if(e===void 0)return;const s=u(this,G)[e],i=D(this,I,rt).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const o={value:i};if(u(this,te)&&u(this,we)){const r=u(this,te)[e],a=u(this,we)[e];if(r&&a){const l=r-(as.now()-a);o.ttl=l,o.start=Date.now()}}return u(this,_e)&&(o.size=u(this,_e)[e]),o}dump(){const t=[];for(const e of D(this,I,He).call(this,{allowStale:!0})){const s=u(this,at)[e],i=u(this,G)[e],o=D(this,I,rt).call(this,i)?i.__staleWhileFetching:i;if(o===void 0||s===void 0)continue;const r={value:o};if(u(this,te)&&u(this,we)){r.ttl=u(this,te)[e];const a=as.now()-u(this,we)[e];r.start=Math.floor(Date.now()-a)}u(this,_e)&&(r.size=u(this,_e)[e]),t.unshift([s,r])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const i=Date.now()-s.start;s.start=as.now()-i}this.set(e,s.value,s)}}set(t,e,s={}){var f,g,b,m,_;if(e===void 0)return this.delete(t),this;const{ttl:i=this.ttl,start:o,noDisposeOnSet:r=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:l}=s;let{noUpdateTTL:c=this.noUpdateTTL}=s;const d=u(this,Ks).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&d>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),D(this,I,Ge).call(this,t,"set"),this;let p=u(this,St)===0?void 0:u(this,wt).get(t);if(p===void 0)p=u(this,St)===0?u(this,At):u(this,be).length!==0?u(this,be).pop():u(this,St)===u(this,fe)?D(this,I,wn).call(this,!1):u(this,St),u(this,at)[p]=t,u(this,G)[p]=e,u(this,wt).set(t,p),u(this,Bt)[u(this,At)]=p,u(this,Jt)[p]=u(this,At),U(this,At,p),on(this,St)._++,u(this,_s).call(this,p,d,l),l&&(l.set="add"),c=!1;else{D(this,I,Ns).call(this,p);const v=u(this,G)[p];if(e!==v){if(u(this,Ve)&&D(this,I,rt).call(this,v)){v.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:x}=v;x===void 0||r||(u(this,ye)&&((f=u(this,ge))==null||f.call(this,x,t,"set")),u(this,Gt)&&((g=u(this,Dt))==null||g.push([x,t,"set"])))}else r||(u(this,ye)&&((b=u(this,ge))==null||b.call(this,v,t,"set")),u(this,Gt)&&((m=u(this,Dt))==null||m.push([v,t,"set"])));if(u(this,ns).call(this,p),u(this,_s).call(this,p,d,l),u(this,G)[p]=e,l){l.set="replace";const x=v&&D(this,I,rt).call(this,v)?v.__staleWhileFetching:v;x!==void 0&&(l.oldValue=x)}}else l&&(l.set="update")}if(i===0||u(this,te)||D(this,I,li).call(this),u(this,te)&&(c||u(this,Qs).call(this,p,i,o),l&&u(this,Ne).call(this,l,p)),!r&&u(this,Gt)&&u(this,Dt)){const v=u(this,Dt);let x;for(;x=v==null?void 0:v.shift();)(_=u(this,me))==null||_.call(this,...x)}return this}pop(){var t;try{for(;u(this,St);){const e=u(this,G)[u(this,zt)];if(D(this,I,wn).call(this,!0),D(this,I,rt).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(u(this,Gt)&&u(this,Dt)){const e=u(this,Dt);let s;for(;s=e==null?void 0:e.shift();)(t=u(this,me))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,o=u(this,wt).get(t);if(o!==void 0){const r=u(this,G)[o];if(D(this,I,rt).call(this,r)&&r.__staleWhileFetching===void 0)return!1;if(!u(this,ee).call(this,o))return s&&u(this,ss).call(this,o),i&&(i.has="hit",u(this,Ne).call(this,i,o)),!0;i&&(i.has="stale",u(this,Ne).call(this,i,o))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=u(this,wt).get(t);if(i===void 0||!s&&u(this,ee).call(this,i))return;const o=u(this,G)[i];return D(this,I,rt).call(this,o)?o.__staleWhileFetching:o}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,ttl:r=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:l=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:p=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:b=this.allowStaleOnFetchAbort,context:m,forceRefresh:_=!1,status:v,signal:x}=e;if(!u(this,Ve))return v&&(v.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:o,status:v});const A={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:o,ttl:r,noDisposeOnSet:a,size:l,sizeCalculation:c,noUpdateTTL:d,noDeleteOnFetchRejection:p,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:b,ignoreFetchAbort:g,status:v,signal:x};let L=u(this,wt).get(t);if(L===void 0){v&&(v.fetch="miss");const w=D(this,I,yn).call(this,t,L,A,m);return w.__returned=w}{const w=u(this,G)[L];if(D(this,I,rt).call(this,w)){const S=s&&w.__staleWhileFetching!==void 0;return v&&(v.fetch="inflight",S&&(v.returnedStale=!0)),S?w.__staleWhileFetching:w.__returned=w}const K=u(this,ee).call(this,L);if(!_&&!K)return v&&(v.fetch="hit"),D(this,I,Ns).call(this,L),i&&u(this,ss).call(this,L),v&&u(this,Ne).call(this,v,L),w;const V=D(this,I,yn).call(this,t,L,A,m),M=V.__staleWhileFetching!==void 0&&s;return v&&(v.fetch=K?"stale":"refresh",M&&K&&(v.returnedStale=!0)),M?V.__staleWhileFetching:V.__returned=V}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=u(this,bs);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:o,...r}=e,a=this.get(t,r);if(!o&&a!==void 0)return a;const l=s(t,a,{options:r,context:i});return this.set(t,l,r),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,status:r}=e,a=u(this,wt).get(t);if(a!==void 0){const l=u(this,G)[a],c=D(this,I,rt).call(this,l);return r&&u(this,Ne).call(this,r,a),u(this,ee).call(this,a)?(r&&(r.get="stale"),c?(r&&s&&l.__staleWhileFetching!==void 0&&(r.returnedStale=!0),s?l.__staleWhileFetching:void 0):(o||D(this,I,Ge).call(this,t,"expire"),r&&s&&(r.returnedStale=!0),s?l:void 0)):(r&&(r.get="hit"),c?l.__staleWhileFetching:(D(this,I,Ns).call(this,a),i&&u(this,ss).call(this,a),l))}r&&(r.get="miss")}delete(t){return D(this,I,Ge).call(this,t,"delete")}clear(){return D(this,I,ui).call(this,"delete")}};fe=new WeakMap,Kt=new WeakMap,ge=new WeakMap,me=new WeakMap,vs=new WeakMap,bs=new WeakMap,St=new WeakMap,ve=new WeakMap,wt=new WeakMap,at=new WeakMap,G=new WeakMap,Bt=new WeakMap,Jt=new WeakMap,zt=new WeakMap,At=new WeakMap,be=new WeakMap,Dt=new WeakMap,_e=new WeakMap,we=new WeakMap,te=new WeakMap,ye=new WeakMap,Ve=new WeakMap,Gt=new WeakMap,I=new WeakSet,li=function(){const t=new _n(u(this,fe)),e=new _n(u(this,fe));U(this,te,t),U(this,we,e),U(this,Qs,(o,r,a=as.now())=>{if(e[o]=r!==0?a:0,t[o]=r,r!==0&&this.ttlAutopurge){const l=setTimeout(()=>{u(this,ee).call(this,o)&&D(this,I,Ge).call(this,u(this,at)[o],"expire")},r+1);l.unref&&l.unref()}}),U(this,ss,o=>{e[o]=t[o]!==0?as.now():0}),U(this,Ne,(o,r)=>{if(t[r]){const a=t[r],l=e[r];if(!a||!l)return;o.ttl=a,o.start=l,o.now=s||i();const c=o.now-l;o.remainingTTL=a-c}});let s=0;const i=()=>{const o=as.now();if(this.ttlResolution>0){s=o;const r=setTimeout(()=>s=0,this.ttlResolution);r.unref&&r.unref()}return o};this.getRemainingTTL=o=>{const r=u(this,wt).get(o);if(r===void 0)return 0;const a=t[r],l=e[r];return!a||!l?1/0:a-((s||i())-l)},U(this,ee,o=>{const r=e[o],a=t[o];return!!a&&!!r&&(s||i())-r>a})},ss=new WeakMap,Ne=new WeakMap,Qs=new WeakMap,ee=new WeakMap,or=function(){const t=new _n(u(this,fe));U(this,ve,0),U(this,_e,t),U(this,ns,e=>{U(this,ve,u(this,ve)-t[e]),t[e]=0}),U(this,Ks,(e,s,i,o)=>{if(D(this,I,rt).call(this,s))return 0;if(!qe(i)){if(!o)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof o!="function")throw new TypeError("sizeCalculation must be a function");if(i=o(s,e),!qe(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),U(this,_s,(e,s,i)=>{if(t[e]=s,u(this,Kt)){const o=u(this,Kt)-t[e];for(;u(this,ve)>o;)D(this,I,wn).call(this,!0)}U(this,ve,u(this,ve)+t[e]),i&&(i.entrySize=s,i.totalCalculatedSize=u(this,ve))})},ns=new WeakMap,_s=new WeakMap,Ks=new WeakMap,He=function*({allowStale:t=this.allowStale}={}){if(u(this,St))for(let e=u(this,At);D(this,I,ci).call(this,e)&&(!t&&u(this,ee).call(this,e)||(yield e),e!==u(this,zt));)e=u(this,Jt)[e]},Be=function*({allowStale:t=this.allowStale}={}){if(u(this,St))for(let e=u(this,zt);D(this,I,ci).call(this,e)&&(!t&&u(this,ee).call(this,e)||(yield e),e!==u(this,At));)e=u(this,Bt)[e]},ci=function(t){return t!==void 0&&u(this,wt).get(u(this,at)[t])===t},wn=function(t){var o,r;const e=u(this,zt),s=u(this,at)[e],i=u(this,G)[e];return u(this,Ve)&&D(this,I,rt).call(this,i)?i.__abortController.abort(new Error("evicted")):(u(this,ye)||u(this,Gt))&&(u(this,ye)&&((o=u(this,ge))==null||o.call(this,i,s,"evict")),u(this,Gt)&&((r=u(this,Dt))==null||r.push([i,s,"evict"]))),u(this,ns).call(this,e),t&&(u(this,at)[e]=void 0,u(this,G)[e]=void 0,u(this,be).push(e)),u(this,St)===1?(U(this,zt,U(this,At,0)),u(this,be).length=0):U(this,zt,u(this,Bt)[e]),u(this,wt).delete(s),on(this,St)._--,e},yn=function(t,e,s,i){const o=e===void 0?void 0:u(this,G)[e];if(D(this,I,rt).call(this,o))return o;const r=new In,{signal:a}=s;a==null||a.addEventListener("abort",()=>r.abort(a.reason),{signal:r.signal});const l={signal:r.signal,options:s,context:i},c=(g,b=!1)=>{const{aborted:m}=r.signal,_=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(m&&!b?(s.status.fetchAborted=!0,s.status.fetchError=r.signal.reason,_&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),m&&!_&&!b)return d(r.signal.reason);const v=p;return u(this,G)[e]===p&&(g===void 0?v.__staleWhileFetching?u(this,G)[e]=v.__staleWhileFetching:D(this,I,Ge).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,l.options))),g},d=g=>{const{aborted:b}=r.signal,m=b&&s.allowStaleOnFetchAbort,_=m||s.allowStaleOnFetchRejection,v=_||s.noDeleteOnFetchRejection,x=p;if(u(this,G)[e]===p&&(!v||x.__staleWhileFetching===void 0?D(this,I,Ge).call(this,t,"fetch"):m||(u(this,G)[e]=x.__staleWhileFetching)),_)return s.status&&x.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),x.__staleWhileFetching;if(x.__returned===x)throw g};s.status&&(s.status.fetchDispatched=!0);const p=new Promise((g,b)=>{var _;const m=(_=u(this,vs))==null?void 0:_.call(this,t,o,l);m&&m instanceof Promise&&m.then(v=>g(v===void 0?void 0:v),b),r.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=v=>c(v,!0)))})}).then(c,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),d(g))),f=Object.assign(p,{__abortController:r,__staleWhileFetching:o,__returned:void 0});return e===void 0?(this.set(t,f,{...l.options,status:void 0}),e=u(this,wt).get(t)):u(this,G)[e]=f,f},rt=function(t){if(!u(this,Ve))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof In},di=function(t,e){u(this,Jt)[e]=t,u(this,Bt)[t]=e},Ns=function(t){t!==u(this,At)&&(t===u(this,zt)?U(this,zt,u(this,Bt)[t]):D(this,I,di).call(this,u(this,Jt)[t],u(this,Bt)[t]),D(this,I,di).call(this,u(this,At),t),U(this,At,t))},Ge=function(t,e){var i,o,r,a;let s=!1;if(u(this,St)!==0){const l=u(this,wt).get(t);if(l!==void 0)if(s=!0,u(this,St)===1)D(this,I,ui).call(this,e);else{u(this,ns).call(this,l);const c=u(this,G)[l];if(D(this,I,rt).call(this,c)?c.__abortController.abort(new Error("deleted")):(u(this,ye)||u(this,Gt))&&(u(this,ye)&&((i=u(this,ge))==null||i.call(this,c,t,e)),u(this,Gt)&&((o=u(this,Dt))==null||o.push([c,t,e]))),u(this,wt).delete(t),u(this,at)[l]=void 0,u(this,G)[l]=void 0,l===u(this,At))U(this,At,u(this,Jt)[l]);else if(l===u(this,zt))U(this,zt,u(this,Bt)[l]);else{const d=u(this,Jt)[l];u(this,Bt)[d]=u(this,Bt)[l];const p=u(this,Bt)[l];u(this,Jt)[p]=u(this,Jt)[l]}on(this,St)._--,u(this,be).push(l)}}if(u(this,Gt)&&((r=u(this,Dt))!=null&&r.length)){const l=u(this,Dt);let c;for(;c=l==null?void 0:l.shift();)(a=u(this,me))==null||a.call(this,...c)}return s},ui=function(t){var e,s,i;for(const o of D(this,I,Be).call(this,{allowStale:!0})){const r=u(this,G)[o];if(D(this,I,rt).call(this,r))r.__abortController.abort(new Error("deleted"));else{const a=u(this,at)[o];u(this,ye)&&((e=u(this,ge))==null||e.call(this,r,a,t)),u(this,Gt)&&((s=u(this,Dt))==null||s.push([r,a,t]))}}if(u(this,wt).clear(),u(this,G).fill(void 0),u(this,at).fill(void 0),u(this,te)&&u(this,we)&&(u(this,te).fill(0),u(this,we).fill(0)),u(this,_e)&&u(this,_e).fill(0),U(this,zt,0),U(this,At,0),u(this,be).length=0,U(this,ve,0),U(this,St,0),u(this,Gt)&&u(this,Dt)){const o=u(this,Dt);let r;for(;r=o==null?void 0:o.shift();)(i=u(this,me))==null||i.call(this,...r)}};let ai=Mi;class Vd{constructor(){h(this,"_syncStatus",{status:Jr.done,foldersProgress:[]});h(this,"_syncEnabledState",Li.initializing);h(this,"_workspaceGuidelines",[]);h(this,"_openUserGuidelinesInput",!1);h(this,"_userGuidelines");h(this,"_contextStore",new Ka);h(this,"_prevOpenFiles",[]);h(this,"_disableContext",!1);h(this,"_enableAgentMemories",!1);h(this,"subscribers",new Set);h(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));h(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case Et.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case Et.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case Et.fileRangesSelected:this.updateSelections(e.data);break;case Et.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case Et.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case Et.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});h(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:Ct.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});h(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});h(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});h(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});h(this,"addFile",t=>{this.addFiles([t])});h(this,"addFiles",t=>{this.updateFiles(t,[])});h(this,"removeFile",t=>{this.removeFiles([t])});h(this,"removeFiles",t=>{this.updateFiles([],t)});h(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});h(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});h(this,"updateFiles",(t,e)=>{const s=r=>({file:r,...Hn(r)}),i=t.map(s),o=e.map(s);this._contextStore.update(i,o,r=>r.id),this.notifySubscribers()});h(this,"updateRules",(t,e)=>{const s=r=>({rule:r,...Hr(r)}),i=t.map(s),o=e.map(s);this._contextStore.update(i,o,r=>r.id),this.notifySubscribers()});h(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});h(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});h(this,"setCurrentlyOpenFiles",t=>{const e=t.map(i=>({recentFile:i,...Hn(i)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,i=>i.id),s.forEach(i=>{const o=this._contextStore.peekKey(i.id);o!=null&&o.recentFile&&(o.file=o.recentFile,delete o.recentFile)}),e.forEach(i=>{const o=this._contextStore.peekKey(i.id);o!=null&&o.file&&(o.recentFile=o.file,delete o.file)}),this.notifySubscribers()});h(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});h(this,"updateUserGuidelines",t=>{const e=this.userGuidelines,s={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:Ct.active,referenceCount:1,showWarning:t.overLimit};this._contextStore.update([s],e,i=>{var o,r;return i.id+String((o=i.userGuidelines)==null?void 0:o.enabled)+String((r=i.userGuidelines)==null?void 0:r.overLimit)}),this.notifySubscribers()});h(this,"onGuidelinesStateUpdate",t=>{this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines;e&&this.updateUserGuidelines(e),this.onSourceFoldersUpdated(this.sourceFolders.map(s=>s.sourceFolder))});h(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===e.folderRoot);return{...e,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));h(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});h(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});h(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});h(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Ri);this._contextStore.update(t.map(s=>({selection:s,...Hn(s)})),e,s=>s.id),this.notifySubscribers()});h(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});h(this,"markInactive",t=>{this.markItemsInactive([t])});h(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,Ct.inactive)}),this.notifySubscribers()});h(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});h(this,"markActive",t=>{this.markItemsActive([t])});h(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,Ct.active)}),this.notifySubscribers()});h(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});h(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});h(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});h(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Pr(t)&&!Di(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Di)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Ri)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Ur)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Fi)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(zr)}get userGuidelines(){return this._contextStore.values.filter(Oi)}get agentMemories(){return[{...qr,status:this._enableAgentMemories?Ct.active:Ct.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Ni(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===Ct.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===Ct.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===Ct.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===Ct.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===Ct.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===Ct.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===Li.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(c=>c.progress!==void 0);if(t.length===0)return;const e=t.reduce((c,d)=>{var p;return c+(((p=d==null?void 0:d.progress)==null?void 0:p.trackedFiles)??0)},0),s=t.reduce((c,d)=>{var p;return c+(((p=d==null?void 0:d.progress)==null?void 0:p.backlogSize)??0)},0),i=Math.max(e,0),o=Math.min(Math.max(s,0),i),r=i-o,a=[];for(const c of t)(l=c==null?void 0:c.progress)!=null&&l.newlyTracked&&a.push(c.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:r,backlogSize:o,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Fi(t)||Oi(t)||Wo(t)||Ni(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===Ct.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===Ct.inactive)}get isContextDisabled(){return this._disableContext}}class Ka{constructor(){h(this,"_cache",new ai({max:1e3}));h(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));h(this,"clear",()=>{this._cache.clear()});h(this,"update",(t,e,s)=>{t.forEach(i=>this.addInPlace(i,s)),e.forEach(i=>this.removeInPlace(i,s))});h(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});h(this,"addInPlace",(t,e)=>{const s=e(t),i=t.referenceCount??1,o=this._cache.get(s),r=t.status??(o==null?void 0:o.status)??Ct.active;o?(o.referenceCount+=i,o.status=r,o.pinned=t.pinned??o.pinned,o.showWarning=t.showWarning??o.showWarning):this._cache.set(s,{...t,pinned:void 0,referenceCount:i,status:r})});h(this,"removeInPlace",(t,e)=>{const s=e(t),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});h(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});h(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});h(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});h(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});h(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===Ct.active?Ct.inactive:Ct.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Ja{constructor(t){h(this,"_githubTimeoutMs",3e4);this._asyncMsgSender=t}async listUserRepos(){try{const t=await this._asyncMsgSender.send({type:Et.listGithubReposForAuthenticatedUserRequest},this._githubTimeoutMs);return{repos:t.data.repos,error:t.data.error,isDevDeploy:t.data.isDevDeploy}}catch(t){return console.error("Failed to list user repos:",t),{repos:[],error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async listRepoBranches(t,e){try{const s=await this._asyncMsgSender.send({type:Et.listGithubRepoBranchesRequest,data:{repo:t,page:e}},this._githubTimeoutMs);return{branches:s.data.branches,hasNextPage:s.data.hasNextPage,nextPage:s.data.nextPage,error:s.data.error,isDevDeploy:s.data.isDevDeploy}}catch(s){return console.error("Failed to list repo branches:",s),{branches:[],hasNextPage:!1,nextPage:0,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async getGithubRepo(t){try{const e=await this._asyncMsgSender.send({type:Et.getGithubRepoRequest,data:{repo:t}},this._githubTimeoutMs);return{repo:e.data.repo,error:e.data.error,isDevDeploy:e.data.isDevDeploy}}catch(e){return console.error("Failed to get GitHub repo:",e),{repo:t,error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async getCurrentLocalBranch(){try{return(await this._asyncMsgSender.send({type:Et.getCurrentLocalBranchRequest},1e4)).data.branch}catch(t){return void console.error("Failed to get current local branch:",t)}}async listBranches(t=""){try{return{branches:(await this._asyncMsgSender.send({type:Et.getGitBranchesRequest,data:{prefix:t}},1e4)).data.branches}}catch(e){return console.error("Failed to fetch branches:",e),{branches:[]}}}async getWorkspaceDiff(t){try{return(await this._asyncMsgSender.send({type:Et.getWorkspaceDiffRequest,data:{branchName:t}},1e4)).data.diff}catch(e){return console.error("Failed to get workspace diff:",e),""}}async getRemoteUrl(){try{return{remoteUrl:(await this._asyncMsgSender.send({type:Et.getRemoteUrlRequest},1e4)).data.remoteUrl}}catch(t){return console.error("Failed to get remote url:",t),{remoteUrl:"",error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async fetch(){try{await this._asyncMsgSender.send({type:Et.gitFetchRequest},1e4)}catch(t){console.error("Failed to fetch remote branch:",t)}}async isGitRepository(){try{return(await this._asyncMsgSender.send({type:Et.isGitRepositoryRequest},1e4)).data.isGitRepository}catch(t){return console.error("Failed to check if is git repository:",t),!1}}async isGithubAuthenticated(){try{return(await this._asyncMsgSender.send({type:Et.isGithubAuthenticatedRequest},this._githubTimeoutMs)).data.isAuthenticated}catch(t){return console.error("Failed to check GitHub authentication status:",t),!1}}async authenticateGithub(){try{const t=await this._asyncMsgSender.send({type:Et.authenticateGithubRequest},this._githubTimeoutMs);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to authenticate with GitHub:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}async revokeGithubAccess(){try{const t=await this._asyncMsgSender.send({type:Et.revokeGithubAccessRequest},1e4);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to revoke GitHub access:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}}h(Ja,"key","gitReferenceModel");const tl={doHideStatusBars:!1,doHideSlashActions:!1,doHideAtMentions:!1,doHideNewThreadButton:!1,doHideMultimodalActions:!1,doHideContextBar:!1,doShowTurnSelector:!1},el={doHideStatusBars:!0,doHideSlashActions:!0,doHideAtMentions:!0,doHideNewThreadButton:!0,doHideMultimodalActions:!0,doHideContextBar:!0,doShowTurnSelector:!0},Yd="selectedTurnIndex";function Xd(n){let t=tl;return n!=null&&n.isActive&&(t=el,n.isRemoteAgentSshWindow&&(t.doHideAtMentions=!1)),t}var B=(n=>(n.NOT_STARTED="NOT_STARTED",n.IN_PROGRESS="IN_PROGRESS",n.CANCELLED="CANCELLED",n.COMPLETE="COMPLETE",n))(B||{}),xt=(n=>(n.USER="USER",n.AGENT="AGENT",n))(xt||{});const rr={[B.NOT_STARTED]:"[ ]",[B.IN_PROGRESS]:"[/]",[B.COMPLETE]:"[x]",[B.CANCELLED]:"[-]"};function ar(n,t){if(n.uuid===t)return n;if(n.subTasksData)for(const e of n.subTasksData){const s=ar(e,t);if(s)return s}}function Gs(n,t=!1){return lr(n,t).join(`
`)}function lr(n,t=!1){const e=`${rr[n.state]} UUID:${n.uuid} NAME:${n.name} DESCRIPTION:${n.description}`;return t||!n.subTasksData||n.subTasksData.length===0?[e]:[e,...(n.subTasksData||[]).map(s=>lr(s,t).map(i=>`-${i}`)).flat()]}function hi(n,t){var s;const e=(s=n.subTasksData)==null?void 0:s.map(i=>hi(i,t));return{...n,uuid:t!=null&&t.keepUuid?n.uuid:crypto.randomUUID(),subTasks:(e==null?void 0:e.map(i=>i.uuid))||[],subTasksData:e}}function Ki(n){if(!n.trim())throw new Error("Empty markdown");const t=n.split(`
`);function e(){for(;t.length>0;){const r=t.shift(),a=sl(r);try{return{task:cr(r,a),level:a}}catch{}}}const s=e();if(!s)throw new Error("No root task found");const i=[s.task];let o;for(;o=e();){const r=i[o.level-1];if(!r)throw new Error(`Invalid markdown: level ${o.level+1} has no parent
Line: ${o.task.name} is missing a parent
Current tasks: 
${Gs(s.task)}`);r.subTasksData&&r.subTasks||(r.subTasks=[],r.subTasksData=[]),r.subTasksData.push(o.task),r.subTasks.push(o.task.uuid),i[o.level]=o.task,i.splice(o.level+1)}return s.task}function sl(n){const t=n.trimStart().match(/^-+/);return t?t[0].length:0}function cr(n,t){const e=n.trimStart().substring(t),s=e.match(/^\[([ x\-/?])\]/);if(!s)throw new Error(`Invalid task line: ${n} (missing state)`);const i=s[1],o=Object.entries(rr).reduce((d,[p,f])=>(d[f.substring(1,2)]=p,d),{})[i]||B.NOT_STARTED,r=e.match(/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i);if(!r){const d=/\b(?:uuid|UUID):/i.test(e),p=/\b(?:name|NAME):/i.test(e),f=/\b(?:description|DESCRIPTION):/i.test(e);if(!d||!p||!f)throw new Error(`Invalid task line: ${n} (missing required fields)`);const g=e.toLowerCase().indexOf("uuid:"),b=e.toLowerCase().indexOf("name:"),m=e.toLowerCase().indexOf("description:");throw g<b&&b<m?new Error(`Invalid task line: ${n} (invalid format)`):new Error(`Invalid task line: ${n} (incorrect field order)`)}let a=r[1].trim();const l=r[2].trim(),c=r[3].trim();if(!a||!l)throw new Error(`Invalid task line: ${n} (missing required fields)`);return a==="NEW_UUID"&&(a=crypto.randomUUID()),{uuid:a,name:l,description:c,state:o,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:xt.USER}}const xs=n=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:B.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:xt.USER,...n}),Ji=xs({name:"Task 1.1",description:"This is the first sub task",state:B.IN_PROGRESS}),to=xs({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:B.NOT_STARTED}),eo=xs({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:B.IN_PROGRESS}),so=xs({name:"Task 1.2",description:"This is the second sub task",state:B.COMPLETE,subTasks:[to.uuid,eo.uuid],subTasksData:[to,eo]}),no=xs({name:"Task 1.3",description:"This is the third sub task",state:B.CANCELLED}),nl=Gs(xs({name:"Task 1",description:"This is the first task",state:B.NOT_STARTED,subTasks:[Ji.uuid,so.uuid,no.uuid],subTasksData:[Ji,so,no]}));function dr(n){const t=n.split(`
`);let e=null;const s={created:[],updated:[],deleted:[]};for(const i of t){const o=i.trim();if(o!=="## Created Tasks")if(o!=="## Updated Tasks")if(o!=="## Deleted Tasks"){if(e&&(o.startsWith("[ ]")||o.startsWith("[/]")||o.startsWith("[x]")||o.startsWith("[-]")))try{const r=cr(o,0);r&&s[e].push(r)}catch{}}else e="deleted";else e="updated";else e="created"}return s}function Zd(n){const t=n.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const e=dr(ur(n));return{created:e.created.length,updated:e.updated.length,deleted:e.deleted.length}}function ur(n){const t=n.indexOf("# Task Changes");if(t===-1)return"";const e=n.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let i=e.length;for(const a of s){const l=e.indexOf(a);l!==-1&&l<i&&(i=l)}const o=e.substring(0,i),r=o.indexOf(`
`);return r===-1?"":o.substring(r+1).trim()}function Qd(n){return dr(ur(n))}const Ys=class Ys{constructor(t,e,s,i){h(this,"_disposables",[]);h(this,"rootTaskUuid");h(this,"_rootTask",yt(void 0));h(this,"rootTask",zn(this._rootTask));h(this,"_isEnhancing",yt(!1));h(this,"isEnhancing",zn(this._isEnhancing));h(this,"_isImportingExporting",yt(!1));h(this,"isImportingExporting",zn(this._isImportingExporting));h(this,"canShowTaskList");h(this,"_refreshInterval");h(this,"_backlinks",yt({}));h(this,"uuidToTask",vn(this._rootTask,t=>{const e=new Map;if(!t)return e;const s={},i=t?[t]:[];for(;i.length>0;){const o=i.pop();if(e.set(o.uuid,o),o.subTasksData){i.push(...o.subTasksData);for(const r of o.subTasks)s[r]=o.uuid}}return this._backlinks.set(s),e}));h(this,"enhanceTaskList",async()=>{if(!mt(this._isEnhancing))try{this.setEnhancing(!0);const t=mt(this._rootTask);if(!t)return;const e=`Please enhance this task list by adding more details, subtasks, or improving the organization:

`+Gs(t);await this._agentConversationModel.interruptAgent(),await this._chatModel.currentConversationModel.sendExchange({request_message:e,status:Rt.draft,model_id:this._chatModel.currentConversationModel.selectedModelId??void 0})}finally{this.setEnhancing(!1)}});this._chatModel=t,this._extensionClient=e,this._conversationModel=s,this._agentConversationModel=i,this.rootTaskUuid=vn(this._conversationModel,o=>o.rootTaskUuid),this._disposables.push({dispose:this.rootTaskUuid.subscribe(async o=>{o&&(this._extensionClient.setCurrentRootTaskUuid(o),await this.refreshTasks())})}),this._disposables.push({dispose:this._conversationModel.onNewConversation(async()=>{await this._maybeInitializeConversationRootTask()})}),this.canShowTaskList=vn([this._chatModel.flags,this._agentConversationModel.isCurrConversationAgentic],([o,r])=>o.enableTaskList&&r),this._disposables.push({dispose:this.canShowTaskList.subscribe(o=>{o?this._startRefreshInterval():this._stopRefreshInterval()})}),this._maybeInitializeConversationRootTask()}static createReadOnlyStore(t,e=!0){const s=yt(t),i=new Map;if(t){const o=[t];for(;o.length>0;){const r=o.pop();i.set(r.uuid,r),r.subTasksData&&o.push(...r.subTasksData)}}return{rootTaskUuid:yt(t==null?void 0:t.uuid),rootTask:s,isEnhancing:yt(e),canShowTaskList:yt(!0),uuidToTask:yt(i),createTask:()=>Promise.resolve(""),updateTask:()=>Promise.resolve(),getHydratedTask:()=>Promise.resolve(void 0),cloneHydratedTask:()=>Promise.resolve(void 0),refreshTasks:()=>Promise.resolve(),updateTaskListStatuses:()=>Promise.resolve(),syncTaskListWithConversation:()=>Promise.resolve(),deleteTask:()=>Promise.resolve(),cleanupEmptyAndCancelledTasks:()=>Promise.resolve(),getParentTask:()=>{},addNewTaskAfter:()=>Promise.resolve(void 0),saveHydratedTask:()=>Promise.resolve(),runHydratedTask:()=>Promise.resolve(),dispose:()=>{},handleMessageFromExtension:()=>!1,runAllTasks:()=>Promise.resolve(),exportTask:()=>Promise.resolve(),exportTasksToMarkdown:()=>Promise.resolve(),importTasksFromMarkdown:()=>Promise.resolve(),isImportingExporting:yt(!1),setEnhancing:()=>{},enhanceTaskList:()=>Promise.resolve()}}dispose(){for(const t of this._disposables)t.dispose();this._disposables=[]}handleMessageFromExtension(t){return!1}async createTask(t,e,s){const i=await this._extensionClient.createTask(t,e,s);return await this.refreshTasks(),i}async updateTask(t,e,s){await this._extensionClient.updateTask(t,e,s),await this.refreshTasks()}async getHydratedTask(t){return this._extensionClient.getHydratedTask(t)}async refreshTasks(){const t=mt(this.rootTaskUuid);if(t){const e=await this._extensionClient.getHydratedTask(t);this._rootTask.set(e)}else this._rootTask.set(void 0)}async syncTaskListWithConversation(t){await this._updateTaskList(t,"Update the task list to reflect the current state of the conversation. Add any new tasks that have been created, update the status of existing tasks, and remove any tasks that are no longer relevant. The updated task list should reflect the current state of the conversation. If the tasks are not detailed enough, please add new tasks to fill in the steps you think are necessary, provide more details by adding more information to the description, or add sub-tasks",["You should add new tasks if any tasks are missing.","You should update the details of tasks if their details are outdated.","You should update the status of tasks if their status is outdated.","You should remove any tasks that are no longer relevant by not including them in the task list."])}async updateTaskListStatuses(t){await this._updateTaskList(t,"Update the status of each task in the list to reflect the current state of the conversation.",["You may update the status of tasks if necessary.","Do not add any new tasks.","Do not remove any tasks.","Do not update the details of any tasks."])}async _maybeInitializeConversationRootTask(){const t=mt(this.rootTaskUuid);if(t)return t;const e=mt(this._conversationModel),s=e.id,i=`Conversation: ${e.name||"New Chat"}`,o=`Root task for conversation ${s}`,r=await this._extensionClient.createTask(i,o);return this._conversationModel.rootTaskUuid=r,this._extensionClient.setCurrentRootTaskUuid(r),r}async _updateTaskList(t,e="",s=[]){if(!mt(this._isEnhancing))try{this._isEnhancing.set(!0);const i=mt(this._rootTask);if(!i)return;const o=ar(i,t);if(!o)return;const r=Gs(o),a=e+`
Follow these rules when updating the task list:
`+(s==null?void 0:s.join(`
`))+`
Maintain the hierarchical structure, given by the \`Example task list structure\`, with proper indentation. If a task is new, give it a UUID of "NEW_UUID". Always structure each task with [ ] for not started, [/] for in progress, [x] for completed, and [-] for cancelled. 
Example task list structure: 
`+nl+`

Current working task list - This is ACTUAL working task list to use, read from, and modify:
`+r+`

Only output the updated markdown without any additional explanation. Do not include any sentences before or after the markdown, ONLY the markdown itself. Do not use a tool call, just return the markdown in plaintext, without tools, or anything else. Just plaintext markdown.`,{responseText:l}=await this._conversationModel.sendSilentExchange({request_message:a,disableSelectedCodeDetails:!0});console.log("Updating task list for conversation",mt(this._conversationModel).id),console.log({instructions:a,currentTaskListMarkdown:r,enhancedTaskList:l});const c=Ki(l);c.uuid=o.uuid;const{created:d,updated:p,deleted:f}=await this._extensionClient.updateHydratedTask(c,xt.AGENT);console.log("Task tree update results:",{created:d,updated:p,deleted:f})}finally{this._isEnhancing.set(!1),await this.refreshTasks()}}getParentTask(t){const e=mt(this._backlinks)[t];if(e)return mt(this.uuidToTask).get(e)}async addNewTaskAfter(t,e){const s=this.getParentTask(t);if(!s)return;const i=s.subTasks.indexOf(t);if(i===-1)return;const o=await this.cloneHydratedTask(e);return o?(s.subTasks.splice(i+1,0,o.uuid),await this.saveHydratedTask(s),o):void 0}async deleteTask(t){var i;const e=mt(this._backlinks)[t];if(!e)return;const s=await this.getHydratedTask(e);s&&(s.subTasks=s.subTasks.filter(o=>o!==t),s.subTasksData=(i=s.subTasksData)==null?void 0:i.filter(o=>o.uuid!==t),await this.updateTask(s.uuid,{subTasks:s.subTasks},xt.USER))}async cleanupEmptyAndCancelledTasks(){const t=mt(this.rootTask);if(!t)return;const e=function(s){const i=[],o=[s];for(;o.length>0;){const r=o.pop();i.push(r),r.subTasksData&&o.push(...r.subTasksData)}return i}(t).filter(s=>s.uuid!==t.uuid).filter(s=>{if(s.state===B.CANCELLED)return!0;const i=!s.name||s.name.trim()==="",o=!s.description||s.description.trim()==="";return i&&o});for(const s of e.reverse())await this.deleteTask(s.uuid);await this.refreshTasks()}async saveHydratedTask(t){await this._extensionClient.updateHydratedTask(t,xt.USER),await this.refreshTasks()}async cloneHydratedTask(t){const e=hi(t),s=await this.createTask(e.name,e.description);if(s)return e.uuid=s,await this._extensionClient.updateHydratedTask(e,xt.USER),await this.getHydratedTask(s)}async runAllTasks(){const t=mt(this._rootTask);t&&this.runHydratedTask(t,{message:"Run all tasks for the task list: "})}async runHydratedTask(t,e){const s=this._chatModel.currentConversationId;if(await this._agentConversationModel.interruptAgent(),s!==this._chatModel.currentConversationId)return;if(e!=null&&e.newConversation){const o=await this.cloneHydratedTask(t);if(!o)return;const r=await this.createTask(t.name,t.description);if(await this.saveHydratedTask({uuid:r,name:t.name,description:t.description,state:B.NOT_STARTED,subTasks:[o.uuid],subTasksData:[o],lastUpdated:Date.now(),lastUpdatedBy:xt.USER}),s!==this._chatModel.currentConversationId)return;await this._chatModel.setCurrentConversation(void 0,!0,{newTaskUuid:r})}const i={type:"doc",content:[{type:"paragraph",content:[{type:"text",text:(e==null?void 0:e.message)??"Please shift focus to work on task: "},{type:"mention",attrs:{id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name,data:{...t,id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name}}}]}]};await this._chatModel.currentConversationModel.sendExchange({request_message:"",rich_text_json_repr:i,structured_request_nodes:this._chatModel.currentConversationModel.createStructuredRequestNodes(i),status:Rt.draft,model_id:this._chatModel.currentConversationModel.selectedModelId??void 0}),await this.refreshTasks()}_startRefreshInterval(){this._refreshInterval=setInterval(async()=>{await this.refreshTasks()},Ys.REFRESH_INTERVAL_MS)}setEnhancing(t){this._isEnhancing.set(t)}_stopRefreshInterval(){this._refreshInterval&&(clearInterval(this._refreshInterval),this._refreshInterval=void 0)}async exportTask(t,e){try{this._isImportingExporting.set(!0);const s=(e==null?void 0:e.format)||"markdown";let i,o,r;if(s!=="markdown")throw new Error(`Unsupported export format: ${s}`);i=Gs(t,e==null?void 0:e.shallow),o="md",(e==null?void 0:e.fileName)?r=e.fileName:r=`${((e==null?void 0:e.baseName)||t.name||"Task").replace(/[/:*?"<>|\s]/g,"_")}_${new Date().toISOString().replace(/[:.]/g,"-").slice(0,19)}.${o}`,this._extensionClient.createFile(i,r)}catch(s){console.error("Error exporting task:",s)}finally{this._isImportingExporting.set(!1)}}async exportTasksToMarkdown(){const t=mt(this._rootTask);if(!t)return;const e=mt(this._conversationModel).name||"Tasks";await this.exportTask(t,{baseName:e,format:"markdown"})}async importTasksFromMarkdown(){try{this._isImportingExporting.set(!0);const t=document.createElement("input");t.type="file",t.accept=".md,text/markdown",t.style.display="none";const e=new Promise(i=>{t.onchange=()=>{i(t.files?t.files[0]:null)},t.oncancel=()=>{i(null)}});document.body.appendChild(t),t.click();const s=await e;if(document.body.removeChild(t),s){const i=await s.text();await this._processImportedFileContent(i)}}catch(t){console.error("Error importing tasks from markdown:",t)}finally{this._isImportingExporting.set(!1)}}async _processImportedFileContent(t){try{if(!t.trim())return void console.error("Empty file content");const e=Ki(t);if(!e)return void console.error("Failed to parse task tree from markdown");const s=mt(this._rootTask);if(!s)return void console.error("No root task found");const i=(e.subTasksData||[]).map(d=>hi(d,{keepUuid:!1})),o=s.subTasksData||[],r=s.subTasks||[],a=[...o,...i],l=[...r,...i.map(d=>d.uuid)],c={...s,subTasks:l,subTasksData:a};await this._extensionClient.updateHydratedTask(c,xt.USER),await this.refreshTasks()}catch(e){console.error("Error processing imported file content:",e)}}};h(Ys,"key","currentConversationTaskStore"),h(Ys,"REFRESH_INTERVAL_MS",2e3);let pi=Ys;function il(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class Kd extends st{constructor(t){super(),nt(this,t,null,il,it,{})}}function ol(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function rl(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class Jd extends st{constructor(t){super(),nt(this,t,rl,ol,it,{})}}function al(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class tu extends st{constructor(t){super(),nt(this,t,null,al,it,{})}}function io(n){return n.replace(/\.git$/,"")}function eu(n){if(!n)return"";const t=n.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return t[2].replace(".git","");const e=n.split("/").filter(Boolean);return(e.length>0?e[e.length-1]:"").replace(".git","")}function su(n){if(!n)return"";const t=n.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return`${t[1]}/${t[2].replace(".git","")}`;const e=n.split("/").filter(Boolean);return e.length>1?`${e[e.length-2]}/${e[e.length-1].replace(".git","")}`:e.length>0?e[e.length-1].replace(".git",""):""}function nu(n){return vn(n,t=>e=>{if(!t)return!0;const s=io(t),i=io(function(o){var r,a,l;return((l=(a=(r=o.workspace_setup)==null?void 0:r.starting_files)==null?void 0:a.github_commit_ref)==null?void 0:l.repository_url)||""}(e));return!!i&&!!s&&i!==s})}function ll(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 128c0 53-43 96-96 96-28.9 0-54.8-12.8-72.4-33l-89.7 44.9c1.4 6.5 2.1 13.2 2.1 20.1s-.7 13.6-2.1 20.1l89.7 44.9c17.6-20.2 43.5-33 72.4-33 53 0 96 43 96 96s-43 96-96 96-96-43-96-96c0-6.9.7-13.6 2.1-20.1L168.4 319c-17.6 20.2-43.5 33-72.4 33-53 0-96-43-96-96s43-96 96-96c28.9 0 54.8 12.8 72.4 33l89.7-44.9c-1.4-6.5-2.1-13.2-2.1-20.1 0-53 43-96 96-96s96 43 96 96M96 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96m304-176a48 48 0 1 0-96 0 48 48 0 1 0 96 0m-48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function cl(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class iu extends st{constructor(t){super(),nt(this,t,cl,ll,it,{})}}function dl(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M1.43555 8.19985C1.43555 4.29832 4.59837 1.1355 8.4999 1.1355C12.4014 1.1355 15.5642 4.29832 15.5642 8.19985C15.5642 12.1013 12.4014 15.2642 8.4999 15.2642C4.59837 15.2642 1.43555 12.1013 1.43555 8.19985ZM8.4999 2.14883C5.15802 2.14883 2.44889 4.85797 2.44889 8.19985C2.44889 11.5417 5.15802 14.2509 8.4999 14.2509C11.8418 14.2509 14.5509 11.5417 14.5509 8.19985C14.5509 4.85797 11.8418 2.14883 8.4999 2.14883ZM11.0105 5.68952C11.2187 5.8978 11.2187 6.23549 11.0105 6.44377L9.25427 8.19997L11.0105 9.95619C11.2187 10.1645 11.2187 10.5022 11.0105 10.7104C10.8022 10.9187 10.4645 10.9187 10.2562 10.7104L8.50002 8.95422L6.74382 10.7104C6.53554 10.9187 6.19784 10.9187 5.98957 10.7104C5.78129 10.5022 5.78129 10.1645 5.98957 9.95619L7.74578 8.19997L5.98957 6.44377C5.78129 6.23549 5.78129 5.8978 5.98957 5.68952C6.19784 5.48124 6.53554 5.48124 6.74382 5.68952L8.50002 7.44573L10.2562 5.68952C10.4645 5.48124 10.8022 5.48124 11.0105 5.68952Z"),y(e,"fill","currentColor"),y(t,"width","17"),y(t,"height","17"),y(t,"viewBox","0 0 17 17"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class ul extends st{constructor(t){super(),nt(this,t,null,dl,it,{})}}function hl(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M7.49991 0.877075C3.84222 0.877075 0.877075 3.84222 0.877075 7.49991C0.877075 11.1576 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1576 14.1227 7.49991C14.1227 3.84222 11.1576 0.877075 7.49991 0.877075ZM3.85768 3.15057C4.84311 2.32448 6.11342 1.82708 7.49991 1.82708C10.6329 1.82708 13.1727 4.36689 13.1727 7.49991C13.1727 8.88638 12.6753 10.1567 11.8492 11.1421L3.85768 3.15057ZM3.15057 3.85768C2.32448 4.84311 1.82708 6.11342 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C8.88638 13.1727 10.1567 12.6753 11.1421 11.8492L3.15057 3.85768Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class pl extends st{constructor(t){super(),nt(this,t,null,hl,it,{})}}function fl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m113-303c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0z"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function gl(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class hr extends st{constructor(t){super(),nt(this,t,gl,fl,it,{})}}function ml(n){let t,e;return t=new pl({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function vl(n){let t,e;return t=new ul({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function bl(n){let t,e;return t=new hr({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function _l(n){let t,e;return t=new Ho({props:{size:1}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function wl(n){let t,e,s,i,o;const r=[_l,bl,vl,ml],a=[];function l(c,d){return c[0]==="loading"?0:c[0]==="success"?1:c[0]==="error"?2:c[0]==="skipped"?3:-1}return~(e=l(n))&&(s=a[e]=r[e](n)),{c(){t=pt("div"),s&&s.c(),y(t,"class",i="c-setup-script-command-status c-setup-script-command-status--"+n[0]+" svelte-1azgu93")},m(c,d){R(c,t,d),~e&&a[e].m(t,null),o=!0},p(c,[d]){let p=e;e=l(c),e!==p&&(s&&(It(),$(a[p],1,1,()=>{a[p]=null}),Mt()),~e?(s=a[e],s||(s=a[e]=r[e](c),s.c()),k(s,1),s.m(t,null)):s=null),(!o||1&d&&i!==(i="c-setup-script-command-status c-setup-script-command-status--"+c[0]+" svelte-1azgu93"))&&y(t,"class",i)},i(c){o||(k(s),o=!0)},o(c){$(s),o=!1},d(c){c&&E(t),~e&&a[e].d()}}}function yl(n,t,e){let{commandResult:s}=t;return n.$$set=i=>{"commandResult"in i&&e(0,s=i.commandResult)},[s]}class ou extends st{constructor(t){super(),nt(this,t,yl,wl,it,{commandResult:0})}}function oo(n){let t,e;return t=new Bo({props:{class:"edit-item__added-lines",size:1,$$slots:{default:[kl]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};5&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function kl(n){let t,e;return{c(){t=ce("+"),e=ce(n[0])},m(s,i){R(s,t,i),R(s,e,i)},p(s,i){1&i&&wi(e,s[0])},d(s){s&&(E(t),E(e))}}}function ro(n){let t,e;return t=new Bo({props:{class:"edit-item__removed-lines",size:1,$$slots:{default:[xl]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};6&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function xl(n){let t,e;return{c(){t=ce("-"),e=ce(n[1])},m(s,i){R(s,t,i),R(s,e,i)},p(s,i){2&i&&wi(e,s[1])},d(s){s&&(E(t),E(e))}}}function $l(n){let t,e,s,i=n[0]>0&&oo(n),o=n[1]>0&&ro(n);return{c(){t=pt("div"),i&&i.c(),e=Ft(),o&&o.c(),y(t,"class","edit-item__changes svelte-1k8sltp")},m(r,a){R(r,t,a),i&&i.m(t,null),ht(t,e),o&&o.m(t,null),s=!0},p(r,[a]){r[0]>0?i?(i.p(r,a),1&a&&k(i,1)):(i=oo(r),i.c(),k(i,1),i.m(t,e)):i&&(It(),$(i,1,1,()=>{i=null}),Mt()),r[1]>0?o?(o.p(r,a),2&a&&k(o,1)):(o=ro(r),o.c(),k(o,1),o.m(t,null)):o&&(It(),$(o,1,1,()=>{o=null}),Mt())},i(r){s||(k(i),k(o),s=!0)},o(r){$(i),$(o),s=!1},d(r){r&&E(t),i&&i.d(),o&&o.d()}}}function Tl(n,t,e){let{totalAddedLines:s=0}=t,{totalRemovedLines:i=0}=t;return n.$$set=o=>{"totalAddedLines"in o&&e(0,s=o.totalAddedLines),"totalRemovedLines"in o&&e(1,i=o.totalRemovedLines)},[s,i]}class ru extends st{constructor(t){super(),nt(this,t,Tl,$l,it,{totalAddedLines:0,totalRemovedLines:1})}}function Sl(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M3.5 2.82672C3.5 2.55058 3.72386 2.32672 4 2.32672H9.79289L12.5 5.03383V12.8267C12.5 13.1028 12.2761 13.3267 12 13.3267H4C3.72386 13.3267 3.5 13.1028 3.5 12.8267V2.82672ZM4 1.32672C3.17157 1.32672 2.5 1.99829 2.5 2.82672V12.8267C2.5 13.6551 3.17157 14.3267 4 14.3267H12C12.8284 14.3267 13.5 13.6551 13.5 12.8267V4.93027C13.5 4.73136 13.421 4.5406 13.2803 4.39994L10.3535 1.47317C10.2598 1.3794 10.1326 1.32672 10 1.32672H4ZM10.25 6.6595C10.5261 6.6595 10.75 6.43564 10.75 6.1595C10.75 5.88336 10.5261 5.6595 10.25 5.6595H8.49996L8.49996 3.9095C8.49996 3.6334 8.2761 3.4095 7.99996 3.4095C7.72382 3.4095 7.49996 3.6334 7.49996 3.9095V5.6595H5.74996C5.47386 5.6595 5.24996 5.88336 5.24996 6.1595C5.24996 6.43564 5.47386 6.6595 5.74996 6.6595L7.49996 6.6595V8.4095C7.49996 8.68564 7.72382 8.9095 7.99996 8.9095C8.2761 8.9095 8.49996 8.68564 8.49996 8.4095V6.6595H10.25ZM10.4999 11.4188C10.4999 11.695 10.2761 11.9188 9.99993 11.9188H5.99993C5.72379 11.9188 5.49993 11.695 5.49993 11.4188C5.49993 11.1427 5.72379 10.9188 5.99993 10.9188H9.99993C10.2761 10.9188 10.4999 11.1427 10.4999 11.4188Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class au extends st{constructor(t){super(),nt(this,t,null,Sl,it,{})}}function Cl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M153.8 72.1c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 79c-9.4-9.3-24.6-9.3-34 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zm0 160c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zM216 120h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24m-24 136c0 13.3 10.7 24 24 24h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24m-32 160c0 13.3 10.7 24 24 24h304c13.3 0 24-10.7 24-24s-10.7-24-24-24H184c-13.3 0-24 10.7-24 24m-64 0a32 32 0 1 0-64 0 32 32 0 1 0 64 0"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function El(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class lu extends st{constructor(t){super(),nt(this,t,El,Cl,it,{})}}function Il(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 128 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M64 368a48 48 0 1 0 0 96 48 48 0 1 0 0-96m0-160a48 48 0 1 0 0 96 48 48 0 1 0 0-96m48-112a48 48 0 1 0-96 0 48 48 0 1 0 96 0"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 128 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function Ml(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class Al extends st{constructor(t){super(),nt(this,t,Ml,Il,it,{})}}function Dl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M336 448c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h160v80c0 17.7 14.3 32 32 32h80v96h48V154.5c0-17-6.7-33.3-18.7-45.3l-90.6-90.5C262.7 6.7 246.5 0 229.5 0H64C28.7 0 0 28.7 0 64v384c0 35.3 28.7 64 64 64h256c35.3 0 64-28.7 64-64v-80h-48zm153-233c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l39 39-278.1.1c-13.3 0-24 10.7-24 24s10.7 24 24 24h278.1l-39 39c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9z"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function Rl(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class pr extends st{constructor(t){super(),nt(this,t,Rl,Dl,it,{})}}function Fl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256a208 208 0 1 0-416 0 208 208 0 1 0 416 0M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function Ol(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class Nl extends st{constructor(t){super(),nt(this,t,Ol,Fl,it,{})}}function Ll(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256c0-114.9-93.1-208-208-208v416c114.9 0 208-93.1 208-208M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function Pl(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class Ul extends st{constructor(t){super(),nt(this,t,Pl,Ll,it,{})}}function fi(n){switch(n){case B.IN_PROGRESS:return"info";case B.COMPLETE:return"success";case B.CANCELLED:return"error";case B.NOT_STARTED:default:return"neutral"}}function ao(n){switch(n){case B.IN_PROGRESS:return"In Progress";case B.COMPLETE:return"Completed";case B.CANCELLED:return"Cancelled";case B.NOT_STARTED:default:return"Not Started"}}function Ls(n){switch(n){case B.IN_PROGRESS:return Ul;case B.COMPLETE:return hr;case B.CANCELLED:return Vr;case B.NOT_STARTED:default:return Nl}}function zl(n){let t,e,s;var i=n[2];function o(r,a){return{props:{width:r[1],height:r[3]}}}return i&&(e=Ze(i,o(n))),{c(){t=pt("span"),e&&F(e.$$.fragment),y(t,"class","c-task-icon svelte-1dmbt8o"),rn(t,"--icon-color","var(--ds-color-"+fi(n[0])+"-9)"),rn(t,"--icon-size",n[1])},m(r,a){R(r,t,a),e&&O(e,t,null),s=!0},p(r,[a]){if(4&a&&i!==(i=r[2])){if(e){It();const l=e;$(l.$$.fragment,1,0,()=>{N(l,1)}),Mt()}i?(e=Ze(i,o(r)),F(e.$$.fragment),k(e.$$.fragment,1),O(e,t,null)):e=null}else if(i){const l={};2&a&&(l.width=r[1]),8&a&&(l.height=r[3]),e.$set(l)}(!s||1&a)&&rn(t,"--icon-color","var(--ds-color-"+fi(r[0])+"-9)"),(!s||2&a)&&rn(t,"--icon-size",r[1])},i(r){s||(e&&k(e.$$.fragment,r),s=!0)},o(r){e&&$(e.$$.fragment,r),s=!1},d(r){r&&E(t),e&&N(e)}}}function ql(n,t,e){let s,i,o,{taskState:r}=t,{size:a=1}=t;return n.$$set=l=>{"taskState"in l&&e(0,r=l.taskState),"size"in l&&e(4,a=l.size)},n.$$.update=()=>{16&n.$$.dirty&&e(1,s={1:"14px",2:"16px",3:"18px",4:"20px"}[a]),2&n.$$.dirty&&e(3,i=s),1&n.$$.dirty&&e(2,o=Ls(r))},[r,s,o,i,a]}class Hl extends st{constructor(t){super(),nt(this,t,ql,zl,it,{taskState:0,size:4})}}const Bl=n=>({item:16&n}),lo=n=>({item:n[4]}),Gl=n=>({item:16&n}),co=n=>({item:n[4]}),jl=n=>({item:16&n}),uo=n=>({item:n[4]}),Wl=n=>({item:16&n}),ho=n=>({item:n[4]});function po(n){let t,e;return t=new Js({props:{class:"c-draggable-list-item__expand-collapse-button",size:1,variant:"ghost",color:"neutral","aria-expanded":n[0],"aria-label":n[0]?"Collapse":"Expand",$$slots:{default:[Xl]},$$scope:{ctx:n}}}),t.$on("click",n[9]),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};1&i&&(o["aria-expanded"]=s[0]),1&i&&(o["aria-label"]=s[0]?"Collapse":"Expand"),2097153&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Vl(n){let t,e;return t=new ia({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Yl(n){let t,e;return t=new Yr({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Xl(n){let t,e,s,i;const o=[Yl,Vl],r=[];function a(l,c){return l[0]?0:1}return t=a(n),e=r[t]=o[t](n),{c(){e.c(),s=Yt()},m(l,c){r[t].m(l,c),R(l,s,c),i=!0},p(l,c){let d=t;t=a(l),t!==d&&(It(),$(r[d],1,1,()=>{r[d]=null}),Mt(),e=r[t],e||(e=r[t]=o[t](l),e.c()),k(e,1),e.m(s.parentNode,s))},i(l){i||(k(e),i=!0)},o(l){$(e),i=!1},d(l){l&&E(s),r[t].d(l)}}}function Zl(n){let t,e,s,i,o,r,a,l,c,d,p,f,g,b;const m=n[10].handle,_=Ms(m,n,n[21],ho);let v=n[6]&&po(n);const x=n[10]["header-contents"],A=Ms(x,n,n[21],uo),L=n[10].actions,w=Ms(L,n,n[21],co),K=n[10].contents,V=Ms(K,n,n[21],lo);return{c(){t=pt("div"),e=pt("div"),s=pt("div"),_&&_.c(),i=Ft(),v&&v.c(),o=Ft(),r=pt("div"),A&&A.c(),a=Ft(),l=pt("div"),w&&w.c(),c=Ft(),d=pt("div"),V&&V.c(),y(s,"class","c-draggable-list-item__handle svelte-3e5xer"),y(r,"class","c-draggable-list-item__main svelte-3e5xer"),y(l,"class","c-draggable-list-item__actions"),y(e,"class","c-draggable-list-item__content svelte-3e5xer"),y(d,"class","c-draggable-list-item__contents"),ae(d,"c-draggable-list-item__show-connectors",n[8]),y(t,"class",p="c-draggable-list-item "+n[2]+" svelte-3e5xer"),y(t,"id",n[3]),y(t,"data-item-id",n[3]),y(t,"data-testid","draggable-list-item"),y(t,"tabindex","0"),y(t,"role","button"),ae(t,"is-disabled",n[5]),ae(t,"has-nested-items",n[6]),ae(t,"is-expanded",n[0]),ae(t,"is-selected",n[7])},m(M,S){R(M,t,S),ht(t,e),ht(e,s),_&&_.m(s,null),ht(e,i),v&&v.m(e,null),ht(e,o),ht(e,r),A&&A.m(r,null),ht(e,a),ht(e,l),w&&w.m(l,null),ht(t,c),ht(t,d),V&&V.m(d,null),n[20](t),f=!0,g||(b=[Oe(t,"mousedown",n[11]),Oe(t,"click",n[12]),Oe(t,"keydown",n[13]),Oe(t,"keyup",n[14]),Oe(t,"keypress",n[15]),Oe(t,"focus",n[16]),Oe(t,"blur",n[17]),Oe(t,"focusin",n[18]),Oe(t,"focusout",n[19])],g=!0)},p(M,[S]){_&&_.p&&(!f||2097168&S)&&As(_,m,M,M[21],f?Rs(m,M[21],S,Wl):Ds(M[21]),ho),M[6]?v?(v.p(M,S),64&S&&k(v,1)):(v=po(M),v.c(),k(v,1),v.m(e,o)):v&&(It(),$(v,1,1,()=>{v=null}),Mt()),A&&A.p&&(!f||2097168&S)&&As(A,x,M,M[21],f?Rs(x,M[21],S,jl):Ds(M[21]),uo),w&&w.p&&(!f||2097168&S)&&As(w,L,M,M[21],f?Rs(L,M[21],S,Gl):Ds(M[21]),co),V&&V.p&&(!f||2097168&S)&&As(V,K,M,M[21],f?Rs(K,M[21],S,Bl):Ds(M[21]),lo),(!f||256&S)&&ae(d,"c-draggable-list-item__show-connectors",M[8]),(!f||4&S&&p!==(p="c-draggable-list-item "+M[2]+" svelte-3e5xer"))&&y(t,"class",p),(!f||8&S)&&y(t,"id",M[3]),(!f||8&S)&&y(t,"data-item-id",M[3]),(!f||36&S)&&ae(t,"is-disabled",M[5]),(!f||68&S)&&ae(t,"has-nested-items",M[6]),(!f||5&S)&&ae(t,"is-expanded",M[0]),(!f||132&S)&&ae(t,"is-selected",M[7])},i(M){f||(k(_,M),k(v),k(A,M),k(w,M),k(V,M),f=!0)},o(M){$(_,M),$(v),$(A,M),$(w,M),$(V,M),f=!1},d(M){M&&E(t),_&&_.d(M),v&&v.d(),A&&A.d(M),w&&w.d(M),V&&V.d(M),n[20](null),g=!1,Ar(b)}}}function Ql(n,t,e){let{$$slots:s={},$$scope:i}=t,{class:o=""}=t,{id:r}=t,{item:a}=t,{disabled:l=!1}=t,{hasNestedItems:c=!1}=t,{expanded:d=!0}=t,{selected:p=!1}=t,{showConnectors:f=!0}=t,{element:g}=t;return n.$$set=b=>{"class"in b&&e(2,o=b.class),"id"in b&&e(3,r=b.id),"item"in b&&e(4,a=b.item),"disabled"in b&&e(5,l=b.disabled),"hasNestedItems"in b&&e(6,c=b.hasNestedItems),"expanded"in b&&e(0,d=b.expanded),"selected"in b&&e(7,p=b.selected),"showConnectors"in b&&e(8,f=b.showConnectors),"element"in b&&e(1,g=b.element),"$$scope"in b&&e(21,i=b.$$scope)},[d,g,o,r,a,l,c,p,f,function(){e(0,d=!d)},s,function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){vt.call(this,n,b)},function(b){Fe[b?"unshift":"push"](()=>{g=b,e(1,g)})},i]}class Kl extends st{constructor(t){super(),nt(this,t,Ql,Zl,it,{class:2,id:3,item:4,disabled:5,hasNestedItems:6,expanded:0,selected:7,showConnectors:8,element:1})}}function Jl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M64 128a32 32 0 1 0 0-64 32 32 0 1 0 0 64m0 160a32 32 0 1 0 0-64 32 32 0 1 0 0 64m32 128a32 32 0 1 0-64 0 32 32 0 1 0 64 0m96-288a32 32 0 1 0 0-64 32 32 0 1 0 0 64m32 128a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-32 192a32 32 0 1 0 0-64 32 32 0 1 0 0 64"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function tc(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class ec extends st{constructor(t){super(),nt(this,t,tc,Jl,it,{})}}function fo(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);t&&(s=s.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,s)}return e}function De(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?fo(Object(e),!0).forEach(function(s){sc(n,s,e[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):fo(Object(e)).forEach(function(s){Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(e,s))})}return n}function gi(n){return gi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gi(n)}function sc(n,t,e){return t in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Le(){return Le=Object.assign||function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(n[s]=e[s])}return n},Le.apply(this,arguments)}function nc(n,t){if(n==null)return{};var e,s,i=function(r,a){if(r==null)return{};var l,c,d={},p=Object.keys(r);for(c=0;c<p.length;c++)l=p[c],a.indexOf(l)>=0||(d[l]=r[l]);return d}(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(s=0;s<o.length;s++)e=o[s],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function Pe(n){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(n)}var Ue=Pe(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),sn=Pe(/Edge/i),go=Pe(/firefox/i),js=Pe(/safari/i)&&!Pe(/chrome/i)&&!Pe(/android/i),Ii=Pe(/iP(ad|od|hone)/i),fr=Pe(/chrome/i)&&Pe(/android/i),gr={capture:!1,passive:!1};function Z(n,t,e){n.addEventListener(t,e,!Ue&&gr)}function Y(n,t,e){n.removeEventListener(t,e,!Ue&&gr)}function Mn(n,t){if(t){if(t[0]===">"&&(t=t.substring(1)),n)try{if(n.matches)return n.matches(t);if(n.msMatchesSelector)return n.msMatchesSelector(t);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(t)}catch{return!1}return!1}}function mr(n){return n.host&&n!==document&&n.host.nodeType?n.host:n.parentNode}function pe(n,t,e,s){if(n){e=e||document;do{if(t!=null&&(t[0]===">"?n.parentNode===e&&Mn(n,t):Mn(n,t))||s&&n===e)return n;if(n===e)break}while(n=mr(n))}return null}var Ws,mo=/\s+/g;function Xt(n,t,e){if(n&&t)if(n.classList)n.classList[e?"add":"remove"](t);else{var s=(" "+n.className+" ").replace(mo," ").replace(" "+t+" "," ");n.className=(s+(e?" "+t:"")).replace(mo," ")}}function H(n,t,e){var s=n&&n.style;if(s){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(n,""):n.currentStyle&&(e=n.currentStyle),t===void 0?e:e[t];t in s||t.indexOf("webkit")!==-1||(t="-webkit-"+t),s[t]=e+(typeof e=="string"?"":"px")}}function fs(n,t){var e="";if(typeof n=="string")e=n;else do{var s=H(n,"transform");s&&s!=="none"&&(e=s+" "+e)}while(!t&&(n=n.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(e)}function vo(n,t,e){if(n){var s=n.getElementsByTagName(t),i=0,o=s.length;if(e)for(;i<o;i++)e(s[i],i);return s}return[]}function Re(){var n=document.scrollingElement;return n||document.documentElement}function kt(n,t,e,s,i){if(n.getBoundingClientRect||n===window){var o,r,a,l,c,d,p;if(n!==window&&n.parentNode&&n!==Re()?(r=(o=n.getBoundingClientRect()).top,a=o.left,l=o.bottom,c=o.right,d=o.height,p=o.width):(r=0,a=0,l=window.innerHeight,c=window.innerWidth,d=window.innerHeight,p=window.innerWidth),(t||e)&&n!==window&&(i=i||n.parentNode,!Ue))do if(i&&i.getBoundingClientRect&&(H(i,"transform")!=="none"||e&&H(i,"position")!=="static")){var f=i.getBoundingClientRect();r-=f.top+parseInt(H(i,"border-top-width")),a-=f.left+parseInt(H(i,"border-left-width")),l=r+o.height,c=a+o.width;break}while(i=i.parentNode);if(s&&n!==window){var g=fs(i||n),b=g&&g.a,m=g&&g.d;g&&(l=(r/=m)+(d/=m),c=(a/=b)+(p/=b))}return{top:r,left:a,bottom:l,right:c,width:p,height:d}}}function bo(n,t,e){for(var s=Xe(n,!0),i=kt(n)[t];s;){if(!(i>=kt(s)[e]))return s;if(s===Re())break;s=Xe(s,!1)}return!1}function gs(n,t,e,s){for(var i=0,o=0,r=n.children;o<r.length;){if(r[o].style.display!=="none"&&r[o]!==q.ghost&&(s||r[o]!==q.dragged)&&pe(r[o],e.draggable,n,!1)){if(i===t)return r[o];i++}o++}return null}function mi(n,t){for(var e=n.lastElementChild;e&&(e===q.ghost||H(e,"display")==="none"||t&&!Mn(e,t));)e=e.previousElementSibling;return e||null}function re(n,t){var e=0;if(!n||!n.parentNode)return-1;for(;n=n.previousElementSibling;)n.nodeName.toUpperCase()==="TEMPLATE"||n===q.clone||t&&!Mn(n,t)||e++;return e}function _o(n){var t=0,e=0,s=Re();if(n)do{var i=fs(n),o=i.a,r=i.d;t+=n.scrollLeft*o,e+=n.scrollTop*r}while(n!==s&&(n=n.parentNode));return[t,e]}function Xe(n,t){if(!n||!n.getBoundingClientRect)return Re();var e=n,s=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var i=H(e);if(e.clientWidth<e.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return Re();if(s||t)return e;s=!0}}while(e=e.parentNode);return Re()}function Gn(n,t){return Math.round(n.top)===Math.round(t.top)&&Math.round(n.left)===Math.round(t.left)&&Math.round(n.height)===Math.round(t.height)&&Math.round(n.width)===Math.round(t.width)}function vr(n,t){return function(){if(!Ws){var e=arguments;e.length===1?n.call(this,e[0]):n.apply(this,e),Ws=setTimeout(function(){Ws=void 0},t)}}}function br(n,t,e){n.scrollLeft+=t,n.scrollTop+=e}function wo(n){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(n).cloneNode(!0):e?e(n).clone(!0)[0]:n.cloneNode(!0)}function yo(n,t,e){var s={};return Array.from(n.children).forEach(function(i){var o,r,a,l;if(pe(i,t.draggable,n,!1)&&!i.animated&&i!==e){var c=kt(i);s.left=Math.min((o=s.left)!==null&&o!==void 0?o:1/0,c.left),s.top=Math.min((r=s.top)!==null&&r!==void 0?r:1/0,c.top),s.right=Math.max((a=s.right)!==null&&a!==void 0?a:-1/0,c.right),s.bottom=Math.max((l=s.bottom)!==null&&l!==void 0?l:-1/0,c.bottom)}}),s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}var Wt="Sortable"+new Date().getTime();function ic(){var n,t=[];return{captureAnimationState:function(){t=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(e){if(H(e,"display")!=="none"&&e!==q.ghost){t.push({target:e,rect:kt(e)});var s=De({},t[t.length-1].rect);if(e.thisAnimationDuration){var i=fs(e,!0);i&&(s.top-=i.f,s.left-=i.e)}e.fromRect=s}})},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(s,i){for(var o in s)if(s.hasOwnProperty(o)){for(var r in i)if(i.hasOwnProperty(r)&&i[r]===s[o][r])return Number(o)}return-1}(t,{target:e}),1)},animateAll:function(e){var s=this;if(!this.options.animation)return clearTimeout(n),void(typeof e=="function"&&e());var i=!1,o=0;t.forEach(function(r){var a=0,l=r.target,c=l.fromRect,d=kt(l),p=l.prevFromRect,f=l.prevToRect,g=r.rect,b=fs(l,!0);b&&(d.top-=b.f,d.left-=b.e),l.toRect=d,l.thisAnimationDuration&&Gn(p,d)&&!Gn(c,d)&&(g.top-d.top)/(g.left-d.left)==(c.top-d.top)/(c.left-d.left)&&(a=function(m,_,v,x){return Math.sqrt(Math.pow(_.top-m.top,2)+Math.pow(_.left-m.left,2))/Math.sqrt(Math.pow(_.top-v.top,2)+Math.pow(_.left-v.left,2))*x.animation}(g,p,f,s.options)),Gn(d,c)||(l.prevFromRect=c,l.prevToRect=d,a||(a=s.options.animation),s.animate(l,g,d,a)),a&&(i=!0,o=Math.max(o,a),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout(function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null},a),l.thisAnimationDuration=a)}),clearTimeout(n),i?n=setTimeout(function(){typeof e=="function"&&e()},o):typeof e=="function"&&e(),t=[]},animate:function(e,s,i,o){if(o){H(e,"transition",""),H(e,"transform","");var r=fs(this.el),a=r&&r.a,l=r&&r.d,c=(s.left-i.left)/(a||1),d=(s.top-i.top)/(l||1);e.animatingX=!!c,e.animatingY=!!d,H(e,"transform","translate3d("+c+"px,"+d+"px,0)"),this.forRepaintDummy=function(p){return p.offsetWidth}(e),H(e,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),H(e,"transform","translate3d(0,0,0)"),typeof e.animated=="number"&&clearTimeout(e.animated),e.animated=setTimeout(function(){H(e,"transition",""),H(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1},o)}}}}var ls=[],jn={initializeByDefault:!0},Zs={mount:function(n){for(var t in jn)jn.hasOwnProperty(t)&&!(t in n)&&(n[t]=jn[t]);ls.forEach(function(e){if(e.pluginName===n.pluginName)throw"Sortable: Cannot mount plugin ".concat(n.pluginName," more than once")}),ls.push(n)},pluginEvent:function(n,t,e){var s=this;this.eventCanceled=!1,e.cancel=function(){s.eventCanceled=!0};var i=n+"Global";ls.forEach(function(o){t[o.pluginName]&&(t[o.pluginName][i]&&t[o.pluginName][i](De({sortable:t},e)),t.options[o.pluginName]&&t[o.pluginName][n]&&t[o.pluginName][n](De({sortable:t},e)))})},initializePlugins:function(n,t,e,s){for(var i in ls.forEach(function(r){var a=r.pluginName;if(n.options[a]||r.initializeByDefault){var l=new r(n,t,n.options);l.sortable=n,l.options=n.options,n[a]=l,Le(e,l.defaults)}}),n.options)if(n.options.hasOwnProperty(i)){var o=this.modifyOption(n,i,n.options[i]);o!==void 0&&(n.options[i]=o)}},getEventProperties:function(n,t){var e={};return ls.forEach(function(s){typeof s.eventProperties=="function"&&Le(e,s.eventProperties.call(t[s.pluginName],n))}),e},modifyOption:function(n,t,e){var s;return ls.forEach(function(i){n[i.pluginName]&&i.optionListeners&&typeof i.optionListeners[t]=="function"&&(s=i.optionListeners[t].call(n[i.pluginName],e))}),s}},oc=["evt"],jt=function(n,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=e.evt,i=nc(e,oc);Zs.pluginEvent.bind(q)(n,t,De({dragEl:C,parentEl:ft,ghostEl:j,rootEl:dt,nextEl:ts,lastDownEl:kn,cloneEl:gt,cloneHidden:We,dragStarted:Ps,putSortable:Lt,activeSortable:q.active,originalEvent:s,oldIndex:us,oldDraggableIndex:Vs,newIndex:Qt,newDraggableIndex:je,hideGhostForTarget:kr,unhideGhostForTarget:xr,cloneNowHidden:function(){We=!0},cloneNowShown:function(){We=!1},dispatchSortableEvent:function(o){Ht({sortable:t,name:o,originalEvent:s})}},i))};function Ht(n){(function(t){var e=t.sortable,s=t.rootEl,i=t.name,o=t.targetEl,r=t.cloneEl,a=t.toEl,l=t.fromEl,c=t.oldIndex,d=t.newIndex,p=t.oldDraggableIndex,f=t.newDraggableIndex,g=t.originalEvent,b=t.putSortable,m=t.extraEventProperties;if(e=e||s&&s[Wt]){var _,v=e.options,x="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||Ue||sn?(_=document.createEvent("Event")).initEvent(i,!0,!0):_=new CustomEvent(i,{bubbles:!0,cancelable:!0}),_.to=a||s,_.from=l||s,_.item=o||s,_.clone=r,_.oldIndex=c,_.newIndex=d,_.oldDraggableIndex=p,_.newDraggableIndex=f,_.originalEvent=g,_.pullMode=b?b.lastPutMode:void 0;var A=De(De({},m),Zs.getEventProperties(i,e));for(var L in A)_[L]=A[L];s&&s.dispatchEvent(_),v[x]&&v[x].call(e,_)}})(De({putSortable:Lt,cloneEl:gt,targetEl:C,rootEl:dt,oldIndex:us,oldDraggableIndex:Vs,newIndex:Qt,newDraggableIndex:je},n))}var C,ft,j,dt,ts,kn,gt,We,us,Qt,Vs,je,dn,Lt,Qe,he,Wn,Vn,ko,xo,Ps,cs,Es,un,Ut,ds=!1,An=!1,Dn=[],Is=!1,hn=!1,Yn=[],vi=!1,pn=[],Nn=typeof document<"u",fn=Ii,$o=sn||Ue?"cssFloat":"float",rc=Nn&&!fr&&!Ii&&"draggable"in document.createElement("div"),_r=function(){if(Nn){if(Ue)return!1;var n=document.createElement("x");return n.style.cssText="pointer-events:auto",n.style.pointerEvents==="auto"}}(),wr=function(n,t){var e=H(n),s=parseInt(e.width)-parseInt(e.paddingLeft)-parseInt(e.paddingRight)-parseInt(e.borderLeftWidth)-parseInt(e.borderRightWidth),i=gs(n,0,t),o=gs(n,1,t),r=i&&H(i),a=o&&H(o),l=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+kt(i).width,c=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+kt(o).width;if(e.display==="flex")return e.flexDirection==="column"||e.flexDirection==="column-reverse"?"vertical":"horizontal";if(e.display==="grid")return e.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&r.float&&r.float!=="none"){var d=r.float==="left"?"left":"right";return!o||a.clear!=="both"&&a.clear!==d?"horizontal":"vertical"}return i&&(r.display==="block"||r.display==="flex"||r.display==="table"||r.display==="grid"||l>=s&&e[$o]==="none"||o&&e[$o]==="none"&&l+c>s)?"vertical":"horizontal"},yr=function(n){function t(i,o){return function(r,a,l,c){var d=r.options.group.name&&a.options.group.name&&r.options.group.name===a.options.group.name;if(i==null&&(o||d))return!0;if(i==null||i===!1)return!1;if(o&&i==="clone")return i;if(typeof i=="function")return t(i(r,a,l,c),o)(r,a,l,c);var p=(o?r:a).options.group.name;return i===!0||typeof i=="string"&&i===p||i.join&&i.indexOf(p)>-1}}var e={},s=n.group;s&&gi(s)=="object"||(s={name:s}),e.name=s.name,e.checkPull=t(s.pull,!0),e.checkPut=t(s.put),e.revertClone=s.revertClone,n.group=e},kr=function(){!_r&&j&&H(j,"display","none")},xr=function(){!_r&&j&&H(j,"display","")};Nn&&!fr&&document.addEventListener("click",function(n){if(An)return n.preventDefault(),n.stopPropagation&&n.stopPropagation(),n.stopImmediatePropagation&&n.stopImmediatePropagation(),An=!1,!1},!0);var Ke=function(n){if(C){n=n.touches?n.touches[0]:n;var t=(i=n.clientX,o=n.clientY,Dn.some(function(a){var l=a[Wt].options.emptyInsertThreshold;if(l&&!mi(a)){var c=kt(a),d=i>=c.left-l&&i<=c.right+l,p=o>=c.top-l&&o<=c.bottom+l;return d&&p?r=a:void 0}}),r);if(t){var e={};for(var s in n)n.hasOwnProperty(s)&&(e[s]=n[s]);e.target=e.rootEl=t,e.preventDefault=void 0,e.stopPropagation=void 0,t[Wt]._onDragOver(e)}}var i,o,r},ac=function(n){C&&C.parentNode[Wt]._isOutsideThisEl(n.target)};function q(n,t){if(!n||!n.nodeType||n.nodeType!==1)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(n));this.el=n,this.options=t=Le({},t),n[Wt]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(n.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return wr(n,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(o,r){o.setData("Text",r.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:q.supportPointer!==!1&&"PointerEvent"in window&&(!js||Ii),emptyInsertThreshold:5};for(var s in Zs.initializePlugins(this,n,e),e)!(s in t)&&(t[s]=e[s]);for(var i in yr(t),this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=!t.forceFallback&&rc,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?Z(n,"pointerdown",this._onTapStart):(Z(n,"mousedown",this._onTapStart),Z(n,"touchstart",this._onTapStart)),this.nativeDraggable&&(Z(n,"dragover",this),Z(n,"dragenter",this)),Dn.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Le(this,ic())}function gn(n,t,e,s,i,o,r,a){var l,c,d=n[Wt],p=d.options.onMove;return!window.CustomEvent||Ue||sn?(l=document.createEvent("Event")).initEvent("move",!0,!0):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=t,l.from=n,l.dragged=e,l.draggedRect=s,l.related=i||t,l.relatedRect=o||kt(t),l.willInsertAfter=a,l.originalEvent=r,n.dispatchEvent(l),p&&(c=p.call(d,l,r)),c}function Xn(n){n.draggable=!1}function lc(){vi=!1}function cc(n){for(var t=n.tagName+n.className+n.src+n.href+n.textContent,e=t.length,s=0;e--;)s+=t.charCodeAt(e);return s.toString(36)}function mn(n){return setTimeout(n,0)}function Zn(n){return clearTimeout(n)}q.prototype={constructor:q,_isOutsideThisEl:function(n){this.el.contains(n)||n===this.el||(cs=null)},_getDirection:function(n,t){return typeof this.options.direction=="function"?this.options.direction.call(this,n,t,C):this.options.direction},_onTapStart:function(n){if(n.cancelable){var t=this,e=this.el,s=this.options,i=s.preventOnFilter,o=n.type,r=n.touches&&n.touches[0]||n.pointerType&&n.pointerType==="touch"&&n,a=(r||n).target,l=n.target.shadowRoot&&(n.path&&n.path[0]||n.composedPath&&n.composedPath()[0])||a,c=s.filter;if(function(d){pn.length=0;for(var p=d.getElementsByTagName("input"),f=p.length;f--;){var g=p[f];g.checked&&pn.push(g)}}(e),!C&&!(/mousedown|pointerdown/.test(o)&&n.button!==0||s.disabled)&&!l.isContentEditable&&(this.nativeDraggable||!js||!a||a.tagName.toUpperCase()!=="SELECT")&&!((a=pe(a,s.draggable,e,!1))&&a.animated||kn===a)){if(us=re(a),Vs=re(a,s.draggable),typeof c=="function"){if(c.call(this,n,a,this))return Ht({sortable:t,rootEl:l,name:"filter",targetEl:a,toEl:e,fromEl:e}),jt("filter",t,{evt:n}),void(i&&n.preventDefault())}else if(c&&(c=c.split(",").some(function(d){if(d=pe(l,d.trim(),e,!1))return Ht({sortable:t,rootEl:d,name:"filter",targetEl:a,fromEl:e,toEl:e}),jt("filter",t,{evt:n}),!0})))return void(i&&n.preventDefault());s.handle&&!pe(l,s.handle,e,!1)||this._prepareDragStart(n,r,a)}}},_prepareDragStart:function(n,t,e){var s,i=this,o=i.el,r=i.options,a=o.ownerDocument;if(e&&!C&&e.parentNode===o){var l=kt(e);if(dt=o,ft=(C=e).parentNode,ts=C.nextSibling,kn=e,dn=r.group,q.dragged=C,Qe={target:C,clientX:(t||n).clientX,clientY:(t||n).clientY},ko=Qe.clientX-l.left,xo=Qe.clientY-l.top,this._lastX=(t||n).clientX,this._lastY=(t||n).clientY,C.style["will-change"]="all",s=function(){jt("delayEnded",i,{evt:n}),q.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!go&&i.nativeDraggable&&(C.draggable=!0),i._triggerDragStart(n,t),Ht({sortable:i,name:"choose",originalEvent:n}),Xt(C,r.chosenClass,!0))},r.ignore.split(",").forEach(function(c){vo(C,c.trim(),Xn)}),Z(a,"dragover",Ke),Z(a,"mousemove",Ke),Z(a,"touchmove",Ke),r.supportPointer?(Z(a,"pointerup",i._onDrop),!this.nativeDraggable&&Z(a,"pointercancel",i._onDrop)):(Z(a,"mouseup",i._onDrop),Z(a,"touchend",i._onDrop),Z(a,"touchcancel",i._onDrop)),go&&this.nativeDraggable&&(this.options.touchStartThreshold=4,C.draggable=!0),jt("delayStart",this,{evt:n}),!r.delay||r.delayOnTouchOnly&&!t||this.nativeDraggable&&(sn||Ue))s();else{if(q.eventCanceled)return void this._onDrop();r.supportPointer?(Z(a,"pointerup",i._disableDelayedDrag),Z(a,"pointercancel",i._disableDelayedDrag)):(Z(a,"mouseup",i._disableDelayedDrag),Z(a,"touchend",i._disableDelayedDrag),Z(a,"touchcancel",i._disableDelayedDrag)),Z(a,"mousemove",i._delayedDragTouchMoveHandler),Z(a,"touchmove",i._delayedDragTouchMoveHandler),r.supportPointer&&Z(a,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(s,r.delay)}}},_delayedDragTouchMoveHandler:function(n){var t=n.touches?n.touches[0]:n;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){C&&Xn(C),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var n=this.el.ownerDocument;Y(n,"mouseup",this._disableDelayedDrag),Y(n,"touchend",this._disableDelayedDrag),Y(n,"touchcancel",this._disableDelayedDrag),Y(n,"pointerup",this._disableDelayedDrag),Y(n,"pointercancel",this._disableDelayedDrag),Y(n,"mousemove",this._delayedDragTouchMoveHandler),Y(n,"touchmove",this._delayedDragTouchMoveHandler),Y(n,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(n,t){t=t||n.pointerType=="touch"&&n,!this.nativeDraggable||t?this.options.supportPointer?Z(document,"pointermove",this._onTouchMove):Z(document,t?"touchmove":"mousemove",this._onTouchMove):(Z(C,"dragend",this),Z(dt,"dragstart",this._onDragStart));try{document.selection?mn(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(n,t){if(ds=!1,dt&&C){jt("dragStarted",this,{evt:t}),this.nativeDraggable&&Z(document,"dragover",ac);var e=this.options;!n&&Xt(C,e.dragClass,!1),Xt(C,e.ghostClass,!0),q.active=this,n&&this._appendGhost(),Ht({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(he){this._lastX=he.clientX,this._lastY=he.clientY,kr();for(var n=document.elementFromPoint(he.clientX,he.clientY),t=n;n&&n.shadowRoot&&(n=n.shadowRoot.elementFromPoint(he.clientX,he.clientY))!==t;)t=n;if(C.parentNode[Wt]._isOutsideThisEl(n),t)do{if(t[Wt]&&t[Wt]._onDragOver({clientX:he.clientX,clientY:he.clientY,target:n,rootEl:t})&&!this.options.dragoverBubble)break;n=t}while(t=mr(t));xr()}},_onTouchMove:function(n){if(Qe){var t=this.options,e=t.fallbackTolerance,s=t.fallbackOffset,i=n.touches?n.touches[0]:n,o=j&&fs(j,!0),r=j&&o&&o.a,a=j&&o&&o.d,l=fn&&Ut&&_o(Ut),c=(i.clientX-Qe.clientX+s.x)/(r||1)+(l?l[0]-Yn[0]:0)/(r||1),d=(i.clientY-Qe.clientY+s.y)/(a||1)+(l?l[1]-Yn[1]:0)/(a||1);if(!q.active&&!ds){if(e&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<e)return;this._onDragStart(n,!0)}if(j){o?(o.e+=c-(Wn||0),o.f+=d-(Vn||0)):o={a:1,b:0,c:0,d:1,e:c,f:d};var p="matrix(".concat(o.a,",").concat(o.b,",").concat(o.c,",").concat(o.d,",").concat(o.e,",").concat(o.f,")");H(j,"webkitTransform",p),H(j,"mozTransform",p),H(j,"msTransform",p),H(j,"transform",p),Wn=c,Vn=d,he=i}n.cancelable&&n.preventDefault()}},_appendGhost:function(){if(!j){var n=this.options.fallbackOnBody?document.body:dt,t=kt(C,!0,fn,!0,n),e=this.options;if(fn){for(Ut=n;H(Ut,"position")==="static"&&H(Ut,"transform")==="none"&&Ut!==document;)Ut=Ut.parentNode;Ut!==document.body&&Ut!==document.documentElement?(Ut===document&&(Ut=Re()),t.top+=Ut.scrollTop,t.left+=Ut.scrollLeft):Ut=Re(),Yn=_o(Ut)}Xt(j=C.cloneNode(!0),e.ghostClass,!1),Xt(j,e.fallbackClass,!0),Xt(j,e.dragClass,!0),H(j,"transition",""),H(j,"transform",""),H(j,"box-sizing","border-box"),H(j,"margin",0),H(j,"top",t.top),H(j,"left",t.left),H(j,"width",t.width),H(j,"height",t.height),H(j,"opacity","0.8"),H(j,"position",fn?"absolute":"fixed"),H(j,"zIndex","100000"),H(j,"pointerEvents","none"),q.ghost=j,n.appendChild(j),H(j,"transform-origin",ko/parseInt(j.style.width)*100+"% "+xo/parseInt(j.style.height)*100+"%")}},_onDragStart:function(n,t){var e=this,s=n.dataTransfer,i=e.options;jt("dragStart",this,{evt:n}),q.eventCanceled?this._onDrop():(jt("setupClone",this),q.eventCanceled||((gt=wo(C)).removeAttribute("id"),gt.draggable=!1,gt.style["will-change"]="",this._hideClone(),Xt(gt,this.options.chosenClass,!1),q.clone=gt),e.cloneId=mn(function(){jt("clone",e),q.eventCanceled||(e.options.removeCloneOnHide||dt.insertBefore(gt,C),e._hideClone(),Ht({sortable:e,name:"clone"}))}),!t&&Xt(C,i.dragClass,!0),t?(An=!0,e._loopId=setInterval(e._emulateDragOver,50)):(Y(document,"mouseup",e._onDrop),Y(document,"touchend",e._onDrop),Y(document,"touchcancel",e._onDrop),s&&(s.effectAllowed="move",i.setData&&i.setData.call(e,s,C)),Z(document,"drop",e),H(C,"transform","translateZ(0)")),ds=!0,e._dragStartId=mn(e._dragStarted.bind(e,t,n)),Z(document,"selectstart",e),Ps=!0,window.getSelection().removeAllRanges(),js&&H(document.body,"user-select","none"))},_onDragOver:function(n){var t,e,s,i,o=this.el,r=n.target,a=this.options,l=a.group,c=q.active,d=dn===l,p=a.sort,f=Lt||c,g=this,b=!1;if(!vi){if(n.preventDefault!==void 0&&n.cancelable&&n.preventDefault(),r=pe(r,a.draggable,o,!0),Pt("dragOver"),q.eventCanceled)return b;if(C.contains(n.target)||r.animated&&r.animatingX&&r.animatingY||g._ignoreWhileAnimating===r)return Nt(!1);if(An=!1,c&&!a.disabled&&(d?p||(s=ft!==dt):Lt===this||(this.lastPutMode=dn.checkPull(this,c,C,n))&&l.checkPut(this,c,C,n))){if(i=this._getDirection(n,r)==="vertical",t=kt(C),Pt("dragOverValid"),q.eventCanceled)return b;if(s)return ft=dt,Ot(),this._hideClone(),Pt("revert"),q.eventCanceled||(ts?dt.insertBefore(C,ts):dt.appendChild(C)),Nt(!0);var m=mi(o,a.draggable);if(!m||function(Q,J,tt){var Tt=kt(mi(tt.el,tt.options.draggable)),de=yo(tt.el,tt.options,j),oe=10;return J?Q.clientX>de.right+oe||Q.clientY>Tt.bottom&&Q.clientX>Tt.left:Q.clientY>de.bottom+oe||Q.clientX>Tt.right&&Q.clientY>Tt.top}(n,i,this)&&!m.animated){if(m===C)return Nt(!1);if(m&&o===n.target&&(r=m),r&&(e=kt(r)),gn(dt,o,C,t,r,e,n,!!r)!==!1)return Ot(),m&&m.nextSibling?o.insertBefore(C,m.nextSibling):o.appendChild(C),ft=o,Ie(),Nt(!0)}else if(m&&function(Q,J,tt){var Tt=kt(gs(tt.el,0,tt.options,!0)),de=yo(tt.el,tt.options,j),oe=10;return J?Q.clientX<de.left-oe||Q.clientY<Tt.top&&Q.clientX<Tt.right:Q.clientY<de.top-oe||Q.clientY<Tt.bottom&&Q.clientX<Tt.left}(n,i,this)){var _=gs(o,0,a,!0);if(_===C)return Nt(!1);if(e=kt(r=_),gn(dt,o,C,t,r,e,n,!1)!==!1)return Ot(),o.insertBefore(C,_),ft=o,Ie(),Nt(!0)}else if(r.parentNode===o){e=kt(r);var v,x,A,L=C.parentNode!==o,w=!function(Q,J,tt){var Tt=tt?Q.left:Q.top,de=tt?Q.right:Q.bottom,oe=tt?Q.width:Q.height,$s=tt?J.left:J.top,Ln=tt?J.right:J.bottom,ue=tt?J.width:J.height;return Tt===$s||de===Ln||Tt+oe/2===$s+ue/2}(C.animated&&C.toRect||t,r.animated&&r.toRect||e,i),K=i?"top":"left",V=bo(r,"top","top")||bo(C,"top","top"),M=V?V.scrollTop:void 0;if(cs!==r&&(x=e[K],Is=!1,hn=!w&&a.invertSwap||L),v=function(Q,J,tt,Tt,de,oe,$s,Ln){var ue=Tt?Q.clientY:Q.clientX,ze=Tt?tt.height:tt.width,Ts=Tt?tt.top:tt.left,nn=Tt?tt.bottom:tt.right,Pn=!1;if(!$s){if(Ln&&un<ze*de){if(!Is&&(Es===1?ue>Ts+ze*oe/2:ue<nn-ze*oe/2)&&(Is=!0),Is)Pn=!0;else if(Es===1?ue<Ts+un:ue>nn-un)return-Es}else if(ue>Ts+ze*(1-de)/2&&ue<nn-ze*(1-de)/2)return function(Sr){return re(C)<re(Sr)?1:-1}(J)}return(Pn=Pn||$s)&&(ue<Ts+ze*oe/2||ue>nn-ze*oe/2)?ue>Ts+ze/2?1:-1:0}(n,r,e,i,w?1:a.swapThreshold,a.invertedSwapThreshold==null?a.swapThreshold:a.invertedSwapThreshold,hn,cs===r),v!==0){var S=re(C);do S-=v,A=ft.children[S];while(A&&(H(A,"display")==="none"||A===j))}if(v===0||A===r)return Nt(!1);cs=r,Es=v;var T=r.nextElementSibling,P=!1,$t=gn(dt,o,C,t,r,e,n,P=v===1);if($t!==!1)return $t!==1&&$t!==-1||(P=$t===1),vi=!0,setTimeout(lc,30),Ot(),P&&!T?o.appendChild(C):r.parentNode.insertBefore(C,P?T:r),V&&br(V,0,M-V.scrollTop),ft=C.parentNode,x===void 0||hn||(un=Math.abs(x-kt(r)[K])),Ie(),Nt(!0)}if(o.contains(C))return Nt(!1)}return!1}function Pt(Q,J){jt(Q,g,De({evt:n,isOwner:d,axis:i?"vertical":"horizontal",revert:s,dragRect:t,targetRect:e,canSort:p,fromSortable:f,target:r,completed:Nt,onMove:function(tt,Tt){return gn(dt,o,C,t,tt,kt(tt),n,Tt)},changed:Ie},J))}function Ot(){Pt("dragOverAnimationCapture"),g.captureAnimationState(),g!==f&&f.captureAnimationState()}function Nt(Q){return Pt("dragOverCompleted",{insertion:Q}),Q&&(d?c._hideClone():c._showClone(g),g!==f&&(Xt(C,Lt?Lt.options.ghostClass:c.options.ghostClass,!1),Xt(C,a.ghostClass,!0)),Lt!==g&&g!==q.active?Lt=g:g===q.active&&Lt&&(Lt=null),f===g&&(g._ignoreWhileAnimating=r),g.animateAll(function(){Pt("dragOverAnimationComplete"),g._ignoreWhileAnimating=null}),g!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(r===C&&!C.animated||r===o&&!r.animated)&&(cs=null),a.dragoverBubble||n.rootEl||r===document||(C.parentNode[Wt]._isOutsideThisEl(n.target),!Q&&Ke(n)),!a.dragoverBubble&&n.stopPropagation&&n.stopPropagation(),b=!0}function Ie(){Qt=re(C),je=re(C,a.draggable),Ht({sortable:g,name:"change",toEl:o,newIndex:Qt,newDraggableIndex:je,originalEvent:n})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){Y(document,"mousemove",this._onTouchMove),Y(document,"touchmove",this._onTouchMove),Y(document,"pointermove",this._onTouchMove),Y(document,"dragover",Ke),Y(document,"mousemove",Ke),Y(document,"touchmove",Ke)},_offUpEvents:function(){var n=this.el.ownerDocument;Y(n,"mouseup",this._onDrop),Y(n,"touchend",this._onDrop),Y(n,"pointerup",this._onDrop),Y(n,"pointercancel",this._onDrop),Y(n,"touchcancel",this._onDrop),Y(document,"selectstart",this)},_onDrop:function(n){var t=this.el,e=this.options;Qt=re(C),je=re(C,e.draggable),jt("drop",this,{evt:n}),ft=C&&C.parentNode,Qt=re(C),je=re(C,e.draggable),q.eventCanceled||(ds=!1,hn=!1,Is=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Zn(this.cloneId),Zn(this._dragStartId),this.nativeDraggable&&(Y(document,"drop",this),Y(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),js&&H(document.body,"user-select",""),H(C,"transform",""),n&&(Ps&&(n.cancelable&&n.preventDefault(),!e.dropBubble&&n.stopPropagation()),j&&j.parentNode&&j.parentNode.removeChild(j),(dt===ft||Lt&&Lt.lastPutMode!=="clone")&&gt&&gt.parentNode&&gt.parentNode.removeChild(gt),C&&(this.nativeDraggable&&Y(C,"dragend",this),Xn(C),C.style["will-change"]="",Ps&&!ds&&Xt(C,Lt?Lt.options.ghostClass:this.options.ghostClass,!1),Xt(C,this.options.chosenClass,!1),Ht({sortable:this,name:"unchoose",toEl:ft,newIndex:null,newDraggableIndex:null,originalEvent:n}),dt!==ft?(Qt>=0&&(Ht({rootEl:ft,name:"add",toEl:ft,fromEl:dt,originalEvent:n}),Ht({sortable:this,name:"remove",toEl:ft,originalEvent:n}),Ht({rootEl:ft,name:"sort",toEl:ft,fromEl:dt,originalEvent:n}),Ht({sortable:this,name:"sort",toEl:ft,originalEvent:n})),Lt&&Lt.save()):Qt!==us&&Qt>=0&&(Ht({sortable:this,name:"update",toEl:ft,originalEvent:n}),Ht({sortable:this,name:"sort",toEl:ft,originalEvent:n})),q.active&&(Qt!=null&&Qt!==-1||(Qt=us,je=Vs),Ht({sortable:this,name:"end",toEl:ft,originalEvent:n}),this.save())))),this._nulling()},_nulling:function(){jt("nulling",this),dt=C=ft=j=ts=gt=kn=We=Qe=he=Ps=Qt=je=us=Vs=cs=Es=Lt=dn=q.dragged=q.ghost=q.clone=q.active=null,pn.forEach(function(n){n.checked=!0}),pn.length=Wn=Vn=0},handleEvent:function(n){switch(n.type){case"drop":case"dragend":this._onDrop(n);break;case"dragenter":case"dragover":C&&(this._onDragOver(n),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(n));break;case"selectstart":n.preventDefault()}},toArray:function(){for(var n,t=[],e=this.el.children,s=0,i=e.length,o=this.options;s<i;s++)pe(n=e[s],o.draggable,this.el,!1)&&t.push(n.getAttribute(o.dataIdAttr)||cc(n));return t},sort:function(n,t){var e={},s=this.el;this.toArray().forEach(function(i,o){var r=s.children[o];pe(r,this.options.draggable,s,!1)&&(e[i]=r)},this),t&&this.captureAnimationState(),n.forEach(function(i){e[i]&&(s.removeChild(e[i]),s.appendChild(e[i]))}),t&&this.animateAll()},save:function(){var n=this.options.store;n&&n.set&&n.set(this)},closest:function(n,t){return pe(n,t||this.options.draggable,this.el,!1)},option:function(n,t){var e=this.options;if(t===void 0)return e[n];var s=Zs.modifyOption(this,n,t);e[n]=s!==void 0?s:t,n==="group"&&yr(e)},destroy:function(){jt("destroy",this);var n=this.el;n[Wt]=null,Y(n,"mousedown",this._onTapStart),Y(n,"touchstart",this._onTapStart),Y(n,"pointerdown",this._onTapStart),this.nativeDraggable&&(Y(n,"dragover",this),Y(n,"dragenter",this)),Array.prototype.forEach.call(n.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Dn.splice(Dn.indexOf(this.el),1),this.el=n=null},_hideClone:function(){if(!We){if(jt("hideClone",this),q.eventCanceled)return;H(gt,"display","none"),this.options.removeCloneOnHide&&gt.parentNode&&gt.parentNode.removeChild(gt),We=!0}},_showClone:function(n){if(n.lastPutMode==="clone"){if(We){if(jt("showClone",this),q.eventCanceled)return;C.parentNode!=dt||this.options.group.revertClone?ts?dt.insertBefore(gt,ts):dt.appendChild(gt):dt.insertBefore(gt,C),this.options.group.revertClone&&this.animate(C,gt),H(gt,"display",""),We=!1}}else this._hideClone()}},Nn&&Z(document,"touchmove",function(n){(q.active||ds)&&n.cancelable&&n.preventDefault()}),q.utils={on:Z,off:Y,css:H,find:vo,is:function(n,t){return!!pe(n,t,n,!1)},extend:function(n,t){if(n&&t)for(var e in t)t.hasOwnProperty(e)&&(n[e]=t[e]);return n},throttle:vr,closest:pe,toggleClass:Xt,clone:wo,index:re,nextTick:mn,cancelNextTick:Zn,detectDirection:wr,getChild:gs,expando:Wt},q.get=function(n){return n[Wt]},q.mount=function(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(s){if(!s.prototype||!s.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(s));s.utils&&(q.utils=De(De({},q.utils),s.utils)),Zs.mount(s)})},q.create=function(n,t){return new q(n,t)},q.version="1.15.6";var Us,bi,Qn,Kn,Rn,zs,_t=[],_i=!1;function xn(){_t.forEach(function(n){clearInterval(n.pid)}),_t=[]}function To(){clearInterval(zs)}var Jn=vr(function(n,t,e,s){if(t.scroll){var i,o=(n.touches?n.touches[0]:n).clientX,r=(n.touches?n.touches[0]:n).clientY,a=t.scrollSensitivity,l=t.scrollSpeed,c=Re(),d=!1;bi!==e&&(bi=e,xn(),Us=t.scroll,i=t.scrollFn,Us===!0&&(Us=Xe(e,!0)));var p=0,f=Us;do{var g=f,b=kt(g),m=b.top,_=b.bottom,v=b.left,x=b.right,A=b.width,L=b.height,w=void 0,K=void 0,V=g.scrollWidth,M=g.scrollHeight,S=H(g),T=g.scrollLeft,P=g.scrollTop;g===c?(w=A<V&&(S.overflowX==="auto"||S.overflowX==="scroll"||S.overflowX==="visible"),K=L<M&&(S.overflowY==="auto"||S.overflowY==="scroll"||S.overflowY==="visible")):(w=A<V&&(S.overflowX==="auto"||S.overflowX==="scroll"),K=L<M&&(S.overflowY==="auto"||S.overflowY==="scroll"));var $t=w&&(Math.abs(x-o)<=a&&T+A<V)-(Math.abs(v-o)<=a&&!!T),Pt=K&&(Math.abs(_-r)<=a&&P+L<M)-(Math.abs(m-r)<=a&&!!P);if(!_t[p])for(var Ot=0;Ot<=p;Ot++)_t[Ot]||(_t[Ot]={});_t[p].vx==$t&&_t[p].vy==Pt&&_t[p].el===g||(_t[p].el=g,_t[p].vx=$t,_t[p].vy=Pt,clearInterval(_t[p].pid),$t==0&&Pt==0||(d=!0,_t[p].pid=setInterval((function(){s&&this.layer===0&&q.active._onTouchMove(Rn);var Nt=_t[this.layer].vy?_t[this.layer].vy*l:0,Ie=_t[this.layer].vx?_t[this.layer].vx*l:0;typeof i=="function"&&i.call(q.dragged.parentNode[Wt],Ie,Nt,n,Rn,_t[this.layer].el)!=="continue"||br(_t[this.layer].el,Ie,Nt)}).bind({layer:p}),24))),p++}while(t.bubbleScroll&&f!==c&&(f=Xe(f,!1)));_i=d}},30),So=function(n){var t=n.originalEvent,e=n.putSortable,s=n.dragEl,i=n.activeSortable,o=n.dispatchSortableEvent,r=n.hideGhostForTarget,a=n.unhideGhostForTarget;if(t){var l=e||i;r();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,d=document.elementFromPoint(c.clientX,c.clientY);a(),l&&!l.el.contains(d)&&(o("spill"),this.onSpill({dragEl:s,putSortable:e}))}};function ti(){}function ei(){}ti.prototype={startIndex:null,dragStart:function(n){var t=n.oldDraggableIndex;this.startIndex=t},onSpill:function(n){var t=n.dragEl,e=n.putSortable;this.sortable.captureAnimationState(),e&&e.captureAnimationState();var s=gs(this.sortable.el,this.startIndex,this.options);s?this.sortable.el.insertBefore(t,s):this.sortable.el.appendChild(t),this.sortable.animateAll(),e&&e.animateAll()},drop:So},Le(ti,{pluginName:"revertOnSpill"}),ei.prototype={onSpill:function(n){var t=n.dragEl,e=n.putSortable||this.sortable;e.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),e.animateAll()},drop:So},Le(ei,{pluginName:"removeOnSpill"}),q.mount(new function(){function n(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return n.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?Z(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Z(document,"pointermove",this._handleFallbackAutoScroll):e.touches?Z(document,"touchmove",this._handleFallbackAutoScroll):Z(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?Y(document,"dragover",this._handleAutoScroll):(Y(document,"pointermove",this._handleFallbackAutoScroll),Y(document,"touchmove",this._handleFallbackAutoScroll),Y(document,"mousemove",this._handleFallbackAutoScroll)),To(),xn(),clearTimeout(Ws),Ws=void 0},nulling:function(){Rn=bi=Us=_i=zs=Qn=Kn=null,_t.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var s=this,i=(t.touches?t.touches[0]:t).clientX,o=(t.touches?t.touches[0]:t).clientY,r=document.elementFromPoint(i,o);if(Rn=t,e||this.options.forceAutoScrollFallback||sn||Ue||js){Jn(t,this.options,r,e);var a=Xe(r,!0);!_i||zs&&i===Qn&&o===Kn||(zs&&To(),zs=setInterval(function(){var l=Xe(document.elementFromPoint(i,o),!0);l!==a&&(a=l,xn()),Jn(t,s.options,l,e)},10),Qn=i,Kn=o)}else{if(!this.options.bubbleScroll||Xe(r,!0)===Re())return void xn();Jn(t,this.options,Xe(r,!1),!1)}}},Le(n,{pluginName:"scroll",initializeByDefault:!0})}),q.mount(ei,ti);const dc="c-draggable-list-item__sortable-ghost",uc="c-draggable-list-item__sortable-chosen",hc="c-draggable-list-item__sortable-drag",pc=n=>({items:4&n,onEnd:8&n}),Co=n=>({items:n[2],onEnd:n[3]});function fc(n){let t,e,s;const i=n[12].items,o=Ms(i,n,n[11],Co);return{c(){t=pt("div"),o&&o.c(),y(t,"class",e="c-draggable-list "+n[0]+" svelte-1ncmqin"),y(t,"id",n[1]),y(t,"data-list-id",n[1]),y(t,"data-testid","draggable-list")},m(r,a){R(r,t,a),o&&o.m(t,null),n[13](t),s=!0},p(r,[a]){o&&o.p&&(!s||2060&a)&&As(o,i,r,r[11],s?Rs(i,r[11],a,pc):Ds(r[11]),Co),(!s||1&a&&e!==(e="c-draggable-list "+r[0]+" svelte-1ncmqin"))&&y(t,"class",e),(!s||2&a)&&y(t,"id",r[1]),(!s||2&a)&&y(t,"data-list-id",r[1])},i(r){s||(k(o,r),s=!0)},o(r){$(o,r),s=!1},d(r){r&&E(t),o&&o.d(r),n[13](null)}}}function gc(n,t,e){let s,{$$slots:i={},$$scope:o}=t,{class:r=""}=t,{id:a}=t,{items:l=[]}=t,{options:c={}}=t,{disabled:d=!1}=t,{useHandle:p=!0}=t,{onEnd:f=()=>{}}=t,g=null,b={...c},m=d;function _(){try{g&&typeof g.destroy=="function"&&g.destroy()}catch(x){console.error("Error destroying Sortable instance:",x)}finally{e(8,g=null)}}async function v(){if(await Xs(),_(),s)try{e(8,g=new q(s,{animation:150,fallbackOnBody:!0,swapThreshold:.65,ghostClass:dc,chosenClass:uc,dragClass:hc,disabled:d,group:c.group||"nested",dataIdAttr:"data-item-id",handle:p?".c-draggable-list-item__handle":void 0,...c,onEnd:f}))}catch(x){console.error("Error initializing Sortable:",x),e(8,g=null)}}return Dr(v),Rr(()=>{_(),e(4,s=null),e(9,b={})}),n.$$set=x=>{"class"in x&&e(0,r=x.class),"id"in x&&e(1,a=x.id),"items"in x&&e(2,l=x.items),"options"in x&&e(5,c=x.options),"disabled"in x&&e(6,d=x.disabled),"useHandle"in x&&e(7,p=x.useHandle),"onEnd"in x&&e(3,f=x.onEnd),"$$scope"in x&&e(11,o=x.$$scope)},n.$$.update=()=>{if(1360&n.$$.dirty&&g&&s&&d!==m)try{typeof g.option=="function"&&(g.option("disabled",d),e(10,m=d))}catch(x){console.error("Error updating Sortable disabled state:",x)}if(816&n.$$.dirty&&g&&s&&JSON.stringify(c)!==JSON.stringify(b))try{e(9,b={...c}),v()}catch(x){console.error("Error updating Sortable options:",x)}},[r,a,l,f,s,c,d,p,g,b,m,o,i,function(x){Fe[x?"unshift":"push"](()=>{s=x,e(4,s)})}]}class $r extends st{constructor(t){super(),nt(this,t,gc,fc,it,{class:0,id:1,items:2,options:5,disabled:6,useHandle:7,onEnd:3})}}function mc(n){let t,e,s,i,o,r,a;function l(p){n[7](p)}function c(p){n[8](p)}let d={size:1,variant:"surface",placeholder:"Add task description...",disabled:!n[0],rows:1,resize:"vertical"};return n[2]!==void 0&&(d.textInput=n[2]),n[1]!==void 0&&(d.value=n[1]),i=new ra({props:d}),Fe.push(()=>ys(i,"textInput",l)),Fe.push(()=>ys(i,"value",c)),i.$on("keydown",n[4]),i.$on("blur",n[3]),{c(){t=pt("div"),e=pt("div"),s=pt("div"),F(i.$$.fragment),y(s,"class","c-task-details__section c-task-details__description-contents svelte-1k3razm"),y(e,"class","c-task-details__content svelte-1k3razm"),y(t,"class","c-task-details svelte-1k3razm")},m(p,f){R(p,t,f),ht(t,e),ht(e,s),O(i,s,null),a=!0},p(p,[f]){const g={};1&f&&(g.disabled=!p[0]),!o&&4&f&&(o=!0,g.textInput=p[2],ks(()=>o=!1)),!r&&2&f&&(r=!0,g.value=p[1],ks(()=>r=!1)),i.$set(g)},i(p){a||(k(i.$$.fragment,p),a=!0)},o(p){$(i.$$.fragment,p),a=!1},d(p){p&&E(t),N(i)}}}function vc(n,t,e){let s,{task:i}=t,{taskStore:o}=t,{editable:r=!0}=t,a=i.description;function l(){a.trim()!==i.description&&o.updateTask(i.uuid,{description:a.trim()},xt.USER),s==null||s.blur()}return n.$$set=c=>{"task"in c&&e(5,i=c.task),"taskStore"in c&&e(6,o=c.taskStore),"editable"in c&&e(0,r=c.editable)},[r,a,s,l,function(c){c.key!=="Enter"||c.shiftKey||c.ctrlKey||c.metaKey?c.key==="Escape"&&(c.preventDefault(),c.stopPropagation(),e(1,a=i.description),s==null||s.blur()):(c.preventDefault(),c.stopPropagation(),l())},i,o,function(c){s=c,e(2,s)},function(c){a=c,e(1,a)}]}class bc extends st{constructor(t){super(),nt(this,t,vc,mc,it,{task:5,taskStore:6,editable:0})}}function _c(n){let t,e;const s=[{size:n[0]},{variant:"ghost"},{color:n[6]},{disabled:n[4]},n[13]];let i={$$slots:{default:[yc]},$$scope:{ctx:n}};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return t=new Js({props:i}),t.$on("click",n[19]),t.$on("keyup",n[20]),t.$on("keydown",n[21]),t.$on("mousedown",n[22]),t.$on("mouseover",n[23]),t.$on("focus",n[24]),t.$on("mouseleave",n[25]),t.$on("blur",n[26]),t.$on("contextmenu",n[27]),{c(){F(t.$$.fragment)},m(o,r){O(t,o,r),e=!0},p(o,r){const a=8273&r[0]?Vt(s,[1&r[0]&&{size:o[0]},s[1],64&r[0]&&{color:o[6]},16&r[0]&&{disabled:o[4]},8192&r[0]&&yi(o[13])]):{};1025&r[0]|2&r[1]&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){e||(k(t.$$.fragment,o),e=!0)},o(o){$(t.$$.fragment,o),e=!1},d(o){N(t,o)}}}function wc(n){let t,e,s;return e=new ki({props:{content:n[8],$$slots:{default:[Tc]},$$scope:{ctx:n}}}),{c(){t=pt("div"),F(e.$$.fragment),y(t,"class","c-task-icon-button")},m(i,o){R(i,t,o),O(e,t,null),s=!0},p(i,o){const r={};256&o[0]&&(r.content=i[8]),9905&o[0]|2&o[1]&&(r.$$scope={dirty:o,ctx:i}),e.$set(r)},i(i){s||(k(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&E(t),N(e)}}}function yc(n){let t,e,s;var i=n[10];function o(r,a){return{props:{size:r[0]}}}return i&&(t=Ze(i,o(n))),{c(){t&&F(t.$$.fragment),e=Yt()},m(r,a){t&&O(t,r,a),R(r,e,a),s=!0},p(r,a){if(1024&a[0]&&i!==(i=r[10])){if(t){It();const l=t;$(l.$$.fragment,1,0,()=>{N(l,1)}),Mt()}i?(t=Ze(i,o(r)),F(t.$$.fragment),k(t.$$.fragment,1),O(t,e.parentNode,e)):t=null}else if(i){const l={};1&a[0]&&(l.size=r[0]),t.$set(l)}},i(r){s||(t&&k(t.$$.fragment,r),s=!0)},o(r){t&&$(t.$$.fragment,r),s=!1},d(r){r&&E(e),t&&N(t,r)}}}function kc(n){let t,e,s;var i=n[10];function o(r,a){return{props:{size:r[0]}}}return i&&(t=Ze(i,o(n))),{c(){t&&F(t.$$.fragment),e=Yt()},m(r,a){t&&O(t,r,a),R(r,e,a),s=!0},p(r,a){if(1024&a[0]&&i!==(i=r[10])){if(t){It();const l=t;$(l.$$.fragment,1,0,()=>{N(l,1)}),Mt()}i?(t=Ze(i,o(r)),F(t.$$.fragment),k(t.$$.fragment,1),O(t,e.parentNode,e)):t=null}else if(i){const l={};1&a[0]&&(l.size=r[0]),t.$set(l)}},i(r){s||(t&&k(t.$$.fragment,r),s=!0)},o(r){t&&$(t.$$.fragment,r),s=!1},d(r){r&&E(e),t&&N(t,r)}}}function xc(n){let t,e,s;var i=n[9];function o(r,a){return{props:{size:r[0]}}}return i&&(t=Ze(i,o(n))),{c(){t&&F(t.$$.fragment),e=Yt()},m(r,a){t&&O(t,r,a),R(r,e,a),s=!0},p(r,a){if(512&a[0]&&i!==(i=r[9])){if(t){It();const l=t;$(l.$$.fragment,1,0,()=>{N(l,1)}),Mt()}i?(t=Ze(i,o(r)),F(t.$$.fragment),k(t.$$.fragment,1),O(t,e.parentNode,e)):t=null}else if(i){const l={};1&a[0]&&(l.size=r[0]),t.$set(l)}},i(r){s||(t&&k(t.$$.fragment,r),s=!0)},o(r){t&&$(t.$$.fragment,r),s=!1},d(r){r&&E(e),t&&N(t,r)}}}function $c(n){let t,e,s,i;const o=[xc,kc],r=[];function a(l,c){return l[5]?0:1}return t=a(n),e=r[t]=o[t](n),{c(){e.c(),s=Yt()},m(l,c){r[t].m(l,c),R(l,s,c),i=!0},p(l,c){let d=t;t=a(l),t===d?r[t].p(l,c):(It(),$(r[d],1,1,()=>{r[d]=null}),Mt(),e=r[t],e?e.p(l,c):(e=r[t]=o[t](l),e.c()),k(e,1),e.m(s.parentNode,s))},i(l){i||(k(e),i=!0)},o(l){$(e),i=!1},d(l){l&&E(s),r[t].d(l)}}}function Tc(n){let t,e;const s=[{size:n[0]},{variant:"ghost"},{color:n[7]},{disabled:n[4]},n[13]];let i={$$slots:{default:[$c]},$$scope:{ctx:n}};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return t=new Js({props:i}),t.$on("click",n[12]),t.$on("mouseover",n[17]),t.$on("mouseleave",n[18]),{c(){F(t.$$.fragment)},m(o,r){O(t,o,r),e=!0},p(o,r){const a=8337&r[0]?Vt(s,[1&r[0]&&{size:o[0]},s[1],128&r[0]&&{color:o[7]},16&r[0]&&{disabled:o[4]},8192&r[0]&&yi(o[13])]):{};1569&r[0]|2&r[1]&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){e||(k(t.$$.fragment,o),e=!0)},o(o){$(t.$$.fragment,o),e=!1},d(o){N(t,o)}}}function Sc(n){let t,e,s,i;const o=[wc,_c],r=[];function a(l,c){return l[3]&&l[1]&&l[2]?0:1}return t=a(n),e=r[t]=o[t](n),{c(){e.c(),s=Yt()},m(l,c){r[t].m(l,c),R(l,s,c),i=!0},p(l,c){let d=t;t=a(l),t===d?r[t].p(l,c):(It(),$(r[d],1,1,()=>{r[d]=null}),Mt(),e=r[t],e?e.p(l,c):(e=r[t]=o[t](l),e.c()),k(e,1),e.m(s.parentNode,s))},i(l){i||(k(e),i=!0)},o(l){$(e),i=!1},d(l){l&&E(s),r[t].d(l)}}}function Cc(n,t,e){let s,i,o,r,a,l,c,d;const p=["taskState","size","taskUuid","taskStore","interactive","disabled"];let f,g=Tn(t,p),b=z;n.$$.on_destroy.push(()=>b());let{taskState:m}=t,{size:_=1}=t,{taskUuid:v}=t,{taskStore:x}=t,{interactive:A=!0}=t,{disabled:L=!1}=t,w=!1;async function K(){if(!v||!x||L)return;const S=f==null?void 0:f.get(v);S&&(await x.updateTask(v,{state:B.IN_PROGRESS},xt.USER),await x.runHydratedTask(S))}async function V(){v&&x&&!L&&await x.updateTask(v,{state:B.COMPLETE},xt.USER)}async function M(){v&&x&&!L&&await x.updateTask(v,{state:B.NOT_STARTED},xt.USER)}return n.$$set=S=>{t=W(W({},t),ut(S)),e(13,g=Tn(t,p)),"taskState"in S&&e(14,m=S.taskState),"size"in S&&e(0,_=S.size),"taskUuid"in S&&e(1,v=S.taskUuid),"taskStore"in S&&e(2,x=S.taskStore),"interactive"in S&&e(3,A=S.interactive),"disabled"in S&&e(4,L=S.disabled)},n.$$.update=()=>{4&n.$$.dirty[0]&&(e(11,s=x==null?void 0:x.uuidToTask),b(),b=Go(s,S=>e(28,f=S))),16384&n.$$.dirty[0]&&e(15,i=m===B.NOT_STARTED?{currentState:B.NOT_STARTED,nextState:B.IN_PROGRESS,icon:aa,color:"accent",tooltip:"Run task",handler:K}:m===B.IN_PROGRESS?{currentState:B.IN_PROGRESS,nextState:B.COMPLETE,icon:Ls(B.COMPLETE),color:"success",tooltip:"Complete task",handler:V}:m===B.COMPLETE||m===B.CANCELLED?{currentState:m,nextState:B.NOT_STARTED,icon:Ls(B.NOT_STARTED),color:"accent",tooltip:"Mark as Not Started",handler:M}:{currentState:m,nextState:B.NOT_STARTED,icon:Ls(B.NOT_STARTED),color:"neutral",tooltip:"Mark as Not Started",handler:M}),32768&n.$$.dirty[0]&&e(10,o=Ls(i.currentState)),32768&n.$$.dirty[0]&&e(6,r=fi(i.currentState)),32768&n.$$.dirty[0]&&e(9,a=i.icon),32768&n.$$.dirty[0]&&e(16,l=i.color),32768&n.$$.dirty[0]&&e(8,c=i.tooltip),65632&n.$$.dirty[0]&&e(7,d=w?l:r)},[_,v,x,A,L,w,r,d,c,a,o,s,async function(){await i.handler()},g,m,i,l,()=>e(5,w=!0),()=>e(5,w=!1),function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)},function(S){vt.call(this,n,S)}]}class Ec extends st{constructor(t){super(),nt(this,t,Cc,Sc,it,{taskState:14,size:0,taskUuid:1,taskStore:2,interactive:3,disabled:4},null,[-1,-1])}}function Ic(n){let t,e,s,i,o,r;const a=[{size:n[2]},{variant:n[1]},{color:n[3]},{placeholder:n[0]},n[11]];function l(p){n[18](p)}function c(p){n[19](p)}let d={};for(let p=0;p<a.length;p+=1)d=W(d,a[p]);return n[7]!==void 0&&(d.textInput=n[7]),n[6]!==void 0&&(d.value=n[6]),e=new oa({props:d}),Fe.push(()=>ys(e,"textInput",l)),Fe.push(()=>ys(e,"value",c)),e.$on("keydown",n[10]),e.$on("focus",n[9]),e.$on("blur",n[20]),e.$on("keydown",n[21]),e.$on("click",n[22]),e.$on("blur",n[23]),e.$on("focus",n[24]),{c(){t=pt("div"),F(e.$$.fragment),y(t,"class",o="c-editable-text "+n[4]+" svelte-jooyia")},m(p,f){R(p,t,f),O(e,t,null),n[25](t),r=!0},p(p,[f]){const g=2063&f?Vt(a,[4&f&&{size:p[2]},2&f&&{variant:p[1]},8&f&&{color:p[3]},1&f&&{placeholder:p[0]},2048&f&&yi(p[11])]):{};!s&&128&f&&(s=!0,g.textInput=p[7],ks(()=>s=!1)),!i&&64&f&&(i=!0,g.value=p[6],ks(()=>i=!1)),e.$set(g),(!r||16&f&&o!==(o="c-editable-text "+p[4]+" svelte-jooyia"))&&y(t,"class",o)},i(p){r||(k(e.$$.fragment,p),r=!0)},o(p){$(e.$$.fragment,p),r=!1},d(p){p&&E(t),N(e),n[25](null)}}}function Mc(n,t,e){const s=["value","disabled","placeholder","clickToEdit","variant","size","color","class","editing","startEdit","acceptEdit","cancelEdit"];let i=Tn(t,s);const o=Fr();let r,a,{value:l=""}=t,{disabled:c=!1}=t,{placeholder:d=""}=t,{clickToEdit:p=!1}=t,{variant:f="surface"}=t,{size:g=2}=t,{color:b}=t,{class:m=""}=t,{editing:_=!1}=t,v=l;async function x(w){c||!r||_||(e(6,v=l),o("startEdit",{value:l}),r.focus(),w!=null&&w.selectAll&&(await Xs(),r==null||r.select()),e(13,_=!0))}function A(){const w=l,K=v.trim();w!==K?(e(12,l=K),o("acceptEdit",{oldValue:w,newValue:K}),document.activeElement===r&&(r==null||r.blur()),e(13,_=!1)):L()}function L(){e(6,v=l),o("cancelEdit",{value:l}),document.activeElement===r&&(r==null||r.blur()),e(13,_=!1)}return n.$$set=w=>{t=W(W({},t),ut(w)),e(11,i=Tn(t,s)),"value"in w&&e(12,l=w.value),"disabled"in w&&e(14,c=w.disabled),"placeholder"in w&&e(0,d=w.placeholder),"clickToEdit"in w&&e(15,p=w.clickToEdit),"variant"in w&&e(1,f=w.variant),"size"in w&&e(2,g=w.size),"color"in w&&e(3,b=w.color),"class"in w&&e(4,m=w.class),"editing"in w&&e(13,_=w.editing)},[d,f,g,b,m,A,v,r,a,function(){!p||c||_||x()},function(w){w.key==="Enter"?A():w.key==="Escape"&&L()},i,l,_,c,p,x,L,function(w){r=w,e(7,r)},function(w){v=w,e(6,v)},()=>A(),function(w){vt.call(this,n,w)},function(w){vt.call(this,n,w)},function(w){vt.call(this,n,w)},function(w){vt.call(this,n,w)},function(w){Fe[w?"unshift":"push"](()=>{a=w,e(8,a)})}]}class Ac extends st{constructor(t){super(),nt(this,t,Mc,Ic,it,{value:12,disabled:14,placeholder:0,clickToEdit:15,variant:1,size:2,color:3,class:4,editing:13,startEdit:16,acceptEdit:5,cancelEdit:17})}get startEdit(){return this.$$.ctx[16]}get acceptEdit(){return this.$$.ctx[5]}get cancelEdit(){return this.$$.ctx[17]}}function Dc(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let o=0;o<s.length;o+=1)i=W(i,s[o]);return{c(){t=ct("svg"),e=new $e(!0),this.h()},l(o){t=Te(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=Se(t);e=Ce(r,!0),r.forEach(E),this.h()},h(){e.a=null,bt(t,i)},m(o,r){Ee(o,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M0 96c0-35.3 28.7-64 64-64h384c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64H152v96c0 22.1 17.9 40 40 40h32v-8c0-35.3 28.7-64 64-64h160c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64H288c-35.3 0-64-28.7-64-64v-8h-32c-48.6 0-88-39.4-88-88v-96H64c-35.3 0-64-28.7-64-64zm448 240H288c-8.8 0-16 7.2-16 16v64c0 8.8 7.2 16 16 16h160c8.8 0 16-7.2 16-16v-64c0-8.8-7.2-16-16-16"/>',t)},p(o,[r]){bt(t,i=Vt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&o[0]]))},i:z,o:z,d(o){o&&E(t)}}}function Rc(n,t,e){return n.$$set=s=>{e(0,t=W(W({},t),ut(s)))},[t=ut(t)]}class Fc extends st{constructor(t){super(),nt(this,t,Rc,Dc,it,{})}}function Eo(n,t,e){const s=n.slice();return s[16]=t[e],s}function Oc(n){let t,e;return t=new Al({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Nc(n){let t,e;return t=new Ho({props:{size:1}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Lc(n){let t,e,s,i;const o=[Nc,Oc],r=[];function a(l,c){return l[5]?0:1}return t=a(n),e=r[t]=o[t](n),{c(){e.c(),s=Yt()},m(l,c){r[t].m(l,c),R(l,s,c),i=!0},p(l,c){let d=t;t=a(l),t!==d&&(It(),$(r[d],1,1,()=>{r[d]=null}),Mt(),e=r[t],e||(e=r[t]=o[t](l),e.c()),k(e,1),e.m(s.parentNode,s))},i(l){i||(k(e),i=!0)},o(l){$(e),i=!1},d(l){l&&E(s),r[t].d(l)}}}function Pc(n){let t,e;return t=new Js({props:{size:1,variant:"ghost",color:"neutral",disabled:!n[1]||n[5],class:"c-task-tree-item-actions__trigger",$$slots:{default:[Lc]},$$scope:{ctx:n}}}),t.$on("click",n[13]),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};34&i&&(o.disabled=!s[1]||s[5]),524320&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Uc(n){let t,e;return t=new ki({props:{content:"More actions",triggerOn:[$n.Hover],$$slots:{default:[Pc]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};524326&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function zc(n){let t;return{c(){t=ce("Update Status")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function qc(n){let t,e,s=ao(n[16])+"";return{c(){t=ce(s),e=Ft()},m(i,o){R(i,t,o),R(i,e,o)},p(i,o){8&o&&s!==(s=ao(i[16])+"")&&wi(t,s)},d(i){i&&(E(t),E(e))}}}function Hc(n){let t,e;return t=new Hl({props:{slot:"iconLeft",taskState:n[16],size:1}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};8&i&&(o.taskState=s[16]),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Io(n){let t,e;function s(){return n[14](n[16])}return t=new se.Item({props:{onSelect:s,highlight:n[16]===n[0].state,disabled:!n[1]||n[16]===n[0].state,$$slots:{iconLeft:[Hc],default:[qc]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(i,o){O(t,i,o),e=!0},p(i,o){n=i;const r={};8&o&&(r.onSelect=s),9&o&&(r.highlight=n[16]===n[0].state),11&o&&(r.disabled=!n[1]||n[16]===n[0].state),524296&o&&(r.$$scope={dirty:o,ctx:n}),t.$set(r)},i(i){e||(k(t.$$.fragment,i),e=!0)},o(i){$(t.$$.fragment,i),e=!1},d(i){N(t,i)}}}function Bc(n){let t;return{c(){t=ce("Add")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function Gc(n){let t;return{c(){t=ce("Add Task After")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function jc(n){let t,e;return t=new Vo({props:{slot:"iconLeft"}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p:z,i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Wc(n){let t;return{c(){t=ce("Add Child Task")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function Vc(n){let t,e;return t=new Fc({props:{slot:"iconLeft"}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p:z,i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Yc(n){let t;return{c(){t=ce("Export")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function Xc(n){let t,e;return t=new se.Item({props:{onSelect:n[7],disabled:n[5]||!n[1],$$slots:{iconLeft:[Kc],default:[Qc]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};34&i&&(o.disabled=s[5]||!s[1]),524288&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Zc(n){let t,e;return t=new se.Item({props:{onSelect:n[8],disabled:n[5]||!n[1],$$slots:{iconLeft:[td],default:[Jc]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};34&i&&(o.disabled=s[5]||!s[1]),524288&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Qc(n){let t;return{c(){t=ce("Export Task")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function Kc(n){let t,e;return t=new pr({props:{slot:"iconLeft"}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p:z,i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Jc(n){let t;return{c(){t=ce("Export Task Tree")},m(e,s){R(e,t,s)},d(e){e&&E(t)}}}function td(n){let t,e;return t=new pr({props:{slot:"iconLeft"}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p:z,i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function ed(n){let t,e,s,i,o,r,a,l,c,d,p,f,g,b,m,_,v,x,A;t=new se.Label({props:{$$slots:{default:[zc]},$$scope:{ctx:n}}});let L=ws(n[3]),w=[];for(let T=0;T<L.length;T+=1)w[T]=Io(Eo(n,L,T));const K=T=>$(w[T],1,1,()=>{w[T]=null});i=new se.Separator({}),r=new se.Label({props:{$$slots:{default:[Bc]},$$scope:{ctx:n}}}),l=new se.Item({props:{onSelect:n[9],disabled:n[5]||!n[1],$$slots:{iconLeft:[jc],default:[Gc]},$$scope:{ctx:n}}}),d=new se.Item({props:{onSelect:n[10],disabled:n[5]||!n[1],$$slots:{iconLeft:[Vc],default:[Wc]},$$scope:{ctx:n}}}),f=new se.Separator({}),b=new se.Label({props:{$$slots:{default:[Yc]},$$scope:{ctx:n}}});const V=[Zc,Xc],M=[];function S(T,P){return T[4]?0:1}return _=S(n),v=M[_]=V[_](n),{c(){F(t.$$.fragment),e=Ft();for(let T=0;T<w.length;T+=1)w[T].c();s=Ft(),F(i.$$.fragment),o=Ft(),F(r.$$.fragment),a=Ft(),F(l.$$.fragment),c=Ft(),F(d.$$.fragment),p=Ft(),F(f.$$.fragment),g=Ft(),F(b.$$.fragment),m=Ft(),v.c(),x=Yt()},m(T,P){O(t,T,P),R(T,e,P);for(let $t=0;$t<w.length;$t+=1)w[$t]&&w[$t].m(T,P);R(T,s,P),O(i,T,P),R(T,o,P),O(r,T,P),R(T,a,P),O(l,T,P),R(T,c,P),O(d,T,P),R(T,p,P),O(f,T,P),R(T,g,P),O(b,T,P),R(T,m,P),M[_].m(T,P),R(T,x,P),A=!0},p(T,P){const $t={};if(524288&P&&($t.$$scope={dirty:P,ctx:T}),t.$set($t),2059&P){let J;for(L=ws(T[3]),J=0;J<L.length;J+=1){const tt=Eo(T,L,J);w[J]?(w[J].p(tt,P),k(w[J],1)):(w[J]=Io(tt),w[J].c(),k(w[J],1),w[J].m(s.parentNode,s))}for(It(),J=L.length;J<w.length;J+=1)K(J);Mt()}const Pt={};524288&P&&(Pt.$$scope={dirty:P,ctx:T}),r.$set(Pt);const Ot={};34&P&&(Ot.disabled=T[5]||!T[1]),524288&P&&(Ot.$$scope={dirty:P,ctx:T}),l.$set(Ot);const Nt={};34&P&&(Nt.disabled=T[5]||!T[1]),524288&P&&(Nt.$$scope={dirty:P,ctx:T}),d.$set(Nt);const Ie={};524288&P&&(Ie.$$scope={dirty:P,ctx:T}),b.$set(Ie);let Q=_;_=S(T),_===Q?M[_].p(T,P):(It(),$(M[Q],1,1,()=>{M[Q]=null}),Mt(),v=M[_],v?v.p(T,P):(v=M[_]=V[_](T),v.c()),k(v,1),v.m(x.parentNode,x))},i(T){if(!A){k(t.$$.fragment,T);for(let P=0;P<L.length;P+=1)k(w[P]);k(i.$$.fragment,T),k(r.$$.fragment,T),k(l.$$.fragment,T),k(d.$$.fragment,T),k(f.$$.fragment,T),k(b.$$.fragment,T),k(v),A=!0}},o(T){$(t.$$.fragment,T),w=w.filter(Boolean);for(let P=0;P<w.length;P+=1)$(w[P]);$(i.$$.fragment,T),$(r.$$.fragment,T),$(l.$$.fragment,T),$(d.$$.fragment,T),$(f.$$.fragment,T),$(b.$$.fragment,T),$(v),A=!1},d(T){T&&(E(e),E(s),E(o),E(a),E(c),E(p),E(g),E(m),E(x)),N(t,T),Or(w,T),N(i,T),N(r,T),N(l,T),N(d,T),N(f,T),N(b,T),M[_].d(T)}}}function sd(n){let t,e,s,i;return t=new se.Trigger({props:{$$slots:{default:[Uc]},$$scope:{ctx:n}}}),s=new se.Content({props:{size:1,side:"bottom",align:"end",$$slots:{default:[ed]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment),e=Ft(),F(s.$$.fragment)},m(o,r){O(t,o,r),R(o,e,r),O(s,o,r),i=!0},p(o,r){const a={};524326&r&&(a.$$scope={dirty:r,ctx:o}),t.$set(a);const l={};524347&r&&(l.$$scope={dirty:r,ctx:o}),s.$set(l)},i(o){i||(k(t.$$.fragment,o),k(s.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),$(s.$$.fragment,o),i=!1},d(o){o&&E(e),N(t,o),N(s,o)}}}function nd(n){let t,e;return t=new se.Root({props:{triggerOn:[$n.Hover,$n.Click],open:n[2],onHoverEnd:n[15],$$slots:{default:[sd]},$$scope:{ctx:n}}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,[i]){const o={};4&i&&(o.open=s[2]),4&i&&(o.onHoverEnd=s[15]),524351&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function id(n,t,e){let s,i,o,{task:r}=t,{taskStore:a}=t,{editable:l=!0}=t;const{isImportingExporting:c}=a;jo(n,c,f=>e(5,o=f));let d=!1;async function p(f){l&&f!==r.state&&(await a.updateTask(r.uuid,{state:f},xt.USER),e(2,d=!1))}return n.$$set=f=>{"task"in f&&e(0,r=f.task),"taskStore"in f&&e(12,a=f.taskStore),"editable"in f&&e(1,l=f.editable)},n.$$.update=()=>{1&n.$$.dirty&&e(4,s=r.subTasksData&&r.subTasksData.length>0)},e(3,i=Object.values(B)),[r,l,d,i,s,o,c,async function(){!o&&l&&(await a.exportTask(r,{baseName:r.name}),e(2,d=!1))},async function(){!o&&l&&(await a.exportTask(r,{baseName:`${r.name}_tree`}),e(2,d=!1))},async function(){if(o||!l)return;const f={uuid:crypto.randomUUID(),name:"",description:"",state:B.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:xt.USER},g=await a.addNewTaskAfter(r.uuid,f);if(g){await Xs();const b=document.getElementById(`task-${g.uuid}`),m=b==null?void 0:b.querySelector(".c-task-tree-item__name-editable"),_=m==null?void 0:m.querySelector("input");_==null||_.focus()}e(2,d=!1)},async function(){if(o||!l)return;const f=await a.createTask("","",r.uuid);if(f){await Xs();const g=document.getElementById(`task-${f}`),b=g==null?void 0:g.querySelector(".c-task-tree-item__name-editable"),m=b==null?void 0:b.querySelector("input");m==null||m.focus()}e(2,d=!1)},p,a,()=>e(2,d=!d),f=>p(f),()=>e(2,d=!1)]}class od extends st{constructor(t){super(),nt(this,t,id,nd,it,{task:0,taskStore:12,editable:1})}}function rd(n){let t,e;return t=new Xr({}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function ad(n){let t,e;return t=new Js({props:{size:1,variant:"ghost",color:"error",disabled:!n[1],class:"c-task-action-button c-task-action-button--delete",$$slots:{default:[rd]},$$scope:{ctx:n}}}),t.$on("click",n[4]),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};2&i&&(o.disabled=!s[1]),128&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function Mo(n){let t,e;return t=new od({props:{task:n[2],taskStore:n[0],editable:n[1]}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p(s,i){const o={};4&i&&(o.task=s[2]),1&i&&(o.taskStore=s[0]),2&i&&(o.editable=s[1]),t.$set(o)},i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function ld(n){let t,e,s,i;e=new ki({props:{content:"Delete task",triggerOn:[$n.Hover],$$slots:{default:[ad]},$$scope:{ctx:n}}});let o=n[2]&&Mo(n);return{c(){t=pt("div"),F(e.$$.fragment),s=Ft(),o&&o.c(),y(t,"class","c-task-action-buttons svelte-55fcbs")},m(r,a){R(r,t,a),O(e,t,null),ht(t,s),o&&o.m(t,null),i=!0},p(r,[a]){const l={};130&a&&(l.$$scope={dirty:a,ctx:r}),e.$set(l),r[2]?o?(o.p(r,a),4&a&&k(o,1)):(o=Mo(r),o.c(),k(o,1),o.m(t,null)):o&&(It(),$(o,1,1,()=>{o=null}),Mt())},i(r){i||(k(e.$$.fragment,r),k(o),i=!0)},o(r){$(e.$$.fragment,r),$(o),i=!1},d(r){r&&E(t),N(e),o&&o.d()}}}function cd(n,t,e){let s,i,o,r=z;n.$$.on_destroy.push(()=>r());let{taskUuid:a}=t,{taskStore:l}=t,{editable:c=!0}=t;return n.$$set=d=>{"taskUuid"in d&&e(5,a=d.taskUuid),"taskStore"in d&&e(0,l=d.taskStore),"editable"in d&&e(1,c=d.editable)},n.$$.update=()=>{1&n.$$.dirty&&(e(3,s=l.uuidToTask),r(),r=Go(s,d=>e(6,o=d))),96&n.$$.dirty&&e(2,i=o.get(a))},[l,c,i,s,async function(){c&&await l.deleteTask(a)},a,o]}class dd extends st{constructor(t){super(),nt(this,t,cd,ld,it,{taskUuid:5,taskStore:0,editable:1})}}function ud(n,t=new Set){const e={...n,isVisible:!1,subTasksData:[]};let s=!1;if(n.subTasksData&&n.subTasksData.length>0){const o=[];for(const r of n.subTasksData){const a=ud(r,t);o.push(a),a.isVisible&&(s=!0)}e.subTasksData=o}const i=function(o,r){return r.size===0||r.has(o.state)}(n,t);return e.isVisible=i||s,e}function Ao(n,t,e){const s=n.slice();return s[19]=t[e],s}function Do(n,t,e){const s=n.slice();return s[19]=t[e],s}function hd(n){let t,e,s,i;function o(l){n[14](l)}function r(l){n[15](l)}let a={class:"c-task-tree-item",item:n[0],id:`task-${n[0].uuid}`,hasNestedItems:!!n[0].subTasksData&&n[0].subTasksData.length>0,disabled:!n[2],$$slots:{contents:[bd],actions:[md],"header-contents":[gd],handle:[fd]},$$scope:{ctx:n}};return n[5]!==void 0&&(a.element=n[5]),n[6]!==void 0&&(a.expanded=n[6]),t=new Kl({props:a}),Fe.push(()=>ys(t,"element",o)),Fe.push(()=>ys(t,"expanded",r)),{c(){F(t.$$.fragment)},m(l,c){O(t,l,c),i=!0},p(l,c){const d={};1&c&&(d.item=l[0]),1&c&&(d.id=`task-${l[0].uuid}`),1&c&&(d.hasNestedItems=!!l[0].subTasksData&&l[0].subTasksData.length>0),4&c&&(d.disabled=!l[2]),16777687&c&&(d.$$scope={dirty:c,ctx:l}),!e&&32&c&&(e=!0,d.element=l[5],ks(()=>e=!1)),!s&&64&c&&(s=!0,d.expanded=l[6],ks(()=>s=!1)),t.$set(d)},i(l){i||(k(t.$$.fragment,l),i=!0)},o(l){$(t.$$.fragment,l),i=!1},d(l){N(t,l)}}}function pd(n){let t,e,s;return e=new $r({props:{id:`list-${n[0].uuid}`,items:n[7],onEnd:n[12],disabled:!n[2],$$slots:{items:[_d,({items:i})=>({18:i}),({items:i})=>i?262144:0]},$$scope:{ctx:n}}}),{c(){t=pt("div"),F(e.$$.fragment),y(t,"class","c-task-tree-root-children svelte-ugjowu")},m(i,o){R(i,t,o),O(e,t,null),s=!0},p(i,o){const r={};1&o&&(r.id=`list-${i[0].uuid}`),128&o&&(r.items=i[7]),4&o&&(r.disabled=!i[2]),17039366&o&&(r.$$scope={dirty:o,ctx:i}),e.$set(r)},i(i){s||(k(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&E(t),N(e)}}}function fd(n){let t,e;return t=new ec({props:{slot:"handle",width:"8px",height:"10px"}}),{c(){F(t.$$.fragment)},m(s,i){O(t,s,i),e=!0},p:z,i(s){e||(k(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){N(t,s)}}}function gd(n){let t,e,s,i,o,r,a;s=new Ec({props:{taskState:n[0].state,taskUuid:n[0].uuid,taskStore:n[1],disabled:!n[2],size:1}});let l={class:"c-task-tree-item__name-editable",value:n[0].name,placeholder:"Name this task...",size:1,disabled:!n[2],clickToEdit:!0};return r=new Ac({props:l}),n[13](r),r.$on("keydown",n[11]),r.$on("blur",n[10]),{c(){t=pt("div"),e=pt("div"),F(s.$$.fragment),i=Ft(),o=pt("div"),F(r.$$.fragment),y(e,"class","c-task-tree-item__status-cell svelte-ugjowu"),y(o,"class","c-task-tree-item__name svelte-ugjowu"),ae(o,"c-task-tree-item__text--cancelled",n[8]),y(t,"slot","header-contents"),y(t,"class","c-task-tree-item__header svelte-ugjowu")},m(c,d){R(c,t,d),ht(t,e),O(s,e,null),ht(t,i),ht(t,o),O(r,o,null),a=!0},p(c,d){const p={};1&d&&(p.taskState=c[0].state),1&d&&(p.taskUuid=c[0].uuid),2&d&&(p.taskStore=c[1]),4&d&&(p.disabled=!c[2]),s.$set(p);const f={};1&d&&(f.value=c[0].name),4&d&&(f.disabled=!c[2]),r.$set(f),(!a||256&d)&&ae(o,"c-task-tree-item__text--cancelled",c[8])},i(c){a||(k(s.$$.fragment,c),k(r.$$.fragment,c),a=!0)},o(c){$(s.$$.fragment,c),$(r.$$.fragment,c),a=!1},d(c){c&&E(t),N(s),n[13](null),N(r)}}}function md(n){let t,e,s;return e=new dd({props:{taskUuid:n[0].uuid,taskStore:n[1],editable:n[2]}}),{c(){t=pt("div"),F(e.$$.fragment),y(t,"class","c-task-tree-item__action-buttons svelte-ugjowu"),y(t,"slot","actions")},m(i,o){R(i,t,o),O(e,t,null),s=!0},p(i,o){const r={};1&o&&(r.taskUuid=i[0].uuid),2&o&&(r.taskStore=i[1]),4&o&&(r.editable=i[2]),e.$set(r)},i(i){s||(k(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&E(t),N(e)}}}function Ro(n){let t,e,s;return e=new $r({props:{id:`list-${n[0].uuid}`,items:n[7],disabled:!n[2],useHandle:!1,onEnd:n[12],$$slots:{items:[vd,({items:i})=>({18:i}),({items:i})=>i?262144:0]},$$scope:{ctx:n}}}),{c(){t=pt("div"),F(e.$$.fragment),y(t,"class","c-task-tree-item__subtasks")},m(i,o){R(i,t,o),O(e,t,null),s=!0},p(i,o){const r={};1&o&&(r.id=`list-${i[0].uuid}`),128&o&&(r.items=i[7]),4&o&&(r.disabled=!i[2]),17039366&o&&(r.$$scope={dirty:o,ctx:i}),e.$set(r)},i(i){s||(k(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&E(t),N(e)}}}function Fo(n,t){let e,s,i;return s=new Tr({props:{taskStore:t[1],task:t[19],editable:t[2],isRootTask:!1}}),{key:n,first:null,c(){e=Yt(),F(s.$$.fragment),this.first=e},m(o,r){R(o,e,r),O(s,o,r),i=!0},p(o,r){t=o;const a={};2&r&&(a.taskStore=t[1]),262144&r&&(a.task=t[19]),4&r&&(a.editable=t[2]),s.$set(a)},i(o){i||(k(s.$$.fragment,o),i=!0)},o(o){$(s.$$.fragment,o),i=!1},d(o){o&&E(e),N(s,o)}}}function vd(n){let t,e,s=[],i=new Map,o=ws(n[18]);const r=a=>a[19].uuid;for(let a=0;a<o.length;a+=1){let l=Ao(n,o,a),c=r(l);i.set(c,s[a]=Fo(c,l))}return{c(){for(let a=0;a<s.length;a+=1)s[a].c();t=Yt()},m(a,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(a,l);R(a,t,l),e=!0},p(a,l){262150&l&&(o=ws(a[18]),It(),s=zo(s,l,r,1,a,o,i,t.parentNode,qo,Fo,t,Ao),Mt())},i(a){if(!e){for(let l=0;l<o.length;l+=1)k(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)$(s[l]);e=!1},d(a){a&&E(t);for(let l=0;l<s.length;l+=1)s[l].d(a)}}}function bd(n){let t,e,s,i,o;e=new bc({props:{task:n[0],taskStore:n[1],editable:n[2]}});let r=n[7]&&n[7].length>0&&n[6]&&Ro(n);return{c(){t=pt("div"),F(e.$$.fragment),s=Ft(),r&&r.c(),i=Yt(),y(t,"class","c-task-tree-item__details")},m(a,l){R(a,t,l),O(e,t,null),R(a,s,l),r&&r.m(a,l),R(a,i,l),o=!0},p(a,l){const c={};1&l&&(c.task=a[0]),2&l&&(c.taskStore=a[1]),4&l&&(c.editable=a[2]),e.$set(c),a[7]&&a[7].length>0&&a[6]?r?(r.p(a,l),192&l&&k(r,1)):(r=Ro(a),r.c(),k(r,1),r.m(i.parentNode,i)):r&&(It(),$(r,1,1,()=>{r=null}),Mt())},i(a){o||(k(e.$$.fragment,a),k(r),o=!0)},o(a){$(e.$$.fragment,a),$(r),o=!1},d(a){a&&(E(t),E(s),E(i)),N(e),r&&r.d(a)}}}function Oo(n,t){let e,s,i;return s=new Tr({props:{taskStore:t[1],task:t[19],editable:t[2],isRootTask:!1}}),{key:n,first:null,c(){e=Yt(),F(s.$$.fragment),this.first=e},m(o,r){R(o,e,r),O(s,o,r),i=!0},p(o,r){t=o;const a={};2&r&&(a.taskStore=t[1]),262144&r&&(a.task=t[19]),4&r&&(a.editable=t[2]),s.$set(a)},i(o){i||(k(s.$$.fragment,o),i=!0)},o(o){$(s.$$.fragment,o),i=!1},d(o){o&&E(e),N(s,o)}}}function _d(n){let t,e,s=[],i=new Map,o=ws(n[18]);const r=a=>a[19].uuid;for(let a=0;a<o.length;a+=1){let l=Do(n,o,a),c=r(l);i.set(c,s[a]=Oo(c,l))}return{c(){for(let a=0;a<s.length;a+=1)s[a].c();t=Yt()},m(a,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(a,l);R(a,t,l),e=!0},p(a,l){262150&l&&(o=ws(a[18]),It(),s=zo(s,l,r,1,a,o,i,t.parentNode,qo,Oo,t,Do),Mt())},i(a){if(!e){for(let l=0;l<o.length;l+=1)k(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)$(s[l]);e=!1},d(a){a&&E(t);for(let l=0;l<s.length;l+=1)s[l].d(a)}}}function wd(n){let t,e,s,i;const o=[pd,hd],r=[];function a(l,c){return l[3]?0:l[0].isVisible?1:-1}return~(t=a(n))&&(e=r[t]=o[t](n)),{c(){e&&e.c(),s=Yt()},m(l,c){~t&&r[t].m(l,c),R(l,s,c),i=!0},p(l,[c]){let d=t;t=a(l),t===d?~t&&r[t].p(l,c):(e&&(It(),$(r[d],1,1,()=>{r[d]=null}),Mt()),~t?(e=r[t],e?e.p(l,c):(e=r[t]=o[t](l),e.c()),k(e,1),e.m(s.parentNode,s)):e=null)},i(l){i||(k(e),i=!0)},o(l){$(e),i=!1},d(l){l&&E(s),~t&&r[t].d(l)}}}function yd(n,t,e){let s,i,o,{task:r}=t,{taskStore:a=Nr(pi.key)}=t,{editable:l=!0}=t,{isRootTask:c=!0}=t;const{uuidToTask:d}=a;let p,f;jo(n,d,m=>e(16,o=m));let g=!1;async function b(m){const _=m.trim();_!==r.name&&_&&await a.updateTask(r.uuid,{name:_},xt.USER)}return n.$$set=m=>{"task"in m&&e(0,r=m.task),"taskStore"in m&&e(1,a=m.taskStore),"editable"in m&&e(2,l=m.editable),"isRootTask"in m&&e(3,c=m.isRootTask)},n.$$.update=()=>{1&n.$$.dirty&&e(8,s=r.state===B.CANCELLED),1&n.$$.dirty&&e(7,i=function(m){var _;return((_=m.subTasksData)==null?void 0:_.filter(v=>v.isVisible))||[]}(r))},[r,a,l,c,p,f,g,i,s,d,async function(m){var v;const _=(v=m.target)==null?void 0:v.value;await b(_||"")},async function(m){var _,v;if(!(m.shiftKey||m.ctrlKey||m.metaKey))switch(m.key){case"Enter":{const x=(_=m.target)==null?void 0:_.value;await b(x||"");const A=await a.addNewTaskAfter(r.uuid,{uuid:"new-task",name:"",description:"",state:B.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:xt.USER});if(!A)return;await Xs();const L=document.getElementById(`task-${A.uuid}`);if(!L)return;const w=L.querySelector(".c-task-tree-item__name-editable");if(!w)return;const K=w.querySelector("input");if(!K)return;K.focus(),K.select();break}case"Tab":{const x=(v=m.target)==null?void 0:v.value;await b(x||"");break}}},async m=>{var K,V,M,S,T,P,$t,Pt;if(!m.from.id||!m.to.id||!m.item.id||m.oldIndex===void 0||m.newIndex===void 0)return;const{fromParentTaskUuid:_,toParentTaskUuid:v,targetUuid:x}=function(Ot){return{fromParentTaskUuid:Ot.from.id.replace(/^list-/,""),toParentTaskUuid:Ot.to.id.replace(/^list-/,""),targetUuid:Ot.item.id.replace(/^task-/,"")}}(m);if(_===v&&m.oldIndex===m.newIndex)return;const A=o.get(_),L=o.get(v),w=o.get(x);return A&&L&&w?_!==v?(m.from.appendChild(m.item),(K=A==null?void 0:A.subTasks)==null||K.splice(m.oldIndex,1),(V=A==null?void 0:A.subTasksData)==null||V.splice(m.oldIndex,1),(M=L==null?void 0:L.subTasks)==null||M.splice(m.newIndex,0,x),(S=L==null?void 0:L.subTasksData)==null||S.splice(m.newIndex,0,w),await a.updateTask(A.uuid,{subTasks:A.subTasks},xt.USER),void await a.updateTask(L.uuid,{subTasks:L.subTasks},xt.USER)):((T=A==null?void 0:A.subTasks)==null||T.splice(m.oldIndex,1),(P=A==null?void 0:A.subTasks)==null||P.splice(m.newIndex,0,x),($t=A==null?void 0:A.subTasksData)==null||$t.splice(m.oldIndex,1),(Pt=A==null?void 0:A.subTasksData)==null||Pt.splice(m.newIndex,0,w),void await a.updateTask(A.uuid,{subTasks:A.subTasks},xt.USER)):void 0},function(m){Fe[m?"unshift":"push"](()=>{p=m,e(4,p)})},function(m){f=m,e(5,f)},function(m){g=m,e(6,g)}]}class Tr extends st{constructor(t){super(),nt(this,t,yd,wd,it,{task:0,taskStore:1,editable:2,isRootTask:3})}}function kd(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"fill-rule","evenodd"),y(e,"clip-rule","evenodd"),y(e,"d","M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"),y(e,"fill","currentColor"),y(t,"width","15"),y(t,"height","15"),y(t,"viewBox","0 0 15 15"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class cu extends st{constructor(t){super(),nt(this,t,null,kd,it,{})}}class du{static generateDiff(t,e,s,i){return la(t,e,s,i)}static generateDiffs(t){return ca(t)}static getDiffStats(t){return Ui(t)}static getDiffObjectStats(t){return Ui(t.diff)}static isNewFile(t){return da(t)}static isDeletedFile(t){return ua(t)}}function xd(n){let t,e;return{c(){t=ct("svg"),e=ct("path"),y(e,"d","M14.5 3H7.70996L6.85999 2.15002L6.51001 2H1.51001L1.01001 2.5V6.5V13.5L1.51001 14H14.51L15.01 13.5V9V3.5L14.5 3ZM13.99 11.49V13H1.98999V11.49V7.48999V7H6.47998L6.82996 6.84998L7.68994 5.98999H14V7.48999L13.99 11.49ZM13.99 5H7.48999L7.14001 5.15002L6.28003 6.01001H2V3.01001H6.29004L7.14001 3.85999L7.5 4.01001H14L13.99 5Z"),y(e,"fill","#C5C5C5"),y(t,"width","16"),y(t,"height","16"),y(t,"viewBox","0 0 16 16"),y(t,"fill","none"),y(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){R(s,t,i),ht(t,e)},p:z,i:z,o:z,d(s){s&&E(t)}}}class uu extends st{constructor(t){super(),nt(this,t,null,xd,it,{})}}export{Za as A,ao as B,Qa as C,Wd as D,ru as E,pr as F,Ja as G,ud as H,Tr as I,du as J,Yd as K,lu as L,nu as M,Zt as N,ga as O,uu as P,Yo as Q,hr as R,Vd as S,Jd as T,cu as U,ul as V,Qd as W,xt as X,Zd as Y,Ki as Z,tu as a,pl as b,qd as c,lt as d,zd as e,On as f,Hd as g,Bd as h,ps as i,Wa as j,Gd as k,bn as l,Kd as m,Ct as n,pi as o,Xd as p,fa as q,jd as r,eu as s,su as t,iu as u,ou as v,au as w,Al as x,Hl as y,B as z};
