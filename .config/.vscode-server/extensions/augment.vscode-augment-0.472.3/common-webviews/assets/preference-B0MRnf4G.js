import{S as T,i as V,s as J,V as f,D as b,c,a3 as z,e as S,f as r,a5 as F,n as L,h as W,a7 as $e,G as se,H as ie,a4 as ue,ac as ge,y as O,z as P,q as re,t as D,r as ce,u as B,B as H,an as ve,a1 as be,w as ee,x as te,A as ne,E as ye}from"./SpinnerAugment-JC8TPhVf.js";import{h as N,W as Q}from"./BaseButton-C1unVd78.js";import{aF as oe}from"./AugmentMessage-BNt-hbbl.js";import{C as we,S as ke}from"./folder-IxOJUoWR.js";import{M as xe}from"./TextTooltipAugment-WghC7pXE.js";import{s as Ce}from"./open-in-new-window-CkR7J3XO.js";import{M as qe}from"./index-CK0xjdO4.js";import"./pen-to-square-3TLxExQu.js";import"./Content-xvE836E_.js";import"./globals-D0QH3NT1.js";import"./lodash-l00D6itj.js";import"./rules-parser-D2d7xQ-G.js";import"./chat-types-NgqNgjwU.js";import"./file-paths-BcSg4gks.js";import"./diff-utils-Bh9TYMbV.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./IconButtonAugment-D_WR2I26.js";import"./index-DiI90jLk.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-bSDyFrZo.js";import"./await_block-C0teov-5.js";import"./ButtonAugment-PWVCCDdn.js";import"./expand-C7dSG_GJ.js";import"./index-Yat2JVWz.js";import"./CardAugment-D2Hs6UjS.js";import"./mcp-logo-TxRUeZhS.js";import"./github-C1jNNmyr.js";import"./ellipsis-ce3_p-Q7.js";import"./IconFilePath-BjUJjRTr.js";import"./LanguageIcon-CA8dtZ_C.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-D5YyJVGd.js";import"./MaterialIcon-D8Nb6HkU.js";import"./Filespan-DeFTcAEj.js";import"./chevron-down-B-gSyyd4.js";import"./terminal-BBUsFUTj.js";import"./types-B5Ac2hek.js";import"./types-Cgd-nZOV.js";import"./augment-logo-E1jEbeRV.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-BYCw5p2g.js";function pe(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){S(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&W(t)}}}function Ae(s){let t,n,e,i,l,o,p,h,d,u,A,y,$,C,k,w,m,M,q=s[1]&&pe(s);return{c(){t=f("div"),q&&q.c(),n=b(),e=f("div"),i=f("button"),i.textContent="A",l=b(),o=f("button"),o.textContent="A",p=b(),h=f("button"),h.textContent="A",d=b(),u=f("button"),u.textContent="=",A=b(),y=f("button"),y.textContent="B",$=b(),C=f("button"),C.textContent="B",k=b(),w=f("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),z(i,"highlighted",s[0]==="A3"),c(o,"type","button"),c(o,"class","button medium svelte-1894wv4"),z(o,"highlighted",s[0]==="A2"),c(h,"type","button"),c(h,"class","button small svelte-1894wv4"),z(h,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),z(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),z(y,"highlighted",s[0]==="B1"),c(C,"type","button"),c(C,"class","button medium svelte-1894wv4"),z(C,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),z(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,R){S(g,t,R),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,l),r(e,o),r(e,p),r(e,h),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,C),r(e,k),r(e,w),m||(M=[F(i,"click",s[3]),F(o,"click",s[4]),F(h,"click",s[5]),F(u,"click",s[6]),F(y,"click",s[7]),F(C,"click",s[8]),F(w,"click",s[9])],m=!0)},p(g,[R]){g[1]?q?q.p(g,R):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&R&&z(i,"highlighted",g[0]==="A3"),1&R&&z(o,"highlighted",g[0]==="A2"),1&R&&z(h,"highlighted",g[0]==="A1"),1&R&&z(u,"highlighted",g[0]==="="),1&R&&z(y,"highlighted",g[0]==="B1"),1&R&&z(C,"highlighted",g[0]==="B2"),1&R&&z(w,"highlighted",g[0]==="B3")},i:L,o:L,d(g){g&&W(t),q&&q.d(),m=!1,$e(M)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function l(o){n(0,e=o)}return s.$$set=o=>{"selected"in o&&n(0,e=o.selected),"question"in o&&n(1,i=o.question)},[e,i,l,()=>l("A3"),()=>l("A2"),()=>l("A1"),()=>l("="),()=>l("B1"),()=>l("B2"),()=>l("B3")]}class ae extends T{constructor(t){super(),V(this,t,Be,Ae,J,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){S(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&W(t)}}}function De(s){let t,n,e,i,l,o=s[1]&&de(s);return{c(){t=f("div"),o&&o.c(),n=b(),e=f("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,h){S(p,t,h),o&&o.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(l=F(e,"input",s[3]),i=!0)},p(p,[h]){p[1]?o?o.p(p,h):(o=de(p),o.c(),o.m(t,n)):o&&(o.d(1),o=null),4&h&&c(e,"placeholder",p[2]),1&h&&ue(e,p[0])},i:L,o:L,d(p){p&&W(t),o&&o.d(),i=!1,l()}}}function Me(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:l=""}=t;return s.$$set=o=>{"value"in o&&n(0,e=o.value),"question"in o&&n(1,i=o.question),"placeholder"in o&&n(2,l=o.placeholder)},[e,i,l,function(){e=this.value,n(0,e)}]}class Re extends T{constructor(t){super(),V(this,t,Me,De,J,{value:0,question:1,placeholder:2})}}function Se(s){let t,n,e,i;return{c(){t=f("button"),n=se(s[0]),c(t,"class","button svelte-2k5n")},m(l,o){S(l,t,o),r(t,n),e||(i=F(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(l,[o]){s=l,1&o&&ie(n,s[0])},i:L,o:L,d(l){l&&W(t),e=!1,i()}}}function We(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=l=>{"label"in l&&n(0,e=l.label),"onClick"in l&&n(1,i=l.onClick)},[e,i]}class Ie extends T{constructor(t){super(),V(this,t,We,Se,J,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=f("div"),n=se(s[1])},m(e,i){S(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&W(t)}}}function _e(s){let t,n,e,i,l,o,p,h,d=s[1]&&me(s);return{c(){t=f("div"),d&&d.c(),n=b(),e=f("label"),i=f("input"),l=b(),o=f("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(o,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){S(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,l),r(e,o),p||(h=F(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:L,o:L,d(u){u&&W(t),d&&d.d(),p=!1,h()}}}function ze(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=l=>{"isChecked"in l&&n(0,e=l.isChecked),"question"in l&&n(1,i=l.question)},[e,i,function(){e=this.checked,n(0,e)}]}class Ee extends T{constructor(t){super(),V(this,t,ze,_e,J,{isChecked:0,question:1})}}function Fe(s){let t;return{c(){t=f("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){S(n,t,e)},p:L,i:L,o:L,d(n){n&&W(t)}}}function Oe(s){let t,n,e,i,l,o,p,h,d,u,A,y,$,C,k,w,m;function M(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),ee.push(()=>te(t,"selected",M));let R={question:"Which response follows your instruction better?"};function K(a){s[14](a)}s[3]!==void 0&&(R.selected=s[3]),i=new ae({props:R}),ee.push(()=>te(i,"selected",g));let U={question:"Which response is better overall?"};function I(a){s[15](a)}s[1]!==void 0&&(U.selected=s[1]),p=new ae({props:U}),ee.push(()=>te(p,"selected",K));let _={question:s[9]};function j(a){s[16](a)}s[5]!==void 0&&(_.isChecked=s[5]),u=new Ee({props:_}),ee.push(()=>te(u,"isChecked",I));let X={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(X.value=s[4]),$=new Re({props:X}),ee.push(()=>te($,"value",j)),w=new Ie({props:{label:"Submit",onClick:s[10]}}),{c(){O(t.$$.fragment),e=b(),O(i.$$.fragment),o=b(),O(p.$$.fragment),d=b(),O(u.$$.fragment),y=b(),O($.$$.fragment),k=b(),O(w.$$.fragment)},m(a,x){P(t,a,x),S(a,e,x),P(i,a,x),S(a,o,x),P(p,a,x),S(a,d,x),P(u,a,x),S(a,y,x),P($,a,x),S(a,k,x),P(w,a,x),m=!0},p(a,x){const v={};!n&&4&x&&(n=!0,v.selected=a[2],ne(()=>n=!1)),t.$set(v);const E={};!l&&8&x&&(l=!0,E.selected=a[3],ne(()=>l=!1)),i.$set(E);const Y={};!h&&2&x&&(h=!0,Y.selected=a[1],ne(()=>h=!1)),p.$set(Y);const G={};512&x&&(G.question=a[9]),!A&&32&x&&(A=!0,G.isChecked=a[5],ne(()=>A=!1)),u.$set(G);const Z={};!C&&16&x&&(C=!0,Z.value=a[4],ne(()=>C=!1)),$.$set(Z)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){D(t.$$.fragment,a),D(i.$$.fragment,a),D(p.$$.fragment,a),D(u.$$.fragment,a),D($.$$.fragment,a),D(w.$$.fragment,a),m=!1},d(a){a&&(W(e),W(o),W(d),W(y),W(k)),H(t,a),H(i,a),H(p,a),H(u,a),H($,a),H(w,a)}}}function Pe(s){let t,n,e,i,l,o,p,h,d,u,A,y,$,C,k,w,m,M,q,g,R,K,U,I,_,j;l=new oe({props:{markdown:s[0].data.a.message}}),$=new oe({props:{markdown:s[8]}}),g=new oe({props:{markdown:s[7]}});const X=[Oe,Fe],a=[];function x(v,E){return v[6]?0:1}return I=x(s),_=a[I]=X[I](s),{c(){t=f("main"),n=f("div"),e=f("h1"),e.textContent="Input message",i=b(),O(l.$$.fragment),o=b(),p=f("hr"),h=b(),d=f("div"),u=f("div"),A=f("h1"),A.textContent="Option A",y=b(),O($.$$.fragment),C=b(),k=f("div"),w=b(),m=f("div"),M=f("h1"),M.textContent="Option B",q=b(),O(g.$$.fragment),R=b(),K=f("hr"),U=b(),_.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(M,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(K,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,E){S(v,t,E),r(t,n),r(n,e),r(n,i),P(l,n,null),r(n,o),r(n,p),r(n,h),r(n,d),r(d,u),r(u,A),r(u,y),P($,u,null),r(d,C),r(d,k),r(d,w),r(d,m),r(m,M),r(m,q),P(g,m,null),r(n,R),r(n,K),r(n,U),a[I].m(n,null),j=!0},p(v,[E]){const Y={};1&E&&(Y.markdown=v[0].data.a.message),l.$set(Y);const G={};256&E&&(G.markdown=v[8]),$.$set(G);const Z={};128&E&&(Z.markdown=v[7]),g.$set(Z);let le=I;I=x(v),I===le?a[I].p(v,E):(re(),D(a[le],1,1,()=>{a[le]=null}),ce(),_=a[I],_?_.p(v,E):(_=a[I]=X[I](v),_.c()),B(_,1),_.m(n,null))},i(v){j||(B(l.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(_),j=!0)},o(v){D(l.$$.fragment,v),D($.$$.fragment,v),D(g.$$.fragment,v),D(_),j=!1},d(v){v&&W(t),H(l),H($),H(g),a[I].d()}}}function He(s,t,n){let e,i,l,{inputData:o}=t;const p=ve();let h=new we(new xe(N),N,new ke);Ce(h);let d=null,u=null,A=null,y=null,$="",C=!1,k={a:null,b:null},w=o.data.a.response.length>0&&o.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const M=m.data;M.type===Q.chatModelReply?(M.stream==="A"?n(11,k.a=M.data.text,k):M.stream==="B"&&n(11,k.b=M.data.text,k),n(11,k)):M.type===Q.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,o=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:o.data.a.response),2049&s.$$.dirty&&n(7,l=k.b!==null?k.b:o.data.b.response),1&s.$$.dirty&&n(6,w=o.data.a.response.length>0&&o.data.b.response.length>0)},[o,y,d,u,$,C,w,l,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:C,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){C=m,n(5,C)},function(m){$=m,n(4,$)}]}class Le extends T{constructor(t){super(),V(this,t,He,Pe,J,{inputData:0})}}function fe(s){let t,n,e=s[0].type==="Chat"&&he(s);return{c(){e&&e.c(),t=ye()},m(i,l){e&&e.m(i,l),S(i,t,l),n=!0},p(i,l){i[0].type==="Chat"?e?(e.p(i,l),1&l&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&W(t),e&&e.d(i)}}}function he(s){let t,n;return t=new Le({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){O(t.$$.fragment)},m(e,i){P(t,e,i),n=!0},p(e,i){const l={};1&i&&(l.inputData=e[0]),t.$set(l)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){D(t.$$.fragment,e),n=!1},d(e){H(t,e)}}}function Ne(s){let t,n,e=s[0]&&fe(s);return{c(){t=f("main"),e&&e.c()},m(i,l){S(i,t,l),e&&e.m(t,null),n=!0},p(i,l){i[0]?e?(e.p(i,l),1&l&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&W(t),e&&e.d()}}}function je(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[Ne]},$$scope:{ctx:s}}}),{c(){O(t.$$.fragment)},m(l,o){P(t,l,o),n=!0,e||(i=F(window,"message",s[1]),e=!0)},p(l,[o]){const p={};17&o&&(p.$$scope={dirty:o,ctx:l}),t.$set(p)},i(l){n||(B(t.$$.fragment,l),n=!0)},o(l){D(t.$$.fragment,l),n=!1},d(l){H(t,l),e=!1,i()}}}function Ge(s,t,n){let e;return N.postMessage({type:Q.preferencePanelLoaded}),[e,function(i){const l=i.data;l.type===Q.preferenceInit&&n(0,e=l.data)},function(i){const l=i.detail;N.postMessage({type:Q.preferenceResultMessage,data:l})},function(i){N.postMessage({type:Q.preferenceNotify,data:i.detail})}]}class Qe extends T{constructor(t){super(),V(this,t,Ge,je,J,{})}}(async function(){N&&N.initialize&&await N.initialize(),new Qe({target:document.getElementById("app")})})();
