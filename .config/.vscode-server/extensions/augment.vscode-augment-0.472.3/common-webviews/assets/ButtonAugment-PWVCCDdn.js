import{S as G,i as H,s as I,a as x,y as A,z as E,g as J,_ as K,u as d,t as g,B as F,$ as D,aa as M,j as N,a8 as m,V as h,D as S,c as f,W as T,e as p,f as V,q as b,r as z,h as v,R as C,X as w,Y as y,Z as R,T as O}from"./SpinnerAugment-JC8TPhVf.js";import{B as P}from"./BaseButton-C1unVd78.js";const Q=s=>({}),W=s=>({}),U=s=>({}),X=s=>({});function Y(s){let t,l;const c=s[10].iconLeft,o=C(c,s,s[20],X);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-psg43r")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&w(o,c,i,i[20],l?R(c,i[20],a,U):y(i[20]),X)},i(i){l||(d(o,i),l=!0)},o(i){g(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function Z(s){let t,l,c;return l=new O({props:{size:s[0],weight:s[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:s}}}),{c(){t=h("div"),A(l.$$.fragment),f(t,"class","c-button--text svelte-psg43r")},m(o,i){p(o,t,i),E(l,t,null),c=!0},p(o,i){const a={};1&i&&(a.size=o[0]),2&i&&(a.weight=o[1]==="ghost"?"regular":"medium"),1048576&i&&(a.$$scope={dirty:i,ctx:o}),l.$set(a)},i(o){c||(d(l.$$.fragment,o),c=!0)},o(o){g(l.$$.fragment,o),c=!1},d(o){o&&v(t),F(l)}}}function tt(s){let t;const l=s[10].default,c=C(l,s,s[20],null);return{c(){c&&c.c()},m(o,i){c&&c.m(o,i),t=!0},p(o,i){c&&c.p&&(!t||1048576&i)&&w(c,l,o,o[20],t?R(l,o[20],i,null):y(o[20]),null)},i(o){t||(d(c,o),t=!0)},o(o){g(c,o),t=!1},d(o){c&&c.d(o)}}}function _(s){let t,l;const c=s[10].iconRight,o=C(c,s,s[20],W);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-psg43r")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&w(o,c,i,i[20],l?R(c,i[20],a,Q):y(i[20]),W)},i(i){l||(d(o,i),l=!0)},o(i){g(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function it(s){let t,l,c,o,i,a=s[9].iconLeft&&Y(s),r=s[9].default&&Z(s),u=s[9].iconRight&&_(s);return{c(){t=h("div"),a&&a.c(),l=S(),r&&r.c(),c=S(),u&&u.c(),f(t,"class",o=T(`c-button--content c-button--size-${s[0]}`)+" svelte-psg43r")},m(n,$){p(n,t,$),a&&a.m(t,null),V(t,l),r&&r.m(t,null),V(t,c),u&&u.m(t,null),i=!0},p(n,$){n[9].iconLeft?a?(a.p(n,$),512&$&&d(a,1)):(a=Y(n),a.c(),d(a,1),a.m(t,l)):a&&(b(),g(a,1,1,()=>{a=null}),z()),n[9].default?r?(r.p(n,$),512&$&&d(r,1)):(r=Z(n),r.c(),d(r,1),r.m(t,c)):r&&(b(),g(r,1,1,()=>{r=null}),z()),n[9].iconRight?u?(u.p(n,$),512&$&&d(u,1)):(u=_(n),u.c(),d(u,1),u.m(t,null)):u&&(b(),g(u,1,1,()=>{u=null}),z()),(!i||1&$&&o!==(o=T(`c-button--content c-button--size-${n[0]}`)+" svelte-psg43r"))&&f(t,"class",o)},i(n){i||(d(a),d(r),d(u),i=!0)},o(n){g(a),g(r),g(u),i=!1},d(n){n&&v(t),a&&a.d(),r&&r.d(),u&&u.d()}}}function ot(s){let t,l;const c=[{size:s[0]},{variant:s[1]},{color:s[2]},{highContrast:s[3]},{disabled:s[4]},{loading:s[6]},{alignment:s[7]},{radius:s[5]},s[8]];let o={$$slots:{default:[it]},$$scope:{ctx:s}};for(let i=0;i<c.length;i+=1)o=x(o,c[i]);return t=new P({props:o}),t.$on("click",s[11]),t.$on("keyup",s[12]),t.$on("keydown",s[13]),t.$on("mousedown",s[14]),t.$on("mouseover",s[15]),t.$on("focus",s[16]),t.$on("mouseleave",s[17]),t.$on("blur",s[18]),t.$on("contextmenu",s[19]),{c(){A(t.$$.fragment)},m(i,a){E(t,i,a),l=!0},p(i,[a]){const r=511&a?J(c,[1&a&&{size:i[0]},2&a&&{variant:i[1]},4&a&&{color:i[2]},8&a&&{highContrast:i[3]},16&a&&{disabled:i[4]},64&a&&{loading:i[6]},128&a&&{alignment:i[7]},32&a&&{radius:i[5]},256&a&&K(i[8])]):{};1049091&a&&(r.$$scope={dirty:a,ctx:i}),t.$set(r)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){g(t.$$.fragment,i),l=!1},d(i){F(t,i)}}}function st(s,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let o=D(t,c),{$$slots:i={},$$scope:a}=t;const r=M(i);let{size:u=2}=t,{variant:n="solid"}=t,{color:$="accent"}=t,{highContrast:k=!1}=t,{disabled:B=!1}=t,{radius:L="medium"}=t,{loading:j=!1}=t,{alignment:q="center"}=t;return s.$$set=e=>{t=x(x({},t),N(e)),l(8,o=D(t,c)),"size"in e&&l(0,u=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,k=e.highContrast),"disabled"in e&&l(4,B=e.disabled),"radius"in e&&l(5,L=e.radius),"loading"in e&&l(6,j=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,a=e.$$scope)},[u,n,$,k,B,L,j,q,o,r,i,function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},a]}class et extends G{constructor(t){super(),H(this,t,st,ot,I,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
