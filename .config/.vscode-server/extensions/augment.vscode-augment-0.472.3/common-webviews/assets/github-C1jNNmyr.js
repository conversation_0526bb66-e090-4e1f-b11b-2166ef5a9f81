var P=Object.defineProperty;var D=(t,e,s)=>e in t?P(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var i=(t,e,s)=>D(t,typeof e!="symbol"?e+"":e,s);import{S as x}from"./BaseButton-C1unVd78.js";import{S as m,i as b,s as g,b as u,c as a,e as M,f as p,n as o,h as c,a as d,K as y,L as f,M as w,N as T,d as _,O as S,g as v,j as C}from"./SpinnerAugment-JC8TPhVf.js";const j=15,$=1e3,F=25e4,J=2e4;class W{constructor(e){i(this,"_enableEditableHistory",!1);i(this,"_enablePreferenceCollection",!1);i(this,"_enableRetrievalDataCollection",!1);i(this,"_enableDebugFeatures",!1);i(this,"_enableRichTextHistory",!1);i(this,"_modelDisplayNameToId",{});i(this,"_fullFeatured",!0);i(this,"_enableExternalSourcesInChat",!1);i(this,"_smallSyncThreshold",15);i(this,"_bigSyncThreshold",1e3);i(this,"_enableSmartPaste",!1);i(this,"_enableDirectApply",!1);i(this,"_summaryTitles",!1);i(this,"_suggestedEditsAvailable",!1);i(this,"_enableShareService",!1);i(this,"_maxTrackableFileCount",F);i(this,"_enableDesignSystemRichTextEditor",!1);i(this,"_enableSources",!1);i(this,"_enableChatMermaidDiagrams",!1);i(this,"_smartPastePrecomputeMode",x.visibleHover);i(this,"_useNewThreadsMenu",!1);i(this,"_enableChatMermaidDiagramsMinVersion",!1);i(this,"_enablePromptEnhancer",!1);i(this,"_idleNewSessionNotificationTimeoutMs");i(this,"_idleNewSessionMessageTimeoutMs");i(this,"_enableChatMultimodal",!1);i(this,"_enableAgentMode",!1);i(this,"_enableAgentAutoMode",!1);i(this,"_enableRichCheckpointInfo",!1);i(this,"_agentMemoriesFilePathName");i(this,"_userTier","unknown");i(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});i(this,"_truncateChatHistory",!1);i(this,"_enableBackgroundAgents",!1);i(this,"_enableVirtualizedMessageList",!1);i(this,"_customPersonalityPrompts",{});i(this,"_enablePersonalities",!1);i(this,"_enableRules",!1);i(this,"_memoryClassificationOnFirstToken",!1);i(this,"_enableGenerateCommitMessage",!1);i(this,"_doUseNewDraftFunctionality",!1);i(this,"_modelRegistry",{});i(this,"_enableModelRegistry",!1);i(this,"_enableTaskList",!1);i(this,"_clientAnnouncement","");i(this,"_subscribers",new Set);i(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));i(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=e.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableVirtualizedMessageList=e.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._enableRules=e.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._doUseNewDraftFunctionality=e.doUseNewDraftFunctionality??this._doUseNewDraftFunctionality,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=e.enableTaskList??this._enableTaskList,this._clientAnnouncement=e.clientAnnouncement??this._clientAnnouncement,this._subscribers.forEach(s=>s(this))});i(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));i(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(s=>this._modelDisplayNameToId[s]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,s)=>{const n=e.toLowerCase(),l=s.toLowerCase();return n==="default"&&l!=="default"?-1:l==="default"&&n!=="default"?1:e.localeCompare(s)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get doUseNewDraftFunctionality(){return this._doUseNewDraftFunctionality}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}}function N(t){let e,s;return{c(){e=u("svg"),s=u("path"),a(s,"fill-rule","evenodd"),a(s,"clip-rule","evenodd"),a(s,"d","M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"),a(s,"fill","currentColor"),a(e,"width","15"),a(e,"height","15"),a(e,"viewBox","0 0 15 15"),a(e,"fill","none"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(n,l){M(n,e,l),p(e,s)},p:o,i:o,o,d(n){n&&c(e)}}}class K extends m{constructor(e){super(),b(this,e,null,N,g,{})}}function A(t){let e,s;return{c(){e=u("svg"),s=u("path"),a(s,"fill-rule","evenodd"),a(s,"clip-rule","evenodd"),a(s,"d","M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H7V5.25C7 4.97386 7.22386 4.75 7.5 4.75C7.77614 4.75 8 4.97386 8 5.25V7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H8V9.75C8 10.0261 7.77614 10.25 7.5 10.25C7.22386 10.25 7 10.0261 7 9.75V8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z"),a(s,"fill","currentColor"),a(e,"width","15"),a(e,"height","15"),a(e,"viewBox","0 0 15 15"),a(e,"fill","none"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(n,l){M(n,e,l),p(e,s)},p:o,i:o,o,d(n){n&&c(e)}}}class q extends m{constructor(e){super(),b(this,e,null,A,g,{})}}var R=(t=>(t.readFile="read-file",t.saveFile="save-file",t.editFile="edit-file",t.clarify="clarify",t.onboardingSubAgent="onboarding-sub-agent",t.launchProcess="launch-process",t.killProcess="kill-process",t.readProcess="read-process",t.writeProcess="write-process",t.listProcesses="list-processes",t.waitProcess="wait-process",t.openBrowser="open-browser",t.strReplaceEditor="str-replace-editor",t.remember="remember",t.diagnostics="diagnostics",t.setupScript="setup-script",t.readTerminal="read-terminal",t.gitCommitRetrieval="git-commit-retrieval",t))(R||{}),H=(t=>(t.remoteToolHost="remoteToolHost",t.localToolHost="localToolHost",t.sidecarToolHost="sidecarToolHost",t.mcpHost="mcpHost",t))(H||{}),L=(t=>(t[t.ContentText=0]="ContentText",t[t.ContentImage=1]="ContentImage",t))(L||{}),k=(t=>(t[t.Unknown=0]="Unknown",t[t.WebSearch=1]="WebSearch",t[t.GitHubApi=8]="GitHubApi",t[t.Linear=12]="Linear",t[t.Jira=13]="Jira",t[t.Confluence=14]="Confluence",t[t.Notion=15]="Notion",t[t.Supabase=16]="Supabase",t[t.Glean=17]="Glean",t))(k||{});function E(t){let e,s;return{c(){e=u("svg"),s=u("path"),a(s,"fill-rule","evenodd"),a(s,"clip-rule","evenodd"),a(s,"d","M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"),a(s,"fill","currentColor"),a(e,"width","15"),a(e,"height","15"),a(e,"viewBox","0 0 15 15"),a(e,"fill","none"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(n,l){M(n,e,l),p(e,s)},p:o,i:o,o,d(n){n&&c(e)}}}class Q extends m{constructor(e){super(),b(this,e,null,E,g,{})}}function I(t){let e,s,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},t[0]],l={};for(let r=0;r<n.length;r+=1)l=d(l,n[r]);return{c(){e=u("svg"),s=new y(!0),this.h()},l(r){e=f(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var h=w(e);s=T(h,!0),h.forEach(c),this.h()},h(){s.a=null,_(e,l)},m(r,h){S(r,e,h),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-81-337c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0"/>',e)},p(r,[h]){_(e,l=v(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&h&&r[0]]))},i:o,o,d(r){r&&c(e)}}}function V(t,e,s){return t.$$set=n=>{s(0,e=d(d({},e),C(n)))},[e=C(e)]}class X extends m{constructor(e){super(),b(this,e,V,I,g,{})}}function B(t){let e,s,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],l={};for(let r=0;r<n.length;r+=1)l=d(l,n[r]);return{c(){e=u("svg"),s=new y(!0),this.h()},l(r){e=f(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var h=w(e);s=T(h,!0),h.forEach(c),this.h()},h(){s.a=null,_(e,l)},m(r,h){S(r,e,h),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M177.1 48h93.7c2.7 0 5.2 1.3 6.7 3.6l19 28.4h-145l19-28.4c1.5-2.2 4-3.6 6.7-3.6zm177.1 32-36.7-55.1C307.1 9.4 289.6 0 270.9 0h-93.8c-18.7 0-36.2 9.4-46.6 24.9L93.8 80H24C10.7 80 0 90.7 0 104s10.7 24 24 24h11.6l24 324.7c2.5 33.4 30.3 59.3 63.8 59.3h201.1c33.5 0 61.3-25.9 63.8-59.3L412.4 128H424c13.3 0 24-10.7 24-24s-10.7-24-24-24h-56.1zm10.1 48-23.8 321.2c-.6 8.4-7.6 14.8-16 14.8H123.4c-8.4 0-15.3-6.5-16-14.8L83.7 128z"/>',e)},p(r,[h]){_(e,l=v(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&h&&r[0]]))},i:o,o,d(r){r&&c(e)}}}function G(t,e,s){return t.$$set=n=>{s(0,e=d(d({},e),C(n)))},[e=C(e)]}class Y extends m{constructor(e){super(),b(this,e,G,B,g,{})}}function O(t){let e,s;return{c(){e=u("svg"),s=u("path"),a(s,"fill-rule","evenodd"),a(s,"clip-rule","evenodd"),a(s,"d","M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z"),a(s,"fill","currentColor"),a(e,"width","15"),a(e,"height","15"),a(e,"viewBox","0 0 15 15"),a(e,"fill","none"),a(e,"xmlns","http://www.w3.org/2000/svg")},m(n,l){M(n,e,l),p(e,s)},p:o,i:o,o,d(n){n&&c(e)}}}class ee extends m{constructor(e){super(),b(this,e,null,O,g,{})}}export{Q as C,j as D,q as F,ee as G,R as L,k as R,Y as T,W as a,$ as b,F as c,X as d,H as e,K as f,L as g,J as h};
