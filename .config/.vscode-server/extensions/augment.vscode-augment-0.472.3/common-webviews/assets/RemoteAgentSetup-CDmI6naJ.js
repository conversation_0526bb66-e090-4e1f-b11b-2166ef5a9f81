import{S as ie,i as ae,s as le,a as ue,b as Xe,K as We,L as Ye,M as Qe,N as Ze,h as b,d as Re,O as Je,g as et,n as B,j as ke,R as Y,V as S,D as z,E as $e,c as y,e as x,f as N,X as Q,Y as Z,Z as J,u as p,q as j,t as f,r as O,aa as dt,T as Me,y as A,z as R,B as k,G as T,H as ee,an as De,a5 as xe,a7 as ft,w as he,x as we,A as ve,a3 as ge,af as _e,a1 as tt,a0 as an,am as nt,a8 as Oe,ab as xn,a4 as xt,ax as He,ag as ut,ad as ln,ae as Ve}from"./SpinnerAugment-JC8TPhVf.js";import{B as _n,A as un,a as mt,b as $t,C as Ln,g as Cn}from"./main-panel-zHcgjx1c.js";import{d as _t,T as An}from"./Content-xvE836E_.js";import"./lodash-l00D6itj.js";import{R as Rn}from"./types-BSMhNRWH.js";import{G as gt}from"./folder-IxOJUoWR.js";import{B as Pe}from"./ButtonAugment-PWVCCDdn.js";import{C as ot,P as kn}from"./pen-to-square-3TLxExQu.js";import{T as Ne}from"./TextTooltipAugment-WghC7pXE.js";import{f as qe}from"./index-CW7fyhvB.js";import{M as ht,R as mn}from"./magnifying-glass-D5YyJVGd.js";import{C as $n,G as wt,T as Sn}from"./github-C1jNNmyr.js";import{e as Ke,u as pn,o as dn}from"./BaseButton-C1unVd78.js";import{D as me,C as In,T as Fn}from"./index-Yat2JVWz.js";import{R as rt}from"./open-in-new-window-CkR7J3XO.js";import{T as fn}from"./terminal-BBUsFUTj.js";import{A as En}from"./arrow-up-right-from-square-q_X4-2Am.js";import{I as vt}from"./IconButtonAugment-D_WR2I26.js";import{T as gn}from"./Keybindings-C19Zl3Ej.js";import{R as Lt}from"./types-Cgd-nZOV.js";import{E as Nn}from"./exclamation-triangle-DfKf7sb_.js";import{T as Dn}from"./StatusIndicator-BiyeFzqm.js";import"./layer-group-DMm-CNlu.js";import"./design-system-init-Bf1-mlh4.js";import"./rules-parser-D2d7xQ-G.js";import"./chat-types-NgqNgjwU.js";import"./diff-utils-Bh9TYMbV.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CK0xjdO4.js";import"./index-DiI90jLk.js";import"./isObjectLike-BA2QYXi-.js";import"./globals-D0QH3NT1.js";import"./await_block-C0teov-5.js";import"./CardAugment-D2Hs6UjS.js";import"./ellipsis-ce3_p-Q7.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-DeFTcAEj.js";import"./folder-opened-bSDyFrZo.js";import"./MaterialIcon-D8Nb6HkU.js";import"./types-B5Ac2hek.js";import"./TextAreaAugment-BYCw5p2g.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-zeLUoeQd.js";import"./augment-logo-E1jEbeRV.js";import"./chat-flags-model-S_DCcmJS.js";function Ct(s){const e=s.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function At(s){return s.replace(/^origin\//,"")}function Pn(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ue(o,t[r]);return{c(){e=Xe("svg"),n=new We(!0),this.h()},l(r){e=Ye(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ze(c,!0),c.forEach(b),this.h()},h(){n.a=null,Re(e,o)},m(r,c){Je(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(r,[c]){Re(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&r[0]]))},i:B,o:B,d(r){r&&b(e)}}}function Un(s,e,n){return s.$$set=t=>{n(0,e=ue(ue({},e),ke(t)))},[e=ke(e)]}class Mn extends ie{constructor(e){super(),ae(this,e,Un,Pn,le,{})}}const zn=s=>({}),Rt=s=>({}),Bn=s=>({}),kt=s=>({}),Tn=s=>({}),St=s=>({}),Gn=s=>({}),It=s=>({});function jn(s){let e;return{c(){e=T(s[0])},m(n,t){x(n,e,t)},p(n,t){1&t&&ee(e,n[0])},d(n){n&&b(e)}}}function Ft(s){let e,n;const t=s[3].subtitle,o=Y(t,s,s[4],kt),r=o||function(c){let i,a;return i=new Me({props:{size:2,$$slots:{default:[On]},$$scope:{ctx:c}}}),{c(){A(i.$$.fragment)},m(l,u){R(i,l,u),a=!0},p(l,u){const m={};18&u&&(m.$$scope={dirty:u,ctx:l}),i.$set(m)},i(l){a||(p(i.$$.fragment,l),a=!0)},o(l){f(i.$$.fragment,l),a=!1},d(l){k(i,l)}}}(s);return{c(){e=S("div"),r&&r.c(),y(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,i){x(c,e,i),r&&r.m(e,null),n=!0},p(c,i){o?o.p&&(!n||16&i)&&Q(o,t,c,c[4],n?J(t,c[4],i,Bn):Z(c[4]),kt):r&&r.p&&(!n||2&i)&&r.p(c,n?i:-1)},i(c){n||(p(r,c),n=!0)},o(c){f(r,c),n=!1},d(c){c&&b(e),r&&r.d(c)}}}function On(s){let e;return{c(){e=T(s[1])},m(n,t){x(n,e,t)},p(n,t){2&t&&ee(e,n[1])},d(n){n&&b(e)}}}function Et(s){let e,n;const t=s[3].iconRight,o=Y(t,s,s[4],Rt);return{c(){e=S("div"),o&&o.c(),y(e,"class","c-card-button__icon-right svelte-z367s9")},m(r,c){x(r,e,c),o&&o.m(e,null),n=!0},p(r,c){o&&o.p&&(!n||16&c)&&Q(o,t,r,r[4],n?J(t,r[4],c,zn):Z(r[4]),Rt)},i(r){n||(p(o,r),n=!0)},o(r){f(o,r),n=!1},d(r){r&&b(e),o&&o.d(r)}}}function Hn(s){let e,n,t,o,r,c,i,a;const l=s[3].iconLeft,u=Y(l,s,s[4],It),m=s[3].title,g=Y(m,s,s[4],St),w=g||function($){let v,L;return v=new Me({props:{size:2,$$slots:{default:[jn]},$$scope:{ctx:$}}}),{c(){A(v.$$.fragment)},m(F,D){R(v,F,D),L=!0},p(F,D){const P={};17&D&&(P.$$scope={dirty:D,ctx:F}),v.$set(P)},i(F){L||(p(v.$$.fragment,F),L=!0)},o(F){f(v.$$.fragment,F),L=!1},d(F){k(v,F)}}}(s);let h=s[1]&&Ft(s),d=s[2].iconRight&&Et(s);return{c(){e=S("div"),u&&u.c(),n=z(),t=S("div"),o=S("div"),w&&w.c(),r=z(),h&&h.c(),c=z(),d&&d.c(),i=$e(),y(e,"class","c-card-button__icon-left svelte-z367s9"),y(o,"class","c-card-button__title svelte-z367s9"),y(t,"class","c-card-button__content svelte-z367s9")},m($,v){x($,e,v),u&&u.m(e,null),x($,n,v),x($,t,v),N(t,o),w&&w.m(o,null),N(t,r),h&&h.m(t,null),x($,c,v),d&&d.m($,v),x($,i,v),a=!0},p($,[v]){u&&u.p&&(!a||16&v)&&Q(u,l,$,$[4],a?J(l,$[4],v,Gn):Z($[4]),It),g?g.p&&(!a||16&v)&&Q(g,m,$,$[4],a?J(m,$[4],v,Tn):Z($[4]),St):w&&w.p&&(!a||1&v)&&w.p($,a?v:-1),$[1]?h?(h.p($,v),2&v&&p(h,1)):(h=Ft($),h.c(),p(h,1),h.m(t,null)):h&&(j(),f(h,1,1,()=>{h=null}),O()),$[2].iconRight?d?(d.p($,v),4&v&&p(d,1)):(d=Et($),d.c(),p(d,1),d.m(i.parentNode,i)):d&&(j(),f(d,1,1,()=>{d=null}),O())},i($){a||(p(u,$),p(w,$),p(h),p(d),a=!0)},o($){f(u,$),f(w,$),f(h),f(d),a=!1},d($){$&&(b(e),b(n),b(t),b(c),b(i)),u&&u.d($),w&&w.d($),h&&h.d(),d&&d.d($)}}}function Vn(s,e,n){let{$$slots:t={},$$scope:o}=e;const r=dt(t);let{title:c="Select an option"}=e,{subtitle:i=""}=e;return s.$$set=a=>{"title"in a&&n(0,c=a.title),"subtitle"in a&&n(1,i=a.subtitle),"$$scope"in a&&n(4,o=a.$$scope)},[c,i,r,t,o]}class hn extends ie{constructor(e){super(),ae(this,e,Vn,Hn,le,{title:0,subtitle:1})}}const qn=s=>({}),Nt=s=>({slot:"iconLeft"}),Kn=s=>({}),Dt=s=>({slot:"iconRight"});function Pt(s,e,n){const t=s.slice();return t[19]=e[n],t}const Xn=s=>({}),Ut=s=>({}),Wn=s=>({}),Mt=s=>({}),Yn=s=>({}),zt=s=>({slot:"iconLeft"}),Qn=s=>({}),Bt=s=>({slot:"title"}),Zn=s=>({}),Tt=s=>({slot:"iconRight"});function Jn(s){let e,n,t,o,r;return n=new hn({props:{title:s[3],subtitle:s[4],$$slots:{iconRight:[no],iconLeft:[to]},$$scope:{ctx:s}}}),{c(){e=S("button"),A(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"type","button"),e.disabled=s[10]},m(c,i){x(c,e,i),R(n,e,null),t=!0,o||(r=[xe(e,"click",s[16]),xe(e,"keydown",s[17])],o=!0)},p(c,i){const a={};8&i&&(a.title=c[3]),16&i&&(a.subtitle=c[4]),262144&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a),(!t||1024&i)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){f(n.$$.fragment,c),t=!1},d(c){c&&b(e),k(n),o=!1,ft(r)}}}function eo(s){let e,n,t;function o(c){s[15](c)}let r={onOpenChange:s[9],$$slots:{default:[$o]},$$scope:{ctx:s}};return s[1]!==void 0&&(r.requestClose=s[1]),e=new me.Root({props:r}),he.push(()=>we(e,"requestClose",o)),{c(){A(e.$$.fragment)},m(c,i){R(e,c,i),t=!0},p(c,i){const a={};512&i&&(a.onOpenChange=c[9]),263641&i&&(a.$$scope={dirty:i,ctx:c}),!n&&2&i&&(n=!0,a.requestClose=c[1],ve(()=>n=!1)),e.$set(a)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){f(e.$$.fragment,c),t=!1},d(c){k(e,c)}}}function to(s){let e;const n=s[13].iconLeft,t=Y(n,s,s[18],Nt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||262144&r)&&Q(t,n,o,o[18],e?J(n,o[18],r,qn):Z(o[18]),Nt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function no(s){let e;const n=s[13].iconRight,t=Y(n,s,s[18],Dt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||262144&r)&&Q(t,n,o,o[18],e?J(n,o[18],r,Kn):Z(o[18]),Dt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function oo(s){let e;const n=s[13].iconLeft,t=Y(n,s,s[18],zt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||262144&r)&&Q(t,n,o,o[18],e?J(n,o[18],r,Yn):Z(o[18]),zt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function ro(s){let e;const n=s[13].title,t=Y(n,s,s[18],Bt),o=t||function(r){let c;return{c(){c=T(r[3])},m(i,a){x(i,c,a)},p(i,a){8&a&&ee(c,i[3])},d(i){i&&b(c)}}}(s);return{c(){o&&o.c()},m(r,c){o&&o.m(r,c),e=!0},p(r,c){t?t.p&&(!e||262144&c)&&Q(t,n,r,r[18],e?J(n,r[18],c,Qn):Z(r[18]),Bt):o&&o.p&&(!e||8&c)&&o.p(r,e?c:-1)},i(r){e||(p(o,r),e=!0)},o(r){f(o,r),e=!1},d(r){o&&o.d(r)}}}function so(s){let e;const n=s[13].iconRight,t=Y(n,s,s[18],Tt),o=t||function(r){let c,i;return c=new $n({}),{c(){A(c.$$.fragment)},m(a,l){R(c,a,l),i=!0},i(a){i||(p(c.$$.fragment,a),i=!0)},o(a){f(c.$$.fragment,a),i=!1},d(a){k(c,a)}}}();return{c(){o&&o.c()},m(r,c){o&&o.m(r,c),e=!0},p(r,c){t&&t.p&&(!e||262144&c)&&Q(t,n,r,r[18],e?J(n,r[18],c,Zn):Z(r[18]),Tt)},i(r){e||(p(o,r),e=!0)},o(r){f(o,r),e=!1},d(r){o&&o.d(r)}}}function co(s){let e,n,t,o;return n=new hn({props:{subtitle:s[4],$$slots:{iconRight:[so],title:[ro],iconLeft:[oo]},$$scope:{ctx:s}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"role","button"),y(e,"tabindex",t=s[10]?-1:0),ge(e,"disabled",s[10])},m(r,c){x(r,e,c),R(n,e,null),o=!0},p(r,c){const i={};16&c&&(i.subtitle=r[4]),262152&c&&(i.$$scope={dirty:c,ctx:r}),n.$set(i),(!o||1024&c&&t!==(t=r[10]?-1:0))&&y(e,"tabindex",t),(!o||1024&c)&&ge(e,"disabled",r[10])},i(r){o||(p(n.$$.fragment,r),o=!0)},o(r){f(n.$$.fragment,r),o=!1},d(r){r&&b(e),k(n)}}}function io(s){let e,n;return e=new me.Label({props:{$$slots:{default:[lo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};262400&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ao(s){let e,n,t=[],o=new Map,r=Ke(s[6]);const c=i=>i[7](i[19]);for(let i=0;i<r.length;i+=1){let a=Pt(s,r,i),l=c(a);o.set(l,t[i]=Gt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=$e()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);x(i,e,a),n=!0},p(i,a){2241&a&&(r=Ke(i[6]),j(),t=pn(t,a,c,1,i,r,o,e.parentNode,dn,Gt,e,Pt),O())},i(i){if(!n){for(let a=0;a<r.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)f(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function lo(s){let e;return{c(){e=T(s[8])},m(n,t){x(n,e,t)},p(n,t){256&t&&ee(e,n[8])},d(n){n&&b(e)}}}function uo(s){let e,n,t=s[7](s[19])+"";return{c(){e=T(t),n=z()},m(o,r){x(o,e,r),x(o,n,r)},p(o,r){192&r&&t!==(t=o[7](o[19])+"")&&ee(e,t)},d(o){o&&(b(e),b(n))}}}function Gt(s,e){let n,t,o;function r(){return e[14](e[19])}return t=new me.Item({props:{onSelect:r,highlight:e[0]===e[19],$$slots:{default:[uo]},$$scope:{ctx:e}}}),{key:s,first:null,c(){n=$e(),A(t.$$.fragment),this.first=n},m(c,i){x(c,n,i),R(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=r),65&i&&(a.highlight=e[0]===e[19]),262336&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){f(t.$$.fragment,c),o=!1},d(c){c&&b(n),k(t,c)}}}function mo(s){let e,n,t,o,r;const c=s[13]["dropdown-top"],i=Y(c,s,s[18],Mt),a=s[13]["dropdown-content"],l=Y(a,s,s[18],Ut),u=l||function(m){let g,w,h,d;const $=[ao,io],v=[];function L(F,D){return F[6].length>0?0:1}return g=L(m),w=v[g]=$[g](m),{c(){w.c(),h=$e()},m(F,D){v[g].m(F,D),x(F,h,D),d=!0},p(F,D){let P=g;g=L(F),g===P?v[g].p(F,D):(j(),f(v[P],1,1,()=>{v[P]=null}),O(),w=v[g],w?w.p(F,D):(w=v[g]=$[g](F),w.c()),p(w,1),w.m(h.parentNode,h))},i(F){d||(p(w),d=!0)},o(F){f(w),d=!1},d(F){F&&b(h),v[g].d(F)}}}(s);return{c(){e=S("div"),n=S("div"),i&&i.c(),t=z(),o=S("div"),u&&u.c(),y(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),y(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),y(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m(m,g){x(m,e,g),N(e,n),i&&i.m(n,null),N(e,t),N(e,o),u&&u.m(o,null),r=!0},p(m,g){i&&i.p&&(!r||262144&g)&&Q(i,c,m,m[18],r?J(c,m[18],g,Wn):Z(m[18]),Mt),l?l.p&&(!r||262144&g)&&Q(l,a,m,m[18],r?J(a,m[18],g,Xn):Z(m[18]),Ut):u&&u.p&&(!r||449&g)&&u.p(m,r?g:-1)},i(m){r||(p(i,m),p(u,m),r=!0)},o(m){f(i,m),f(u,m),r=!1},d(m){m&&b(e),i&&i.d(m),u&&u.d(m)}}}function $o(s){let e,n,t,o;return e=new me.Trigger({props:{$$slots:{default:[co]},$$scope:{ctx:s}}}),t=new me.Content({props:{align:"start",side:"bottom",$$slots:{default:[mo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment),n=z(),A(t.$$.fragment)},m(r,c){R(e,r,c),x(r,n,c),R(t,r,c),o=!0},p(r,c){const i={};263192&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i);const a={};262593&c&&(a.$$scope={dirty:c,ctx:r}),t.$set(a)},i(r){o||(p(e.$$.fragment,r),p(t.$$.fragment,r),o=!0)},o(r){f(e.$$.fragment,r),f(t.$$.fragment,r),o=!1},d(r){r&&b(n),k(e,r),k(t,r)}}}function po(s){let e,n,t,o;const r=[eo,Jn],c=[];function i(a,l){return a[2]==="dropdown"?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),y(e,"class","c-card-button svelte-1km5ln2")},m(a,l){x(a,e,l),c[n].m(e,null),o=!0},p(a,[l]){let u=n;n=i(a),n===u?c[n].p(a,l):(j(),f(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){f(t),o=!1},d(a){a&&b(e),c[n].d()}}}function fo(s,e,n){let{$$slots:t={},$$scope:o}=e,{type:r="button"}=e,{title:c="Select an option"}=e,{subtitle:i=""}=e,{onClick:a=()=>{}}=e,{items:l=[]}=e,{selectedItem:u}=e,{formatItemLabel:m=L=>(L==null?void 0:L.toString())||""}=e,{noItemsLabel:g="No items found"}=e,{onDropdownOpenChange:w=()=>{}}=e,{requestClose:h=()=>{}}=e,{disabled:d=!1}=e;function $(L){n(0,u=L),v("select",L)}const v=De();return s.$$set=L=>{"type"in L&&n(2,r=L.type),"title"in L&&n(3,c=L.title),"subtitle"in L&&n(4,i=L.subtitle),"onClick"in L&&n(5,a=L.onClick),"items"in L&&n(6,l=L.items),"selectedItem"in L&&n(0,u=L.selectedItem),"formatItemLabel"in L&&n(7,m=L.formatItemLabel),"noItemsLabel"in L&&n(8,g=L.noItemsLabel),"onDropdownOpenChange"in L&&n(9,w=L.onDropdownOpenChange),"requestClose"in L&&n(1,h=L.requestClose),"disabled"in L&&n(10,d=L.disabled),"$$scope"in L&&n(18,o=L.$$scope)},[u,h,r,c,i,a,l,m,g,w,d,$,v,t,L=>$(L),function(L){h=L,n(1,h)},()=>{a(),v("click")},L=>{L.key!=="Enter"&&L.key!==" "||(a(),v("click"))},o]}class wn extends ie{constructor(e){super(),ae(this,e,fo,po,le,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function go(s){let e,n;return e=new wn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[Lo],iconLeft:[wo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};4100&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ho(s){let e,n;return e=new wn({props:{type:"button",title:s[1]?"Cancel":"Connect to GitHub",onClick:s[4],$$slots:{iconRight:[ko],iconLeft:[Co]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};2&o&&(r.title=t[1]?"Cancel":"Connect to GitHub"),4098&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function wo(s){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function vo(s){let e,n;return e=new Me({props:{size:1,weight:"medium",$$slots:{default:[yo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function bo(s){let e,n,t,o;return e=new nt({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new Me({props:{size:1,weight:"medium",$$slots:{default:[xo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment),n=z(),A(t.$$.fragment)},m(r,c){R(e,r,c),x(r,n,c),R(t,r,c),o=!0},i(r){o||(p(e.$$.fragment,r),p(t.$$.fragment,r),o=!0)},o(r){f(e.$$.fragment,r),f(t.$$.fragment,r),o=!1},d(r){r&&b(n),k(e,r),k(t,r)}}}function yo(s){let e;return{c(){e=T("Revoke Access")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function xo(s){let e;return{c(){e=T("Revoking...")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function _o(s){let e,n,t,o;const r=[bo,vo],c=[];function i(a,l){return a[2]?0:1}return e=i(s),n=c[e]=r[e](s),{c(){n.c(),t=$e()},m(a,l){c[e].m(a,l),x(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e!==u&&(j(),f(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n||(n=c[e]=r[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){f(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Lo(s){let e,n,t;return n=new me.Item({props:{color:"error",onSelect:s[6],$$slots:{default:[_o]},$$scope:{ctx:s}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"slot","dropdown-content")},m(o,r){x(o,e,r),R(n,e,null),t=!0},p(o,r){const c={};4100&r&&(c.$$scope={dirty:r,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function Co(s){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ao(s){let e,n;return e=new In({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ro(s){let e,n;return e=new nt({props:{size:1,useCurrentColor:!0}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ko(s){let e,n,t,o;const r=[Ro,Ao],c=[];function i(a,l){return a[1]?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),y(e,"slot","iconRight")},m(a,l){x(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(j(),f(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t||(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){f(t),o=!1},d(a){a&&b(e),c[n].d()}}}function So(s){let e,n,t,o,r;const c=[ho,go],i=[];function a(l,u){return l[0]?1:0}return t=a(s),o=i[t]=c[t](s),{c(){e=S("div"),n=S("div"),o.c(),y(n,"class","github-auth-button"),y(e,"class","github-auth-card svelte-zdlnsr")},m(l,u){x(l,e,u),N(e,n),i[t].m(n,null),r=!0},p(l,[u]){let m=t;t=a(l),t===m?i[t].p(l,u):(j(),f(i[m],1,1,()=>{i[m]=null}),O(),o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){r||(p(o),r=!0)},o(l){f(o),r=!1},d(l){l&&b(e),i[t].d()}}}function Io(s,e,n){const t=De(),o=_e(gt.key);let r=!1,c=!1,i=!1,a=null,l=null;async function u(){if(!i){n(2,i=!0);try{const m=await o.revokeGithubAccess();m.success?(n(0,r=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",m.message)}catch(m){console.error("Error revoking GitHub access:",m)}finally{n(2,i=!1)}}}return tt(async()=>{await async function(){try{const m=await o.isGithubAuthenticated();m!==r?(n(0,r=m),t("authStateChange",{isAuthenticated:r})):n(0,r=m)}catch(m){console.error("Failed to check GitHub authentication status:",m),n(0,r=!1),t("authStateChange",{isAuthenticated:!1})}}()}),an(()=>{a&&(clearTimeout(a),a=null),l&&(clearInterval(l),l=null)}),[r,c,i,()=>{},async function(){if(c)return n(1,c=!1),void(a&&(clearTimeout(a),a=null));n(1,c=!0);try{await o.authenticateGithub(),l=setInterval(async()=>{await o.isGithubAuthenticated()&&(n(0,r=!0),n(1,c=!1),t("authStateChange",{isAuthenticated:!0}),l&&clearInterval(l),a&&(clearTimeout(a),a=null))},5e3),a=setTimeout(()=>{l&&clearInterval(l),n(1,c=!1),a=null},6e4)}catch(m){console.error("Failed to authenticate with GitHub:",m),n(1,c=!1)}},u,()=>{u()}]}class Fo extends ie{constructor(e){super(),ae(this,e,Io,So,le,{})}}const Eo=s=>({}),jt=s=>({});function Ot(s,e,n){const t=s.slice();return t[27]=e[n],t[29]=n,t}const No=s=>({item:64&s}),Ht=s=>({item:s[27]}),Do=s=>({}),Vt=s=>({}),Po=s=>({}),qt=s=>({}),Uo=s=>({}),Kt=s=>({}),Mo=s=>({}),Xt=s=>({});function zo(s){let e,n,t,o,r,c,i,a,l,u,m,g,w,h;const d=[Go,To],$=[];function v(P,U){return P[4]?0:1}o=v(s),r=$[o]=d[o](s);const L=[Oo,jo],F=[];function D(P,U){return P[17].title?0:1}return a=D(s),l=F[a]=L[a](s),g=new $n({}),{c(){e=S("div"),n=S("div"),t=S("div"),r.c(),c=z(),i=S("span"),l.c(),u=z(),m=S("div"),A(g.$$.fragment),y(t,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(i,"class","c-searchable-dropdown__button-text svelte-jowwyu"),y(n,"class","c-searchable-dropdown__icon-text svelte-jowwyu"),y(m,"class","c-searchable-dropdown__chevron svelte-jowwyu"),y(e,"class","c-searchable-dropdown__button svelte-jowwyu"),y(e,"role","button"),y(e,"tabindex",w=s[5]?-1:0),ge(e,"c-searchable-dropdown__button--disabled",s[5])},m(P,U){x(P,e,U),N(e,n),N(n,t),$[o].m(t,null),N(n,c),N(n,i),F[a].m(i,null),N(e,u),N(e,m),R(g,m,null),h=!0},p(P,U){let C=o;o=v(P),o===C?$[o].p(P,U):(j(),f($[C],1,1,()=>{$[C]=null}),O(),r=$[o],r?r.p(P,U):(r=$[o]=d[o](P),r.c()),p(r,1),r.m(t,null));let H=a;a=D(P),a===H?F[a].p(P,U):(j(),f(F[H],1,1,()=>{F[H]=null}),O(),l=F[a],l?l.p(P,U):(l=F[a]=L[a](P),l.c()),p(l,1),l.m(i,null)),(!h||32&U&&w!==(w=P[5]?-1:0))&&y(e,"tabindex",w),(!h||32&U)&&ge(e,"c-searchable-dropdown__button--disabled",P[5])},i(P){h||(p(r),p(l),p(g.$$.fragment,P),h=!0)},o(P){f(r),f(l),f(g.$$.fragment,P),h=!1},d(P){P&&b(e),$[o].d(),F[a].d(),k(g)}}}function Bo(s){let e,n,t,o,r,c,i;const a=s[18].searchIcon,l=Y(a,s,s[25],Xt),u=l||function(m){let g;const w=m[18].icon,h=Y(w,m,m[25],Kt);return{c(){h&&h.c()},m(d,$){h&&h.m(d,$),g=!0},p(d,$){h&&h.p&&(!g||33554432&$)&&Q(h,w,d,d[25],g?J(w,d[25],$,Uo):Z(d[25]),Kt)},i(d){g||(p(h,d),g=!0)},o(d){f(h,d),g=!1},d(d){h&&h.d(d)}}}(s);return{c(){e=S("div"),n=S("div"),u&&u.c(),t=z(),o=S("input"),y(n,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(o,"type","text"),y(o,"class","c-searchable-dropdown__trigger-input svelte-jowwyu"),y(o,"placeholder",s[3]),y(e,"class","c-searchable-dropdown__input-container svelte-jowwyu")},m(m,g){x(m,e,g),N(e,n),u&&u.m(n,null),N(e,t),N(e,o),xt(o,s[0]),r=!0,c||(i=[xe(o,"input",s[21]),xe(o,"input",s[22]),xe(o,"click",He(s[19])),xe(o,"mousedown",He(s[20]))],c=!0)},p(m,g){l?l.p&&(!r||33554432&g)&&Q(l,a,m,m[25],r?J(a,m[25],g,Mo):Z(m[25]),Xt):u&&u.p&&(!r||33554432&g)&&u.p(m,r?g:-1),(!r||8&g)&&y(o,"placeholder",m[3]),1&g&&o.value!==m[0]&&xt(o,m[0])},i(m){r||(p(u,m),r=!0)},o(m){f(u,m),r=!1},d(m){m&&b(e),u&&u.d(m),c=!1,ft(i)}}}function To(s){let e;const n=s[18].icon,t=Y(n,s,s[25],qt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||33554432&r)&&Q(t,n,o,o[25],e?J(n,o[25],r,Po):Z(o[25]),qt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function Go(s){let e,n;return e=new nt({props:{size:1,useCurrentColor:!0}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function jo(s){let e,n=(s[4]?s[11]:s[2])+"";return{c(){e=T(n)},m(t,o){x(t,e,o)},p(t,o){2068&o&&n!==(n=(t[4]?t[11]:t[2])+"")&&ee(e,n)},i:B,o:B,d(t){t&&b(e)}}}function Oo(s){let e;const n=s[18].title,t=Y(n,s,s[25],Vt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||33554432&r)&&Q(t,n,o,o[25],e?J(n,o[25],r,Do):Z(o[25]),Vt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function Ho(s){let e,n,t,o;const r=[Bo,zo],c=[];function i(a,l){return a[12]?0:1}return e=i(s),n=c[e]=r[e](s),{c(){n.c(),t=$e()},m(a,l){c[e].m(a,l),x(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(j(),f(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){f(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Wt(s){let e,n;return e=new me.Content({props:{side:"bottom",align:"start",$$slots:{default:[Qo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};33689298&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Vo(s){let e,n;return e=new me.Item({props:{disabled:!0,$$slots:{default:[Xo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};33555456&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function qo(s){let e,n,t=[],o=new Map,r=Ke(s[6]);const c=i=>i[27]===null?`null-item-${i[29]}`:i[8](i[27]);for(let i=0;i<r.length;i+=1){let a=Ot(s,r,i),l=c(a);o.set(l,t[i]=Yt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=$e()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);x(i,e,a),n=!0},p(i,a){33620930&a&&(r=Ke(i[6]),j(),t=pn(t,a,c,1,i,r,o,e.parentNode,dn,Yt,e,Ot),O())},i(i){if(!n){for(let a=0;a<r.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)f(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function Ko(s){let e,n;return e=new me.Item({props:{disabled:!0,$$slots:{default:[Yo]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};33556480&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Xo(s){let e;return{c(){e=T(s[10])},m(n,t){x(n,e,t)},p(n,t){1024&t&&ee(e,n[10])},d(n){n&&b(e)}}}function Wo(s){let e,n;const t=s[18].item,o=Y(t,s,s[25],Ht),r=o||function(c){let i,a=c[7](c[27])+"";return{c(){i=T(a)},m(l,u){x(l,i,u)},p(l,u){192&u&&a!==(a=l[7](l[27])+"")&&ee(i,a)},d(l){l&&b(i)}}}(s);return{c(){r&&r.c(),e=z()},m(c,i){r&&r.m(c,i),x(c,e,i),n=!0},p(c,i){o?o.p&&(!n||33554496&i)&&Q(o,t,c,c[25],n?J(t,c[25],i,No):Z(c[25]),Ht):r&&r.p&&(!n||192&i)&&r.p(c,n?i:-1)},i(c){n||(p(r,c),n=!0)},o(c){f(r,c),n=!1},d(c){c&&b(e),r&&r.d(c)}}}function Yt(s,e){let n,t,o;function r(){return e[23](e[27])}return t=new me.Item({props:{onSelect:r,highlight:e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27]),$$slots:{default:[Wo]},$$scope:{ctx:e}}}),{key:s,first:null,c(){n=$e(),A(t.$$.fragment),this.first=n},m(c,i){x(c,n,i),R(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=r),706&i&&(a.highlight=e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27])),33554624&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){f(t.$$.fragment,c),o=!1},d(c){c&&b(n),k(t,c)}}}function Yo(s){let e,n,t,o,r,c;return n=new nt({props:{size:1,useCurrentColor:!0}}),{c(){e=S("div"),A(n.$$.fragment),t=z(),o=S("span"),r=T(s[11]),y(e,"class","c-searchable-dropdown__loading svelte-jowwyu")},m(i,a){x(i,e,a),R(n,e,null),N(e,t),N(e,o),N(o,r),c=!0},p(i,a){(!c||2048&a)&&ee(r,i[11])},i(i){c||(p(n.$$.fragment,i),c=!0)},o(i){f(n.$$.fragment,i),c=!1},d(i){i&&b(e),k(n)}}}function Qt(s){let e;const n=s[18].footer,t=Y(n,s,s[25],jt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||33554432&r)&&Q(t,n,o,o[25],e?J(n,o[25],r,Eo):Z(o[25]),jt)},i(o){e||(p(t,o),e=!0)},o(o){f(t,o),e=!1},d(o){t&&t.d(o)}}}function Qo(s){let e,n,t,o,r,c;const i=[Ko,qo,Vo],a=[];function l(m,g){return m[4]?0:m[6].length>0?1:2}e=l(s),n=a[e]=i[e](s);let u=s[17].footer&&Qt(s);return{c(){n.c(),t=z(),u&&u.c(),o=z(),r=S("div"),xn(r,"margin-bottom","var(--ds-spacing-2)")},m(m,g){a[e].m(m,g),x(m,t,g),u&&u.m(m,g),x(m,o,g),x(m,r,g),c=!0},p(m,g){let w=e;e=l(m),e===w?a[e].p(m,g):(j(),f(a[w],1,1,()=>{a[w]=null}),O(),n=a[e],n?n.p(m,g):(n=a[e]=i[e](m),n.c()),p(n,1),n.m(t.parentNode,t)),m[17].footer?u?(u.p(m,g),131072&g&&p(u,1)):(u=Qt(m),u.c(),p(u,1),u.m(o.parentNode,o)):u&&(j(),f(u,1,1,()=>{u=null}),O())},i(m){c||(p(n),p(u),c=!0)},o(m){f(n),f(u),c=!1},d(m){m&&(b(t),b(o),b(r)),a[e].d(m),u&&u.d(m)}}}function Zo(s){let e,n,t,o;e=new me.Trigger({props:{$$slots:{default:[Ho]},$$scope:{ctx:s}}});let r=!s[5]&&Wt(s);return{c(){A(e.$$.fragment),n=z(),r&&r.c(),t=$e()},m(c,i){R(e,c,i),x(c,n,i),r&&r.m(c,i),x(c,t,i),o=!0},p(c,i){const a={};33691709&i&&(a.$$scope={dirty:i,ctx:c}),e.$set(a),c[5]?r&&(j(),f(r,1,1,()=>{r=null}),O()):r?(r.p(c,i),32&i&&p(r,1)):(r=Wt(c),r.c(),p(r,1),r.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(r),o=!0)},o(c){f(e.$$.fragment,c),f(r),o=!1},d(c){c&&(b(n),b(t)),k(e,c),r&&r.d(c)}}}function Jo(s){let e,n,t,o;function r(i){s[24](i)}let c={onOpenChange:s[14],$$slots:{default:[Zo]},$$scope:{ctx:s}};return s[13]!==void 0&&(c.requestClose=s[13]),n=new me.Root({props:c}),he.push(()=>we(n,"requestClose",r)),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-searchable-dropdown svelte-jowwyu")},m(i,a){x(i,e,a),R(n,e,null),o=!0},p(i,[a]){const l={};33693439&a&&(l.$$scope={dirty:a,ctx:i}),!t&&8192&a&&(t=!0,l.requestClose=i[13],ve(()=>t=!1)),n.$set(l)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){f(n.$$.fragment,i),o=!1},d(i){i&&b(e),k(n)}}}function er(s,e,n){let{$$slots:t={},$$scope:o}=e;const r=dt(t);let{title:c=""}=e,{placeholder:i="Search..."}=e,{isLoading:a=!1}=e,{disabled:l=!1}=e,{searchValue:u=""}=e,{items:m=[]}=e,{selectedItem:g=null}=e,{itemLabelFn:w=C=>(C==null?void 0:C.toString())||""}=e,{itemKeyFn:h=C=>(C==null?void 0:C.toString())||""}=e,{isItemSelected:d}=e,{noItemsLabel:$="No items found"}=e,{loadingLabel:v="Loading..."}=e,L=!1,F=()=>{};const D=De();function P(C){n(0,u=C),D("search",C)}function U(C){n(1,g=C),D("select",C),F()}return s.$$set=C=>{"title"in C&&n(2,c=C.title),"placeholder"in C&&n(3,i=C.placeholder),"isLoading"in C&&n(4,a=C.isLoading),"disabled"in C&&n(5,l=C.disabled),"searchValue"in C&&n(0,u=C.searchValue),"items"in C&&n(6,m=C.items),"selectedItem"in C&&n(1,g=C.selectedItem),"itemLabelFn"in C&&n(7,w=C.itemLabelFn),"itemKeyFn"in C&&n(8,h=C.itemKeyFn),"isItemSelected"in C&&n(9,d=C.isItemSelected),"noItemsLabel"in C&&n(10,$=C.noItemsLabel),"loadingLabel"in C&&n(11,v=C.loadingLabel),"$$scope"in C&&n(25,o=C.$$scope)},[u,g,c,i,a,l,m,w,h,d,$,v,L,F,function(C){if(!l){if(n(12,L=C),C&&g){const H=w(g);n(0,u=H),D("search",""),setTimeout(()=>{const V=document.querySelector(".c-searchable-dropdown__trigger-input");V&&V.select()},0)}else C&&(n(0,u=""),D("search",""));D("openChange",C)}},P,U,r,t,function(C){Oe.call(this,s,C)},function(C){Oe.call(this,s,C)},function(){u=this.value,n(0,u)},C=>P(C.currentTarget.value),C=>U(C),function(C){F=C,n(13,F)},o]}class pt extends ie{constructor(e){super(),ae(this,e,er,Jo,le,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}function tr(s){let e,n,t;return n=new ot({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[ir]},$$scope:{ctx:s}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-commit-ref-selector__error svelte-14w5nl7")},m(o,r){x(o,e,r),R(n,e,null),t=!0},p(o,r){const c={};8195&r[0]|2&r[2]&&(c.$$scope={dirty:r,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function nr(s){var h,d;let e,n,t,o,r,c,i,a,l;function u($){s[30]($)}let m={title:((h=s[2])==null?void 0:h.name)||"Choose repository...",placeholder:"Search repositories...",itemKeyFn:fr,isLoading:s[8],disabled:!s[5].length,items:s[6],selectedItem:s[2],itemLabelFn:gr,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[lr],icon:[ar]},$$scope:{ctx:s}};function g($){s[35]($)}s[11]!==void 0&&(m.searchValue=s[11]),t=new pt({props:m}),he.push(()=>we(t,"searchValue",u)),t.$on("openChange",s[31]),t.$on("search",s[32]),t.$on("select",s[33]);let w={title:((d=s[3])==null?void 0:d.name)||"Choose branch...",itemKeyFn:hr,placeholder:"Search branches...",isLoading:s[16],disabled:s[14],items:s[7],selectedItem:s[3],itemLabelFn:wr,noItemsLabel:"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[pr],searchIcon:[mr],icon:[ur]},$$scope:{ctx:s}};return s[12]!==void 0&&(w.searchValue=s[12]),i=new pt({props:w}),he.push(()=>we(i,"searchValue",g)),i.$on("openChange",s[36]),i.$on("search",s[37]),i.$on("select",s[38]),{c(){e=S("div"),n=S("div"),A(t.$$.fragment),r=z(),c=S("div"),A(i.$$.fragment),y(n,"class","c-commit-ref-selector__selector svelte-14w5nl7"),y(c,"class","c-commit-ref-selector__selector svelte-14w5nl7"),y(e,"class","c-commit-ref-selector__selectors-container svelte-14w5nl7")},m($,v){x($,e,v),N(e,n),R(t,n,null),N(e,r),N(e,c),R(i,c,null),l=!0},p($,v){var D,P;const L={};4&v[0]&&(L.title=((D=$[2])==null?void 0:D.name)||"Choose repository..."),256&v[0]&&(L.isLoading=$[8]),32&v[0]&&(L.disabled=!$[5].length),64&v[0]&&(L.items=$[6]),4&v[0]&&(L.selectedItem=$[2]),2&v[2]&&(L.$$scope={dirty:v,ctx:$}),!o&&2048&v[0]&&(o=!0,L.searchValue=$[11],ve(()=>o=!1)),t.$set(L);const F={};8&v[0]&&(F.title=((P=$[3])==null?void 0:P.name)||"Choose branch..."),65536&v[0]&&(F.isLoading=$[16]),16384&v[0]&&(F.disabled=$[14]),128&v[0]&&(F.items=$[7]),8&v[0]&&(F.selectedItem=$[3]),1552&v[0]|2&v[2]&&(F.$$scope={dirty:v,ctx:$}),!a&&4096&v[0]&&(a=!0,F.searchValue=$[12],ve(()=>a=!1)),i.$set(F)},i($){l||(p(t.$$.fragment,$),p(i.$$.fragment,$),l=!0)},o($){f(t.$$.fragment,$),f(i.$$.fragment,$),l=!1},d($){$&&b(e),k(t),k(i)}}}function or(s){let e,n;return e=new Pe({props:{variant:"ghost",color:"warning",size:1,loading:s[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[cr],default:[sr]},$$scope:{ctx:s}}}),e.$on("click",s[21]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};2&o[0]&&(r.loading=t[1]),2&o[2]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function rr(s){let e,n;return e=new Fo({}),e.$on("authStateChange",s[39]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function sr(s){let e;return{c(){e=T("Reload available repos and branches")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function cr(s){let e,n,t;return n=new mn({}),{c(){e=S("span"),A(n.$$.fragment),y(e,"slot","iconLeft"),y(e,"class","svelte-14w5nl7")},m(o,r){x(o,e,r),R(n,e,null),t=!0},p:B,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function ir(s){let e,n,t,o,r,c,i;const a=[rr,or],l=[];function u(m,g){return m[13]?1:0}return r=u(s),c=l[r]=a[r](s),{c(){e=S("div"),n=S("div"),t=T(s[0]),o=z(),c.c(),y(n,"class","c-commit-ref-selector__error-message svelte-14w5nl7"),y(e,"class","c-commit-ref-selector__error-content svelte-14w5nl7")},m(m,g){x(m,e,g),N(e,n),N(n,t),N(e,o),l[r].m(e,null),i=!0},p(m,g){(!i||1&g[0])&&ee(t,m[0]);let w=r;r=u(m),r===w?l[r].p(m,g):(j(),f(l[w],1,1,()=>{l[w]=null}),O(),c=l[r],c?c.p(m,g):(c=l[r]=a[r](m),c.c()),p(c,1),c.m(e,null))},i(m){i||(p(c),i=!0)},o(m){f(c),i=!1},d(m){m&&b(e),l[r].d()}}}function ar(s){let e,n;return e=new wt({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function lr(s){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ur(s){let e,n;return e=new _n({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function mr(s){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Zt(s){let e,n,t,o,r,c,i,a,l;return t=new Mn({}),c=new Me({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[$r]},$$scope:{ctx:s}}}),{c(){e=S("button"),n=S("div"),A(t.$$.fragment),o=z(),r=S("div"),A(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-14w5nl7"),y(r,"class","c-commit-ref-selector__item-content svelte-14w5nl7"),y(e,"type","button"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-14w5nl7")},m(u,m){x(u,e,m),N(e,n),R(t,n,null),N(e,o),N(e,r),R(c,r,null),i=!0,a||(l=xe(e,"click",s[34]),a=!0)},p(u,m){const g={};2&m[2]&&(g.$$scope={dirty:m,ctx:u}),c.$set(g)},i(u){i||(p(t.$$.fragment,u),p(c.$$.fragment,u),i=!0)},o(u){f(t.$$.fragment,u),f(c.$$.fragment,u),i=!1},d(u){u&&b(e),k(t),k(c),a=!1,l()}}}function $r(s){let e;return{c(){e=T("Load more branches")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function pr(s){let e,n,t=s[10]&&!s[4]&&Zt(s);return{c(){t&&t.c(),e=$e()},m(o,r){t&&t.m(o,r),x(o,e,r),n=!0},p(o,r){o[10]&&!o[4]?t?(t.p(o,r),1040&r[0]&&p(t,1)):(t=Zt(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(j(),f(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){f(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function dr(s){let e,n,t,o,r;const c=[nr,tr],i=[];function a(l,u){return l[15]?l[15]?1:-1:0}return~(t=a(s))&&(o=i[t]=c[t](s)),{c(){e=S("div"),n=S("div"),o&&o.c(),y(n,"class","c-commit-ref-selector__content svelte-14w5nl7"),y(e,"class","c-commit-ref-selector svelte-14w5nl7")},m(l,u){x(l,e,u),N(e,n),~t&&i[t].m(n,null),r=!0},p(l,u){let m=t;t=a(l),t===m?~t&&i[t].p(l,u):(o&&(j(),f(i[m],1,1,()=>{i[m]=null}),O()),~t?(o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){r||(p(o),r=!0)},o(l){f(o),r=!1},d(l){l&&b(e),~t&&i[t].d()}}}const fr=s=>`${s.owner}-${s.name}`,gr=s=>`${s.owner}/${s.name}`,hr=s=>`${s.name}-${s.commit.sha}`,wr=s=>s.name.replace("origin/","");function vr(s,e,n){let t,o,r;const c=_e(gt.key),i=De(),a=_e(rt.key);let l,u,{errorMessage:m=""}=e,{isLoading:g=!1}=e,{lastUsedBranchName:w=null}=e,{lastUsedRepoUrl:h=null}=e,d=[],$=d,v=[],L=v,F=!1,D=!1,P=0,U=!1,C=!1,H=!1,V="",W="";function re(I){n(11,V=I),C=!0,Be(I)}function se(I){n(12,W=I),n(29,H=!0),Te(I)}const ce={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function Fe(){n(8,D=!0),n(4,F=!0);const{repos:I,error:G,isDevDeploy:X}=await c.listUserRepos();if(X)return await async function(){console.warn("Fetching branches from local git environment.");const{remoteUrl:fe,error:Ae}=await c.getRemoteUrl();n(1,g=!0);const Ee=Ct(fe);if(!Ee||Ae)return _(Ae??ce.failedToParseRemoteUrl),void n(1,g=!1);n(2,l={name:Ee.name,owner:Ee.owner,html_url:fe}),n(5,d=[l]),n(6,$=d);const at=function(Ie){const je=Ie.find(ye=>ye.isCurrentBranch),lt=Ie.find(ye=>ye.isDefault),yn=!!je&&(je==null?void 0:je.name)===(lt==null?void 0:lt.name.replace("origin/",""));return Ie.filter(ye=>(!yn||!ye.isDefault)&&(!ye.isCurrentBranch||!ye.isRemote)&&!!ye.isRemote&&ye.isRemote)}((await c.listBranches()).branches),Ge=at.find(Ie=>Ie.isDefault);n(3,u={name:Ge!=null&&Ge.name?At(Ge.name):at[0].name,commit:{sha:"",url:""},protected:!1}),n(28,v=at.map(Ie=>({name:At(Ie.name),commit:{sha:"",url:""},protected:!1}))),n(7,L=v),t||ne(),n(1,g=!1)}(),void n(8,D=!1);if(G)return _(`An error occured while fetching your repos. If this continues, please contact support. Error: ${G}`),n(1,g=!1),void n(8,D=!1);if(n(5,d=I),n(6,$=d),!l&&h){const fe=d.find(Ae=>Ae.html_url===h);fe&&n(2,l=fe)}const{remoteUrl:Ue,error:vn}=await c.getRemoteUrl(),bn=Ct(Ue);if(vn)return n(1,g=!1),void n(8,D=!1);const{owner:st,name:ct}=bn||{},it=d.find(fe=>fe.name===ct&&fe.owner===st);if(it&&!l)n(2,l=it);else if(!it&&ct&&st){const fe={name:ct,owner:st,html_url:Ue};try{const{repo:Ae,error:Ee}=await c.getGithubRepo(fe);Ee?(console.warn("Failed to fetch GitHub repo details:",Ee),n(2,l=d[0])):(n(2,l=Ae),n(5,d=[l,...d]))}catch(Ae){console.error("Error fetching GitHub repo:",Ae),n(2,l=d[0])}}else if(!l)return n(1,g=!1),void n(8,D=!1);n(8,D=!1)}async function pe(I){if(!l)return;n(4,F=!0);const G=l;do{if(G!==l){n(4,F=!1),n(28,v=[]);break}const X=await c.listRepoBranches(l,I);if(X.error)return _(`Failed to fetch branches for the repo ${l.owner}/${l.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${X.error}`),void n(1,g=!1);if(n(28,v=[...v,...X.branches]),n(10,U=X.hasNextPage),q(),!U)break;I=X.nextPage,n(9,P++,P),E()}while(P%20!=0&&U);n(7,L=v),n(4,F=!1)}function q(){if(l&&!u){if(w){const I=v.find(G=>G.name===w);if(I)return n(3,u=I),void ne()}if(l.default_branch){const I=l.default_branch;if(v.length===0)return n(3,u={name:I,commit:{sha:"",url:""},protected:!1}),void ne();const G=L.find(X=>X.name===I);if(G)return n(3,u=G),void ne()}F||n(3,u=v[0]),ne()}}function Se(){l&&async function(){l&&(n(9,P=0),await pe(P+1))}().then(()=>{E(),n(1,g=!1),t||ne()}).catch(I=>{console.error("Error fetching all branches:",I),_(`Failed to fetch branches: ${I instanceof Error?I.message:String(I)}`)})}tt(async()=>{await be()});let de=!0;const te=async()=>{try{n(13,de=await c.isGithubAuthenticated()),de||_("Please authenticate with GitHub to use this feature.")}catch(I){console.error("Failed to check GitHub authentication status:",I),_("Please authenticate with GitHub to use this feature."),n(13,de=!1)}};async function Le(){n(1,g=!0),n(15,t=!1),n(0,m="");try{if(await te(),!de)return void n(1,g=!1);await Fe(),t||Se(),t||ne()}catch(I){console.error("Error fetching git data:",I),_(ce.failedToFetchBranches)}finally{n(1,g=!1)}}async function be(){n(1,g=!0);try{await Le()}catch(I){console.error("Error fetching and syncing branches:",I),_("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,g=!1)}}function _(I){console.error("Error:",I),n(15,t=!0),n(0,m=I)}async function E(I=""){n(15,t=!1);try{if(H&&W.trim()!=="")n(7,(G=I||W,L=v.filter(X=>X.name.includes(G.toLowerCase()))));else{let X;n(7,L=v.filter(Ue=>Ue.name!==(l==null?void 0:l.default_branch)||(X=Ue,!1))),X?L.unshift(X):l!=null&&l.default_branch&&L.unshift({name:l.default_branch,commit:{sha:"",url:""},protected:!1})}}catch(X){console.error("Error fetching branches:",X),n(7,L=[]),_(ce.failedToFetchBranches)}var G}async function M(I){n(3,u=I),n(29,H=!1),Ce((u==null?void 0:u.name)??""),E();const G=a.creationMetrics;a.setCreationMetrics({changedRepo:(G==null?void 0:G.changedRepo)??!1,changedBranch:!0}),ne()}async function oe(I){n(4,F=!0),n(2,l=I),n(3,u=void 0),n(28,v=[]),n(7,L=[]),C=!1,ze(""),n(6,$=d),Se();const G=a.creationMetrics;a.setCreationMetrics({changedRepo:!0,changedBranch:(G==null?void 0:G.changedBranch)??!1})}function K(I,G){I||(G==="repo"?C=!1:(G==="branch"||(C=!1),n(29,H=!1)))}function ne(){if(!(l!=null&&l.html_url)||!u)return;const I={github_commit_ref:{repository_url:l.html_url,git_ref:u.name}};i("commitRefChange",{commitRef:I,selectedBranch:u})}const Ce=I=>{n(12,W=I)},ze=I=>{n(11,V=I)},Be=_t(async function(I=""){n(15,t=!1);try{C?n(6,(G=I||V,$=d.filter(X=>X.name.includes(G.toLowerCase())||X.owner.includes(G.toLowerCase())))):n(6,$=d)}catch(X){console.error("Error fetching repos:",X),n(6,$=[]),_(ce.failedToFetchFromRemote)}var G},300,{leading:!1,trailing:!0}),Te=_t(E,300,{leading:!1,trailing:!0});function bt(I){I&&r||K(I,"branch")}function yt(I){I&&!d.length||K(I,"repo")}return s.$$set=I=>{"errorMessage"in I&&n(0,m=I.errorMessage),"isLoading"in I&&n(1,g=I.isLoading),"lastUsedBranchName"in I&&n(26,w=I.lastUsedBranchName),"lastUsedRepoUrl"in I&&n(27,h=I.lastUsedRepoUrl)},s.$$.update=()=>{1&s.$$.dirty[0]&&n(15,t=m!==""),28&s.$$.dirty[0]&&n(16,o=l&&F&&!u),536870920&s.$$.dirty[0]&&Ce(H?"":(u==null?void 0:u.name)??""),805306372&s.$$.dirty[0]&&n(14,r=!l||!H&&!v.length)},[m,g,l,u,F,d,$,L,D,P,U,V,W,de,r,t,o,re,se,pe,te,be,M,oe,bt,yt,w,h,v,H,function(I){V=I,n(11,V)},I=>yt(I.detail),I=>re(I.detail),I=>oe(I.detail),()=>{pe(P+1)},function(I){W=I,n(12,W)},I=>bt(I.detail),I=>se(I.detail),I=>M(I.detail),async()=>{await te(),de&&await be()}]}class br extends ie{constructor(e){super(),ae(this,e,vr,dr,le,{errorMessage:0,isLoading:1,lastUsedBranchName:26,lastUsedRepoUrl:27},null,[-1,-1,-1])}}function yr(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ue(o,t[r]);return{c(){e=Xe("svg"),n=new We(!0),this.h()},l(r){e=Ye(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ze(c,!0),c.forEach(b),this.h()},h(){n.a=null,Re(e,o)},m(r,c){Je(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(r,[c]){Re(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&r[0]]))},i:B,o:B,d(r){r&&b(e)}}}function xr(s,e,n){return s.$$set=t=>{n(0,e=ue(ue({},e),ke(t)))},[e=ke(e)]}class _r extends ie{constructor(e){super(),ae(this,e,xr,yr,le,{})}}function Lr(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ue(o,t[r]);return{c(){e=Xe("svg"),n=new We(!0),this.h()},l(r){e=Ye(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ze(c,!0),c.forEach(b),this.h()},h(){n.a=null,Re(e,o)},m(r,c){Je(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',e)},p(r,[c]){Re(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&r[0]]))},i:B,o:B,d(r){r&&b(e)}}}function Cr(s,e,n){return s.$$set=t=>{n(0,e=ue(ue({},e),ke(t)))},[e=ke(e)]}class Ar extends ie{constructor(e){super(),ae(this,e,Cr,Lr,le,{})}}const Rr=s=>({}),Jt=s=>({});function en(s){let e,n;const t=s[12].icon,o=Y(t,s,s[11],Jt);return{c(){e=S("div"),o&&o.c(),y(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(r,c){x(r,e,c),o&&o.m(e,null),n=!0},p(r,c){o&&o.p&&(!n||2048&c)&&Q(o,t,r,r[11],n?J(t,r[11],c,Rr):Z(r[11]),Jt)},i(r){n||(p(o,r),n=!0)},o(r){f(o,r),n=!1},d(r){r&&b(e),o&&o.d(r)}}}function kr(s){let e,n,t,o,r;return{c(){e=S("span"),n=T(s[0]),t=z(),o=S("span"),r=T(s[1]),y(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),y(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,i){x(c,e,i),N(e,n),x(c,t,i),x(c,o,i),N(o,r)},p(c,i){1&i&&ee(n,c[0]),2&i&&ee(r,c[1])},i:B,o:B,d(c){c&&(b(e),b(t),b(o))}}}function Sr(s){let e,n,t,o,r,c,i,a,l;function u(h){s[15](h)}function m(h){s[16](h)}let g={size:1,variant:"surface"};s[6]!==void 0&&(g.value=s[6]),s[5]!==void 0&&(g.textInput=s[5]),t=new Fn({props:g}),he.push(()=>we(t,"value",u)),he.push(()=>we(t,"textInput",m)),t.$on("keydown",s[8]),t.$on("blur",s[9]);let w=s[7]&&function(h){let d;return{c(){d=S("span"),d.textContent=`${h[7]}`,y(d,"class","c-setup-script-selector__extension svelte-udt6j8")},m($,v){x($,d,v)},p:B,d($){$&&b(d)}}}(s);return{c(){e=S("div"),n=S("div"),A(t.$$.fragment),c=z(),w&&w.c(),y(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),y(n,"role","presentation"),y(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),y(e,"role","presentation")},m(h,d){x(h,e,d),N(e,n),R(t,n,null),N(n,c),w&&w.m(n,null),i=!0,a||(l=[xe(e,"click",He(s[13])),xe(e,"mousedown",He(s[14]))],a=!0)},p(h,d){const $={};!o&&64&d&&(o=!0,$.value=h[6],ve(()=>o=!1)),!r&&32&d&&(r=!0,$.textInput=h[5],ve(()=>r=!1)),t.$set($),h[7]&&w.p(h,d)},i(h){i||(p(t.$$.fragment,h),i=!0)},o(h){f(t.$$.fragment,h),i=!1},d(h){h&&b(e),k(t),w&&w.d(),a=!1,ft(l)}}}function Ir(s){let e,n,t,o,r,c,i,a,l=s[10].icon&&en(s);const u=[Sr,kr],m=[];function g(d,$){return d[3]?0:1}o=g(s),r=m[o]=u[o](s);const w=s[12].default,h=Y(w,s,s[11],null);return{c(){e=S("div"),l&&l.c(),n=z(),t=S("div"),r.c(),c=z(),i=S("div"),h&&h.c(),y(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),y(i,"class","c-setup-script-selector__script-actions svelte-udt6j8"),y(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),y(e,"role","presentation"),ge(e,"c-setup-script-selector__script-item-content--renaming",s[3]),ge(e,"c-setup-script-selector__script-item-content--is-path",s[2]),ge(e,"c-setup-script-selector__script-item-content--selected",s[4])},m(d,$){x(d,e,$),l&&l.m(e,null),N(e,n),N(e,t),m[o].m(t,null),N(e,c),N(e,i),h&&h.m(i,null),a=!0},p(d,[$]){d[10].icon?l?(l.p(d,$),1024&$&&p(l,1)):(l=en(d),l.c(),p(l,1),l.m(e,n)):l&&(j(),f(l,1,1,()=>{l=null}),O());let v=o;o=g(d),o===v?m[o].p(d,$):(j(),f(m[v],1,1,()=>{m[v]=null}),O(),r=m[o],r?r.p(d,$):(r=m[o]=u[o](d),r.c()),p(r,1),r.m(t,null)),h&&h.p&&(!a||2048&$)&&Q(h,w,d,d[11],a?J(w,d[11],$,null):Z(d[11]),null),(!a||8&$)&&ge(e,"c-setup-script-selector__script-item-content--renaming",d[3]),(!a||4&$)&&ge(e,"c-setup-script-selector__script-item-content--is-path",d[2]),(!a||16&$)&&ge(e,"c-setup-script-selector__script-item-content--selected",d[4])},i(d){a||(p(l),p(r),p(h,d),a=!0)},o(d){f(l),f(r),f(h,d),a=!1},d(d){d&&b(e),l&&l.d(),m[o].d(),h&&h.d(d)}}}function Fr(s,e,n){let{$$slots:t={},$$scope:o}=e;const r=dt(t);let{name:c}=e,{path:i}=e,{isPath:a=!1}=e,{isRenaming:l=!1}=e,{isSelected:u=!1}=e;const m=De(),{baseName:g,extension:w}=function($){const v=$.lastIndexOf(".");return v===-1?{baseName:$,extension:""}:{baseName:$.substring(0,v),extension:$.substring(v)}}(c);let h,d=g;return s.$$set=$=>{"name"in $&&n(0,c=$.name),"path"in $&&n(1,i=$.path),"isPath"in $&&n(2,a=$.isPath),"isRenaming"in $&&n(3,l=$.isRenaming),"isSelected"in $&&n(4,u=$.isSelected),"$$scope"in $&&n(11,o=$.$$scope)},s.$$.update=()=>{40&s.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,i,a,l,u,h,d,w,function($){if($.key!=="ArrowLeft"&&$.key!=="ArrowRight"&&$.key!=="ArrowUp"&&$.key!=="ArrowDown")if($.key==="Enter")if($.preventDefault(),d.trim()&&d!==g){const v=d.trim()+w;m("rename",{oldName:c,newName:v})}else m("cancelRename");else $.key==="Escape"&&($.preventDefault(),$.stopPropagation(),m("cancelRename"));else $.stopPropagation()},function(){m("cancelRename")},r,o,t,function($){Oe.call(this,s,$)},function($){Oe.call(this,s,$)},function($){d=$,n(6,d)},function($){h=$,n(5,h)}]}class Er extends ie{constructor(e){super(),ae(this,e,Fr,Ir,le,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function tn(s){let e,n,t,o,r,c,i,a,l,u;function m(w){s[34](w)}let g={placeholder:"Search scripts...",isLoading:s[1],disabled:!1,items:s[7],selectedItem:s[2],itemLabelFn:ls,itemKeyFn:us,isItemSelected:ms,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[Yr,({item:w})=>({45:w}),({item:w})=>[0,w?16384:0]],searchIcon:[Tr],icon:[Br],title:[zr]},$$scope:{ctx:s}};return s[3]!==void 0&&(g.searchValue=s[3]),t=new pt({props:g}),he.push(()=>we(t,"searchValue",m)),t.$on("openChange",s[35]),t.$on("search",s[36]),t.$on("select",s[37]),i=new Ne({props:{content:s[10],$$slots:{default:[es]},$$scope:{ctx:s}}}),l=new Ne({props:{content:"Open a new file for you to write a setup script that you can edit directly.",$$slots:{default:[rs]},$$scope:{ctx:s}}}),{c(){e=S("div"),n=S("div"),A(t.$$.fragment),r=z(),c=S("div"),A(i.$$.fragment),a=z(),A(l.$$.fragment),y(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),y(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),y(e,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(w,h){x(w,e,h),N(e,n),R(t,n,null),N(n,r),N(n,c),R(i,c,null),N(c,a),R(l,c,null),u=!0},p(w,h){const d={};2&h[0]&&(d.isLoading=w[1]),128&h[0]&&(d.items=w[7]),4&h[0]&&(d.selectedItem=w[2]),884&h[0]|49152&h[1]&&(d.$$scope={dirty:h,ctx:w}),!o&&8&h[0]&&(o=!0,d.searchValue=w[3],ve(()=>o=!1)),t.$set(d);const $={};1024&h[0]&&($.content=w[10]),2048&h[0]|32768&h[1]&&($.$$scope={dirty:h,ctx:w}),i.$set($);const v={};32768&h[1]&&(v.$$scope={dirty:h,ctx:w}),l.$set(v)},i(w){u||(p(t.$$.fragment,w),p(i.$$.fragment,w),p(l.$$.fragment,w),u=!0)},o(w){f(t.$$.fragment,w),f(i.$$.fragment,w),f(l.$$.fragment,w),u=!1},d(w){w&&b(e),k(t),k(i),k(l)}}}function Nr(s){let e,n;return e=new gn({props:{$$slots:{text:[Pr]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};16&o[0]|32768&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Dr(s){let e,n;return e=new gn({props:{$$slots:{grayText:[Mr],text:[Ur]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};768&o[0]|32768&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Pr(s){let e,n;return{c(){e=S("span"),n=T(s[4]),y(e,"slot","text")},m(t,o){x(t,e,o),N(e,n)},p(t,o){16&o[0]&&ee(n,t[4])},d(t){t&&b(e)}}}function Ur(s){let e,n;return{c(){e=S("span"),n=T(s[9]),y(e,"slot","text")},m(t,o){x(t,e,o),N(e,n)},p(t,o){512&o[0]&&ee(n,t[9])},d(t){t&&b(e)}}}function Mr(s){let e,n;return{c(){e=S("span"),n=T(s[8]),y(e,"slot","grayText")},m(t,o){x(t,e,o),N(e,n)},p(t,o){256&o[0]&&ee(n,t[8])},d(t){t&&b(e)}}}function zr(s){let e,n,t,o;const r=[Dr,Nr],c=[];function i(a,l){return a[5]?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),y(e,"slot","title")},m(a,l){x(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(j(),f(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){f(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Br(s){let e,n;return e=new fn({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Tr(s){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Gr(s){var t;let e,n;return e=new Er({props:{name:s[45].name,path:s[45].path,isPath:!0,isRenaming:((t=s[6])==null?void 0:t.path)===s[45].path,isSelected:!(!s[2]||s[2].path!==s[45].path),$$slots:{default:[Wr]},$$scope:{ctx:s}}}),e.$on("rename",function(...o){return s[33](s[45],...o)}),e.$on("cancelRename",s[21]),{c(){A(e.$$.fragment)},m(o,r){R(e,o,r),n=!0},p(o,r){var i;s=o;const c={};16384&r[1]&&(c.name=s[45].name),16384&r[1]&&(c.path=s[45].path),64&r[0]|16384&r[1]&&(c.isRenaming=((i=s[6])==null?void 0:i.path)===s[45].path),4&r[0]|16384&r[1]&&(c.isSelected=!(!s[2]||s[2].path!==s[45].path)),49152&r[1]&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){f(e.$$.fragment,o),n=!1},d(o){k(e,o)}}}function jr(s){let e,n,t,o;return n=new fn({}),{c(){e=S("div"),A(n.$$.fragment),t=T(`
                  Use basic environment`),y(e,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(r,c){x(r,e,c),R(n,e,null),N(e,t),o=!0},p:B,i(r){o||(p(n.$$.fragment,r),o=!0)},o(r){f(n.$$.fragment,r),o=!1},d(r){r&&b(e),k(n)}}}function Or(s){let e,n;return e=new En({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Hr(s){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Or]},$$scope:{ctx:s}}}),e.$on("click",function(...t){return s[30](s[45],...t)}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){s=t;const r={};32768&o[1]&&(r.$$scope={dirty:o,ctx:s}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Vr(s){let e,n;return e=new _r({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function qr(s){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Vr]},$$scope:{ctx:s}}}),e.$on("click",function(...t){return s[31](s[45],...t)}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){s=t;const r={};32768&o[1]&&(r.$$scope={dirty:o,ctx:s}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Kr(s){let e,n;return e=new Sn({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Xr(s){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Kr]},$$scope:{ctx:s}}}),e.$on("click",function(...t){return s[32](s[45],...t)}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){s=t;const r={};32768&o[1]&&(r.$$scope={dirty:o,ctx:s}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Wr(s){let e,n,t,o,r,c;return e=new Ne({props:{content:"Open script in editor",$$slots:{default:[Hr]},$$scope:{ctx:s}}}),t=new Ne({props:{content:"Rename script",$$slots:{default:[qr]},$$scope:{ctx:s}}}),r=new Ne({props:{content:"Delete script",$$slots:{default:[Xr]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment),n=z(),A(t.$$.fragment),o=z(),A(r.$$.fragment)},m(i,a){R(e,i,a),x(i,n,a),R(t,i,a),x(i,o,a),R(r,i,a),c=!0},p(i,a){const l={};49152&a[1]&&(l.$$scope={dirty:a,ctx:i}),e.$set(l);const u={};49152&a[1]&&(u.$$scope={dirty:a,ctx:i}),t.$set(u);const m={};49152&a[1]&&(m.$$scope={dirty:a,ctx:i}),r.$set(m)},i(i){c||(p(e.$$.fragment,i),p(t.$$.fragment,i),p(r.$$.fragment,i),c=!0)},o(i){f(e.$$.fragment,i),f(t.$$.fragment,i),f(r.$$.fragment,i),c=!1},d(i){i&&(b(n),b(o)),k(e,i),k(t,i),k(r,i)}}}function Yr(s){let e,n,t,o;const r=[jr,Gr],c=[];function i(a,l){return a[45]===null?0:1}return e=i(s),n=c[e]=r[e](s),{c(){n.c(),t=$e()},m(a,l){c[e].m(a,l),x(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(j(),f(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){f(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Qr(s){let e,n;return{c(){e=T("Auto-generate"),n=S("span"),n.textContent="a script",y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(t,o){x(t,e,o),x(t,n,o)},p:B,d(t){t&&(b(e),b(n))}}}function Zr(s){let e,n;return e=new Ar({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Jr(s){let e,n;return e=new un({props:{slot:"iconRight"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function es(s){let e,n;return e=new Pe({props:{variant:"soft",color:"neutral",size:1,disabled:s[11],$$slots:{iconRight:[Jr],iconLeft:[Zr],default:[Qr]},$$scope:{ctx:s}}}),e.$on("click",s[14]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};2048&o[0]&&(r.disabled=t[11]),32768&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ts(s){let e,n,t;return{c(){e=T("Write "),n=S("span"),n.textContent="a script",t=T("by hand"),y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(o,r){x(o,e,r),x(o,n,r),x(o,t,r)},p:B,d(o){o&&(b(e),b(n),b(t))}}}function ns(s){let e,n;return e=new kn({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function os(s){let e,n;return e=new un({props:{slot:"iconRight"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function rs(s){let e,n;return e=new Pe({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[os],iconLeft:[ns],default:[ts]},$$scope:{ctx:s}}}),e.$on("click",s[15]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};32768&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function nn(s){let e,n,t;return n=new ot({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[is]},$$scope:{ctx:s}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-setup-script-selector__error svelte-3cd2r2")},m(o,r){x(o,e,r),R(n,e,null),t=!0},p(o,r){const c={};3&r[0]|32768&r[1]&&(c.$$scope={dirty:r,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function ss(s){let e;return{c(){e=T("Refresh")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function cs(s){let e,n,t;return n=new mn({}),{c(){e=S("span"),A(n.$$.fragment),y(e,"slot","iconLeft")},m(o,r){x(o,e,r),R(n,e,null),t=!0},p:B,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){f(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function is(s){let e,n,t,o,r,c;return r=new Pe({props:{variant:"ghost",color:"warning",size:1,loading:s[1],$$slots:{iconLeft:[cs],default:[ss]},$$scope:{ctx:s}}}),r.$on("click",s[17]),{c(){e=S("div"),n=S("div"),t=T(s[0]),o=z(),A(r.$$.fragment),y(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),y(e,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(i,a){x(i,e,a),N(e,n),N(n,t),N(e,o),R(r,e,null),c=!0},p(i,a){(!c||1&a[0])&&ee(t,i[0]);const l={};2&a[0]&&(l.loading=i[1]),32768&a[1]&&(l.$$scope={dirty:a,ctx:i}),r.$set(l)},i(i){c||(p(r.$$.fragment,i),c=!0)},o(i){f(r.$$.fragment,i),c=!1},d(i){i&&b(e),k(r)}}}function as(s){let e,n,t,o,r=(!s[12]||s[0]===s[16].noScriptsFound)&&tn(s),c=s[12]&&s[0]!==s[16].noScriptsFound&&nn(s);return{c(){e=S("div"),n=S("div"),r&&r.c(),t=z(),c&&c.c(),y(n,"class","c-setup-script-selector__content svelte-3cd2r2"),y(e,"class","c-setup-script-selector svelte-3cd2r2")},m(i,a){x(i,e,a),N(e,n),r&&r.m(n,null),N(n,t),c&&c.m(n,null),o=!0},p(i,a){i[12]&&i[0]!==i[16].noScriptsFound?r&&(j(),f(r,1,1,()=>{r=null}),O()):r?(r.p(i,a),4097&a[0]&&p(r,1)):(r=tn(i),r.c(),p(r,1),r.m(n,t)),i[12]&&i[0]!==i[16].noScriptsFound?c?(c.p(i,a),4097&a[0]&&p(c,1)):(c=nn(i),c.c(),p(c,1),c.m(n,null)):c&&(j(),f(c,1,1,()=>{c=null}),O())},i(i){o||(p(r),p(c),o=!0)},o(i){f(r),f(c),o=!1},d(i){i&&b(e),r&&r.d(),c&&c.d()}}}const ls=s=>(s==null?void 0:s.name)||"",us=s=>`${s==null?void 0:s.path}-${s==null?void 0:s.location}-${s==null?void 0:s.name}`,ms=(s,e)=>s===null&&e===null||!(!s||!e)&&s.path===e.path;function $s(s,e,n){var be;let t,o,r,c,i,a,l,u,{errorMessage:m=""}=e,{isLoading:g=!1}=e,{lastUsedScriptPath:w=null}=e,{disableNewAgentCreation:h=!1}=e;const d=_e(rt.key),$=De(),v=_e("chatModel").extensionClient,L=_=>{v.openFile({repoRoot:"",pathName:_.path,allowOutOfWorkspace:!0,openLocalUri:_.location==="home"})};let F=[],D=((be=d.newAgentDraft)==null?void 0:be.setupScript)??null,P="",U=null,C=F,H=!0;const V={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function W(){n(0,m="");try{const _=D==null?void 0:D.path;if(n(28,F=await d.listSetupScripts()),H)if(w&&F.length>0){const E=F.find(M=>M.path===w);E&&(n(2,D=E),Le())}else w===null&&(n(2,D=null),Le());else if(_){const E=F.find(M=>M.path===_);E&&n(2,D=E)}H=!1,F.length===0?n(0,m=V.noScriptsFound):n(0,m="")}catch(_){console.error("Error fetching setup scripts:",_),n(0,m=V.failedToFetchScripts)}}async function re(_,E){E&&E.stopPropagation();try{const M=await d.deleteSetupScript(_.name,_.location);M.success?((D==null?void 0:D.path)===_.path&&te(null),await W()):(console.error("Failed to delete script:",M.error),pe(`Failed to delete script: ${M.error||"Unknown error"}`))}catch(M){console.error("Error deleting script:",M),pe(`Error deleting script: ${M instanceof Error?M.message:String(M)}`)}}async function se(_,E){E&&E.stopPropagation(),n(6,U=_)}async function ce(_,E){const{oldName:M,newName:oe}=E.detail;try{const K=await d.renameSetupScript(M,oe,_.location);if(K.success){await W();const ne=F.find(Ce=>Ce.path===K.path);ne&&te(ne)}else console.error("Failed to rename script:",K.error),pe(`Failed to rename script: ${K.error||"Unknown error"}`)}catch(K){console.error("Error renaming script:",K),pe(`Error renaming script: ${K instanceof Error?K.message:String(K)}`)}finally{Fe()}}function Fe(){n(6,U=null)}function pe(_){n(0,m=_)}function q(_){n(3,P=_)}function Se(_){te(_)}function de(_){_&&(W(),n(3,P=""))}async function te(_){n(2,D=_),Le(),d.saveLastRemoteAgentSetup(null,null,(D==null?void 0:D.path)||null)}function Le(){$("setupScriptChange",{script:D})}return tt(async()=>{var _;await W(),w===null?te(null):(_=d.newAgentDraft)!=null&&_.setupScript&&!D&&te(d.newAgentDraft.setupScript)}),s.$$set=_=>{"errorMessage"in _&&n(0,m=_.errorMessage),"isLoading"in _&&n(1,g=_.isLoading),"lastUsedScriptPath"in _&&n(26,w=_.lastUsedScriptPath),"disableNewAgentCreation"in _&&n(27,h=_.disableNewAgentCreation)},s.$$.update=()=>{var _,E;if(1&s.$$.dirty[0]&&n(12,t=m!==""),134217728&s.$$.dirty[0]&&n(11,o=h||((_=d.newAgentDraft)==null?void 0:_.isDisabled)||!d.newAgentDraft),134217728&s.$$.dirty[0]&&n(10,r=d.newAgentDraft?(E=d.newAgentDraft)!=null&&E.isDisabled?"Please resolve the issues with your workspace selection":h?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),268435464&s.$$.dirty[0])if(P.trim()!==""){const M="Use basic environment".toLowerCase().includes(P.toLowerCase()),oe=F.filter(K=>K.name.toLowerCase().includes(P.toLowerCase())||K.path.toLowerCase().includes(P.toLowerCase()));n(7,C=M?[null,...oe]:oe)}else n(7,C=[null,...F]);6&s.$$.dirty[0]&&n(29,c=()=>g?"...":D?D.isGenerateOption?D.name:D.location==="home"?"~/.augment/env/"+D.name:D.path:"Use basic environment"),536870912&s.$$.dirty[0]&&n(4,i=c()),4&s.$$.dirty[0]&&n(5,a=!!(D!=null&&D.path)),48&s.$$.dirty[0]&&n(9,l=a?i.split("/").pop():i),48&s.$$.dirty[0]&&n(8,u=a?i.slice(0,i.lastIndexOf("/")):"")},[m,g,D,P,i,a,U,C,u,l,r,o,t,L,async()=>{try{const _=d.newAgentDraft;_&&d.setNewAgentDraft({..._,isSetupScriptAgent:!0});const E=await d.createRemoteAgentFromDraft("SETUP_MODE");return E&&d.setCurrentAgent(E),E}catch(_){console.error("Failed to select setup script generation:",_)}},async()=>{try{const _="setup.sh",E=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,M=await d.saveSetupScript(_,E,"home");if(M.success&&M.path){await W();const oe=F.find(K=>K.path===M.path);oe&&(te(oe),L(oe))}else console.error("Failed to create manual setup script:",M.error),n(0,m=`Failed to create manual setup script: ${M.error||"Unknown error"}`)}catch(_){console.error("Error creating manual setup script:",_),n(0,m=`Error creating manual setup script: ${_ instanceof Error?_.message:String(_)}`)}},V,W,re,se,ce,Fe,q,Se,de,te,w,h,F,c,(_,E)=>{E.stopPropagation(),L(_),te(_)},(_,E)=>{E.stopPropagation(),se(_)},(_,E)=>{E.stopPropagation(),re(_)},(_,E)=>ce(_,E),function(_){P=_,n(3,P)},_=>de(_.detail),_=>q(_.detail),_=>Se(_.detail)]}class ps extends ie{constructor(e){super(),ae(this,e,$s,as,le,{errorMessage:0,isLoading:1,lastUsedScriptPath:26,disableNewAgentCreation:27},null,[-1,-1])}}function ds(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ue(o,t[r]);return{c(){e=Xe("svg"),n=new We(!0),this.h()},l(r){e=Ye(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ze(c,!0),c.forEach(b),this.h()},h(){n.a=null,Re(e,o)},m(r,c){Je(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(r,[c]){Re(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&r[0]]))},i:B,o:B,d(r){r&&b(e)}}}function fs(s,e,n){return s.$$set=t=>{n(0,e=ue(ue({},e),ke(t)))},[e=ke(e)]}class gs extends ie{constructor(e){super(),ae(this,e,fs,ds,le,{})}}function on(s){let e,n;return e=new ot({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[xs],default:[ys]},$$scope:{ctx:s}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};16414&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function hs(s){let e;return{c(){e=T(s[4])},m(n,t){x(n,e,t)},p(n,t){16&t&&ee(e,n[4])},d(n){n&&b(e)}}}function ws(s){let e,n;return e=new Dn({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function vs(s){let e,n;return e=new gs({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function bs(s){let e,n,t,o;const r=[vs,ws],c=[];function i(a,l){return a[1]?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),y(e,"slot","iconLeft")},m(a,l){x(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(j(),f(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t||(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){f(t),o=!1},d(a){a&&b(e),c[n].d()}}}function ys(s){let e,n,t,o,r,c,i=(s[2]?mt:$t).replace("%MAX_AGENTS%",(s[2]?s[3].maxRemoteAgents:s[3].maxActiveRemoteAgents).toString())+"";return r=new Pe({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[bs],default:[hs]},$$scope:{ctx:s}}}),r.$on("click",s[11]),{c(){e=S("div"),n=S("p"),t=T(i),o=z(),A(r.$$.fragment),y(n,"class","svelte-f3wuoa"),y(e,"class","agent-limit-message svelte-f3wuoa")},m(a,l){x(a,e,l),N(e,n),N(n,t),N(e,o),R(r,e,null),c=!0},p(a,l){(!c||12&l)&&i!==(i=(a[2]?mt:$t).replace("%MAX_AGENTS%",(a[2]?a[3].maxRemoteAgents:a[3].maxActiveRemoteAgents).toString())+"")&&ee(t,i);const u={};16402&l&&(u.$$scope={dirty:l,ctx:a}),r.$set(u)},i(a){c||(p(r.$$.fragment,a),c=!0)},o(a){f(r.$$.fragment,a),c=!1},d(a){a&&b(e),k(r)}}}function xs(s){let e,n;return e=new Nn({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:B,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function _s(s){let e,n,t=!!s[0]&&on(s);return{c(){t&&t.c(),e=$e()},m(o,r){t&&t.m(o,r),x(o,e,r),n=!0},p(o,[r]){o[0]?t?(t.p(o,r),1&r&&p(t,1)):(t=on(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(j(),f(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){f(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function rn(s){if(!s)return;const e=s.is_setup_script_agent?"Setup script generation":s.session_summary||"";return{id:s.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function sn(s,e){return s.replace("%MAX_AGENTS%",e.toString())}function Ls(s,e,n){let t,o,r,{agentLimitErrorMessage:c}=e;const i=_e(rt.key);ut(s,i,$=>n(3,r=$));let a,l,u,m=!1,g=[];function w(){return r.agentOverviews.sort(($,v)=>new Date($.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!m&&(a!=null&&a.id))try{m=!0,await i.deleteAgent(a.id)}catch($){console.error("Failed to delete oldest agent:",$)}finally{m=!1}}async function d(){if(!m&&(l!=null&&l.id))try{m=!0,await i.pauseRemoteAgentWorkspace(l.id)}catch($){console.error("Failed to pause oldest active agent:",$)}finally{m=!1}}return s.$$set=$=>{"agentLimitErrorMessage"in $&&n(0,c=$.agentLimitErrorMessage)},s.$$.update=()=>{if(8&s.$$.dirty&&n(2,t=!!r.maxRemoteAgents&&r.agentOverviews.length>=r.maxRemoteAgents),8&s.$$.dirty&&n(1,o=!!r.maxActiveRemoteAgents&&r.agentOverviews.filter($=>$.workspace_status===Lt.workspaceRunning).length>=r.maxActiveRemoteAgents),1806&s.$$.dirty)if(t)n(10,g=w()),n(8,a=rn(g[0])),n(0,c=sn(mt,r.maxRemoteAgents)),n(4,u="Delete Oldest Agent"+(a?`: ${a.title}`:""));else if(o){n(10,g=w());const $=g.filter(v=>v.workspace_status===Lt.workspaceRunning);n(9,l=rn($[0])),n(0,c=sn($t,r.maxActiveRemoteAgents)),n(4,u="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,a=void 0),n(0,c=void 0)},[c,o,t,r,u,i,h,d,a,l,g,()=>{o?d():h()}]}class Cs extends ie{constructor(e){super(),ae(this,e,Ls,_s,le,{agentLimitErrorMessage:0})}}function cn(s){let e,n,t,o;return n=new ot({props:{color:"error",variant:"soft",size:2,$$slots:{default:[As]},$$scope:{ctx:s}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","error-message svelte-1klrgvd")},m(r,c){x(r,e,c),R(n,e,null),o=!0},p(r,c){const i={};33554496&c&&(i.$$scope={dirty:c,ctx:r}),n.$set(i)},i(r){o||(p(n.$$.fragment,r),r&&ln(()=>{o&&(t||(t=Ve(e,qe,{y:10},!0)),t.run(1))}),o=!0)},o(r){f(n.$$.fragment,r),r&&(t||(t=Ve(e,qe,{y:10},!1)),t.run(0)),o=!1},d(r){r&&b(e),k(n),r&&t&&t.end()}}}function As(s){let e,n=s[6].remoteAgentCreationError+"";return{c(){e=T(n)},m(t,o){x(t,e,o)},p(t,o){64&o&&n!==(n=t[6].remoteAgentCreationError+"")&&ee(e,n)},d(t){t&&b(e)}}}function Rs(s){let e;return{c(){e=T("Create agent")},m(n,t){x(n,e,t)},d(n){n&&b(e)}}}function ks(s){let e,n;return e=new Pe({props:{variant:"solid",color:"accent",size:2,loading:s[10],disabled:s[11],$$slots:{default:[Rs]},$$scope:{ctx:s}}}),e.$on("click",s[16]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const r={};1024&o&&(r.loading=t[10]),2048&o&&(r.disabled=t[11]),33554432&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ss(s){var Le,be,_;let e,n,t,o,r,c,i,a,l,u,m,g,w,h,d,$,v,L,F,D,P,U,C,H,V,W,re,se,ce;function Fe(E){s[18](E)}let pe={};s[2]!==void 0&&(pe.agentLimitErrorMessage=s[2]),i=new Cs({props:pe}),he.push(()=>we(i,"agentLimitErrorMessage",Fe));let q=s[6].remoteAgentCreationError&&cn(s);function Se(E){s[19](E)}function de(E){s[20](E)}let te={lastUsedRepoUrl:s[7],lastUsedBranchName:s[8]};return s[0]!==void 0&&(te.errorMessage=s[0]),s[1]!==void 0&&(te.isLoading=s[1]),d=new br({props:te}),he.push(()=>we(d,"errorMessage",Se)),he.push(()=>we(d,"isLoading",de)),d.$on("commitRefChange",s[14]),U=new ps({props:{lastUsedScriptPath:s[9],disableNewAgentCreation:!!s[2]||!((Le=s[3])!=null&&Le.name)||!((_=(be=s[4])==null?void 0:be.github_commit_ref)!=null&&_.repository_url)}}),U.$on("setupScriptChange",s[15]),V=new Ln({props:{editable:!0,hasSendButton:!1}}),se=new Ne({props:{class:"full-width-button",content:s[5],triggerOn:[An.Hover],$$slots:{default:[ks]},$$scope:{ctx:s}}}),{c(){e=S("div"),n=S("div"),t=S("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-1klrgvd">in parallel</strong>, in an
        <strong class="svelte-1klrgvd">isolated environment</strong>
        that will keep running, <strong class="svelte-1klrgvd">even when you shut off your laptop</strong>.</p>`,o=z(),r=S("div"),c=S("div"),A(i.$$.fragment),u=z(),q&&q.c(),m=z(),g=S("div"),g.textContent="Start from any GitHub repo and branch:",w=z(),h=S("div"),A(d.$$.fragment),L=z(),F=S("div"),F.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,D=z(),P=S("div"),A(U.$$.fragment),C=z(),H=S("div"),A(V.$$.fragment),W=z(),re=S("div"),A(se.$$.fragment),y(t,"class","main-description svelte-1klrgvd"),y(c,"class","error-message svelte-1klrgvd"),y(g,"class","description svelte-1klrgvd"),y(h,"class","commit-ref-selector svelte-1klrgvd"),y(F,"class","description svelte-1klrgvd"),y(P,"class","setup-script svelte-1klrgvd"),y(H,"class","chat svelte-1klrgvd"),y(re,"class","create-button svelte-1klrgvd"),y(r,"class","form-fields"),y(n,"class","content svelte-1klrgvd"),y(e,"class","remote-agent-setup svelte-1klrgvd")},m(E,M){x(E,e,M),N(e,n),N(n,t),N(n,o),N(n,r),N(r,c),R(i,c,null),N(r,u),q&&q.m(r,null),N(r,m),N(r,g),N(r,w),N(r,h),R(d,h,null),N(r,L),N(r,F),N(r,D),N(r,P),R(U,P,null),N(r,C),N(r,H),R(V,H,null),N(r,W),N(r,re),R(se,re,null),ce=!0},p(E,[M]){var ze,Be,Te;const oe={};!a&&4&M&&(a=!0,oe.agentLimitErrorMessage=E[2],ve(()=>a=!1)),i.$set(oe),E[6].remoteAgentCreationError?q?(q.p(E,M),64&M&&p(q,1)):(q=cn(E),q.c(),p(q,1),q.m(r,m)):q&&(j(),f(q,1,1,()=>{q=null}),O());const K={};128&M&&(K.lastUsedRepoUrl=E[7]),256&M&&(K.lastUsedBranchName=E[8]),!$&&1&M&&($=!0,K.errorMessage=E[0],ve(()=>$=!1)),!v&&2&M&&(v=!0,K.isLoading=E[1],ve(()=>v=!1)),d.$set(K);const ne={};512&M&&(ne.lastUsedScriptPath=E[9]),28&M&&(ne.disableNewAgentCreation=!!E[2]||!((ze=E[3])!=null&&ze.name)||!((Te=(Be=E[4])==null?void 0:Be.github_commit_ref)!=null&&Te.repository_url)),U.$set(ne);const Ce={};32&M&&(Ce.content=E[5]),33557504&M&&(Ce.$$scope={dirty:M,ctx:E}),se.$set(Ce)},i(E){ce||(p(i.$$.fragment,E),E&&ln(()=>{ce&&(l||(l=Ve(c,qe,{y:10},!0)),l.run(1))}),p(q),p(d.$$.fragment,E),p(U.$$.fragment,E),p(V.$$.fragment,E),p(se.$$.fragment,E),ce=!0)},o(E){f(i.$$.fragment,E),E&&(l||(l=Ve(c,qe,{y:10},!1)),l.run(0)),f(q),f(d.$$.fragment,E),f(U.$$.fragment,E),f(V.$$.fragment,E),f(se.$$.fragment,E),ce=!1},d(E){E&&b(e),k(i),E&&l&&l.end(),q&&q.d(),k(d),k(U),k(V),k(se)}}}function Is(s,e,n){let t,o,r,c,i,a,l,u,m;const g=_e(rt.key);ut(s,g,U=>n(6,m=U));const w=_e("chatModel");ut(s,w,U=>n(22,u=U));const h=_e(gt.key);let d,$="",v=!1,L=null,F=null,D=null;tt(async()=>{try{const U=await g.getLastRemoteAgentSetup();n(7,L=U.lastRemoteAgentGitRepoUrl),n(8,F=U.lastRemoteAgentGitBranch),n(9,D=U.lastRemoteAgentSetupScript),g.setHasEverUsedRemoteAgent(!0),await g.reportRemoteAgentEvent({eventName:Rn.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(U){console.error("Failed to load last remote agent setup:",U)}}),an(()=>{g.setNewAgentDraft(null),g.setCreationMetrics(void 0)});const P=Cn(g,u.currentConversationModel,h);return s.$$.update=()=>{var U,C,H;64&s.$$.dirty&&n(4,t=((U=m.newAgentDraft)==null?void 0:U.commitRef)??null),64&s.$$.dirty&&n(3,o=((C=m.newAgentDraft)==null?void 0:C.selectedBranch)??null),64&s.$$.dirty&&(r=((H=m.newAgentDraft)==null?void 0:H.setupScript)??null),31&s.$$.dirty&&n(5,a=(()=>{var re;const V=(re=t==null?void 0:t.github_commit_ref)==null?void 0:re.repository_url,W=o==null?void 0:o.name;return $||d||(v?"Loading repos and branches...":"")||!V&&"Please select a repository"||!W&&"Please select a branch"||(!(!v&&V&&W)&&V&&W?"Loading branch data...":"")||""})()),32&s.$$.dirty&&n(17,l=!!a),131072&s.$$.dirty&&n(11,c=l),64&s.$$.dirty&&n(10,i=m.isCreatingAgent),131136&s.$$.dirty&&g.newAgentDraft&&!m.isCreatingAgent&&g.newAgentDraft.isDisabled!==l&&g.setNewAgentDraft({...g.newAgentDraft,isDisabled:l})},[$,v,d,o,t,a,m,L,F,D,i,c,g,w,async function(U){g.setRemoteAgentCreationError(null);const C=g.newAgentDraft;C?g.setNewAgentDraft({...C,commitRef:U.detail.commitRef,selectedBranch:U.detail.selectedBranch}):g.setNewAgentDraft({commitRef:U.detail.commitRef,selectedBranch:U.detail.selectedBranch,setupScript:null,isDisabled:l,enableNotification:!0})},function(U){g.setRemoteAgentCreationError(null);const C=g.newAgentDraft;C?g.setNewAgentDraft({...C,setupScript:U.detail.script}):g.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:U.detail.script,isDisabled:l,enableNotification:!0})},async function(){try{P(),g.saveLastRemoteAgentSetup((t==null?void 0:t.github_commit_ref.repository_url)||null,(o==null?void 0:o.name)||null,(r==null?void 0:r.path)||null)}catch(U){console.error("Failed to create agent:",U)}},l,function(U){d=U,n(2,d)},function(U){$=U,n(0,$)},function(U){v=U,n(1,v)}]}class Lc extends ie{constructor(e){super(),ae(this,e,Is,Ss,le,{})}}export{Lc as default};
