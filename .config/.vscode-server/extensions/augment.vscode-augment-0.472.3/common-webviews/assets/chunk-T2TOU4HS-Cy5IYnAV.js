import{g as At,s as yt}from"./chunk-5HRBRIJM-CzTE5zxy.js";import{_ as r,i as gt,d as D,a7 as G,s as mt,g as ft,b as Ct,c as Et,q as bt,r as kt,e as x,x as Tt,l as Me,v as Ke,j as te,y as Ft,z as Dt,A as Bt}from"./AugmentMessage-BNt-hbbl.js";var Ge=function(){var e=r(function(l,y,p,i){for(p=p||{},i=l.length;i--;p[l[i]]=y);return p},"o"),n=[1,18],u=[1,19],a=[1,20],c=[1,41],o=[1,42],d=[1,26],A=[1,24],T=[1,25],Ce=[1,32],Ee=[1,33],be=[1,34],f=[1,45],ke=[1,35],Te=[1,36],Fe=[1,37],De=[1,38],Be=[1,27],_e=[1,28],Se=[1,29],Ne=[1,30],Le=[1,31],C=[1,44],E=[1,46],b=[1,43],F=[1,47],$e=[1,9],h=[1,8,9],ne=[1,58],ie=[1,59],ue=[1,60],re=[1,61],ae=[1,62],xe=[1,63],Ie=[1,64],oe=[1,8,9,41],je=[1,76],P=[1,8,9,12,13,22,39,41,44,66,67,68,69,70,71,72,77,79],le=[1,8,9,12,13,17,20,22,39,41,44,48,58,66,67,68,69,70,71,72,77,79,84,99,101,102],ce=[13,58,84,99,101,102],Y=[13,58,71,72,84,99,101,102],Qe=[13,58,66,67,68,69,70,84,99,101,102],Oe=[1,98],W=[1,115],j=[1,107],Q=[1,113],X=[1,108],q=[1,109],H=[1,110],V=[1,111],J=[1,112],Z=[1,114],Xe=[22,58,59,80,84,85,86,87,88,89],ve=[1,8,9,39,41,44],he=[1,8,9,22],qe=[1,143],He=[1,8,9,59],N=[1,8,9,22,58,59,80,84,85,86,87,88,89],we={trace:r(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,DOT:17,className:18,classLiteralName:19,GENERICTYPE:20,relationStatement:21,LABEL:22,namespaceStatement:23,classStatement:24,memberStatement:25,annotationStatement:26,clickStatement:27,styleStatement:28,cssClassStatement:29,noteStatement:30,classDefStatement:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,namespaceIdentifier:38,STRUCT_START:39,classStatements:40,STRUCT_STOP:41,NAMESPACE:42,classIdentifier:43,STYLE_SEPARATOR:44,members:45,CLASS:46,ANNOTATION_START:47,ANNOTATION_END:48,MEMBER:49,SEPARATOR:50,relation:51,NOTE_FOR:52,noteText:53,NOTE:54,CLASSDEF:55,classList:56,stylesOpt:57,ALPHA:58,COMMA:59,direction_tb:60,direction_bt:61,direction_rl:62,direction_lr:63,relationType:64,lineType:65,AGGREGATION:66,EXTENSION:67,COMPOSITION:68,DEPENDENCY:69,LOLLIPOP:70,LINE:71,DOTTED_LINE:72,CALLBACK:73,LINK:74,LINK_TARGET:75,CLICK:76,CALLBACK_NAME:77,CALLBACK_ARGS:78,HREF:79,STYLE:80,CSSCLASS:81,style:82,styleComponent:83,NUM:84,COLON:85,UNIT:86,SPACE:87,BRKT:88,PCT:89,commentToken:90,textToken:91,graphCodeTokens:92,textNoTagsToken:93,TAGSTART:94,TAGEND:95,"==":96,"--":97,DEFAULT:98,MINUS:99,keywords:100,UNICODE_TEXT:101,BQUOTE_STR:102,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",17:"DOT",20:"GENERICTYPE",22:"LABEL",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",39:"STRUCT_START",41:"STRUCT_STOP",42:"NAMESPACE",44:"STYLE_SEPARATOR",46:"CLASS",47:"ANNOTATION_START",48:"ANNOTATION_END",49:"MEMBER",50:"SEPARATOR",52:"NOTE_FOR",54:"NOTE",55:"CLASSDEF",58:"ALPHA",59:"COMMA",60:"direction_tb",61:"direction_bt",62:"direction_rl",63:"direction_lr",66:"AGGREGATION",67:"EXTENSION",68:"COMPOSITION",69:"DEPENDENCY",70:"LOLLIPOP",71:"LINE",72:"DOTTED_LINE",73:"CALLBACK",74:"LINK",75:"LINK_TARGET",76:"CLICK",77:"CALLBACK_NAME",78:"CALLBACK_ARGS",79:"HREF",80:"STYLE",81:"CSSCLASS",84:"NUM",85:"COLON",86:"UNIT",87:"SPACE",88:"BRKT",89:"PCT",92:"graphCodeTokens",94:"TAGSTART",95:"TAGEND",96:"==",97:"--",98:"DEFAULT",99:"MINUS",100:"keywords",101:"UNICODE_TEXT",102:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,3],[15,2],[18,1],[18,3],[18,1],[18,2],[18,2],[18,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[23,4],[23,5],[38,2],[40,1],[40,2],[40,3],[24,1],[24,3],[24,4],[24,6],[43,2],[43,3],[26,4],[45,1],[45,2],[25,1],[25,2],[25,1],[25,1],[21,3],[21,4],[21,4],[21,5],[30,3],[30,2],[31,3],[56,1],[56,3],[32,1],[32,1],[32,1],[32,1],[51,3],[51,2],[51,2],[51,1],[64,1],[64,1],[64,1],[64,1],[64,1],[65,1],[65,1],[27,3],[27,4],[27,3],[27,4],[27,4],[27,5],[27,3],[27,4],[27,4],[27,5],[27,4],[27,5],[27,5],[27,6],[28,3],[29,3],[57,1],[57,3],[82,1],[82,2],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[90,1],[90,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[93,1],[93,1],[93,1],[93,1],[16,1],[16,1],[16,1],[16,1],[19,1],[53,1]],performAction:r(function(l,y,p,i,m,t,ee){var s=t.length-1;switch(m){case 8:this.$=t[s-1];break;case 9:case 12:case 14:this.$=t[s];break;case 10:case 13:this.$=t[s-2]+"."+t[s];break;case 11:case 15:case 95:this.$=t[s-1]+t[s];break;case 16:case 17:this.$=t[s-1]+"~"+t[s]+"~";break;case 18:i.addRelation(t[s]);break;case 19:t[s-1].title=i.cleanupLabel(t[s]),i.addRelation(t[s-1]);break;case 30:this.$=t[s].trim(),i.setAccTitle(this.$);break;case 31:case 32:this.$=t[s].trim(),i.setAccDescription(this.$);break;case 33:i.addClassesToNamespace(t[s-3],t[s-1]);break;case 34:i.addClassesToNamespace(t[s-4],t[s-1]);break;case 35:this.$=t[s],i.addNamespace(t[s]);break;case 36:case 46:case 59:case 92:this.$=[t[s]];break;case 37:this.$=[t[s-1]];break;case 38:t[s].unshift(t[s-2]),this.$=t[s];break;case 40:i.setCssClass(t[s-2],t[s]);break;case 41:i.addMembers(t[s-3],t[s-1]);break;case 42:i.setCssClass(t[s-5],t[s-3]),i.addMembers(t[s-5],t[s-1]);break;case 43:this.$=t[s],i.addClass(t[s]);break;case 44:this.$=t[s-1],i.addClass(t[s-1]),i.setClassLabel(t[s-1],t[s]);break;case 45:i.addAnnotation(t[s],t[s-2]);break;case 47:t[s].push(t[s-1]),this.$=t[s];break;case 48:case 50:case 51:break;case 49:i.addMember(t[s-1],i.cleanupLabel(t[s]));break;case 52:this.$={id1:t[s-2],id2:t[s],relation:t[s-1],relationTitle1:"none",relationTitle2:"none"};break;case 53:this.$={id1:t[s-3],id2:t[s],relation:t[s-1],relationTitle1:t[s-2],relationTitle2:"none"};break;case 54:this.$={id1:t[s-3],id2:t[s],relation:t[s-2],relationTitle1:"none",relationTitle2:t[s-1]};break;case 55:this.$={id1:t[s-4],id2:t[s],relation:t[s-2],relationTitle1:t[s-3],relationTitle2:t[s-1]};break;case 56:i.addNote(t[s],t[s-1]);break;case 57:i.addNote(t[s]);break;case 58:this.$=t[s-2],i.defineClass(t[s-1],t[s]);break;case 60:this.$=t[s-2].concat([t[s]]);break;case 61:i.setDirection("TB");break;case 62:i.setDirection("BT");break;case 63:i.setDirection("RL");break;case 64:i.setDirection("LR");break;case 65:this.$={type1:t[s-2],type2:t[s],lineType:t[s-1]};break;case 66:this.$={type1:"none",type2:t[s],lineType:t[s-1]};break;case 67:this.$={type1:t[s-1],type2:"none",lineType:t[s]};break;case 68:this.$={type1:"none",type2:"none",lineType:t[s]};break;case 69:this.$=i.relationType.AGGREGATION;break;case 70:this.$=i.relationType.EXTENSION;break;case 71:this.$=i.relationType.COMPOSITION;break;case 72:this.$=i.relationType.DEPENDENCY;break;case 73:this.$=i.relationType.LOLLIPOP;break;case 74:this.$=i.lineType.LINE;break;case 75:this.$=i.lineType.DOTTED_LINE;break;case 76:case 82:this.$=t[s-2],i.setClickEvent(t[s-1],t[s]);break;case 77:case 83:this.$=t[s-3],i.setClickEvent(t[s-2],t[s-1]),i.setTooltip(t[s-2],t[s]);break;case 78:this.$=t[s-2],i.setLink(t[s-1],t[s]);break;case 79:this.$=t[s-3],i.setLink(t[s-2],t[s-1],t[s]);break;case 80:this.$=t[s-3],i.setLink(t[s-2],t[s-1]),i.setTooltip(t[s-2],t[s]);break;case 81:this.$=t[s-4],i.setLink(t[s-3],t[s-2],t[s]),i.setTooltip(t[s-3],t[s-1]);break;case 84:this.$=t[s-3],i.setClickEvent(t[s-2],t[s-1],t[s]);break;case 85:this.$=t[s-4],i.setClickEvent(t[s-3],t[s-2],t[s-1]),i.setTooltip(t[s-3],t[s]);break;case 86:this.$=t[s-3],i.setLink(t[s-2],t[s]);break;case 87:this.$=t[s-4],i.setLink(t[s-3],t[s-1],t[s]);break;case 88:this.$=t[s-4],i.setLink(t[s-3],t[s-1]),i.setTooltip(t[s-3],t[s]);break;case 89:this.$=t[s-5],i.setLink(t[s-4],t[s-2],t[s]),i.setTooltip(t[s-4],t[s-1]);break;case 90:this.$=t[s-2],i.setCssStyle(t[s-1],t[s]);break;case 91:i.setCssClass(t[s-1],t[s]);break;case 93:t[s-2].push(t[s]),this.$=t[s-2]}},"anonymous"),table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:n,35:u,37:a,38:22,42:c,43:23,46:o,47:d,49:A,50:T,52:Ce,54:Ee,55:be,58:f,60:ke,61:Te,62:Fe,63:De,73:Be,74:_e,76:Se,80:Ne,81:Le,84:C,99:E,101:b,102:F},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},e($e,[2,5],{8:[1,48]}),{8:[1,49]},e(h,[2,18],{22:[1,50]}),e(h,[2,20]),e(h,[2,21]),e(h,[2,22]),e(h,[2,23]),e(h,[2,24]),e(h,[2,25]),e(h,[2,26]),e(h,[2,27]),e(h,[2,28]),e(h,[2,29]),{34:[1,51]},{36:[1,52]},e(h,[2,32]),e(h,[2,48],{51:53,64:56,65:57,13:[1,54],22:[1,55],66:ne,67:ie,68:ue,69:re,70:ae,71:xe,72:Ie}),{39:[1,65]},e(oe,[2,39],{39:[1,67],44:[1,66]}),e(h,[2,50]),e(h,[2,51]),{16:68,58:f,84:C,99:E,101:b},{16:39,18:69,19:40,58:f,84:C,99:E,101:b,102:F},{16:39,18:70,19:40,58:f,84:C,99:E,101:b,102:F},{16:39,18:71,19:40,58:f,84:C,99:E,101:b,102:F},{58:[1,72]},{13:[1,73]},{16:39,18:74,19:40,58:f,84:C,99:E,101:b,102:F},{13:je,53:75},{56:77,58:[1,78]},e(h,[2,61]),e(h,[2,62]),e(h,[2,63]),e(h,[2,64]),e(P,[2,12],{16:39,19:40,18:80,17:[1,79],20:[1,81],58:f,84:C,99:E,101:b,102:F}),e(P,[2,14],{20:[1,82]}),{15:83,16:84,58:f,84:C,99:E,101:b},{16:39,18:85,19:40,58:f,84:C,99:E,101:b,102:F},e(le,[2,118]),e(le,[2,119]),e(le,[2,120]),e(le,[2,121]),e([1,8,9,12,13,20,22,39,41,44,66,67,68,69,70,71,72,77,79],[2,122]),e($e,[2,6],{10:5,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,18:21,38:22,43:23,16:39,19:40,5:86,33:n,35:u,37:a,42:c,46:o,47:d,49:A,50:T,52:Ce,54:Ee,55:be,58:f,60:ke,61:Te,62:Fe,63:De,73:Be,74:_e,76:Se,80:Ne,81:Le,84:C,99:E,101:b,102:F}),{5:87,10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:n,35:u,37:a,38:22,42:c,43:23,46:o,47:d,49:A,50:T,52:Ce,54:Ee,55:be,58:f,60:ke,61:Te,62:Fe,63:De,73:Be,74:_e,76:Se,80:Ne,81:Le,84:C,99:E,101:b,102:F},e(h,[2,19]),e(h,[2,30]),e(h,[2,31]),{13:[1,89],16:39,18:88,19:40,58:f,84:C,99:E,101:b,102:F},{51:90,64:56,65:57,66:ne,67:ie,68:ue,69:re,70:ae,71:xe,72:Ie},e(h,[2,49]),{65:91,71:xe,72:Ie},e(ce,[2,68],{64:92,66:ne,67:ie,68:ue,69:re,70:ae}),e(Y,[2,69]),e(Y,[2,70]),e(Y,[2,71]),e(Y,[2,72]),e(Y,[2,73]),e(Qe,[2,74]),e(Qe,[2,75]),{8:[1,94],24:95,40:93,43:23,46:o},{16:96,58:f,84:C,99:E,101:b},{45:97,49:Oe},{48:[1,99]},{13:[1,100]},{13:[1,101]},{77:[1,102],79:[1,103]},{22:W,57:104,58:j,80:Q,82:105,83:106,84:X,85:q,86:H,87:V,88:J,89:Z},{58:[1,116]},{13:je,53:117},e(h,[2,57]),e(h,[2,123]),{22:W,57:118,58:j,59:[1,119],80:Q,82:105,83:106,84:X,85:q,86:H,87:V,88:J,89:Z},e(Xe,[2,59]),{16:39,18:120,19:40,58:f,84:C,99:E,101:b,102:F},e(P,[2,15]),e(P,[2,16]),e(P,[2,17]),{39:[2,35]},{15:122,16:84,17:[1,121],39:[2,9],58:f,84:C,99:E,101:b},e(ve,[2,43],{11:123,12:[1,124]}),e($e,[2,7]),{9:[1,125]},e(he,[2,52]),{16:39,18:126,19:40,58:f,84:C,99:E,101:b,102:F},{13:[1,128],16:39,18:127,19:40,58:f,84:C,99:E,101:b,102:F},e(ce,[2,67],{64:129,66:ne,67:ie,68:ue,69:re,70:ae}),e(ce,[2,66]),{41:[1,130]},{24:95,40:131,43:23,46:o},{8:[1,132],41:[2,36]},e(oe,[2,40],{39:[1,133]}),{41:[1,134]},{41:[2,46],45:135,49:Oe},{16:39,18:136,19:40,58:f,84:C,99:E,101:b,102:F},e(h,[2,76],{13:[1,137]}),e(h,[2,78],{13:[1,139],75:[1,138]}),e(h,[2,82],{13:[1,140],78:[1,141]}),{13:[1,142]},e(h,[2,90],{59:qe}),e(He,[2,92],{83:144,22:W,58:j,80:Q,84:X,85:q,86:H,87:V,88:J,89:Z}),e(N,[2,94]),e(N,[2,96]),e(N,[2,97]),e(N,[2,98]),e(N,[2,99]),e(N,[2,100]),e(N,[2,101]),e(N,[2,102]),e(N,[2,103]),e(N,[2,104]),e(h,[2,91]),e(h,[2,56]),e(h,[2,58],{59:qe}),{58:[1,145]},e(P,[2,13]),{15:146,16:84,58:f,84:C,99:E,101:b},{39:[2,11]},e(ve,[2,44]),{13:[1,147]},{1:[2,4]},e(he,[2,54]),e(he,[2,53]),{16:39,18:148,19:40,58:f,84:C,99:E,101:b,102:F},e(ce,[2,65]),e(h,[2,33]),{41:[1,149]},{24:95,40:150,41:[2,37],43:23,46:o},{45:151,49:Oe},e(oe,[2,41]),{41:[2,47]},e(h,[2,45]),e(h,[2,77]),e(h,[2,79]),e(h,[2,80],{75:[1,152]}),e(h,[2,83]),e(h,[2,84],{13:[1,153]}),e(h,[2,86],{13:[1,155],75:[1,154]}),{22:W,58:j,80:Q,82:156,83:106,84:X,85:q,86:H,87:V,88:J,89:Z},e(N,[2,95]),e(Xe,[2,60]),{39:[2,10]},{14:[1,157]},e(he,[2,55]),e(h,[2,34]),{41:[2,38]},{41:[1,158]},e(h,[2,81]),e(h,[2,85]),e(h,[2,87]),e(h,[2,88],{75:[1,159]}),e(He,[2,93],{83:144,22:W,58:j,80:Q,84:X,85:q,86:H,87:V,88:J,89:Z}),e(ve,[2,8]),e(oe,[2,42]),e(h,[2,89])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],83:[2,35],122:[2,11],125:[2,4],135:[2,47],146:[2,10],150:[2,38]},parseError:r(function(l,y){if(!y.recoverable){var p=new Error(l);throw p.hash=y,p}this.trace(l)},"parseError"),parse:r(function(l){var y=this,p=[0],i=[],m=[null],t=[],ee=this.table,s="",de=0,Ve=0,pt=t.slice.call(arguments,1),k=Object.create(this.lexer),O={yy:{}};for(var Re in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Re)&&(O.yy[Re]=this.yy[Re]);k.setInput(l,O.yy),O.yy.lexer=k,O.yy.parser=this,k.yylloc===void 0&&(k.yylloc={});var Pe=k.yylloc;t.push(Pe);var dt=k.options&&k.options.ranges;function Je(){var S;return typeof(S=i.pop()||k.lex()||1)!="number"&&(S instanceof Array&&(S=(i=S).pop()),S=y.symbols_[S]||S),S}typeof O.yy.parseError=="function"?this.parseError=O.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,r(function(S){p.length=p.length-2*S,m.length=m.length-S,t.length=t.length-S},"popStack"),r(Je,"lex");for(var B,v,_,Ze,Ae,$,et,ye,M={};;){if(v=p[p.length-1],this.defaultActions[v]?_=this.defaultActions[v]:(B==null&&(B=Je()),_=ee[v]&&ee[v][B]),_===void 0||!_.length||!_[0]){var tt="";for(Ae in ye=[],ee[v])this.terminals_[Ae]&&Ae>2&&ye.push("'"+this.terminals_[Ae]+"'");tt=k.showPosition?"Parse error on line "+(de+1)+`:
`+k.showPosition()+`
Expecting `+ye.join(", ")+", got '"+(this.terminals_[B]||B)+"'":"Parse error on line "+(de+1)+": Unexpected "+(B==1?"end of input":"'"+(this.terminals_[B]||B)+"'"),this.parseError(tt,{text:k.match,token:this.terminals_[B]||B,line:k.yylineno,loc:Pe,expected:ye})}if(_[0]instanceof Array&&_.length>1)throw new Error("Parse Error: multiple actions possible at state: "+v+", token: "+B);switch(_[0]){case 1:p.push(B),m.push(k.yytext),t.push(k.yylloc),p.push(_[1]),B=null,Ve=k.yyleng,s=k.yytext,de=k.yylineno,Pe=k.yylloc;break;case 2:if($=this.productions_[_[1]][1],M.$=m[m.length-$],M._$={first_line:t[t.length-($||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-($||1)].first_column,last_column:t[t.length-1].last_column},dt&&(M._$.range=[t[t.length-($||1)].range[0],t[t.length-1].range[1]]),(Ze=this.performAction.apply(M,[s,Ve,de,O.yy,_[1],m,t].concat(pt)))!==void 0)return Ze;$&&(p=p.slice(0,-1*$*2),m=m.slice(0,-1*$),t=t.slice(0,-1*$)),p.push(this.productions_[_[1]][0]),m.push(M.$),t.push(M._$),et=ee[p[p.length-2]][p[p.length-1]],p.push(et);break;case 3:return!0}}return!0},"parse")},ht=function(){return{EOF:1,parseError:r(function(l,y){if(!this.yy.parser)throw new Error(l);this.yy.parser.parseError(l,y)},"parseError"),setInput:r(function(l,y){return this.yy=y||this.yy||{},this._input=l,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:r(function(){var l=this._input[0];return this.yytext+=l,this.yyleng++,this.offset++,this.match+=l,this.matched+=l,l.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),l},"input"),unput:r(function(l){var y=l.length,p=l.split(/(?:\r\n?|\n)/g);this._input=l+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-y),this.offset-=y;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),p.length-1&&(this.yylineno-=p.length-1);var m=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:p?(p.length===i.length?this.yylloc.first_column:0)+i[i.length-p.length].length-p[0].length:this.yylloc.first_column-y},this.options.ranges&&(this.yylloc.range=[m[0],m[0]+this.yyleng-y]),this.yyleng=this.yytext.length,this},"unput"),more:r(function(){return this._more=!0,this},"more"),reject:r(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:r(function(l){this.unput(this.match.slice(l))},"less"),pastInput:r(function(){var l=this.matched.substr(0,this.matched.length-this.match.length);return(l.length>20?"...":"")+l.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:r(function(){var l=this.match;return l.length<20&&(l+=this._input.substr(0,20-l.length)),(l.substr(0,20)+(l.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:r(function(){var l=this.pastInput(),y=new Array(l.length+1).join("-");return l+this.upcomingInput()+`
`+y+"^"},"showPosition"),test_match:r(function(l,y){var p,i,m;if(this.options.backtrack_lexer&&(m={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(m.yylloc.range=this.yylloc.range.slice(0))),(i=l[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+l[0].length},this.yytext+=l[0],this.match+=l[0],this.matches=l,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(l[0].length),this.matched+=l[0],p=this.performAction.call(this,this.yy,this,y,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),p)return p;if(this._backtrack){for(var t in m)this[t]=m[t];return!1}return!1},"test_match"),next:r(function(){if(this.done)return this.EOF;var l,y,p,i;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var m=this._currentRules(),t=0;t<m.length;t++)if((p=this._input.match(this.rules[m[t]]))&&(!y||p[0].length>y[0].length)){if(y=p,i=t,this.options.backtrack_lexer){if((l=this.test_match(p,m[t]))!==!1)return l;if(this._backtrack){y=!1;continue}return!1}if(!this.options.flex)break}return y?(l=this.test_match(y,m[i]))!==!1&&l:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:r(function(){var l=this.next();return l||this.lex()},"lex"),begin:r(function(l){this.conditionStack.push(l)},"begin"),popState:r(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:r(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:r(function(l){return(l=this.conditionStack.length-1-Math.abs(l||0))>=0?this.conditionStack[l]:"INITIAL"},"topState"),pushState:r(function(l){this.begin(l)},"pushState"),stateStackSize:r(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:r(function(l,y,p,i){switch(p){case 0:return 60;case 1:return 61;case 2:return 62;case 3:return 63;case 4:case 5:case 14:case 31:case 36:case 40:case 47:break;case 6:return this.begin("acc_title"),33;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),35;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:case 19:case 22:case 24:case 58:case 61:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:case 35:return 8;case 15:case 16:return 7;case 17:case 37:case 45:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 20:this.popState(),this.begin("callback_args");break;case 21:return 77;case 23:return 78;case 25:return"STR";case 26:this.begin("string");break;case 27:return 80;case 28:return 55;case 29:return this.begin("namespace"),42;case 30:case 39:return this.popState(),8;case 32:return this.begin("namespace-body"),39;case 33:case 43:return this.popState(),41;case 34:case 44:return"EOF_IN_STRUCT";case 38:return this.begin("class"),46;case 41:return this.popState(),this.popState(),41;case 42:return this.begin("class-body"),39;case 46:return"OPEN_IN_STRUCT";case 48:return"MEMBER";case 49:return 81;case 50:return 73;case 51:return 74;case 52:return 76;case 53:return 52;case 54:return 54;case 55:return 47;case 56:return 48;case 57:return 79;case 59:return"GENERICTYPE";case 60:this.begin("generic");break;case 62:return"BQUOTE_STR";case 63:this.begin("bqstring");break;case 64:case 65:case 66:case 67:return 75;case 68:case 69:return 67;case 70:case 71:return 69;case 72:return 68;case 73:return 66;case 74:return 70;case 75:return 71;case 76:return 72;case 77:return 22;case 78:return 44;case 79:return 99;case 80:return 17;case 81:return"PLUS";case 82:return 85;case 83:return 59;case 84:case 85:return 88;case 86:return 89;case 87:case 88:return"EQUALS";case 89:return 58;case 90:return 12;case 91:return 14;case 92:return"PUNCTUATION";case 93:return 84;case 94:return 101;case 95:case 96:return 87;case 97:return 9}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:classDef\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,33,34,35,36,37,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},namespace:{rules:[26,29,30,31,32,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},"class-body":{rules:[26,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},class:{rules:[26,39,40,41,42,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr_multiline:{rules:[11,12,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr:{rules:[9,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_title:{rules:[7,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_args:{rules:[22,23,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_name:{rules:[19,20,21,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},href:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},struct:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},generic:{rules:[26,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},bqstring:{rules:[26,49,50,51,52,53,54,55,56,57,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},string:{rules:[24,25,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,29,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],inclusive:!0}}}}();function pe(){this.yy={}}return we.lexer=ht,r(pe,"Parser"),pe.prototype=we,we.Parser=pe,new pe}();Ge.parser=Ge;var ts=Ge,st=["#","+","~","-",""],z,nt=(z=class{constructor(n,u){this.memberType=u,this.visibility="",this.classifier="",this.text="";const a=gt(n,D());this.parseMember(a)}getDisplayDetails(){let n=this.visibility+G(this.id);return this.memberType==="method"&&(n+=`(${G(this.parameters.trim())})`,this.returnType&&(n+=" : "+G(this.returnType))),n=n.trim(),{displayText:n,cssStyle:this.parseClassifier()}}parseMember(n){let u="";if(this.memberType==="method"){const c=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/.exec(n);if(c){const o=c[1]?c[1].trim():"";if(st.includes(o)&&(this.visibility=o),this.id=c[2],this.parameters=c[3]?c[3].trim():"",u=c[4]?c[4].trim():"",this.returnType=c[5]?c[5].trim():"",u===""){const d=this.returnType.substring(this.returnType.length-1);/[$*]/.exec(d)&&(u=d,this.returnType=this.returnType.substring(0,this.returnType.length-1))}}}else{const c=n.length,o=n.substring(0,1),d=n.substring(c-1);st.includes(o)&&(this.visibility=o),/[$*]/.exec(d)&&(u=d),this.id=n.substring(this.visibility===""?0:1,u===""?c:c-1)}this.classifier=u,this.id=this.id.startsWith(" ")?" "+this.id.trim():this.id.trim();const a=`${this.visibility?"\\"+this.visibility:""}${G(this.id)}${this.memberType==="method"?`(${G(this.parameters)})${this.returnType?" : "+G(this.returnType):""}`:""}`;this.text=a.replaceAll("<","&lt;").replaceAll(">","&gt;"),this.text.startsWith("\\&lt;")&&(this.text=this.text.replace("\\&lt;","~"))}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}},r(z,"ClassMember"),z),ge="classId-",me=[],g=new Map,it=new Map,se=[],K=[],ut=0,L=new Map,Ue=0,fe=[],w=r(e=>x.sanitizeText(e,D()),"sanitizeText"),R=r(function(e){const n=x.sanitizeText(e,D());let u="",a=n;if(n.indexOf("~")>0){const c=n.split("~");a=w(c[0]),u=w(c[1])}return{className:a,type:u}},"splitClassNameAndType"),_t=r(function(e,n){const u=x.sanitizeText(e,D());n&&(n=w(n));const{className:a}=R(u);g.get(a).label=n,g.get(a).text=`${n}${g.get(a).type?`<${g.get(a).type}>`:""}`},"setClassLabel"),U=r(function(e){const n=x.sanitizeText(e,D()),{className:u,type:a}=R(n);if(g.has(u))return;const c=x.sanitizeText(u,D());g.set(c,{id:c,type:a,label:c,text:`${c}${a?`&lt;${a}&gt;`:""}`,shape:"classBox",cssClasses:"default",methods:[],members:[],annotations:[],styles:[],domId:ge+c+"-"+ut}),ut++},"addClass"),rt=r(function(e,n){const u={id:`interface${K.length}`,label:e,classId:n};K.push(u)},"addInterface"),at=r(function(e){const n=x.sanitizeText(e,D());if(g.has(n))return g.get(n).domId;throw new Error("Class not found: "+n)},"lookUpDomId"),St=r(function(){me=[],g=new Map,se=[],K=[],(fe=[]).push(lt),L=new Map,Ue=0,We="TB",Tt()},"clear"),Nt=r(function(e){return g.get(e)},"getClass"),Lt=r(function(){return g},"getClasses"),$t=r(function(){return me},"getRelations"),xt=r(function(){return se},"getNotes"),It=r(function(e){Me.debug("Adding relation: "+JSON.stringify(e));const n=[I.LOLLIPOP,I.AGGREGATION,I.COMPOSITION,I.DEPENDENCY,I.EXTENSION];e.relation.type1!==I.LOLLIPOP||n.includes(e.relation.type2)?e.relation.type2!==I.LOLLIPOP||n.includes(e.relation.type1)?(U(e.id1),U(e.id2)):(U(e.id1),rt(e.id2,e.id1),e.id2="interface"+(K.length-1)):(U(e.id2),rt(e.id1,e.id2),e.id1="interface"+(K.length-1)),e.id1=R(e.id1).className,e.id2=R(e.id2).className,e.relationTitle1=x.sanitizeText(e.relationTitle1.trim(),D()),e.relationTitle2=x.sanitizeText(e.relationTitle2.trim(),D()),me.push(e)},"addRelation"),Ot=r(function(e,n){const u=R(e).className;g.get(u).annotations.push(n)},"addAnnotation"),ot=r(function(e,n){U(e);const u=R(e).className,a=g.get(u);if(typeof n=="string"){const c=n.trim();c.startsWith("<<")&&c.endsWith(">>")?a.annotations.push(w(c.substring(2,c.length-2))):c.indexOf(")")>0?a.methods.push(new nt(c,"method")):c&&a.members.push(new nt(c,"attribute"))}},"addMember"),vt=r(function(e,n){Array.isArray(n)&&(n.reverse(),n.forEach(u=>ot(e,u)))},"addMembers"),wt=r(function(e,n){const u={id:`note${se.length}`,class:n,text:e};se.push(u)},"addNote"),Rt=r(function(e){return e.startsWith(":")&&(e=e.substring(1)),w(e.trim())},"cleanupLabel"),Ye=r(function(e,n){e.split(",").forEach(function(u){let a=u;/\d/.exec(u[0])&&(a=ge+a);const c=g.get(a);c&&(c.cssClasses+=" "+n)})},"setCssClass"),Pt=r(function(e,n){for(const u of e){let a=it.get(u);a===void 0&&(a={id:u,styles:[],textStyles:[]},it.set(u,a)),n&&n.forEach(function(c){if(/color/.exec(c)){const o=c.replace("fill","bgFill");a.textStyles.push(o)}a.styles.push(c)}),g.forEach(c=>{c.cssClasses.includes(u)&&c.styles.push(...n.flatMap(o=>o.split(",")))})}},"defineClass"),Mt=r(function(e,n){e.split(",").forEach(function(u){n!==void 0&&(g.get(u).tooltip=w(n))})},"setTooltip"),Gt=r(function(e,n){return n&&L.has(n)?L.get(n).classes.get(e).tooltip:g.get(e).tooltip},"getTooltip"),Ut=r(function(e,n,u){const a=D();e.split(",").forEach(function(c){let o=c;/\d/.exec(c[0])&&(o=ge+o);const d=g.get(o);d&&(d.link=Ke.formatUrl(n,a),a.securityLevel==="sandbox"?d.linkTarget="_top":d.linkTarget=typeof u=="string"?w(u):"_blank")}),Ye(e,"clickable")},"setLink"),zt=r(function(e,n,u){e.split(",").forEach(function(a){Kt(a,n,u),g.get(a).haveCallback=!0}),Ye(e,"clickable")},"setClickEvent"),Kt=r(function(e,n,u){const a=x.sanitizeText(e,D());if(D().securityLevel!=="loose"||n===void 0)return;const c=a;if(g.has(c)){const o=at(c);let d=[];if(typeof u=="string"){d=u.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let A=0;A<d.length;A++){let T=d[A].trim();T.startsWith('"')&&T.endsWith('"')&&(T=T.substr(1,T.length-2)),d[A]=T}}d.length===0&&d.push(o),fe.push(function(){const A=document.querySelector(`[id="${o}"]`);A!==null&&A.addEventListener("click",function(){Ke.runFunc(n,...d)},!1)})}},"setClickFunc"),Yt=r(function(e){fe.forEach(function(n){n(e)})},"bindFunctions"),I={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},lt=r(function(e){let n=te(".mermaidTooltip");(n._groups||n)[0][0]===null&&(n=te("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),te(e).select("svg").selectAll("g.node").on("mouseover",function(){const u=te(this);if(u.attr("title")===null)return;const a=this.getBoundingClientRect();n.transition().duration(200).style("opacity",".9"),n.text(u.attr("title")).style("left",window.scrollX+a.left+(a.right-a.left)/2+"px").style("top",window.scrollY+a.top-14+document.body.scrollTop+"px"),n.html(n.html().replace(/&lt;br\/&gt;/g,"<br/>")),u.classed("hover",!0)}).on("mouseout",function(){n.transition().duration(500).style("opacity",0),te(this).classed("hover",!1)})},"setupToolTips");fe.push(lt);var We="TB",ct=r(()=>We,"getDirection"),Wt=r(e=>{We=e},"setDirection"),jt=r(function(e){L.has(e)||(L.set(e,{id:e,classes:new Map,children:{},domId:ge+e+"-"+Ue}),Ue++)},"addNamespace"),Qt=r(function(e){return L.get(e)},"getNamespace"),Xt=r(function(){return L},"getNamespaces"),qt=r(function(e,n){if(L.has(e))for(const u of n){const{className:a}=R(u);g.get(a).parent=e,L.get(e).classes.set(a,g.get(a))}},"addClassesToNamespace"),Ht=r(function(e,n){const u=g.get(e);if(n&&u)for(const a of n)a.includes(",")?u.styles.push(...a.split(",")):u.styles.push(a)},"setCssStyle");function ze(e){let n;switch(e){case 0:n="aggregation";break;case 1:n="extension";break;case 2:n="composition";break;case 3:n="dependency";break;case 4:n="lollipop";break;default:n="none"}return n}r(ze,"getArrowMarker");var Vt=r(()=>{var c;const e=[],n=[],u=D();for(const o of L.keys()){const d=L.get(o);if(d){const A={id:d.id,label:d.id,isGroup:!0,padding:u.class.padding??16,shape:"rect",cssStyles:["fill: none","stroke: black"],look:u.look};e.push(A)}}for(const o of g.keys()){const d=g.get(o);if(d){const A=d;A.parentId=d.parent,A.look=u.look,e.push(A)}}let a=0;for(const o of se){a++;const d={id:o.id,label:o.text,isGroup:!1,shape:"note",padding:u.class.padding??6,cssStyles:["text-align: left","white-space: nowrap",`fill: ${u.themeVariables.noteBkgColor}`,`stroke: ${u.themeVariables.noteBorderColor}`],look:u.look};e.push(d);const A=((c=g.get(o.class))==null?void 0:c.id)??"";if(A){const T={id:`edgeNote${a}`,start:o.id,end:A,type:"normal",thickness:"normal",classes:"relation",arrowTypeStart:"none",arrowTypeEnd:"none",arrowheadStyle:"",labelStyle:[""],style:["fill: none"],pattern:"dotted",look:u.look};n.push(T)}}for(const o of K){const d={id:o.id,label:o.label,isGroup:!1,shape:"rect",cssStyles:["opacity: 0;"],look:u.look};e.push(d)}a=0;for(const o of me){a++;const d={id:Ft(o.id1,o.id2,{prefix:"id",counter:a}),start:o.id1,end:o.id2,type:"normal",label:o.title,labelpos:"c",thickness:"normal",classes:"relation",arrowTypeStart:ze(o.relation.type1),arrowTypeEnd:ze(o.relation.type2),startLabelRight:o.relationTitle1==="none"?"":o.relationTitle1,endLabelLeft:o.relationTitle2==="none"?"":o.relationTitle2,arrowheadStyle:"",labelStyle:["display: inline-block"],style:o.style||"",pattern:o.relation.lineType==1?"dashed":"solid",look:u.look};n.push(d)}return{nodes:e,edges:n,other:{},config:u,direction:ct()}},"getData"),ss={setAccTitle:mt,getAccTitle:ft,getAccDescription:Ct,setAccDescription:Et,getConfig:r(()=>D().class,"getConfig"),addClass:U,bindFunctions:Yt,clear:St,getClass:Nt,getClasses:Lt,getNotes:xt,addAnnotation:Ot,addNote:wt,getRelations:$t,addRelation:It,getDirection:ct,setDirection:Wt,addMember:ot,addMembers:vt,cleanupLabel:Rt,lineType:{LINE:0,DOTTED_LINE:1},relationType:I,setClickEvent:zt,setCssClass:Ye,defineClass:Pt,setLink:Ut,getTooltip:Gt,setTooltip:Mt,lookUpDomId:at,setDiagramTitle:bt,getDiagramTitle:kt,setClassLabel:_t,addNamespace:jt,addClassesToNamespace:qt,getNamespace:Qt,getNamespaces:Xt,setCssStyle:Ht,getData:Vt},ns=r(e=>`g.classGroup text {
  fill: ${e.nodeBorder||e.classText};
  stroke: none;
  font-family: ${e.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${e.classText};
}
.edgeLabel .label rect {
  fill: ${e.mainBkg};
}
.label text {
  fill: ${e.classText};
}

.labelBkg {
  background: ${e.mainBkg};
}
.edgeLabel .label span {
  background: ${e.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${e.mainBkg};
  stroke: ${e.nodeBorder};
}

g.classGroup line {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${e.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${e.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${e.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: transparent !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
  line-height: initial;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${e.textColor};
}
`,"getStyles"),Jt=r((e,n="TB")=>{if(!e.doc)return n;let u=n;for(const a of e.doc)a.stmt==="dir"&&(u=a.value);return u},"getDir"),is={getClasses:r(function(e,n){return n.db.getClasses()},"getClasses"),draw:r(async function(e,n,u,a){Me.info("REF0:"),Me.info("Drawing class diagram (v3)",n);const{securityLevel:c,state:o,layout:d}=D(),A=a.db.getData(),T=At(n,c);A.type=a.type,A.layoutAlgorithm=Dt(d),A.nodeSpacing=(o==null?void 0:o.nodeSpacing)||50,A.rankSpacing=(o==null?void 0:o.rankSpacing)||50,A.markers=["aggregation","extension","composition","dependency","lollipop"],A.diagramId=n,await Bt(A,T),Ke.insertTitle(T,"classDiagramTitleText",(o==null?void 0:o.titleTopMargin)??25,a.db.getDiagramTitle()),yt(T,8,"classDiagram",(o==null?void 0:o.useMaxWidth)??!0)},"draw"),getDir:Jt};export{ss as a,is as b,ts as c,ns as s};
