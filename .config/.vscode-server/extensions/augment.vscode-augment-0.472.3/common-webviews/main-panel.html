<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment</title>
    <script nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <script type="module" crossorigin src="./assets/main-panel-zHcgjx1c.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-JC8TPhVf.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-C1unVd78.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-D_WR2I26.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/layer-group-DMm-CNlu.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Bf1-mlh4.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/Content-xvE836E_.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-WghC7pXE.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-NgqNgjwU.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/rules-parser-D2d7xQ-G.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-l00D6itj.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/github-C1jNNmyr.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-bSDyFrZo.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/types-BSMhNRWH.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/types-Cgd-nZOV.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/types-B5Ac2hek.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/index-DiI90jLk.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-CkR7J3XO.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-D2Hs6UjS.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/index-Yat2JVWz.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-BYCw5p2g.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/index-CK0xjdO4.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-Bh9TYMbV.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-IxOJUoWR.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/index-CW7fyhvB.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-BiyeFzqm.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-3TLxExQu.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-PWVCCDdn.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-DfKf7sb_.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BA2QYXi-.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-E1jEbeRV.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-C19Zl3Ej.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-C0teov-5.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-ce3_p-Q7.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-DeFTcAEj.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-D8Nb6HkU.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-BBUsFUTj.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/autofix-state-d-ymFdyn.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-zeLUoeQd.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-S_DCcmJS.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/layer-group-Df_FYENN.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-DB7Uu6wb.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/folder-DRaPdzNV.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/pen-to-square-Dvw-pMXw.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-BFFBoxX3.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/main-panel-DPyc9-tp.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
