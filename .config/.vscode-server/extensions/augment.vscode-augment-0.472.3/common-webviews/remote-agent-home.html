<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <script type="module" crossorigin src="./assets/remote-agent-home-HfC6g5QJ.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-JC8TPhVf.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Bf1-mlh4.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/index-yERhhNs7.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-CajyFoaO.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-C1unVd78.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-D_WR2I26.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/Content-xvE836E_.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-WghC7pXE.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/types-Cgd-nZOV.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-NgqNgjwU.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/types-B5Ac2hek.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/index-CW7fyhvB.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-BiyeFzqm.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-D2Hs6UjS.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-BBUsFUTj.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-E1jEbeRV.js" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/index-9HWdRmiB.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-DXGXtJVn.css" nonce="nonce-4avbSpTeHbijcuIbaUarfg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
